<?php

namespace App\Console\Commands\Reminders;

use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class PendingContractCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:pending-contract';
    
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to instructors If candidate hasn't signed contract 1 day after sending contract, and 3 days after sending contract";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = "reminder:pending-contract";
        $now = Carbon::now();
        $oneDayAgo = $now->copy()->subDay(1)->format('Y-m-d');
        $threeDaysAgo = $now->copy()->subDay(3)->format('Y-m-d');
        
        $instructors = User::active()
        ->where("type", "=", "5")
        ->where("app_notification", "=", "1")
        ->where("profile_status", "=", "6")
        ->whereNull("is_contract")
        ->whereNotNull("contract_sent_date")
            ->where(function ($query) use ($oneDayAgo, $threeDaysAgo) {
                $query->where('contract_sent_date', '<=', $oneDayAgo)
                      ->orWhere('contract_sent_date', '<=', $threeDaysAgo);
            })
            ->pluck('id')->toArray();

            
        if (!empty($instructors)) {
            foreach ($instructors as $id ) {
                pendingContractRemNotify($id, $signature, "user", "user");
            }
        }

    }
}
