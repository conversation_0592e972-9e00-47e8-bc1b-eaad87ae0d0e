<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\District;
use App\ClassroomManagementModel;

use Hash;
use Mail;
use Crypt;
use App\CommomModel;
DB::enableQueryLog();

class TrainingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'trainingvideo','view')!=true){
            return redirect("/no-permission");
        } 
        $training = ClassroomManagementModel::orderBy("id", "desc")->get();
        return view("admin.trainingvideo.list", compact("training"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function add()
    {
        return view("admin.trainingvideo.add");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function save(Request $request)
    {
       
        $type = $request->type;

        $data["video_type"] = $request->type;
        $data["type"] = $request->training_type;
        $data["quiz_link"] = $request->quiz_link;
        $data["description"] = $request->description;
        if ($request->training_type == "Other") {
            $data["title"] = $request->title;
        } else {
            $data["title"] = $request->title;
        }
        if ($request->hasfile("video") && $request->type == "Video") {
            $image = $request->file("video");
            // $extension = $file->getClientOriginalExtension(); 
            // $logopic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/classroom/");
            // $file->move($destinationPath, $logopic);

            $filename = 'uploads/classroom/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["video"] = $filename;
        } else {
            $data["video"] = $request->video;
        }

        if ($request->hasfile("thumbnail")) {
            $image = $request->file("thumbnail");
            // $extension = $file->getClientOriginalExtension(); 
            // $thumbnailpic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/classroom/");
            // $file->move($destinationPath, $thumbnailpic);

            $filename = 'uploads/classroom/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["thumbnail"] = $filename;
        }

        $data["status"] = "1";
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $save = ClassroomManagementModel::insertGetId($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Training video  successfully created",
                "redirect" => url("/manage-training-video"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $training = ClassroomManagementModel::where("id", $id)->first();

        return view("admin.trainingvideo.update", ["training" => $training]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = $request->id;

        $data["video_type"] = $request->type;
        $data["type"] = $request->training_type;
        $data["quiz_link"] = $request->quiz_link;
        $data["description"] = $request->description;
        if ($request->training_type == "Other") {
            $data["title"] = $request->title;
        } else {
            $data["title"] = $request->title;
        }

        if ($request->hasfile("video") && $request->type == "Video") {
            $image = $request->file("video");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $logopic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/classroom/");
            // $file->move($destinationPath, $logopic);
            $filename = 'uploads/classroom/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);


            $data["video"] = $filename;
        } else {
            if ($request->type == "Link") {
                $data["video"] = $request->video;
            }
        }

        if ($request->hasfile("thumbnail")) {
            $image = $request->file("thumbnail");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $thumbnailpic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/classroom/");
            // $file->move($destinationPath, $thumbnailpic);

            $filename = 'uploads/classroom/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $data["thumbnail"] = $filename;
        }

        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = ClassroomManagementModel::where("id", $id)->update($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Details successfully updated",
                "redirect" => url("/manage-district"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
        //     }
        // }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = ClassroomManagementModel::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = ClassroomManagementModel::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = ClassroomManagementModel::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = ClassroomManagementModel::where("id", $id)->first();
            if ($record) {
                $res = ClassroomManagementModel::where(
                    "id",
                    "=",
                    $id
                )->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
