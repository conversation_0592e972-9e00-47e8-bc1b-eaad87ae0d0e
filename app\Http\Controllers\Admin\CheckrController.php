<?php 
// app/Http/Controllers/ZoomController.php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CheckrService;
use PhpParser\Node\Stmt\Return_;

class CheckrController extends Controller
{
    protected $checkrService;

    public function __construct(CheckrService $checkrService)
    {
        $this->checkrService = $checkrService;
    }

    public function createCandidates(Request $request)
    {
      
        $data = [
            'first_name' => 'john',
            'middle_name' => '',
            'last_name' => 'smith',
            'email' => '<EMAIL>',
            'zipcode' => '90401',
            // 'work_locations' =>  ',
            
        ];

        // $response = $this->checkrService->createCandidates($data);
       
            //  if($response['id']){
                $response= $this->invitations('99e0f128421324fc14fc06db');
            // }

        return response()->json($response);
    }

    
    function invitations($id){
        
            $work_locations=array(['country'=>"US",'state'=>"TX"]);

        $data = [
            'package' => 'education_verification',
            'candidate_id' => $id,
            // 'node' => 'test',
            // 'tags' => '90401',
            'work_locations' => $work_locations
            
        ];

         $response = $this->checkrService->createInvitations($data);
         return response()->json($response);
    }

   

}
