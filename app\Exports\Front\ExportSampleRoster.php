<?php

namespace App\Exports\Front;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Collection;

class ExportSampleRoster implements FromCollection, WithHeadings
{
    public function collection()
    {
        return new Collection($this->formatData());
    }

    public function headings(): array
    {
        return [
            'Student Name',
            'Grade',
        ];
    }

    private function formatData()
    {
        return [];
    }
}