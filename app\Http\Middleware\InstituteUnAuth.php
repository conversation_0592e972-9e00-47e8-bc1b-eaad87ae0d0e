<?php

namespace App\Http\Middleware;

use Closure;
use Session;
class InstituteUnAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(Session::has('Clientnewlogin')){ 
            return redirect('institute-dashboard'); 
        }
        return $next($request);
    }
}
