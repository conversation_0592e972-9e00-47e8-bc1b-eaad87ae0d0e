<!-- JS here -->
<script src="<?php echo e(asset('js/moment.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/vendor/modernizr-3.5.0.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/vendor/jquery-3.6.0.min.js')); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/js/bootstrap.bundle.min.js"></script>
<script src=https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/1.1.2/js/bootstrap-multiselect.min.js></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.1.7/semantic.js"></script>
<script src="//cdn.jsdelivr.net/npm/alertifyjs@1.13.1/build/alertify.min.js"></script>
<script src="<?php echo e(asset('globals/bootstrap-datepicker.min.js')); ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ion-rangeslider/2.3.0/js/ion.rangeSlider.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.10.2/umd/popper.min.js"></script>
<script src="<?php echo e(asset('website/js/isotope.pkgd.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/slick.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/jquery.meanmenu.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/ajax-form.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/wow.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/imagesloaded.pkgd.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/jquery.magnific-popup.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/waypoints.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/jquery.counterup.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/plugins.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/swiper-bundle.min.js')); ?>"></script>
<script src="<?php echo e(asset('website/js/main.js')); ?>"></script>
<script src="<?php echo e(asset('js/jquery.timepicker.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/daterangepicker.js')); ?>"></script>
<script src="<?php echo e(asset('js/availability.js')); ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.34/moment-timezone-with-data.min.js"></script>
<script src="<?php echo e(asset('js/timezone.js')); ?>"></script>
<script src="//www.gstatic.com/firebasejs/6.2.3/firebase.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-auth.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-storage.js"></script>
<script src="//www.gstatic.com/firebasejs/6.2.3/firebase-database.js"></script>
<script src="<?php echo e(asset('website/bootstrap-datepicker/bootstrap-datepicker.min.js')); ?>"></script>
<script src="https://weareoutman.github.io/clockpicker/dist/jquery-clockpicker.min.js"></script>
<script src="<?php echo e(asset('js/webchatjs.js')); ?>?<?php echo 'v='.rand(); ?>"></script>
<script src="<?php echo e(asset('js/config.js')); ?>?<?php echo 'v='.rand(); ?>"></script>
<script src="<?php echo e(asset('js/new-onboarding.js')); ?>?<?php echo 'v='.rand(); ?>"></script>
<script src="<?php echo e(asset('website/js/video-recorder.js')); ?>"></script>
<script>

window.serverTimezone = <?php echo json_encode(config('app.servertimezone'), 15, 512) ?>;

var url = $(location).attr('href'),
    parts = url.split("/"),
    last_part = parts[parts.length - 1];
$('#i_prefer_to_teach').select2({
    theme: 'bootstrap4',
    width: 'style',
    placeholder: 'Grade Levels',
    allowClear: Boolean($(this).data('allow-clear')),
});

$('#experience_teaching_ages').select2({
    theme: 'bootstrap4',
    width: 'style',
    placeholder: 'Experience teaching grade levels',
    allowClear: Boolean($(this).data('allow-clear')),
});
$('#certified_special_education').select2({
    theme: 'bootstrap4',
    width: 'style',
    placeholder: 'Select Certifications',
    allowClear: Boolean($(this).data('allow-clear')),
});

$('.teaching_certification_states').each(function () {
    $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: 'Teaching certification valid for',
        allowClear: Boolean($(this).data('allow-clear')),
    });
});
</script>

<script type="text/javascript">
var APP_URL =<?php echo json_encode(url('/')); ?>

var secure_token = '<?php echo e(csrf_token()); ?>';
</script>
<?php $d= date('YmdHis'); ?>
<script src="<?php echo e(asset('js/webcustom.js')); ?>"></script>
<script src="<?php echo e(asset('js/front-custom.js')); ?>"></script>
<script>

if (localStorage.getItem("theme-color") === "light") {
    document.getElementById("light--to-dark-button")?.classList.remove("dark--mode");
}
</script>
<script type="text/javascript">
$(document).ready(function() {
    //Enable Tooltips
    var tooltipTriggerList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    //Advance Tabs
    $(".next").click(function() {

    });

    $(".previous").click(function() {
        const prevTabLinkEl = $(".nav-tabs .active")
            .closest("li")
            .prev("li")
            .find("a")[0];
        const prevTab = new bootstrap.Tab(prevTabLinkEl);
        prevTab.show();
    });
});
</script>
<style>
.fierror{
    margin-left: 20px;
}
.rederror {
    color: red;
}

.fa {
    margin-left: -12px;
    margin-right: 8px;
}

.pass-hints {
    display: none;
}

.disableClick {
   pointer-events: none;
}

.fselect {
    border-radius: 132px;
    border: 1px solid #D8DADC !important;
    background: #FFF;
}

.select2-results__option {
    width: 100%;
}

.brederror {
    border-color: red !important;
    border-radius: 132px !important;
    border: 1px solid red !important;

}
.errorc {
    border-color: red !important;


}
.selection .select2-selection--multiple {
    border-radius: 132px !important;
    /* border: 1px solid #D8DADC !important;
      background: #FFF !important; */
}

.sliderdistance {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.slidercontainer {
    width: 90% !important;
}
</style>


<style>
.displaynone {
    display: none !important;
}

.displayblock {
    display: flex !important;
}

.displaynones {
    display: none !important;
}

.displayblocke {
    display: flex !important;
}
</style>

<script>
$('.ui.checkbox')
    .checkbox({
        onChecked() {
            const options = $('#members_dropdown > option').toArray().map(
                (obj) => obj.value
            );
            $('#members_dropdown').dropdown('set exactly', options);
        },
        onUnchecked() {
            $('#members_dropdown').dropdown('clear');
        },
    });
if (last_part == 'my-availability') {
    function initAutocomplete() {
        const map = new google.maps.Map(document.getElementById("map"), {
            center: {
                lat: -33.8688,
                lng: 151.2195
            },
            zoom: 13,
            mapTypeId: "roadmap",
        });
        // Create the search box and link it to the UI element.
        const input = document.getElementById("teach_in_person_location");
        const searchBox = new google.maps.places.SearchBox(input);

        map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);
        // Bias the SearchBox results towards current map's viewport.
        map.addListener("bounds_changed", () => {
            searchBox.setBounds(map.getBounds());
        });

        let markers = [];

        // Listen for the event fired when the user selects a prediction and retrieve
        // more details for that place.
        searchBox.addListener("places_changed", () => {
            const places = searchBox.getPlaces();

            if (places.length == 0) {
                return;
            }

            // Clear out the old markers.
            markers.forEach((marker) => {
                marker.setMap(null);
            });
            markers = [];

            // For each place, get the icon, name and location.
            const bounds = new google.maps.LatLngBounds();

            places.forEach((place) => {
                if (!place.geometry || !place.geometry.location) {
                    return;
                }

                const icon = {
                    url: place.icon,
                    size: new google.maps.Size(71, 71),
                    origin: new google.maps.Point(0, 0),
                    anchor: new google.maps.Point(17, 34),
                    scaledSize: new google.maps.Size(25, 25),
                };

                // Create a marker for each place.
                markers.push(
                    new google.maps.Marker({
                        map,
                        icon,
                        title: place.name,
                        position: place.geometry.location,
                    }),
                );
                if (place.geometry.viewport) {
                    // Only geocodes have viewport.
                    bounds.union(place.geometry.viewport);
                } else {
                    bounds.extend(place.geometry.location);
                }
            });
            map.fitBounds(bounds);
        });
    }

    window.initAutocomplete = initAutocomplete;
}
</script>
<script>
// On page load or when changing themes, best to add inline in `head` to avoid FOUC
if (localStorage.getItem("theme-color") === "dark" || (!("theme-color" in localStorage) && window.matchMedia(
        "(prefers-color-scheme: dark)").matches)) {
    // document.getElementById("light--to-dark-button")?.classList.add("dark--mode");
}
if (localStorage.getItem("theme-color") === "light") {
    document.getElementById("light--to-dark-button")?.classList.remove("dark--mode");
}

// On page load or when  changing themes, best to add inline in `head` to avoid FOUC
if (localStorage.getItem("theme-color") === "dark" || (!("theme-color" in localStorage) && window.matchMedia(
        "(prefers-color-scheme: dark)").matches)) {
    // document.documentElement.classList.add("is_dark");
}
if (localStorage.getItem("theme-color") === "light") {
    document.documentElement.classList.remove("is_dark");
}
$(document).ready(function() {
    $('#example-getting-started').multiselect({
        templates: {
            button: '<button type="button" class="multiselect dropdown-toggle btn btn-primary" data-bs-toggle="dropdown" aria-expanded="false"><span class="multiselect-selected-text"></span></button>',
        },
    });
});

function attachCalTableListner(){

const icons = $('a.cal-table-icon');

icons.on('click', function(event) {
    const show = $(this).data('show');
    const hide = $(this).data('hide');
    $('#' + show).show();
        $('#' + hide).hide();
        $(this).hide().siblings().show();

});
}
</script>

<?php /**PATH D:\whizara\whizara\resources\views/web/layouts/footerscript.blade.php ENDPATH**/ ?>