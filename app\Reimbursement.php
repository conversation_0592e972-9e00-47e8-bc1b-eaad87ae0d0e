<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Reimbursement extends Model
{
    protected $table = 'reimbursements';

    protected $fillable = [
        'user_id','type', 'program_id', 'description', 'amount', 'receipt', 'status', 'updated_by', 'updated_time'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}