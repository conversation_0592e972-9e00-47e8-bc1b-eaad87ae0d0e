<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\cbo;
use Hash;
use Mail;
use Crypt;
use App\CommomModel;
DB::enableQueryLog();

class CBOController extends Controller
{
    /**
     * Display a listing of the cbo.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managecbo','view')!=true){
            return redirect("/no-permission");
        }  

        $where = [];
        $cbo = cbo::all();
        return view("admin.cbo.cbo", compact("cbo"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function addcbo()
    {
        $country = DB::table("tbl_countries")->get();
        return view("admin.cbo.addcbo", compact("country"));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function savecbo(Request $request)
    {
       
        $name = $request->name;

        if ($name != "") {
            $cboExits = cbo::where("name", "=", $name)->get();
            if (count($cboExits)) {
                return response()->json([
                    "success" => false,
                    "message" => "CBO already exits",
                ]);
            } else {
                $data["name"] = $request->name;
                $data["cust_type"] = $request->cust_type;
                $data["website"] = $request->website;
                $data["first_name"] = $request->first_name;
                $data["last_name"] = $request->last_name;
                $data["email"] = $request->email;
                $data["phone"] = $request->phone;
                $data["country"] = $request->country;
                $data["state"] = $request->state;
                $data["address"] = $request->address;
                $data["zipcode"] = $request->zipcode;
                $data["city"] = $request->city;
                $data["notes"] = $request->notes;
                $data["status"] = "1";
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = cbo::insertGetId($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "CBO  successfully created",
                        "redirect" => url("/manage-cbo"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $cbo = cbo::where("id", $id)->first();
        $country = DB::table("tbl_countries")->get();
        return view("admin.cbo.edit_cbo", [
            "cbo" => $cbo,
            "country" => $country,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
       
        $id = $request->id;
        $name = $request->name;
        $data["name"] = $request->name;
        $data["cust_type"] = $request->cust_type;
        $data["website"] = $request->website;
        $data["first_name"] = $request->first_name;
        $data["last_name"] = $request->last_name;
        $data["email"] = $request->email;
        $data["phone"] = $request->phone;
        $data["country"] = $request->country;
        $data["state"] = $request->state;
        $data["address"] = $request->address;
        $data["zipcode"] = $request->zipcode;
        $data["city"] = $request->city;
        $data["notes"] = $request->notes;
        $data["status"] = "1";
        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = cbo::where("id", $id)->update($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Details successfully updated",
                "redirect" => url("/manage-cbo"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = cbo::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = cbo::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = cbo::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = cbo::where("id", $id)->first();
            if ($record) {
                $res = cbo::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
