<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Programs;
use App\Logistics;
use App\SettingTermsModel;
use App\sub_subjects;

use Hash;
use Mail;
use Crypt;
use App\CommomModel;
use App\Helpers\CustomHelper;
use App\Helpers\DataTableHelper;
use Illuminate\Support\Facades\DB;

class SchoolProgramController extends Controller
{

    public $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function programlist($id,Request $request)
    {


        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            $currentDate = now()->toDateString();

            $qry = Programs::query();

            $program_id = Crypt::decryptString($id);
            $qry->where("school_name", $program_id);

            $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');


            $qry->distinct();

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);
                $viewButton = "";

                $viewRoute =  url('view-program/step1/' . $encryptedStrId);

                $viewButton .= " <a href='{$viewRoute}'>{$row->name}</a>";
                $action = $this->generateActionButtons($res, $encryptedStrId, $row->id);


                $data[] = [
                    "id" => '',
                    "name" => $viewButton,
                    "program_type" => $row->program_type,
                    "delivery_type" => $row->delivery_type,
                    "program_status" => $row->program_status,
                    "start_date" => date('m-d-Y', strtotime($row->start_date)),
                    "end_date" => date('m-d-Y', strtotime($row->end_date)),
                    "status" => $this->generateStatusButton($row->status, $row->id, json_decode($res['manageprogram'], true)),
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


    }

    private function generateStatusButton($status, $id, $userPermissions)
    {
        $updatePermission = in_array('update', $userPermissions);

        switch ($status) {
            case 0:
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $id . ', 1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $id . '">Deactive</a>'
                    : 'Deactive';
            case 1:
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $id . ', 0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $id . '">Active</a>'
                    : 'Active';
            default:
                return '';
        }
    }

    private function generateActionButtons($res, $encryptedStrId, $orgId)
    {
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $logisticsButton ='';

        if (isset($res['manageprogram'])) :
            if (array_key_exists('manageprogram', $res)) :
                if (in_array('update', json_decode($res['manageprogram'], true))) :
                    $editRoute = url('edit-program/' . $encryptedStrId);

                    $editButton = "<a href='{$editRoute}' class='btn btn-rounded btn-block btn-xs btn-outline-secondary mt-0' ><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
                endif;

                if (in_array('delete', json_decode($res['manageprogram'], true))) :

                    $deleteRoute = route('admin.program.delete', ['program' => $orgId]);

                    $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')  class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";

                endif;

                if (in_array('logistics/prerequisites', json_decode($res['manageprogram'], true))) :

                    $logisticsRoute = route('admin.program.logistics', ['program_id' => $encryptedStrId]);

                    $logisticsButton = "<a href='{$logisticsRoute}'   class='btn btn-rounded btn-block btn-xs btn-outline-info mt-0'>Logistics/Prerequisites</a>&nbsp;";

                endif;

            endif;
        endif;



        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$logisticsButton}{$editButton}{$deleteButton}</div>";
    }

    public function viewlogistics($program_id,Request $request)
    {

            $id = decrypt_str($program_id);
            $program = Programs::where("id", $id)->first();
            $logistic = Logistics::where("program_id", $id)->first();
        return view("admin.institute.viewlogistics",compact('program','logistic'));
    }

    public function logisticslist($program_id,Request $request)
    {


        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            $currentDate = now()->toDateString();

            $qry = Logistics::with('program','school','subject','subsubject');


            $qry->where("program_id", $program_id);

            $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');


            $qry->distinct();

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {


                $encryptedStrId = encrypt_str($row->id);
                $viewButton = "";

                $viewRoute =  url('view-program/step1/' . $encryptedStrId);

                $viewButton .= " <a href='{$viewRoute}'>{$row->name}</a>";

                $action = $this->generateLogActionButtons($res, $encryptedStrId, $row->id);
                $getPath=$row->document?generateSignedUrl($row->document):'';
                $getprerequisitesPath=$row->prerequisites?generateSignedUrl($row->prerequisites):'';
                $viewdocument = $row->document?"<a href='{$getPath}' target='_blank'>document</a>":'';
                $viewpredocument = $row->prerequisites?"<a href='{$getprerequisitesPath}' target='_blank'>document</a>":'';


                if(strlen($row->classroom_prerequisites)>50)
                {
                    $des=htmlentities($row->classroom_prerequisites);
                    $description= "<p  data-toggle='tooltip' style='cursor: pointer;' title='{$des}'>".substr($row->classroom_prerequisites,0,50)."...</p>";

                }else{
                    $description=$row->classroom_prerequisites;
                }

                if(strlen($row->course_prerequisites)>50)
                {
                    $des=htmlentities($row->course_prerequisites);
                    $cdescription= "<p  data-toggle='tooltip' style='cursor: pointer;' title='{$des}'>".substr($row->course_prerequisites,0,50)."...</p>";

                }else{
                    $cdescription=$row->course_prerequisites;
                }
                $data[] = [
                    "id" => '',
                    "program_id" => $viewButton,

                    "classroom_prerequisites" => $description,
                    "course_prerequisites" => $cdescription,
                    "title" => $row->title,
                    "document"=>$viewdocument,
                    "prerequisites"=>$viewpredocument,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


    }

    private function generateLogActionButtons($res, $encryptedStrId, $orgId)
    {
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $logisticsButton ='';

        if (isset($res['manageprogram'])) :
            if (array_key_exists('manageprogram', $res)) :
                if (in_array('logistics/prerequisites', json_decode($res['manageprogram'], true))) :
                    $editRoute = route('admin.logistics.edit-logistics', ['logistic_id' => $encryptedStrId]);
                    $editButton = "<a href='{$actionUrl}' onclick=openAdminModal('$editRoute','#common-admin-modal') class='btn btn-rounded btn-block btn-xs btn-outline-secondary mt-0' ><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
                endif;

                if (in_array('logistics/prerequisites', json_decode($res['manageprogram'], true))) :

                    $deleteRoute = route('admin.logistics.delete', ['logistic_id' => $orgId]);

                    $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')  class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";

                endif;



            endif;
        endif;



        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$deleteButton}</div>";
    }
    private function generateStatus($status)
    {

        switch ($status) {
            case 2:
                return 'Decline';
            case 1:
                return 'Accepted';
            default:
                return 'Pending';
        }
    }

    public function addLogistics($program_id,Request $request){
        $id = $program_id;

        $program = Programs::where("id", $program_id)->first();
        $school_id=$program->school_name;
        if($program->delivery_type=='Online'){
            $doc = SettingTermsModel::where("id", '12')->first();
            $precdesc=$doc->description;
            if($school_id){
            $precdesc= str_replace('{{school_name}}', schoolusername($school_id), $precdesc);

            }
        }elseif($program->delivery_type=='In-Person'){
            $doc = SettingTermsModel::where("id", '13')->first();
            $precdesc=$doc->description;
            if($school_id){

            $precdesc= str_replace('{{school_name}}', schoolusername($school_id), $precdesc);
            }
        }else{
            $precdesc='';
        }

        if($program->sub_subject_id){
            $res = sub_subjects::where("id", $program->sub_subject_id)->first();
              $subname=$res->name;
            if($program->delivery_type=='Online'){
                $presdesc = $res->online_doc;

            }elseif($program->delivery_type=='In-Person'){
                // $presdesc = $res->inperson_doc;
                // as the inperson is commented on ui now
                $presdesc = $res->online_doc;

            }else{
                $presdesc='';
            }
        }else{

            $presdesc='';
        }


        $view = view("components.admin.modals.addlogistics", compact('id','precdesc','presdesc'))->render();
        return response()->json(['status' => true, 'view' => $view]);

    }

    public function submitLogistics($program_id,Request $request)
    {

        $request->validate(
            [

                'class_prerequisites' => 'required',
                'course_prerequisites' => 'required',
            ]
        );

        if ($request->hasfile("classroom_document")) {
            $request->validate(
                [


                    'classroom_document' => 'required|mimes:PDF,pdf,doc|file|max:2048',

                ]
            );
        }

        if ($request->hasfile("course_document")) {
            $request->validate(
                [



                    'course_document' => 'required|mimes:PDF,pdf,doc|file|max:2048',
                ]
            );
        }



        $program = Programs::where("id", $program_id)->first();

        if($program->delivery_type=='Online'){
            $doc = SettingTermsModel::where("id", '12')->first();
           $fileurl= $doc->document;

        }elseif($program->delivery_type=='In-Person'){
            $doc = SettingTermsModel::where("id", '13')->first();
            $fileurl= $doc->document;

        }

        $subject_id=$program->subject_id?$program->sub_subject_id:'';;
        $sub_subject_id=$program->sub_subject_id?$program->sub_subject_id:'';
        $school_id=$program->school_name;

        $Logistics =new Logistics;
        $res = sub_subjects::where("id", $program->sub_subject_id)->first();
        // $subname=$res->name;

        $Logistics->program_id =  $program_id;
        $Logistics->subject_id = $subject_id;
        $Logistics->sub_subject_id = $sub_subject_id;
        if($school_id){
            $Logistics->school_id = $school_id;
        }

        $Logistics->course_prerequisites = $_POST['course_prerequisites'];
        $Logistics->classroom_prerequisites = $_POST['class_prerequisites'];
        $Logistics->created_by = session()->get('Adminnewlogin')['id'];
        $Logistics->status = '1';

        if($request->hasfile("classroom_document")) {

            $image = $request->file("classroom_document");
            $filename = 'uploads/logistics/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $Logistics->prerequisites = $filename;

        }else{
            $Logistics->prerequisites = $fileurl?$fileurl:'';
        }

        if($request->hasfile("course_document")) {
            $image = $request->file("course_document");
            $filename = 'uploads/logistics/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $Logistics->document = $filename;
        }



        $res=$Logistics->save();

         if($res){

             return response()->json(['status' => true, 'message' => "Saved successfully", 'reload' => true]);
         }




    }
    public function delete($logistic_id)
    {
        $res = Logistics::where('id',$logistic_id)->delete();
        // $res = true;
        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
                'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }

    public function editLogistics($logistic_id,Request $request){
        $id = decrypt_str($logistic_id);
        $res = Logistics::where('id',$id)->first();

        $program = Programs::where("id", $res->program_id)->first();

        if($program->delivery_type=='Online'){
            $doc = SettingTermsModel::where("id", '12')->first();

        }elseif($program->delivery_type=='In-Person'){
            $doc = SettingTermsModel::where("id", '13')->first();
        }

        if($program->sub_subject_id){
            $ressub = sub_subjects::where("id", $program->sub_subject_id)->first();
              $subname=$ressub->name;
            if($program->delivery_type=='Online'){
                $subdoc = $ressub->online_doc;
            }elseif($program->delivery_type=='In-Person'){
                $subdoc = $ressub->inperson_doc;
            }else{
                $subdoc ='';
            }
        }else{
            $subdoc ='';
            $subname='';
        }
        $view = view("components.admin.modals.editlogistics", compact('res','doc','subdoc','subname'))->render();
        return response()->json(['status' => true, 'view' => $view]);

    }

    public function updateLogistics($logistic_id,Request $request)
    {

        $request->validate(
            [

                'class_prerequisites' => 'required',
                'course_prerequisites' => 'required',
            ]
        );

        if ($request->hasfile("classroom_document")) {
            $request->validate(
                [


                    'classroom_document' => 'required|mimes:PDF,pdf,doc|file|max:2048',

                ]
            );
        }

        if ($request->hasfile("course_document")) {
            $request->validate(
                [



                    'course_document' => 'required|mimes:PDF,pdf,doc|file|max:2048',
                ]
            );
        }


        $Logistics['course_prerequisites'] = $_POST['course_prerequisites'];
        $Logistics['classroom_prerequisites'] = $_POST['class_prerequisites'];
        $Logistics['created_by'] = session()->get('Adminnewlogin')['id'];
        $res = Logistics::where('id',$logistic_id)->first();

        $program = Programs::where("id", $res->program_id)->first();


        if($program->delivery_type=='Online'){
            $doc = SettingTermsModel::where("id", '12')->first();
            $fileurl= $doc->document;

        }elseif($program->delivery_type=='In-Person'){
            $doc = SettingTermsModel::where("id", '13')->first();
            $fileurl= $doc->document;
        }

        if ($request->hasfile("classroom_document")) {
            $image = $request->file("classroom_document");
            $filename = 'uploads/logistics/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $Logistics['prerequisites'] = $filename;
        }else{
            $Logistics['prerequisites'] = $fileurl?$fileurl:'';
        }

        if ($request->hasfile("course_document")) {
            $image = $request->file("course_document");
            $filename = 'uploads/logistics/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $Logistics['document'] = $filename;

        }

        $res=Logistics::where('id',$logistic_id)->update($Logistics);

         if($res){

             return response()->json(['status' => true, 'message' => "Updated successfully", 'reload' => false]);
         }




    }
}