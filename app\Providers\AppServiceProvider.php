<?php

namespace App\Providers;

use App\NotificationRequirement;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        
        View::composer('*', function ($view) {
            $unread_notifications = NotificationRequirement::where('is_read', false)->get();
            $view->with('unread_notifications', $unread_notifications);
        });
    }


}
