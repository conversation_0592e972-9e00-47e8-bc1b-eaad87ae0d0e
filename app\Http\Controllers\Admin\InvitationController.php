<?php
namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Programs;
use App\InvitationModel;
use App\Helpers\DataTableHelper;
use Mail;
class InvitationController extends Controller
{

    public function index($id,Request $request)
    {

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'id';
            }

            $qry = InvitationModel::where("program_id", "=", $id)->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            if(session('Adminnewlogin')['type']==4){
                $qry->where(["created_by" => session('Adminnewlogin')['id']]);
            }

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });




            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);

            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);

                $action = $this->generateActionButtons($encryptedStrId,($row));
                if(strlen($row->notes) > 50){
                    $notes=substr($row->notes, 0, 50).'...';

                }else{
                   $notes= $row->notes;
                }

                $exits= getemailexits($row->email);

                if($exits){
                    $status='Accepted';
                }else{
                    $status='Pending';
                }

                $data[] = [
                    "id" => $i,
                    "name" =>  $row->name,
                    "email" => $row->email,
                    "notes" =>  $notes,
                    "type" =>  $row->type,
                    "status" =>  $status,
                    "created_by" =>  $row->user->first_name . ' ' . $row->user->last_name,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


    }

    private function generateActionButtons($encryptedStrId,$res)
    {

        $actionUrl = "javascript:void(0);";
        $deleteButton = $resendButton = '';


        $deleteRoute = route('admin.program.invitation.delete', ['eid' => $encryptedStrId]);
        $resendRoute = route('admin.program.resend-invitation', ['id' => $res->id]);

             if($res->email){
               $exits= getemailexits($res->email);
               if(!$exits){
                $resendButton = "<a href='{$actionUrl}' onclick=openAdminModal('$resendRoute')><button class='btn btn-rounded btn-block btn-xs btn-outline-success'>Resend</button></a>&nbsp";

               }

               }


        $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";




        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$resendButton}{$deleteButton}</div>";
    }

    public function delete($id,Request $request)
    {
        $did = decrypt_str($id);

        if (isset($id)) {
            $record = InvitationModel::where("id", $did)->first();
            if ($record) {
                $res = InvitationModel::where("id", "=", $did)->delete();

                if ($res) {
                    return response()->json([
                        "status" => true,
                        "message" => "Successfully deleted",
                        'reload' => false
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function addinvitation($program, Request $request)
    {

        $view = view("components.admin.modals.instructor-invitation", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function resendinvitation($id, Request $request)
    {
        $obj = InvitationModel::where("id", $id)->first();
        $view = view("components.admin.modals.re-instructor-invitation", compact('obj'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function store(Request $request)
    {
        $request->validate(
            [
                'program_id' => 'required',
                'name' => 'required',
                'email' => 'required',
                'notes' => 'required',

            ]
        );

        $data = [
            'program_id' => $request->program_id,
            'name' => $request->name,
            'email' => $request->email,
            'notes' => $request->notes,
            'type' => 'main',
            'created_by'=>session('Adminnewlogin')['id']
        ];
        $obj = InvitationModel::create($data);
   if($obj){

    $template = DB::table("tbl_email_templates")->where("email_template_id", "22")->first();
    $body =  $template->description;


    $body= str_replace('{{NAME}}', $request->name, $body);
    if($request->notes){
        $body= str_replace('{{NOTE}}', $request->notes, $body);
    }
    $url=url('/teachers');
    $body= str_replace('{{APP_URL}}', $url, $body);

    $subject=$template->subject;
    $email=$request->email;
    $data=array('template'=>$body);
    Mail::send('template', $data, function (
            $message
        ) use ($email,$subject) {
            $message->to($email)->subject($subject);
        });

    return response()->json(['status' => true, 'message' => "Invitation sent successfully", 'reload' => true]);

  }else{
    return response()->json([
        "success" => false,
        "message" => "Something went worng",
    ]);
   }


    }


    public function resendstore(Request $request)
    {
        $request->validate(
            [
                // 'program_id' => 'required',
                // 'name' => 'required',
                // 'email' => 'required',
                'notes' => 'required',
            ]
        );

        $data = [

            'notes' => $request->notes,
        ];

  $obj = InvitationModel::where("id", $request->id)->first();
   if($obj){
    InvitationModel::where("id", $request->inv_id)->update($data);

    $template = DB::table("tbl_email_templates")->where("email_template_id", "22")->first();
    $body =  $template->description;


    $body= str_replace('{{NAME}}', $request->name, $body);
    if($request->notes){
        $body= str_replace('{{NOTE}}', $request->notes, $body);
    }
    $url=url('/teachers');
    $body= str_replace('{{APP_URL}}', $url, $body);

    $subject=$template->subject;
    $email=$obj->email;
    $data=array('template'=>$body);
    Mail::send('template', $data, function (
            $message
        ) use ($email,$subject) {
            $message->to($email)->subject($subject);
        });

    return response()->json(['status' => true, 'message' => "Invitation sent successfully", 'reload' => true]);

  }else{
    return response()->json([
        "success" => false,
        "message" => "Something went worng",
    ]);
   }


    }



}
