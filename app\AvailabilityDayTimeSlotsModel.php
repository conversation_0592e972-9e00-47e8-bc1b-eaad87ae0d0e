<?php

namespace App;

use App\Casts\TimeCast;
use Illuminate\Database\Eloquent\Model;
use DateTime;
use DateTimeZone;

class AvailabilityDayTimeSlotsModel extends Model
{
    protected $table = 'tbl_user_availability_slots';
         protected $casts = [
        'from_time' => TimeCast::class,
        'to_time' => TimeCast::class,
    ];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type', 'position', 'day', 'from_time', 'to_time', 'location', 'to_location', 'timezone', 'step', 'range_id'
    ];


    private function getUserTimezone()
    {
        $timezone = config('app.timezone', 'America/Los_Angeles');
        $user = request()->user();
        $isAdmin = in_array('CheckSession', request()->route()->middleware());
        $isSchool = in_array('CheckSchoolSession', request()->route()->middleware());

        if ($user  && !$isAdmin && !$isSchool) {
            $timezone = optional(@$user->availability)->teach_in_person_timezone ?? $timezone;
        } elseif ($isAdmin || $isSchool) {
            $timezone = $this->range->availability->teach_in_person_timezone ?? $timezone;
        }

        return $timezone;
    }

    public function getFromTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }

    public function getToTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }


    public function getFromTimeUtcAttribute()
    {
        return $this->attributes['from_time']; //  raw attribute in the database
    }

    public function getToTimeUtcAttribute()
    {
        return $this->attributes['to_time']; // raw attribute in the database
    }


    public function range()
    {
        return $this->belongsTo(AvailabilityRangeModel::class, 'range_id');
    }
}
