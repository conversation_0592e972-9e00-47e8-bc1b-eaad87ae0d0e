<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class MakeupClassRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'instructor' => 'required',
            'class_date' => 'required|date',
            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
            'admin_type' => 'required',
            // 'deadline' => 'required_unless:standby,standby',

        ];
    }

    
    public function attributes()
    {
        return [
            'user_id' => 'Instructor',
            'admin_type' => 'Instructor type',
        ];
    }

}
