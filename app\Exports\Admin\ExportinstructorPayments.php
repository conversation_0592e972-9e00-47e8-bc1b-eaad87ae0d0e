<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{Programs, User};
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use DB;
class ExportinstructorPayments implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $filter = [];
        $filter['user_id'] = $request['user_id'];
        $filter['status'] = $request['status'];
        $filter['daterange'] = $request['daterange'];
        $this->requestFilters = $filter;

    }

    public function collection()
    {

        $programsQry = Programs::query();
        $programsQry->whereHas('programNoteAmounts');
        $query = ProgramNoteAmount::with('note', 'user', 'program');
        $this->applyFilters($query);
        $query->select('id',
            DB::raw('sum(amount) as amount'),
            DB::raw('count(*) as classes'),
            DB::raw('FLOOR(SUM(minutes) / 60) + SUM(hours) as hours'),  // Add extra hours from minutes
            DB::raw('MOD(SUM(minutes), 60) as minutes'),                // Remainder minutes after converting to hours
            'user_id',
            'payment_date_updated'
        );
        $query->groupBy('user_id');

        return $query->orderBy('id', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = $this->requestFilters;

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }
        switch($filters['status']) {
            case 'paid': $query->whereNotNull('payment_date_updated'); break;
            case 'unpaid': $query->whereNull('payment_date_updated'); break;
        }
        $separator = ' TO ';
        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }

    protected function applyDateRangeFilter($query, $daterange, $separator)
    {

        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

        $query->whereHas('note', function ($q) use ($startDate, $endDate) {
            $q->whereBetween('class_date', [$startDate, $endDate]);
        });
    }

    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'User Id',
            'Instructor Name',
            'Instructor Email',
            'Classes Taken',
            'Hours',
            'Minutes',
            'Amount',
        ];
        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    public function map($row): array
    {
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
        return [
            $row->user ? ($row->user->id) : '',
            $row->user ? ($row->user->first_name . ' ' . $row->user->last_name) : '',
            $row->user ? ($row->user->email) : '',
            $row->classes,
            $row->hours,
            $row->minutes,
            $formattedAmount,
        ];
    }

    protected function getFiltersInfo(): string
    {
        $filters = $this->requestFilters;

        $filtersInfo = '';

        if (!empty($filters['user_id'])) {
            $user = User::find($filters['user_id']);
            if ($user) {
                $filtersInfo .= 'Instructor: ' . $user->first_name . ' ' . $user->last_name . PHP_EOL;
            }
        }

        if (!empty($filters['daterange'])) {
            $filtersInfo .= 'Date Range: ' . $filters['daterange'] . PHP_EOL;
        }

        if (!empty($filters['status'])) {
            $filtersInfo .= 'Payment Status: ' . $filters['status']  . PHP_EOL;
        }

        return $filtersInfo;
    }
}
