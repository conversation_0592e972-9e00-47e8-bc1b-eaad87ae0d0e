<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\Users;
use App\User;
use App\Permission;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\scheduledInterview;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\ViewClassroomModel;
use App\UserUponCompletionNoteModel;
use App\rubric;
use App\user_references;
use App\UserEducationModel;
use App\user_interview_slots;
use App\user_contract;
use App\document_form;
use App\UserThirdStepModel;
use App\UserFirstStepModel;
use App\StateModel;

use Session;
use Hash;
use Mail;
use App\Helpers\DataTableHelper;
use Illuminate\Support\Facades\Crypt;

DB::enableQueryLog();
class AdminManagementController extends Controller
{
    public function index(Request $request)
    {

        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'staff','view')!=true){
            return redirect("/no-permission");
        }

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }
            $qry = Users::select("users.*", "tbl_roles.name as rolename")
            ->where("users.id", "!=", "1")
            ->where("users.type", "!=", "5")
            ->where("users.type", "!=", "6")
            ->where("users.type", "!=", "7")
            ->join("tbl_roles", "tbl_roles.id", "=", "users.type")
            ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');


            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });




            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);



                $action = $this->generateActionButtons($encryptedStrId,$res);

                $data[] = [
                    "id" => $row->id,
                    "first_name" => $row->first_name.' '.$row->last_name,
                    "email" => $row->email,
                    "type" => $row->rolename,
                    "status" => $this->generateStatusButton($row->status, $row->id),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        // $admin = DB::table("users")
        //     ->select("users.*", "tbl_roles.name as rolename")
        //     ->where("users.id", "!=", "1")

        //     ->where("users.type", "!=", "5")
        //     ->where("users.type", "!=", "6")
        //     ->where("users.type", "!=", "7")
        //     ->join("tbl_roles", "tbl_roles.id", "=", "users.type")
        //     ->orderBy("users.id", "DESC")
        //     ->get();
            $admin = array();
        return view("admin.admin-management.admin_list", compact("admin"));
    }

    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<button onclick="status_update(' . $id . ', 1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $id . '">' . __('messages.deactive') . '</button>';
            case 1:
                return '<button onclick="status_update(' . $id . ', 0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $id . '">' . __('messages.active') . '</button>';
            case 2:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deleted</button>';
            case 3:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deactivated</button>';
            default:
                return '';
        }
    }

    private function generateActionButtons($encryptedStrId,$res)
    {
        $viewRoute = url('viewdetails/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = '';

        if (isset($res['manageinstructor'])) :
            if (array_key_exists('staff', $res)) :
                if (in_array('update', json_decode($res['staff'], true))) :
                    $editRoute = url('edit-admin/' . $encryptedStrId);

                    $editButton = "<a href='{$editRoute}'><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>";
                endif;

                if (in_array('delete', json_decode($res['staff'], true))) :
                    $deleteButton = "<a class='admin_delete' href='{$actionUrl}' data-id='{$encryptedStrId}'><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;

            endif;
        endif;


        $viewButton = "<a href='{$viewRoute}'><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";


        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$viewButton}{$deleteButton}</div>";
    }
    public function add_admin(Request $request)
    {
        $role = DB::table("tbl_roles")
            ->where("status", "1")
            // ->where("id", "!=", "5")
            ->where("id", "!=", "6")
            ->where("id", "!=", "7")
            ->orderBy("id", "asc")
            ->get();

            $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();

        $department = DB::table("tbl_departments")
            ->where("status", "1")
            ->orderBy("id", "asc")
            ->get();
        return view(
            "admin.admin-management.add_admin",
            compact("role", "department","states")
        );
    }
    public function save_admin(Request $request)
    {
        $email = $request->email;
        $userExits = Users::where("email", "=", $email)->get();
        if (count($userExits)) {
            return response()->json([
                "success" => false,
                "message" => "Email already exits",
            ]);
        }
        $obj = [];
        $length = 6;
        $randpassword = substr(
            str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $user_id = substr(str_shuffle("0123456789"), 1, $length);
        $obj["user_id"] = $user_id;
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["email"] = $request->input("email");
        $obj["department"] = $request->input("department");
        $obj["phone_number"] = $request->input("phone");
        $obj["type"] = $request->input("type");

        if($request->input("type")=='5'){

            $obj["description"] = $request->input("about");

        }else{
            $obj["about"] = $request->input("about");
        }



        $obj["password"] = Hash::make($randpassword);
        $obj["profile_status"] = 12;
         $obj["email_verify_status"] = 1;

             if(isset($request->state)) {
                $obj["regions"] = implode(",", $request->state);
             }

        // $obj["passwordStr"] = $randpassword;
        $obj["status"] = "1";
        $obj["created_at"] = date("Y-m-d H:i:s");
        $obj["updated_at"] = date("Y-m-d H:i:s");
        if ($request->hasfile("file_data")) {
            $image = $request->file("file_data");
            $filename = 'uploads/admin/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $obj["image"] = $filename;
        }
        if($request->input("type")=='5'){

        // $obj["teach"] = 'hybrid';
        }
        $obj["onlinerate"] = $request->input("onlinerate") ?? '';
        $obj["inpersonrate"] = $request->input("inpersonrate") ?? '';
        $obj["is_approved"] = $request->input("profilestatusedit") ?? '';
        $obj["is_sub"] = $request->input("is_sub") ? $request->input("is_sub") : 0;
        $obj['city'] = $request->input('city') ?? '';
        if($request->input('state'))
        $obj['state'] = implode(', ', ($request->input('state')));

        $save = Users::insertGetId($obj);
            if($request->input("type")=='5'){
            $redirect=url("/sign-in-with-email");

            $pageurl='/instructor-list';
            }else{
                $pageurl='/add-admin-management';
                $redirect=url("/admin");
            }

            $dataEmail = [
                "email" => $request->email,
                "subject" => "Created Staff",
                "mailbody" => "New Staff",
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "randpassword" => $randpassword,
                "redirect" => $redirect,
            ];

        if($request->input("type")=='5'){
            $dataf["format"] = 'hybrid';
            $dataf["schedule"] = 'During school hours,After school hourss';
            $dataf["user_id"] = $save;
            $dataf["created_at"] = date("Y-m-d H:i:s");
            $dataf["updated_at"] = date("Y-m-d H:i:s");
            $first = UserThirdStepModel::where([
                "user_id" => $save,
            ])->first();
            if (!empty($first)) {
                UserThirdStepModel::where(["user_id" => $save])->delete();
            }
            $resultf = UserThirdStepModel::insert($dataf);
            $obj1["specify_you_work_authorization"] = $request->input("specify");
            $obj1["user_id"] = $save;

            $first1 = UserFirstStepModel::where([
                "user_id" =>  $save,
            ])->first();

            if (!empty($first1)) {
                UserFirstStepModel::where("user_id",  $save)->update($obj1);
            } else {
                UserFirstStepModel::insert($obj1);
            }




            $template = DB::table("tbl_email_templates")->where("email_template_id", "1")->first();
            $body =  $template->description;

            if($request->last_name){
                $full_name=$request->first_name.' '.$request->last_name;
            }else{
                $full_name=$request->first_name;
            }

            $body= str_replace('{{NAME}}', $full_name, $body);

            $body = str_replace('{{Password}}', $randpassword, $body);
            $body = str_replace('{{Username}}', $request->email, $body);
            $subject=$template->subject;
            $data=array('template'=>$body);
           Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });

        }else {
            $template = DB::table("tbl_email_templates")->where("email_template_id", "2")->first();
            $body =  $template->description;
            if($request->last_name){
                $full_name=$request->first_name.' '.$request->last_name;
            }else{
                $full_name=$request->first_name;
            }

            $body= str_replace('{{NAME}}', $full_name, $body);
            $body = str_replace('{{Password}}', $randpassword, $body);
            $body = str_replace('{{Username}}', $request->email, $body);
             $subject=$template->subject;

             $data=array('template'=>$body);

            Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });
        }



        if ($save) {
            if($request->input("type")=='5'){
                return response()->json([
                    "success" => true,
                    "message" => "Instrcutor Created Successfully",
                    "redirect" => url($pageurl),
                ]);
            }else{
                return response()->json([
                    "success" => true,
                    "message" => "Staff Created Successfully",
                    "redirect" => url($pageurl),
                ]);
            }

        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }
    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Users::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }
    public function delete_admin(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = User::where("id", $id)->first();
            if ($record) {

                $user = $record;

                if (count($user->activeClasses) > 0 || count($user->activeSubClasses) > 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Unable to delete a instructor account because its currently running a program.',
                    ]);
                }

                $res = Users::where("id", "=", $id)->delete();
                $res1 = Permission::where("user_id", "=", $id)->delete();


                if($res) {
                AssessmentsModel::where("user_id", "=", $id)->delete();
                UserQuizModel::where("user_id", "=", $id)->delete();
                scheduledInterview::where("user_id", "=", $id)->delete();
                BackgroundMedicalModel::where("application_id", "=", $id)->delete();
                UponCompletionModel::where("user_id", "=", $id)->delete();
                ViewClassroomModel::where("user_id", "=", $id)->delete();
                UserUponCompletionNoteModel::where("user_id", "=", $id)->delete();
                rubric::where("user_id", "=", $id)->delete();
                user_references::where("user_id", "=", $id)->delete();
                UserEducationModel::where("user_id", "=", $id)->delete();
                user_interview_slots::where("user_id", "=", $id)->delete();
                user_contract::where("user_id", "=", $id)->delete();
                DB::table("tbl_user_work_authorizations")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_teaching_preferences")->join(
                    "tbl_user_subjects",
                    "tbl_user_teaching_preferences.id",
                    "=",
                    "tbl_user_subjects.step_id"
                )->where("tbl_user_teaching_preferences.user_id", "=", $id)->delete();

                DB::table("tbl_user_experiences")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_hourly_rates")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_quiz_answers")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_availability_slots")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_availabilities")->join(
                    "tbl_user_availability_ranges",
                    "tbl_user_availabilities.id",
                    "=",
                    "tbl_user_availability_ranges.availability_id"
                )->where("tbl_user_availabilities.user_id", "=", $id)->delete();
                DB::table("tbl_user_availability_location")->where("user_id", "=", $id)->delete();
                DB::table("tbl_user_administrative_information")->where("user_id", "=", $id)->delete();
                DB::table("tbl_profile_status_histories")->where("user_id", "=", $id)->delete();
                DB::table("tbl_notifications")->where("user_id", "=", $id)->delete();
                DB::table("tbl_invite_program_owners")->where("user_id", "=", $id)->delete();
                DB::table("program_notes")->where("user_id", "=", $id)->delete();
                DB::table("tbl_invite_application_recruiters")->where("user_id", "=", $id)->delete();
                DB::table("tbl_invite_programs")->where("user_id", "=", $id)->delete();

                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
    public function edit_admin(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $admin = Users::where("id", $id)->first();
        $role = DB::table("tbl_roles")
            ->where("status", "1")
            // ->where("id", "!=", "5")
            ->where("id", "!=", "6")
            ->where("id", "!=", "7")
            ->orderBy("id", "asc")
            ->get();
            $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
        $department = DB::table("tbl_departments")
            ->where("status", "1")
            ->orderBy("id", "asc")
            ->get();
        return view("admin.admin-management.edit_admin", [
            "admin" => $admin,
            "role" => $role,
            "department" => $department,
            "states"=>$states
        ]);
    }
    public function admin_update(Request $request)
    {
        $obj = [];
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["email"] = $request->input("email");
        // $obj['gender'] = $request->input('gender');
        $obj["department"] = $request->input("department");
        $obj["phone_number"] = $request->input("phone");
        // $obj['dob'] = date('Y-m-d', strtotime($request->input('dob')));
        $obj["type"] = $request->input("type");
        $obj["about"] = $request->input("about");
        if(isset($request->state)) {
            $obj["regions"] = implode(",", $request->state);
         }

        $id = $request->input("id");
        Users::where("id", $id)->update($obj);
        return response()->json([
            "success" => true,
            "message" => "Successfully update",
        ]);
    }
    public function adminimagechange(Request $request)
    {
        if ($request->hasFile("filedata")) {
            $image = $request->file("filedata");
            $filename = 'uploads/admin/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $destinationPathname =  $filename;
            $name =generateSignedUrl($filename);
        }
        $id = $request->input("id");
        $obj = [];
        $obj["image"] = $destinationPathname;
        $result =Users::where("id", $id)
            ->update($obj);
        if ($result) {
            $data["success"] = true;
            $data["message"] = "Image Updated successfully";
            $data["img"] = $name;
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }

    public function viewdetails($id)
    {
        $user_id = decrypt_str($id);

        $user_list =Users::where("users.id", $user_id)
            ->first();

        return view(
            "admin.admin-management.viewdetails",
            compact("user_list", "user_id")
        );
    }

    public function change_password($id)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $user_id = decrypt_str($id);
        return view(
            "admin.admin-management.changepassword",
            compact("user_id")
        );
    }

    public function updateChangePassword(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $new_password = $request->input("new_password");
        $confirm_password = $request->input("confirm_password");
        $id = $request->input("userid");

        $login = Users::where("id", $id)->first();

        if (!empty($login)) {
            if ($new_password == $confirm_password) {
                $insertpwd = bcrypt($new_password);
                Users::where("id", $id)->update([
                    "password" => $insertpwd,
                    // "passwordStr" => $new_password,
                ]);

                return response()->json([
                    "success" => true,
                    "message" => "Password successfully changed",
                    "redirect" => url("/viewdetails/" . encrypt_str($id)),
                ]);
            } else {
                return response()->json([
                    "success" => true,
                    "message" =>
                        "New password and Confirm password does not matchd",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Password does not exist",
            ]);
        }
    }
}
