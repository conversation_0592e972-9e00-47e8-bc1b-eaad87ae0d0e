<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class LastChatModel extends Model
{
    protected $table = 'tbl_last_chat_message';
    protected $fillable = ['from_id', 'to_id', 'message', 'requirement_id', 'from_instructor', 'to_instructor'];

    public function user()
    {
        return $this->belongsTo(User::class, 'to_id', 'id');
    }
    
    public function requirements()
    {
        return $this->hasMany(NewSchoolPostRequirementModel::class, 'id', 'requirement_id');
    }

    public function k12User()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'to_id', 'id');
    }
}
