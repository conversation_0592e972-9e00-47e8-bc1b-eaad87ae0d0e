<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class invite_programs extends Model
{
    use SoftDeletes;
    protected $table = 'tbl_invite_programs';

    protected $fillable = [
        'user_id', 'program_id', 'status', 'is_approved',
        'type', 'admin_type', 'replacement_type', 'requested_by',
        'has_requested', 'parent_id', 'is_makeup', 'deadline', 'timezone', 'current_time',
        'is_sub_only', 'resend_count', 'is_auto_invite', 'deleted_at',
        'has_cancelled','is_standby','is_replace','replacement_start_date','cancelled_by','program_invite_type','deleted_at'
    ];
    protected $dates = ['deadline','replacement_start_date'];
    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function requester()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function parent()
    {
        return $this->belongsTo(invite_programs::class, 'parent_id');
    }

    public function processDeclinedInvite()
    {
        if($this->parent){
            $this->parent->user_id  =  null;
            $this->parent->save();
        }
        if ($this->notes) {


            $program_note_ids = $this->notes()->pluck('program_note_id')->toArray();
            ProgramNote::whereIn('id', $program_note_ids)->update(['sub_user_id' => null, 'is_sub_requested' => 0]);


            $this->notes()->delete();
        }
        if ($this->program->userNotes->isNotEmpty()) {
            $newNote = ProgramNote::firstWhere('invite_id', $this->id);
            if ($newNote === null) {
                return true;
            }
            $newNote->delete();
            $note = ProgramNote::find($newNote->parent_id);
            $note->reassigned_to = '';
            $note->is_reassigned = 0;
            $note->save();
        }

        
    }

    public function notes()
    {
        return $this->hasMany(InviteProgramNote::class, 'invite_program_id');
    }
    public function programNotes()
    {
        return $this->hasManyThrough(
            ProgramNote::class,
            InviteProgramNote::class,
            'invite_program_id',
            'id',
            'id',
            'program_note_id'
        )->first();
    }
    public function makeupNote()
    {
        return $this->hasOne(InviteProgramNote::class, 'invite_program_id');
    }
}
