<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\User;
use App\UserFirstStepModel;
use App\UserSecondStepModel;
use App\StateModel;
use App\UserSubjectsModel;
use App\UserThirdStepModel;
use App\UserFourthStepModel;
use App\GradeLevelModel;
use App\Classes;
use App\Subject;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\QuestionsModel;
use App\EducationListModel;
use App\UserEducationModel;
use App\ViewClassroomModel;
use App\scheduledInterview;
use App\user_interview_slots;
use App\ClassroomManagementModel;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\user_references;
use App\SettingTermsModel;
use App\ProfileStatusHistoryModel;
use App\certifications;

use Hash;
use Mail;
use Auth;
DB::enableQueryLog();

class DashboardController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        if(!session('userewlogin')) {
            return redirect("/");
        }
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }

        if(Auth::user()->type != 5) {
            return redirect("/logout");
        }

            $data["id"] = Auth::user()->id;

            $data["state"] = StateModel::where(["country_id" => "239"])->get();
            $data["grade"] = GradeLevelModel::get();
            $data["subject"] = Subject::get();
            $data["question"] = QuestionsModel::get();
            $data["class"] = Classes::get();
            $id = Auth::user()->id;
            $user = User::where("id", $id)->first();
            $data["user"]=$user;
            $data["first"] = UserFirstStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["second"] = UserSecondStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["third"] = UserThirdStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["four"] = UserFourthStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["intro"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "introduction",
            ])->first();
            $data["teachings"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "teaching",
            ])
                ->orderBy("id", "DESC")
                ->get();
            $data["classroom"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "classroom",
            ])->get();
            $data["award"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "award",
            ])->get();
            $data["distinctionas"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "distinctions",
            ])->get();
            $data["certificate"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "certificate",
            ])->get();
            $data["five"] = AssessmentsModel::where([
                "user_id" => $id,
            ])->first();
            $data["quiz"] = UserQuizModel::where(["user_id" => $id])->first();
            $data["education_list"] = EducationListModel::get();

            $data["scheduledInterview"] = scheduledInterview::where([
                "user_id" => $id
            ])->where("date" ,'>=', date('Y-m-d')) ->orderBy("date", "ASC")
                ->groupBy("date")
                ->get();

            $data["user_interview_slots"] = user_interview_slots::where([
                "user_id" => $id,
            ])->first();


            $data["timeslot"] = scheduledInterview::where(["user_id" => $id])
                ->groupBy("time")
                ->get();


                $data["backgroundfinger"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "fingerprinting",
                    "application_id"=>$id
                ])->get();
                $data["backgroundother"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "other",
                    "application_id"=>$id
                ])->get();
                $data["checkr"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "Checkr",
                    "application_id"=>$id
                ])->get();

                $data["medicalother"] = BackgroundMedicalModel::where([
                    "type" => "medical_requirements",
                    "label" => "other",
                    "application_id"=>$id
                ])->get();
                $data["medicaltbtest"] = BackgroundMedicalModel::where([
                    "type" => "medical_requirements",
                    "label" => "tb_test",
                    "application_id"=>$id
                ])->get();
            $data["user_interview_slots"] = DB::table("tbl_user_interview_slots")
                ->select(
                    "tbl_user_interview_slots.*",
                    "tbl_schedule_interviews.date as date",
                    "tbl_schedule_interviews.time as time",
                    "tbl_schedule_interviews.timezone as timezone",
                    "tbl_schedule_interviews.end_time as end_time",
                    "tbl_schedule_interviews.link as link"
                )
                ->join(
                    "tbl_schedule_interviews",
                    "tbl_schedule_interviews.id",
                    "=",
                    "tbl_user_interview_slots.slot_id"
                )
                ->orderBy("tbl_user_interview_slots.id", "DESC")
                ->get();
            $data["classroomvideo"] = ClassroomManagementModel::where([
                "status" => 1,
            ])->get();
            $data["user_education"] = UserEducationModel::where([
                "user_id" => $id,
            ])->get();
            $data["user_references"] = user_references::where([
                "user_id" => $id,
            ])->get();
            if ($user) {
                $user_data = [
                    "email" => $user["email"],
                    "password" => $user["password"],
                    "type" => "5",
                ];
                if (Auth::attempt($user_data)) {
                    $request
                        ->session()
                        ->put("userewlogin", [
                            "id" => Auth::user()->id,
                            "name" => Auth::user()->first_name,
                            "email" => Auth::user()->email,
                            "type" => Auth::user()->type,
                        ]);
                }
                $data["id"] = $user["id"];

                if (
                    $user["email_verify_status"] == "1" &&
                    $user["profile_status"] == 0
                ) {
                    return redirect("/onboarding-step/" . encrypt($user["id"]));
                } elseif (
                    $user["profile_status"] == 1 &&
                    $user["email_verify_status"] == "1"
                ) {
                    return redirect("/onboarding-step/" . encrypt($user["id"]));
                } elseif (
                    $user["profile_status"] == 1 &&
                    $user["email_verify_status"] == "0"
                ) {
                    return view("web.auth.verify");
                } elseif ($user["profile_status"] == 2) {
                    return view("web.user.status.submit")->with($data);
                } elseif ($user["profile_status"] == 18) {

                    return view("web.user.status.resubmitted")->with($data);
                } elseif ($user["profile_status"] == 19) {
                    return view("web.user.status.contractsubmitted")->with(
                        $data
                    );
                } elseif ($user["profile_status"] == 3) {
                    return view("web.user.status.underreview")->with($data);
                } elseif ($user["profile_status"] == 4) {
                    return view("web.user.status.applicationapproved")->with($data);
                } elseif ($user["profile_status"] == 5) {
                    return view("web.user.status.preonboardedpending")->with(
                        $data
                    );
                } elseif ($user["profile_status"] == 6) {

                    $prvaluec=explode(",",$user->teach);


            if($user->is_approved){
                if($user->is_approved==20){
                $data["setting_terms"] = SettingTermsModel::where([
                    "status" => 1,"type"=>"hybrid contract"
                ])->first();
                }

                if($user->is_approved==16){
                    $data["setting_terms"] = SettingTermsModel::where([
                        "status" => 1,"type"=>"Online contract"
                    ])->first();
                    }

                    if($user->is_approved==17){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"In person contract"
                        ])->first();
                        }
                    }else{
                    if(in_array("hybrid",$prvaluec)){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"hybrid contract"
                        ])->first();
                    }else{

                        if(in_array("online",$prvaluec) && in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }elseif(in_array("online",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"Online contract"
                            ])->first();
                        }elseif(in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"In person contract"
                            ])->first();
                        }else{
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }

                    }

                }

                    $body =  $data["setting_terms"]->description;


                    if($user["last_name"]){
                        $full_name=$user["first_name"].' '.$user["last_name"];
                    }else{
                        $full_name=$user["first_name"];
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);
                    $body= str_replace('{{DATE}}', date('F d, Y'), $body);
                    $body = str_replace('{{IN_PERSON_RATE}}', $user["inpersonrate"], $body);
                    $body = str_replace('{{ONLINE_RATE}}', $user["onlinerate"], $body);
                    $body = str_replace('{{CITY}}', $user["city"], $body);
                    $body = str_replace('{{STATE}}', $user["state"], $body);
                    $body = str_replace('{{ZIPCODE}}', $user["zipcode"], $body);
                    $body = str_replace('{{SIGNATURE_INPUT}}', '<div id="signature-input" class="d-inline-block" style="width: 80%"></div>', $body);
                    $body = str_replace('{{ADDRESS_INPUT}}', '<div id="address-input" class="d-inline-block"></div>', $body);

                       $data['contract']=$body;

                    return view("web.user.status.contractpending")->with($data);
                } elseif ($user["profile_status"] == 7) {
                    return view("web.user.status.onboardedpending")->with(
                        $data
                    );
                } elseif ($user["profile_status"] == 8) {
                    return view("web.dashboard.index")->with($data);

                } elseif ($user["profile_status"] == 9) {
                    return view("web.user.status.interviewscheduled")->with(
                        $data
                    );
                } elseif ($user["profile_status"] == 10) {
                    $data["notes"] = ProfileStatusHistoryModel::where([
                        "user_id" => $id,"status"=>10
                    ])->orderBy("id", "DESC")->first();
                    return view("web.user.status.resubmit")->with($data);
                } elseif ($user["profile_status"] == 11) {
                    return view("web.user.status.pendinginterview")->with(
                        $data
                    );
                } elseif ($user["profile_status"] == 12) {

                    return redirect("/my-program");
                } elseif ($user["profile_status"] == 13) {
                    $data["notes"] = ProfileStatusHistoryModel::where([
                        "user_id" => $id,"status"=>13
                    ])->orderBy("id", "DESC")->first();
                    return view("web.user.status.reject")->with($data);
                } elseif (
                    $user["profile_status"] == 14 ||
                    $user["profile_status"] == 15 ||
                    $user["profile_status"] == 17 ||
                    $user["profile_status"] == 20 ||
                    $user["profile_status"] == 16
                ) {
                    return view("web.user.status.applicationapproved")->with($data);
                    // return view("web.dashboard.index")->with($data);
                } elseif ($user["profile_status"] == 22) {
                    return view("web.user.status.transcript_submitted")->with($data);
                } else {
                    return redirect("/");
                }
            }

    }

    public function onboardingdetails()
    {
        if(!session('userewlogin')) {
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
            $data["id"] = Auth::user()->id;
            $data["state"] = StateModel::where(["country_id" => "239"])->get();
            $data["grade"] = GradeLevelModel::get();
            $data["subject"] = Subject::get();
            $data["question"] = QuestionsModel::get();
            $data["class"] = Classes::get();
            $id = Auth::user()->id;
            $user = User::where("id", $id)->first();
            $data["first"] = UserFirstStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["second"] = UserSecondStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["third"] = UserThirdStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["four"] = UserFourthStepModel::where([
                "user_id" => $id,
            ])->first();
            $data["intro"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "introduction",
            ])->first();
            $data["teachings"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "teaching",
            ])
                ->orderBy("id", "DESC")
                ->get();
            $data["classroom"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "classroom",
            ])->get();
            $data["award"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "award",
            ])->get();
            $data["distinctionas"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "distinctions",
            ])->get();
            $data["certificate"] = AssessmentsModel::where([
                "user_id" => $id,
                "type" => "certificate",
            ])->get();
            $data["five"] = AssessmentsModel::where([
                "user_id" => $id,
            ])->first();
            $data["quiz"] = UserQuizModel::where(["user_id" => $id])->first();
            $data["education_list"] = EducationListModel::get();
            $user = User::where(["id" => Auth::user()->id])->first();
            $data["classroomvideo"] = ClassroomManagementModel::where([
                "status" => 1,
            ])->get();

            $data["scheduledInterview"] = scheduledInterview::where([
                "user_id" => $id,
            ])
                ->groupBy("date")
                ->get();
            $data["user_interview_slots"] = user_interview_slots::where([
                "user_id" => $id,
            ])->first();


            $data["timeslot"] = scheduledInterview::where(["user_id" => $id])
                ->groupBy("time")
                ->get();

                $data["backgroundfinger"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "fingerprinting",
                    "application_id"=>$id
                ])->get();
                $data["backgroundother"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "other",
                    "application_id"=>$id
                ])->get();
                $data["checkr"] = BackgroundMedicalModel::where([
                    "type" => "background_check",
                    "label" => "Checkr",
                    "application_id"=>$id
                ])->get();

                $data["medicalother"] = BackgroundMedicalModel::where([
                    "type" => "medical_requirements",
                    "label" => "other",
                    "application_id"=>$id
                ])->get();
                $data["medicaltbtest"] = BackgroundMedicalModel::where([
                    "type" => "medical_requirements",
                    "label" => "tb_test",
                    "application_id"=>$id
                ])->get();
            $data["user_interview_slots"] = DB::table("tbl_user_interview_slots")
                ->select(
                    "tbl_user_interview_slots.*",
                    "tbl_schedule_interviews.date as date",
                    "tbl_schedule_interviews.time as time",
                    "tbl_schedule_interviews.end_time as end_time",
                    "tbl_schedule_interviews.timezone as timezone",
                    "tbl_schedule_interviews.link as link"
                )
                ->join(
                    "tbl_schedule_interviews",
                    "tbl_schedule_interviews.id",
                    "=",
                    "tbl_user_interview_slots.slot_id"
                )
                ->orderBy("tbl_user_interview_slots.id", "DESC")
                ->get();
            $data["classroomvideo"] = ClassroomManagementModel::where([
                "status" => 1,
            ])->get();

            $data["user_education"] = UserEducationModel::where([
                "user_id" => $id,
            ])->get();
            $data["user_references"] = user_references::where([
                "user_id" => $id,
            ])->get();


            if (
                $user["profile_status"] == 14 ||
                $user["profile_status"] == 15 ||
                $user["profile_status"] == 17 ||
                $user["profile_status"] == 12 ||
                $user["profile_status"] == 20 ||
                $user["profile_status"] == 8 ||
                $user["profile_status"] == 16
            ) {
                return view("web.dashboard.index")->with($data);
            }else{
                return redirect("/web-dashboard");
            }



    }

    public function createCompletion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "reference" => "required",
            "back_med_id" => "required",
            "date" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "file"]);
        if ($request->hasFile("file")) {
            $image = $request->file("file");
            $filename = 'uploads/completion/'.uniqid() . '_' . $image->getClientOriginalName();
           uploads3image($filename,$image);
            $data["file"] = encrypt($filename);
        }
        $data["user_id"] = Auth::user()->id;
        $data["back_med_id"] = $request->back_med_id;
        $data["reference"] = encrypt($request->reference);
        $data["date"] = encrypt($request->date);

        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $result = UponCompletionModel::insert($data);

        if ($result) {
            $res = BackgroundMedicalModel::where("id",$request->back_med_id)->first();
            $rescount = UponCompletionModel::where("back_med_id",$request->back_med_id)->get();;
            $record = User::where("id", Auth::user()->id)->first();
            if($res->school){
                $school_name= institudeName($res->school);
             }else{
                 $school_name='';
             }
             $id = encrypt_str(Auth::user()->id);


             $due_date='';

            if($res->type=='background_check'){
                $link=url('viewinstructordetails/step101/'. $id);
             if(count($rescount) > 1){
                createUserNotification($record,'28','Operations','Admin','user',$school_name,$link,$due_date);

             }else{
                createUserNotification($record,'27','Operations','Admin','user',$school_name,$link,$due_date);

             }
            }

            if($res->type=='medical_requirements'){
                $link=url('viewinstructordetails/step111/'. $id);
                if(count($rescount) > 1){
                   createUserNotification($record,'33','Operations','Admin','user',$school_name,$link,$due_date);

                }else{
                   createUserNotification($record,'32','Operations','Admin','user',$school_name,$link,$due_date);

                }
               }
            return response()->json([
                "success" => true,
                "message" => "Submitted successfully",
                "redirect" => url("submit"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function educationExperience(){

        $data["education_list"] = EducationListModel::get();
        $data["user_education"] = UserEducationModel::where([
            "user_id" => Auth::user()->id,
        ])->get();

        $data["second"] = UserSecondStepModel::where([
            "user_id" => Auth::user()->id,
        ])->first();
        $data["class"] = Classes::get();
        $data['id']=Auth::user()->id;
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["certifications"] = certifications::where(["status" => 1])->orderBy("name", "asc")->get();
        $data["award"] = AssessmentsModel::where([
            "user_id" => Auth::user()->id,
            "type" => "award",
        ])->get();
        $data["distinctionas"] = AssessmentsModel::where([
            "user_id" => Auth::user()->id,
            "type" => "distinctions",
        ])->get();
        $data["certificate"] = AssessmentsModel::where([
            "user_id" => Auth::user()->id,
            "type" => "certificate",
        ])->get();
        return view('web.dashboard.education')->with($data);
    }
    public function notification(Request $request){
        $value=$request->value;
        $notification=$request->notification;
        if($notification==true){ $not='1'; }else{ $not='0';}
        if($value=='app_notification'){
            User::where(["id" => Auth::user()->id])->update(['app_notification'=>$not]);
        }else{
            User::where(["id" => Auth::user()->id])->update(['email_notification'=>$not]);
        }
        return response()->json([
            "success" => true,
            "message" => "Updated successfully",
        ]);

    }

    public function showPreview(Request $request)
    {
        $id = $request->id;
        $data["comp"] = UponCompletionModel::where([
            "back_med_id" => $id,
            "user_id" => Auth::user()->id,
        ])->get();
        return view("web.dashboard.priview_completion")->with($data);
    }

    public function saveTraining(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
            "value" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "value"]);

        $data["classroom_video_id"] = $request->id;
        $data["user_id"] = Auth::user()->id;
        $data["type"] = $request->value;

        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $result = ViewClassroomModel::insert($data);
        if ($result) {
            return response()->json([
                "success" => true,
                "message" => "Submitted successfully",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }
}
