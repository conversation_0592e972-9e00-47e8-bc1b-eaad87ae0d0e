@extends('admin.layouts.master')

@section('title') Add School | Whizara @endsection

@section('content')

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('admin-dashboard')}}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                <!-- <li class="breadcrumb-item active" aria-current="page">Institute Institute</li> -->
                <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{url('admin/k12connections/manage-platform-schools')}}">{{ __('messages.list_platform_institute') }}</a></li>

                <li class="breadcrumb-item active" aria-current="page">{{ __('messages.add_platform_institute') }}</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->


        <!-- EDIT PROFILE SECTION START -->
        <form method="post" id="platform_institute_details" enctype='multipart/form-data'>
            <div class="row justify-content-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="change_profile_img mx-auto mb-4">
                        <div class="avatar-edit">


                            <input type="file" name="profile_upload" class="profileImgUploadBtnd" id="imageUpload" accept=".png, .jpg, .jpeg" />
                            <label for="imageUpload">
                                <span class="btn btn-primary btn-block dbdb">Upload Photo</span>
                            </label>

                        </div>
                        <div class="avatar-preview">

                            <div id="imagePreview" style="background-image: url({{default_user_placeholder()}})"></div>
                        </div>
                    </div>

                </div>
                <div class="col-lg-10 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">{{ __('messages.institute_details') }}</h5>
                                </div>
                                {{ csrf_field() }}
                                <div class="card-body">
                                <input type="hidden" name="latitude" class="form-control" id="latitude" value="">
                                <input type="hidden" name="longitude" class="form-control" id="longitude" value="">
                                    <div class="row">

                                        <div class="col-md-4 form-group">
                                            <label for="comment">Organization Type</label>
                                            <select class="form-control" id="organizationtype" data-toggle="select2" name="organizationtype" >
                                                <option value="">Select Organization Type</option>
                                                <option value="Public School">Public School</option>
                                                <option value="Charter school">Charter school</option>
                                                <option value="Private school">Private school</option>
                                                <option value="Independent school">Independent school</option>
                                                <option value="Community Based Organizations">Community Based Organizations</option>

                                            </select>
                                            <span id="organizationtype_error" class="err"></span>
                                       </div>

                                        <div class="col-md-4 form-group">
                                            <label class="form-label">School name</label>
                                            <input type="text" class="form-control mb-2" name="search_name" id="search_name" placeholder="Enter minimum 4 characters to search school">
                                            <select class="form-control select2" data-placeholder="Select School" id="full_name" data-toggle="select2" name="full_name"></select>
                                            <input type="text" class="form-control mt-3 d-none" name="other_full_name" id="other_full_name" placeholder="Full name">
                                            <span id="full_name_error" class="err"></span>
                                        </div>
                                        <input type="hidden" id="nces_cource_code" name="nces_cource_code">
                                        <div class="col-md-4 form-group">
                                        <label class="form-label">Work email</label>
                                           <input type="text" class="form-control" name="email" id="email" placeholder="Work Email">
                                           <span id="email_error" class="err"></span>
                                       </div>
                                        <div class="col-md-4 form-group">
                                        <label for="comment">Customer Type</label>
                                        <select class="form-control" id="cust_type" data-toggle="select2" name="cust_type" disabled>
                                            <option value="Platform" selected>Platform</option>
                                        </select>
                                        <input type="hidden" name="cust_type" value="Platform">
                                        <span id="cust_type_error" class="err"></span>
                                    </div>
                                    <div class="col-md-4 form-group">
                                        <label for="comment">CBO</label>
                                        <select class="form-control" id="cbo" data-toggle="select2" name="cbo">
                                            <option value="">Select CBO</option>
                                            @foreach(@$cbo as $key => $data)
                                            <option value="{{@$data->id}}">{{@$data->name}}</option>
                                            @endforeach

                                        </select>
                                            <span id="cbo_error" class="err"></span>
                                    </div>

                                    <div class="col-md-4 form-group">
                                        <label for="comment">Grade levels</label>
                                        <select class="form-control select2" data-placeholder="Select Grade levels" id="grade" data-toggle="select2" name="grade[]" multiple>
                                            <option value="">Select Grade levels</option>
                                            <!-- <option value="Middle">Middle</option>
                                            <option value="High">High</option>  -->

                                            @foreach(@$gradeLavel as $key => $data)
                                            <option value="{{@$data->id}}">{{@$data->class_name}}</option>
                                            @endforeach

                                        </select>
                                            <span id="grade_error" class="err"></span>
                                    </div>



                                    <div class="col-md-4 form-group">
                                            <label class="form-label">Website</label>
                                            <input type="text" class="form-control" name="website" id="website" placeholder="Website">
                                            <span id="website_error" class="err"></span>
                                        </div>

                                       <div class="col-md-4 form-group">
                                            <label class="form-label">Organization Name</label>
                                            <input type="text" id="organizationname" class="form-control" name="organizationname" placeholder="Organization Name">
                                            <span id="p_organizationname_error" class="err"></span>
                                        </div>

                                        <div class="col-md-4 form-group"  >
                                        <label class="form-lebel">District</label>
                                        <select class="form-control " id="district" data-toggle="select2" name="district" >
                                            <option value="">Select District</option>
                                            @foreach(@$district as $key => $data)
                                            <option value="{{@$data->id}}">{{@$data->name}}</option>
                                            @endforeach
                                        </select>
                                        <span id="district_error" class="err"></span>
                                    </div>
                                    <div class="col-lg-12">

                                <label for="comment">Contact info</label>

                            </div>

                            <div class="row after-add-more" style="margin-left:0 !important;">

                                        <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="job_title[]" id="first_name" placeholder="Job Title">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="first_name[]" id="first_name" placeholder="First name">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">

                                            <input type="text" id="last_name" class="form-control" name="last_name[]" placeholder="{{ __('messages.last_name') }}">
                                            <span id="p_last_name_error" class="err"></span>
                                        </div>



                                        <div class="col-md-3 form-group">

                                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                                            <span id="email_errors" class="err"></span>
                                        </div>



                                        <div class="col-md-2 form-group">

                                            <input type="number" class="form-control" name="phone[]" id="phone" placeholder="Phone">
                                            <span id="phone_error" class="err"></span>
                                        </div>


                                        <div class="col-md-1 form-group change">


                                            <a class="btn btn-success add-more " onclick="add_more()">+</a>

                                        </div>


                                     </div>


                                        <div class="col-lg-12">

                                            <label for="comment">Address</label>

                                        </div>

                                        <div class="col-md-12 form-group">

                                       <textarea  class="form-control" id="address" name="address" placeholder="Street Address"></textarea>

                                           <span id="address_error" class="err"></span>
                                   </div>


                                   <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">City</label> -->
                                            <input type="text" id="city" class="form-control" name="city" placeholder="City">
                                            <span id="p_city_error" class="err"></span>
                                    </div>

                                    <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">State</label> -->
                                            <input type="text" id="state" class="form-control" name="state" placeholder="State">
                                            <span id="p_state_error" class="err"></span>
                                    </div>



                                    <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">Zip code</label> -->
                                            <input type="text" id="zipcode" class="form-control" name="zipcode" placeholder="zipcode">
                                            <span id="p_city_error" class="err"></span>
                                    </div>
                                    <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">Country</label> -->
                                            <input type="text" id="country" class="form-control" name="country" placeholder="Country">
                                            <span id="p_country_error" class="err"></span>
                                    </div>

                                   <div class="col-md-12 form-group">
                                        <label for="comment">Note</label>
                                        <textarea  class="form-control" name="notes" placeholder="Notes"></textarea>

                                            <span id="notes_error" class="err"></span>
                                    </div>

                                    <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                        <a href="{{redirect()->getUrlGenerator()->previous()}}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                        <button type="button" id="addPlatformInstitute" class="btn btn-primary">{{ __('messages.add') }}</button>
                                    </div>
                                </div>
                            </div>
                            <!-- </form> -->

                        </div>
                    </div>

                </div>
            </div>
    </div>
    </form>
    <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->


@endsection

@section('scripts')
<script>
    function add_more() {
        var html = ` <div class="row after-add-more" style="margin-left:0 !important;"> <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="job_title[]" id="first_name" placeholder="Job Title">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="first_name[]" id="first_name" placeholder="First name">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">

                                            <input type="text" id="last_name" class="form-control" name="last_name[]" placeholder="{{ __('messages.last_name') }}">
                                            <span id="p_last_name_error" class="err"></span>
                                        </div>



                                        <div class="col-md-3 form-group">

                                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                                            <span id="email_errors" class="err"></span>
                                        </div>



                                        <div class="col-md-2 form-group">

                                            <input type="number" class="form-control" name="phone[]" id="phone" placeholder="Phone">
                                            <span id="phone_error" class="err"></span>
                                        </div>


                                        <div class="col-md-1 form-group change">


                                        <a class='btn btn-danger remove'>-</a>

                                        </div> </div>`;



        $(".after-add-more").last().after(html);
        $(html).find(".change").html("<a class='btn btn-danger remove'>-</a>");


    }

    $("body").on("click",".remove",function(){
        $(this).parents(".after-add-more").remove();
    });
</script>

<script>
    $(document).ready(function () {
        let schoolData = [];
        let debounceTimer = null;

        const $searchInput = $('#search_name');
        const $dropdown = $('#full_name');
        const $otherInput = $('#other_full_name');
        const $gradeSelect = $('#grade');

        const gradeMap = {
            G_PK_OFFERED: 'Tk',   // Mapping "Pre-K" to "Tk"
            G_KG_OFFERED: 'K',
            G_1_OFFERED: '1',
            G_2_OFFERED: '2',
            G_3_OFFERED: '3',
            G_4_OFFERED: '4',
            G_5_OFFERED: '5',
            G_6_OFFERED: '6',
            G_7_OFFERED: '7',
            G_8_OFFERED: '8',
            G_9_OFFERED: '9',
            G_10_OFFERED: '10',
            G_11_OFFERED: '11',
            G_12_OFFERED: '12'
        };

        function getSchoolsData(apiUrl) {
            $.ajax({
                url: apiUrl,
                method: 'GET',
                dataType: 'json',
                success: function (response) {
                    schoolData = response;
                    const chunkSize = 20;
                    let currentChunk = 0;

                    function processChunk() {
                        let options = '';
                        const start = currentChunk * chunkSize;
                        const end = Math.min(start + chunkSize, response.length);
                        response.slice(start, end).forEach(school => {
                            const schoolType = school.CHARTER_TEXT?.toLowerCase() === 'no' ? 'Public' : 'Independent';
                            const street = school.LSTREET1 || school.LSTREET2 || school.LSTREET3 || '';
                            const optionText = `${school.SCH_NAME} - ${schoolType}, ${street} (${school.LCITY}, ${school.LSTATE})`;
                            options += `<option value="${school.SCH_NAME}">${optionText}</option>`;
                        });
                        $dropdown.append(options);
                        currentChunk++;
                        if (currentChunk * chunkSize < response.length) {
                            setTimeout(processChunk, 100);
                        }
                    }

                    $dropdown.html('<option value="">Select School</option><option value="Other">Other</option>');
                    processChunk();
                },
                error: function (xhr, status, error) {
                    console.error("Request error:", status, error);
                }
            });
        }

        function debounceSearch() {
            const searchTerm = $searchInput.val().trim().toLowerCase();
            if (searchTerm.length >= 4) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    const apiUrl = `https://app.whizara.com/node/nces/schools?SCH_NAME=${encodeURIComponent(searchTerm)}`;
                    getSchoolsData(apiUrl);
                }, 1000);
            }
        }

        function handleSchoolSelection() {
            const selectedName = $dropdown.val();

            if (selectedName.toLowerCase() === 'other') {
                $otherInput.removeClass('d-none');
                $('#nces_cource_code').val('');
                $gradeSelect.val(null).trigger('change');
            } else {
                $otherInput.addClass('d-none');
                const selectedSchool = schoolData.find(s => s.SCH_NAME === selectedName);

                if (selectedSchool) {
                    $('#website').val(selectedSchool.WEBSITE || '');
                    $('#phone').val(selectedSchool.PHONE || '');
                    $('#address').val(selectedSchool.LSTREET1 || selectedSchool.LSTREET2 || selectedSchool.LSTREET3 || '');
                    $('#city').val(selectedSchool.LCITY || '');
                    $('#state').val(selectedSchool.STATENAME || '');
                    $('#zipcode').val(selectedSchool.LZIP || '');
                    $('#nces_cource_code').val(selectedSchool.NCESSCH || '');

                    // ===== Grade Level Auto Selection =====
                    let selectedGrades = [];

                    for (const [apiKey, label] of Object.entries(gradeMap)) {
                        if (selectedSchool[apiKey] === 'Yes') {
                            const matchingOption = $gradeSelect.find('option').filter(function () {
                                return $(this).text().trim() === label;
                            }).val();

                            if (matchingOption) {
                                selectedGrades.push(matchingOption);
                            }
                        }
                    }

                    $gradeSelect.val(selectedGrades).trigger('change');
                }
            }
        }

        $searchInput.on('input', debounceSearch);
        $dropdown.on('change', handleSchoolSelection);

        // Ensure select2 is initialized if you're using it
        if ($.fn.select2) {
            $gradeSelect.select2();
        }
    });
</script>
@endsection
