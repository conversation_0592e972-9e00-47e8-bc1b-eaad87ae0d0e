class VideoRecorderService {
    constructor() {
        this.eventTarget = document.createElement("div");
        this.recordingLogs = []; // Array to store recording logs
        this.mediaRecorder = null;
        this.screenMediaRecorder = null;
        this.webcamRecordedChunks = [];
        this.screenRecordDetails = [];
        this.stream = null;
        this.webcamStream = null;
        this.screenStream = null;
        this.microphoneStream = null; // Separate microphone stream
        this.screenViewElement = null;
        this.webcamElement = null;
        this.modalElement = null;
        this.modal = null;
        this.webcamPreviewElement = null; // New video element for playback
        this.screenPreviewElement = null; // New video element for playback
        this.countdownElement = null;
        this.playbackContainer = null;
        this.recordingTimeout = null;
        this.timeLimit = 10 * 60 * 1000; // 10 minutes in milliseconds
        this.isScreenRecording = false;
        this.isWebcamRecording = false;
        this.isRecording = false;
        this.isPaused = false;
        this.isRecorded = false;
        this.currentStreamType = null;
        this.stopBtn = null;
        this.pauseBtn = null;
        this.resumeBtn = null;
        this.cancelBtn = null;
        this.restartBtn = null;
        this.startRecordingBtn = null; // Start recording button
        this.screenShareBtn = null; // Screen Share button
        this.stopScreenShareButton = null;
        this.remainingTimeElement = null;
        this.recordingDot = null;
        this.recordingCloseBtn = null;
        this.continueRecording = true;
        this.timer = new ReverseTimer(10 * 60, {
            onTick: (time) => {
                if (this.remainingTimeElement) {
                    this.remainingTimeElement.textContent = time;
                }
            },
        });
    }

    setupUI(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = "";
        // Create video element for screen view
        this.screenViewElement = this.createVideoElement(container);
        // Create webcam element for preview (minimized view)
        this.webcamElement = this.createVideoElement(container);
        this.modalElement = document.getElementById(
            "recordingConfirmationModal"
        );
        this.recordingCloseBtn = document.getElementById("video-record-close");
        this.modal = new bootstrap.Modal(this.modalElement);

        this.webcamElement.style.bottom = "100px";
        this.webcamElement.style.left = "10px"; // Set width for webcam view
        this.webcamElement.controls = false; // Hide controls for webcam view
        this.screenViewElement.controls = false; // Hide controls for screen view

        this.remainingTimeElement = document.createElement("div");
        this.remainingTimeElement.textContent = "10:00";
        this.remainingTimeElement.classList.add("remainingTimeContainer");
        container.appendChild(this.remainingTimeElement);

        this.recordingDot = document.createElement("div");
        this.recordingDot.classList.add("recordingDot");
        this.recordingDot.style.display = "none";
        container.appendChild(this.recordingDot);

        this.countdownElement = document.createElement("div");
        this.countdownElement.id = "countdown";
        this.countdownElement.style.display = "none";
        container.appendChild(this.countdownElement);

        // Create a new video element for playing the recording (hidden initially)
        this.webcamPreviewElement = this.createVideoElement(container, "none");
        this.webcamPreviewElement.style.bottom = "100px";
        this.webcamPreviewElement.style.left = "10px"; // Set width for webcam view
        this.screenPreviewElement = this.createVideoElement(container, "none");
        this.screenPreviewElement.onplay = () => {
            if (this.webcamPreviewElement.paused)
                this.webcamPreviewElement.play();
        };
        this.screenPreviewElement.onpause = () => {
            if (!this.webcamPreviewElement.paused)
                this.webcamPreviewElement.pause();
        };

        let currentPip = null;

        this.webcamPreviewElement.addEventListener("timeupdate", () => {
            const currentTime = this.webcamPreviewElement.currentTime;

            // Find if any pip should be active at this time
            const activePip = this.screenRecordDetails.find(
                (pip) =>
                    currentTime >= pip.start / 1000 &&
                    currentTime <= pip.end / 1000
            );

            if (
                activePip &&
                (!currentPip || currentPip.src !== activePip.src)
            ) {
                // New pip video to show
                currentPip = activePip;
                this.screenPreviewElement.src = activePip.src;
                this.screenPreviewElement.currentTime =
                    currentTime - activePip.start / 1000;
                if (!this.webcamPreviewElement.paused) {
                    this.screenPreviewElement.play();
                }
                this.webcamPreviewElement.controls = false; // Hide controls for screen preview
                this.screenPreviewElement.style.display = "block"; // or use visibility / opacity
                VideoRecorderService.updatePipPreview(
                    this.screenPreviewElement,
                    this.webcamPreviewElement,
                    true
                );
            } else if (!activePip && currentPip) {
                // No active pip now, hide the pip
                this.webcamPreviewElement.controls = true; // Hide controls for screen preview
                this.webcamPreviewElement.play();
                this.screenPreviewElement.style.display = "none";
                currentPip = null;
                VideoRecorderService.updatePipPreview(
                    this.screenPreviewElement,
                    this.webcamPreviewElement,
                    false
                );
            }
        });

        // Create playback controls container
        this.playbackContainer = document.createElement("div");
        this.playbackContainer.className = "text-center";
        this.playbackContainer.id = "controls";

        // Progress bar wrapper
        this.progressWrapper = document.createElement("div");
        this.progressWrapper.style.width = "100%";
        this.progressWrapper.style.height = "25px";
        this.progressWrapper.style.border = "1px solid rgb(0 0 0 / 70%)";
        this.progressWrapper.style.borderRadius = "8px";
        this.progressWrapper.style.overflow = "hidden";
        this.progressWrapper.style.marginTop = "20px";
        this.progressWrapper.style.background = "#eee";
        this.progressWrapper.style.position = "relative";
        this.progressWrapper.style.display = "none";

        // Progress bar itself
        this.progressBar = document.createElement("div");
        this.progressBar.style.height = "100%";
        this.progressBar.style.width = "0%";
        this.progressBar.style.background = "#4caf50";
        this.progressBar.style.transition = "width 0.3s";

        // Percentage label
        this.percentLabel = document.createElement("span");
        this.percentLabel.style.position = "absolute";
        this.percentLabel.style.top = "50%";
        this.percentLabel.style.left = "50%";
        this.percentLabel.style.transform = "translate(-50%, -50%)";
        this.percentLabel.style.color = "#000";
        this.percentLabel.style.fontWeight = "bold";

        this.progressWrapper.appendChild(this.progressBar);
        this.progressWrapper.appendChild(this.percentLabel);
        container.appendChild(this.progressWrapper);

        container.appendChild(this.playbackContainer);
        // Add control buttons with unique classes
        this.startRecordingBtn = this.addControlButton(
            SVGImageCollection.startRecording,
            () => this.startRecording(),
            "Start recording",
            false,
            "start-recording-btn" // Unique class for start recording button
        );

        this.screenShareBtn = this.addControlButton(
            SVGImageCollection.screenShare,
            () => this.startScreenShare(),
            "Start screen share",
            false,
            "screen-share-btn" // Unique class for screen share button
        );
        this.stopScreenShareButton = this.addControlButton(
            SVGImageCollection.stopScreenShare,
            () => this.stopScreenShare(),
            "Stop screen share",
            true,
            "stop-screen-share-btn" // Unique class for stop screen share button
        );

        this.pauseBtn = this.addControlButton(
            SVGImageCollection.pauseRecording,
            () => this.pauseRecording(),
            "Pause recording",
            true,
            "pause-recording-btn" // Unique class for pause recording button
        );

        this.resumeBtn = this.addControlButton(
            SVGImageCollection.resumeRecording,
            () => this.resumeRecording(),
            "Resume recording",
            true,
            "resume-recording-btn" // Unique class for resume recording button
        );

        this.stopBtn = this.addControlButton(
            SVGImageCollection.stopRecording,
            async () => {
                // Close PiP if it's open
                if (document.pictureInPictureElement) {
                    await document.exitPictureInPicture();
                }

                this.updateRecordingConfirmationModal(
                    "Stop Recording?",
                    "Are you sure you want to stop the recording? You can preview or record again if needed.",
                    "Stop",
                    () => {
                        this.continueRecording = false;
                        this.stopRecording(); // Call the existing stopRecording method

                        // Delay to allow the first modal to close before opening the second
                        setTimeout(() => {
                            this.updateRecordingConfirmationModal(
                                "Preview is Ready",
                                `Your video preview is ready. Click on the <i class="fa fa-play" style="margin-left: 2px; color: #014bbe;"></i> button to review the recording. Click on <i style="margin-left:2px;color:#014bbe" class="fa fa-upload"></i> to upload the recording. Failure to upload the video will result in it being lost permanently.`,
                                "Ok",
                                () => {
                                    // Handle final save or continue logic here
                                },
                                "",
                                false
                            );
                        }, 300); // 300ms delay is typically enough
                    },
                    "Continue Recording"
                );
            },
            "Stop recording",
            true,
            "stop-recording-btn" // Unique class for stop recording button
        );

        this.cancelBtn = this.addControlButton(
            SVGImageCollection.cancelRecording,
            () => {
                this.updateRecordingConfirmationModal(
                    "Delete Recording",
                    "Are you sure you want to delete this recording? Once deleted, this recording cannot be recovered.",
                    "Yes",
                    () => {
                        this.cancelRecording(); // Call the existing cancelRecording method
                    }
                );
            },
            "Discard recording",
            false,
            "cancel-recording-btn" // Unique class for cancel button
        );

        this.restartBtn = this.addControlButton(
            SVGImageCollection.restartRecording,
            () => {
                this.updateRecordingConfirmationModal(
                    "Restart Recording",
                    // "Are you sure you want to restart the recording?",
                    "Restarting will delete your current video. This action cannot be undone. Are you sure you want to restart recording?",
                    "Re-record",
                    () => {
                        this.restartRecording(); // Call the existing restartRecording method
                    }
                );
            },
            "Restart recording",
            true,
            "restart-recording-btn" // Unique class for restart recording button
        );

        // this.recordingCloseBtn =
        //         this.updateRecordingConfirmationModal(
        //             "Unsaved Video",
        //             // "Are you sure you want to restart the recording?",
        //             "You haven't uploaded your video yet. If you close now, it will be lost. Do you want to continue?",
        //             "Yes, submit",
        //             () => {

        //             }
        //         );

        // Upload Button
        this.uploadBtn = this.addControlButton(
            SVGImageCollection.uploadRecording,
            () => {
                this.updateRecordingConfirmationModal(
                    "Confirm Upload",
                    "Are you sure you want to upload this video? Once uploaded, you can't edit later.",
                    "Yes",
                    () => {
                        this.uploadRecording(); // Call the existing uploadRecording method
                    }
                );
            },
            "Upload recording",
            false,
            "upload-recording-btn" // Unique class for upload recording button
        );

        // Initially update button visibility
        this.updateButtonVisibility();

        // Start rendering as soon as devices are available
        this.initializeRendering();
    }

    showPiPButton() {
        // Create a button for PiP
        const pipButton = document.createElement("button");
        pipButton.innerHTML = "Your Button Text"; // Set the button text
        pipButton.className = "pip-button"; // Add a class for styling
        pipButton.onclick = () => {
            // Define the action for the button
            console.log("Button in PiP clicked!");
            // You can add any functionality you want here
        };

        // Append the button to the PiP window
        document.body.appendChild(pipButton);
    }

    closeModal() {
        if (this.modal) {
            this.modal.hide(); // Close the modal
        }
    }

    createVideoElement(container, displayStyle = "inline-block") {
        const videoElement = document.createElement("video");
        videoElement.controls = true;
        videoElement.style.display = displayStyle;
        container.appendChild(videoElement);
        return videoElement;
    }

    addControlButton(
        label,
        onClick,
        tooltip,
        hidden = false,
        uniqueClass = ""
    ) {
        const button = document.createElement("button");
        button.innerHTML = label;
        button.type = "button";
        button.className = `btn btn-primary buttonplayer text-light py-2 px-3 m-2 ${uniqueClass}`; // Add uniqueClass here
        button.addEventListener("click", onClick);
        if (hidden) button.style.display = "none";
        button.style.borderRadius = "38px";
        // Add tooltip attributes
        button.setAttribute("data-toggle", "tooltip");
        button.setAttribute("data-placement", "top");
        button.setAttribute("title", tooltip);
        button.style.setProperty("padding", "14px 16px", "important");
        this.playbackContainer.appendChild(button);
        $(button).tooltip();
        return button;
    }

    static updatePipPreview = (mainElement, pipElement, condition) => {
        if (condition) {
            mainElement.style.display = "inline-block";
            pipElement.style.position = "absolute";
            pipElement.style.maxHeight = "100px";
            pipElement.style.maxWidth = "min-content";
        } else {
            mainElement.style.display = "none";
            pipElement.style.maxHeight = "100%";
            pipElement.style.maxWidth = "100%";
            pipElement.style.position = "initial";
        }
    };
    updateButtonVisibility() {
        const isRecording = this.isRecording;
        const isPaused = this.isPaused;
        const isScreenShare = this.isScreenRecording;
        const isRecordingReady = this.isRecorded;
        const updateVisibility = (element, condition) => {
            element.style.display = condition ? "inline-block" : "none";
        };
        this.startRecordingBtn.disabled = this.isRecording;

        updateVisibility(
            this.startRecordingBtn,
            !isRecording && !isRecordingReady
        );
        updateVisibility(
            this.screenShareBtn,
            !isScreenShare && !isRecordingReady
        );
        updateVisibility(this.recordingDot, isRecording);
        updateVisibility(this.remainingTimeElement, !isRecordingReady);
        updateVisibility(this.stopBtn, isRecording);
        updateVisibility(this.pauseBtn, isRecording && !isPaused);
        updateVisibility(this.resumeBtn, isRecording && isPaused);
        updateVisibility(this.cancelBtn, isRecordingReady || isRecording);
        updateVisibility(this.restartBtn, isRecordingReady);
        updateVisibility(this.uploadBtn, isRecordingReady);
        updateVisibility(this.stopScreenShareButton, isScreenShare);
        updateVisibility(this.webcamPreviewElement, isRecordingReady);
        updateVisibility(this.webcamElement, !isRecordingReady);
        updateVisibility(this.screenViewElement, !isRecordingReady);
        VideoRecorderService.updatePipPreview(
            this.screenViewElement,
            this.webcamElement,
            isScreenShare
        );
        updateVisibility(this.webcamPreviewElement, isRecordingReady);
    }

    async uploadRecording() {
        try {
            this.continueRecording = true;
            this.progressBar.style.width = "0%";
            this.percentLabel.textContent = "0%";
            this.progressWrapper.style.display = "block";

            const webcamFiles = this.webcamRecordedChunks.map(
                (chunk, index) => {
                    return new File([chunk], `webcam_chunk_${index}.webm`, {
                        type: "video/webm",
                    });
                }
            );

            const screenFiles = [];
            this.screenRecordDetails.forEach((screenDetail, index) => {
                screenDetail.chunks.forEach((chunk, chunkIndex) => {
                    screenFiles.push(
                        new File(
                            [chunk],
                            `screen_chunk_${index}_${chunkIndex}.webm`,
                            { type: "video/webm" }
                        )
                    );
                });
            });

            $("#video_loading_spinner").removeClass("d-none");

            const allFiles = [...webcamFiles, ...screenFiles];
            const formData = new FormData();
            allFiles.forEach((file) => formData.append("files", file));

            const screenRecords = this.screenRecordDetails
                .filter((d) => d.type === "screen")
                .map((d) => ({ start: d.start, end: d.end }));
            formData.append("screenSegments", JSON.stringify(screenRecords));

            const remains = this.remainingTimeElement.textContent;
            const duration =
                (9 - parseInt(remains.split(":")[0])) * 60 +
                (60 - parseInt(remains.split(":")[1]));
            formData.append("duration", duration);

            const onProgress = (percent) => {
                this.progressBar.style.width = percent + "%";
                this.percentLabel.textContent = percent + "%";

                if (percent === 100) {
                    // Hide all modals
                    document
                        .querySelectorAll(".modal.show")
                        .forEach((modalEl) => {
                            const modalInstance =
                                bootstrap.Modal.getInstance(modalEl);
                            if (modalInstance) {
                                modalInstance.hide();
                            }
                        });
                    $("#video_loading_spinner").removeClass("d-none");

                    $(".video_record").css({
                        "pointer-events": "none",
                        opacity: "0.5",
                        cursor: "not-allowed",
                    });

                    // Show confirmation modal
                    this.updateRecordingConfirmationModal(
                        "Upload Successful",
                        "Your video has been uploaded successfully.",
                        "OK",
                        () => {
                            $(".upload_video .video_uplod").css("border", "");
                            $(".video_record").css("border", "");
                            this.modal.hide(); // hide the success modal
                        },
                        "cancel",
                        false
                    );
                    this.modal.show();
                }
            };

            const url = `https://app.whizara.com/node/recording/merge`;
            const xhr = new XMLHttpRequest();
            const cookieHeader = document.cookie;
            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content");

            xhr.open("POST", url, true);
            xhr.setRequestHeader("BrowserCookie", cookieHeader);
            xhr.setRequestHeader("X-CSRF-TOKEN", csrfToken);
            xhr.withCredentials = true;

            xhr.upload.onprogress = (event) => {
                if (
                    event.lengthComputable &&
                    typeof onProgress === "function"
                ) {
                    const percent = Math.round(
                        (event.loaded / event.total) * 100
                    );
                    onProgress(percent);
                } else {
                    console.log("Recording uploaded error", xhr);
                }
            };

            xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const json = JSON.parse(xhr.responseText);
                        console.log("response:", json);
                        $("#VideoRecordModal").modal("hide");
                        $("#video_loading_spinner").addClass("d-none");

                        $(".video_record").css({
                            "pointer-events": "",
                            opacity: "",
                            cursor: "",
                        });

                        const event = new CustomEvent("uploadCompleted", {
                            detail: json,
                        });

                        this.eventTarget.dispatchEvent(event);

                        console.log("final_completion");
                    } catch (err) {
                        console.log("Recording uploaded successfully", err);
                    }
                } else {
                    console.log("Recording uploaded error", xhr);
                }
            };

            xhr.onerror = () => console.error(new Error("Network error"));
            xhr.send(formData);
        } catch (error) {
            console.error("Error uploading recording:", error);
        }
    }
    onUploadCompleted(callback) {
        this.eventTarget.addEventListener("uploadCompleted", callback);
    }
    async initializeRendering() {
        try {
            this.timer.reset();
            this.progressWrapper.style.display = "none";
            this.remainingTimeElement.textContent = "10:00";
            this.recordingDot.style.animation = "blink 1s infinite";

            try {
                this.webcamStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                });
            } catch (error) {
                this.showPermissionDeniedModal("Camera");
                return; // Exit the function if permission is denied
            }
            this.webcamElement.srcObject = this.webcamStream;
            this.webcamElement.play();
        } catch (error) {
            console.error("Error initializing rendering: ", error);
        }
    }

    async startRecording() {
        try {
            // Check camera permission before starting the recording
            const cameraPermission = await navigator.permissions.query({
                name: "camera",
            });
            if (cameraPermission.state === "denied") {
                this.showPermissionDeniedModal("Camera");
                return; // Exit if permission is denied
            }

            this.recordingLogs = []; // Reset recording logs
            this.isRecording = true;
            this.isPaused = false;
            this.continueRecording = false;
            this.webcamRecordedChunks = [];

            // Request audio permission
            try {
                this.microphoneStream =
                    await navigator.mediaDevices.getUserMedia({
                        audio: true,
                    });

                this.updateButtonVisibility();
                // Automatically start the microphone as part of the recording

                // Combine video from and audio from microphone
                const combinedStream = new MediaStream([
                    ...this.webcamStream.getVideoTracks(),
                    ...(this.microphoneStream
                        ? this.microphoneStream.getAudioTracks()
                        : []),
                ]);

                this.mediaRecorder = new MediaRecorder(combinedStream, {
                    mimeType: "video/webm",
                });

                this.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        this.webcamRecordedChunks.push(event.data);
                    }
                };

                this.mediaRecorder.onstop = () => {
                    if (this.isRecording) {
                        const blob = new Blob(this.webcamRecordedChunks, {
                            type: "video/webm",
                        });
                        const url = URL.createObjectURL(blob);
                        console.log("Recording stopped. Blob URL:", url);
                        this.webcamPreviewElement.src = url;
                        this.isRecorded = true;
                        this.isRecording = false;
                        // If a recording has been done, show the video element and hide view
                        this.updateButtonVisibility();
                        this.stopAllStreams();
                    }
                };
                await this.countdown(3); // Countdown from 3 seconds
                this.mediaRecorder.start();
                this.recordingLogs.push({
                    event: "start",
                    timestamp: new Date().getTime(),
                });
                this.timer.start();
                if (this.isScreenRecording) {
                    this.screenMediaRecorder.start();
                }
                // Set the timeout to stop recording after the time limit
                this.recordingTimeout = setTimeout(
                    () => this.stopRecording(),
                    this.timeLimit
                );
            } catch (error) {
                this.showPermissionDeniedModal("Microphone");
            }
        } catch (error) {
            console.error("Error starting recording: ", error);
        }
    }

    async startScreenShare() {
        if (this.isScreenRecording) return;

        // Start screen share
        try {
            this.screenStream = await navigator.mediaDevices.getDisplayMedia({
                video: true,
            });
            this.screenViewElement.srcObject = this.screenStream;
            this.screenViewElement.play();
            this.isScreenRecording = true;
            this.screenMediaRecorder = new MediaRecorder(this.screenStream, {
                mimeType: "video/webm",
            });

            this.screenMediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    const last = this.screenRecordDetails.length - 1;
                    this.screenRecordDetails[last].chunks.push(event.data);
                }
            };

            this.screenMediaRecorder.onstop = () => {
                const last = this.screenRecordDetails.length - 1;
                this.screenRecordDetails[last].end = this.getRecordedTime();
                this.stopScreenShare();
            };

            this.screenMediaRecorder.onstart = () => {
                const [track] = this.screenStream.getVideoTracks();
                track.onended = () => {
                    this.screenMediaRecorder.stop();
                    console.log("Screen sharing stopped.");
                };

                this.screenRecordDetails.push({
                    type: "screen",
                    start: this.getRecordedTime(),
                    chunks: [],
                });
            };

            // Start recording if already recording
            if (this.isRecording) {
                this.screenMediaRecorder.start();
            }

            this.updateButtonVisibility();
        } catch (error) {
            console.error("Error starting screen share: ", error);
        }
    }

    stopScreenShare() {
        try {
            if (this.screenStream) {
                this.screenMediaRecorder.stop();
                this.screenStream.getTracks().forEach((track) => track.stop());
                this.screenStream = null;
                this.screenViewElement.srcObject = null;
                this.isScreenRecording = false;
                this.updateButtonVisibility();
                this.screenMediaRecorder = null;
            }
        } catch (error) {
            console.error("Error stopping screen share: ", error);
        }
    }

    pauseRecording() {
        try {
            if (
                this.mediaRecorder &&
                this.mediaRecorder.state === "recording"
            ) {
                this.recordingLogs.push({
                    event: "pause",
                    timestamp: new Date().getTime(),
                });
                this.mediaRecorder.pause();
                this.timer.pause();
                if (this.screenMediaRecorder) {
                    this.screenMediaRecorder.pause();
                }
                this.isPaused = true;
                this.updateButtonVisibility();

                // Stop the blinking effect
                this.recordingDot.style.animation = "none"; // Stop blinking
            }
        } catch (error) {
            console.error("Error pausing recording: ", error);
        }
    }

    async resumeRecording() {
        try {
            if (this.mediaRecorder && this.mediaRecorder.state === "paused") {
                await this.countdown(3); // Countdown from 3 seconds
                if (
                    this.mediaRecorder &&
                    this.mediaRecorder.state !== "inactive"
                ) {
                    this.recordingLogs.push({
                        event: "resume",
                        timestamp: new Date().getTime(),
                    });
                    this.mediaRecorder.resume();
                    this.timer.resume();
                    if (this.screenMediaRecorder) {
                        this.screenMediaRecorder.resume();
                    }
                    this.isPaused = false;
                    this.updateButtonVisibility();

                    // Start the blinking effect
                    this.recordingDot.style.animation = "blink 1s infinite"; // Start blinking
                }
            }
        } catch (error) {
            console.error("Error resuming recording: ", error);
        }
    }

    async stopRecording() {
        try {
            if (
                this.screenMediaRecorder &&
                this.screenMediaRecorder.state !== "inactive"
            ) {
                this.screenMediaRecorder.stop();
                await new Promise((res, rej) => setTimeout(res, 500));
                this.stopScreenShare();
                this.screenStream = null;
            }
            if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
                this.recordingLogs.push({
                    event: "stop",
                    timestamp: new Date().getTime(),
                });
                this.microphoneStream
                    .getTracks()
                    .forEach((track) => track.stop());
                this.webcamStream.getTracks().forEach((track) => track.stop());
                this.microphoneStream = null;
                this.webcamStream = null;
                this.isRecorded = true;
                this.mediaRecorder.stop();
                this.timer.stop();
            }
            if (this.screenRecordDetails.find((t) => t.type == "screen")) {
                this.screenRecordDetails.forEach((details) => {
                    const blob = new Blob(details.chunks, {
                        type: "video/webm",
                    });
                    const url = URL.createObjectURL(blob);
                    details.src = url;
                });
                this.screenPreviewElement.src = this.screenRecordDetails.find(
                    (t) => t.type == "screen"
                )?.src;
                console.log(
                    "Screen recording stopped. Blob URL:",
                    this.screenRecordDetails
                );
            }
            clearTimeout(this.recordingTimeout);
            this.updateButtonVisibility();
        } catch (error) {
            console.error("Error stopping recording: ", error);
        }
    }

    cancelRecording() {
        try {
            this.timer.reset();
            this.stopAllStreams();
            clearTimeout(this.recordingTimeout);
            this.webcamRecordedChunks = [];
            this.isRecorded = false;
            this.isRecording = false;
            this.isPaused = false;
            this.isScreenRecording = false;
            this.isWebcamRecording = false;
            this.initializeRendering();
            this.updateButtonVisibility();
        } catch (error) {
            console.error("Error cancelling recording: ", error);
        }
    }

    restartRecording() {
        try {
            this.webcamRecordedChunks = [];
            this.isRecorded = false;
            this.isRecording = false;
            this.isPaused = false;
            this.isScreenRecording = false;
            this.isWebcamRecording = false;
            this.updateButtonVisibility();
            this.timer.reset();
            [(this.screenStream, this.microphoneStream)].forEach((stream) => {
                if (stream) {
                    stream.getTracks().forEach((track) => track.stop());
                }
            });
            this.screenViewElement.srcObject = null;
            this.webcamPreviewElement.src = "";
            this.webcamPreviewElement.style.display = "none";
            this.initializeRendering();
            this.startRecording();
        } catch (error) {
            console.error("Error restarting recording: ", error);
        }
    }

    // Countdown function that returns a Promise
    countdown(seconds) {
        const isEnableButtons = (element, condition) => {
            element.disabled = condition;
        };

        const buttons = [
            this.startRecordingBtn,
            this.screenShareBtn,
            this.stopBtn,
            this.pauseBtn,
            this.resumeBtn,
            this.cancelBtn,
            this.restartBtn,
            this.uploadBtn,
            this.stopScreenShareButton,
        ];

        // Enable all buttons dynamically
        buttons.forEach((button) => isEnableButtons(button, true));

        // Countdown logic
        return new Promise((resolve) => {
            let countdownValue = seconds;
            this.countdownElement.style.display = "flex";
            this.countdownElement.textContent = countdownValue;

            const countdownInterval = setInterval(() => {
                countdownValue--;
                this.countdownElement.textContent = countdownValue;

                if (countdownValue === 0) {
                    clearInterval(countdownInterval);
                    this.countdownElement.style.display = "none"; // Hide countdown after it finishes
                    buttons.forEach((button) => isEnableButtons(button, false));
                    resolve(); // Resolve the promise when countdown finishes
                }
            }, 1000); // Update every second
        });
    }

    stopAllStreams() {
        try {
            [
                this.webcamStream,
                this.screenStream,
                this.microphoneStream,
            ].forEach((stream) => {
                if (stream) {
                    stream.getTracks().forEach((track) => track.stop());
                }
            });
            this.countdownElement.style.display = "none"; // Hide countdown when stopping streams
            this.webcamStream =
                this.screenStream =
                this.microphoneStream =
                    null;
        } catch (error) {
            console.error("Error stopping all streams: ", error);
        }
    }

    getRecordedTime() {
        let start = this.recordingLogs.find(
            (log) => log.event === "start"
        )?.timestamp;
        if (!start) return 0; // No recording started
        return (
            this.recordingLogs.reduce((acc, log) => {
                if (log.event === "pause") {
                    acc += log.timestamp - start;
                } else if (log.event === "resume") {
                    start = log.timestamp;
                } else if (log.event === "stop") {
                    acc += log.timestamp - start;
                }
                return acc;
            }, 0) || new Date().getTime() - start
        );
    }

    updateRecordingConfirmationModal(
        title,
        text,
        yesButtonText,
        successCallback,
        cancelButtonText = "Cancel",
        cancelButtonVisible = true
    ) {
        $(this.modalElement).find("#recording-confirmation-title").text(title);
        // $(this.modalElement).find("#recording-confirmation-text").text(text);
        // Check if the text contains the phrase
        if (text.includes("delete your current video")) {
            // If it does, replace it and use .html()
            const formattedText = text.replace(
                "delete your current video",
                "<strong>delete your current video</strong>"
            );
            $(this.modalElement)
                .find("#recording-confirmation-text")
                .html(formattedText);
        } else {
            // If it doesn't, use .text()
            $(this.modalElement)
                .find("#recording-confirmation-text")
                .html(text);
        }
        if (cancelButtonVisible) {
            $(".addProctor-btn").removeClass("force-center");
            $(this.modalElement)
                .find("#recording-confirmation-cancel")
                .text(cancelButtonText)
                .css("display", "inline-block"); // or 'block' depending on layout
        } else {
            $(".addProctor-btn").addClass("force-center");

            $(this.modalElement)
                .find("#recording-confirmation-cancel")
                .html("")
                .css("display", "none");
        }

        if (typeof yesButtonText == "string") {
            $(this.modalElement)
                .find("#recording-confirmation")
                .text(yesButtonText);
            $(this.modalElement)
                .find("#recording-confirmation")
                .removeClass("d-none");
        } else {
            if (typeof yesButtonText == "function") {
                successCallback = yesButtonText;
            }
            $(this.modalElement)
                .find("#recording-confirmation")
                .addClass("d-none");
        }

        this.modal.show();

        // Pause recording when showing the modal
        this.pauseRecording();

        const cleanupEventListeners = () => {
            $(this.modalElement)
                .find("#recording-confirmation-cancel")
                .off("click");
            $(this.modalElement).find("#recording-confirmation").off("click");
        };

        $(this.modalElement)
            .find("#recording-confirmation-cancel")
            .on("click", () => {
                this.resumeRecording(); // This will be your function to resume the recording
                this.modal.hide();
                cleanupEventListeners(); // Clean up event listeners after job is done
            });

        $(this.modalElement)
            .find("#recording-confirmation")
            .on("click", () => {
                this.resumeRecording(); // This will be your function to resume the recording
                this.modal.hide();
                successCallback(); // This will be your success callback function
                cleanupEventListeners(); // Clean up event listeners after job is done
            });
    }

    showPermissionDeniedModal(device = "Camera") {
        const title = `${device} Permission Denied`;
        const text = `${device} access is required to record video. Please enable ${device.toLowerCase()} permissions in your browser settings.`;

        this.updateRecordingConfirmationModal(
            title,
            text,
            "Enable Access",
            () => {
                this.checkDevicePermission(device);
                this.requestMediaPermissions();
                // this.requestDevicePermission(device);
            }
        );
    }

    async checkDevicePermission(device = "Camera") {
        try {
            const access = await navigator.permissions.query({
                name: device.toLowerCase(),
            });
            access.onchange = () => {
                if (access.state === "granted") {
                    this.initializeRendering();
                }
            };
        } catch (error) {
            console.error(
                `Error checking ${device.toLowerCase()} permission: `,
                error
            );
        }
    }

    handleCloseFunction = (event) => {
        if ((!this.continueRecording && this.isRecording) || this.isRecorded) {
            this.pauseRecording();
            event.preventDefault(); // Prevent the default close action
            this.updateRecordingConfirmationModal(
                "Unsaved Video",
                "You haven't uploaded your video yet. If you close now, it will be lost. Do you want to continue?",
                "Yes",
                (data) => {
                    this.continueRecording = false;
                    this.closeModal();
                    $("#VideoRecordModal").modal("hide");
                }
            );
        } else {
            this.closeModal(); // Close the modal if not recorded
            $("#VideoRecordModal").modal("hide");
        }
    };

    async requestPictureInPicture() {
        try {
            if (document.pictureInPictureElement) {
                // If already in PiP, exit PiP
                await document.exitPictureInPicture();
            } else {
                // Request PiP
                await this.screenViewElement.requestPictureInPicture();
            }
        } catch (error) {
            console.error("Error requesting Picture-in-Picture: ", error);
        }
    }
    async requestMediaPermissions() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true,
            });
            stream.getTracks().forEach((track) => track.stop());
        } catch (error) {
            if (
                error.name === "NotAllowedError" ||
                error.name === "SecurityError"
            ) {
                alert(
                    "Please enable camera and microphone access near the address bar."
                );
            } else {
                alert("An error occurred: " + error.message);
            }
        }
    }
}

const SVGImageCollection = {
    // startRecording: `
    // <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed">
    // <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h480q33 0 56.5 23.5T720-720v180l160-160v440L720-420v180q0 33-23.5 56.5T640-160H160Zm0-80h480v-480H160v480Zm0 0v-480 480Z"/>
    // </svg>`,
    startRecording: `
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 122.88 122.88" style="enable-background:new 0 0 122.88 122.88" xml:space="preserve"><style type="text/css">.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#FF4141;}</style><g><path class="st0" d="M61.44,0c33.93,0,61.44,27.51,61.44,61.44s-27.51,61.44-61.44,61.44S0,95.37,0,61.44S27.51,0,61.44,0L61.44,0z"/></g></svg>`,
    stopRecording: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 16.6667V3.33333C0 2.41667 0.326666 1.63222 0.98 0.98C1.63333 0.327777 2.41778 0.00111111 3.33333 0H16.6667C17.5833 0 18.3683 0.326666 19.0217 0.98C19.675 1.63333 20.0011 2.41778 20 3.33333V16.6667C20 17.5833 19.6739 18.3683 19.0217 19.0217C18.3694 19.675 17.5844 20.0011 16.6667 20H3.33333C2.41667 20 1.63222 19.6739 0.98 19.0217C0.327777 18.3694 0.00111111 17.5844 0 16.6667ZM3.33333 16.6667H16.6667V3.33333H3.33333V16.6667Z" fill="white"/>
</svg>`,
    pauseRecording: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.2857 20C13.5 20 12.8276 19.7205 12.2686 19.1614C11.7095 18.6024 11.4295 17.9295 11.4286 17.1429V2.85714C11.4286 2.07143 11.7086 1.39905 12.2686 0.84C12.8286 0.280952 13.501 0.000952381 14.2857 0H17.1429C17.9286 0 18.6014 0.28 19.1614 0.84C19.7214 1.4 20.0009 2.07238 20 2.85714V17.1429C20 17.9286 19.7205 18.6014 19.1614 19.1614C18.6024 19.7214 17.9295 20.0009 17.1429 20H14.2857ZM2.85714 20C2.07143 20 1.39905 19.7205 0.84 19.1614C0.280952 18.6024 0.000952381 17.9295 0 17.1429V2.85714C0 2.07143 0.28 1.39905 0.84 0.84C1.4 0.280952 2.07238 0.000952381 2.85714 0H5.71428C6.5 0 7.17286 0.28 7.73286 0.84C8.29286 1.4 8.57238 2.07238 8.57143 2.85714V17.1429C8.57143 17.9286 8.2919 18.6014 7.73286 19.1614C7.17381 19.7214 6.50095 20.0009 5.71428 20H2.85714ZM14.2857 17.1429H17.1429V2.85714H14.2857V17.1429ZM2.85714 17.1429H5.71428V2.85714H2.85714V17.1429Z" fill="white"/>
</svg>`,
    resumeRecording: `<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.077 7.35309C16.5067 7.60849 16.8662 7.98976 17.1169 8.45604C17.3676 8.92232 17.5 9.45604 17.5 10C17.5 10.544 17.3676 11.0777 17.1169 11.544C16.8662 12.0103 16.5067 12.3915 16.077 12.6469L4.61324 19.6137C2.76734 20.7367 0.5 19.2767 0.5 16.9678V3.03322C0.5 0.723286 2.76734 -0.735671 4.61324 0.385296L16.077 7.35309Z" fill="white"/>
            </svg>`,
    cancelRecording: `<svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 5.88889H21M8.5 10.7778V18.1111M13.5 10.7778V18.1111M2.25 5.88889L3.5 20.5556C3.5 21.2039 3.76339 21.8256 4.23223 22.284C4.70107 22.7425 5.33696 23 6 23H16C16.663 23 17.2989 22.7425 17.7678 22.284C18.2366 21.8256 18.5 21.2039 18.5 20.5556L19.75 5.88889M7.25 5.88889V2.22222C7.25 1.89807 7.3817 1.58719 7.61612 1.35798C7.85054 1.12877 8.16848 1 8.5 1H13.5C13.8315 1 14.1495 1.12877 14.3839 1.35798C14.6183 1.58719 14.75 1.89807 14.75 2.22222V5.88889" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
    restartRecording: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.99999 20C7.20833 20 4.84375 19.0312 2.90625 17.0938C0.968749 15.1563 0 12.7917 0 10C0 7.20834 0.968749 4.84375 2.90625 2.90625C4.84375 0.968754 7.20833 4.31034e-06 9.99999 4.31034e-06C11.4375 4.31034e-06 12.8125 0.296671 14.125 0.890004C15.4375 1.48334 16.5625 2.3325 17.5 3.4375V1.25C17.5 0.895837 17.62 0.599171 17.86 0.360004C18.1 0.120838 18.3967 0.000837644 18.75 4.31034e-06C19.1033 -0.000829023 19.4004 0.119171 19.6412 0.360004C19.8821 0.600838 20.0016 0.897504 20 1.25V7.5C20 7.85417 19.88 8.15125 19.64 8.39125C19.4 8.63125 19.1033 8.75084 18.75 8.75H12.5C12.1458 8.75 11.8492 8.63 11.61 8.39C11.3708 8.15 11.2508 7.85334 11.25 7.5C11.2492 7.14667 11.3692 6.85 11.61 6.61C11.8508 6.37 12.1475 6.25 12.5 6.25H16.5C15.8333 5.08334 14.9221 4.16667 13.7662 3.5C12.6104 2.83334 11.355 2.5 9.99999 2.5C7.91666 2.5 6.14583 3.22917 4.6875 4.6875C3.22916 6.14584 2.5 7.91667 2.5 10C2.5 12.0833 3.22916 13.8542 4.6875 15.3125C6.14583 16.7708 7.91666 17.5 9.99999 17.5C11.4167 17.5 12.7137 17.1408 13.8912 16.4225C15.0687 15.7042 15.98 14.7404 16.625 13.5313C16.7917 13.2396 17.0262 13.0367 17.3287 12.9225C17.6312 12.8083 17.9383 12.8029 18.25 12.9063C18.5833 13.0104 18.8229 13.2292 18.9687 13.5625C19.1146 13.8958 19.1041 14.2083 18.9375 14.5C18.0833 16.1667 16.8646 17.5 15.2812 18.5C13.6979 19.5 11.9375 20 9.99999 20Z" fill="white"/>
</svg>`,
    uploadRecording: `
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed">
    <path d="M440-320v-326L336-542l-56-58 200-200 200 200-56 58-104-104v326h-80ZM240-160q-33 0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z"/>
    </svg>`,
    screenShare: `<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed">
    <path d="M320-400h80v-80q0-17 11.5-28.5T440-520h80v80l120-120-120-120v80h-80q-50 0-85 35t-35 85v80ZM160-240q-33 0-56.5-23.5T80-320v-440q0-33 23.5-56.5T160-840h640q33 0 56.5 23.5T880-760v440q0 33-23.5 56.5T800-240H160Zm0-80h640v-440H160v440Zm0 0v-440 440ZM40-120v-80h880v80H40Z"/>
    </svg>`,
    stopScreenShare: `
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e8eaed">
    <path d="M577-497 474-600h46v-80l120 120-63 63Zm251 251-74-74h46v-440H314l-80-80h566q33 0 56.5 23.5T880-760v440q0 26-14.5 45.5T828-246Zm-8 218-92-92H40v-80h607l-40-40H160q-33 0-56.5-23.5T80-320v-446l-52-54 56-56L876-84l-56 56ZM400-446v46h-80v-80q0-11 1-21t6-19L160-687v367h366L400-446Zm134-94Zm-191 36Z"/>
    </svg>`,
};

class ReverseTimer {
    constructor(durationInSeconds, { onTick, onStop, onStart } = {}) {
        this.initialDuration = durationInSeconds;
        this.remainingTime = durationInSeconds;
        this.interval = null;
        this.paused = false;

        this.onTick = onTick;
        this.onStop = onStop;
        this.onStart = onStart;
    }

    reset() {
        this.remainingTime = this.initialDuration;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.paused = false;
    }

    start() {
        if (this.interval || this.remainingTime <= 0) return;
        this.paused = false;
        this.onStart?.(this.remainingTime);
        this.interval = setInterval(() => {
            if (this.remainingTime <= 0) {
                this.stop();
                this.onStop?.();
            } else {
                this.remainingTime--;
                this.onTick?.(this.formatTime(this.remainingTime));
            }
        }, 1000);
    }

    pause() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
            this.paused = true;
        }
    }

    resume() {
        if (!this.paused || this.interval) return;
        this.start();
    }

    restart() {
        this.stop();
        this.remainingTime = this.initialDuration;
        this.start();
    }

    stop() {
        clearInterval(this.interval);
        this.interval = null;
        this.paused = false;
    }

    formatTime(seconds) {
        const mins = String(Math.floor(seconds / 60)).padStart(2, "0");
        const secs = String(seconds % 60).padStart(2, "0");
        return `${mins}:${secs}`;
    }
}
