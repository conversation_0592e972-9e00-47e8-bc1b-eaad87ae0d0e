<?php

namespace App\Http\Controllers;

use Session;
use Mail;
use DB;
use App\Http\Requests;
use App\Users;
use Validator;
use View;
use URL;
use DateTime;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

DB::enableQueryLog();

class LoginController extends Controller
{
    public function index(Request $request)
    {
        return view("admin.login_signup.login");
    }

    public function signup(Request $request)
    {
        return view("admin.login_signup.signup");
    }

    public function forgot_password(Request $request)
    {
        return view("admin.forgot-password");
    }

    public function loginpost(Request $request)
    {
        $this->validate(
            $request,
            ["email" => "required", "password" => "required"],
            [
                "email" => "Email Field is required",
                "password" => "Password Field is required",
            ]
        );

        $email = $request->input("email");
        $password = $request->input("password");

        $user = new Users();

        $login = Users::where("email", "=", $email)->where("type", "!=", 5)->where("type", "!=", 6)->first();
        if (!$login) {
            return response()->json([
                "success" => false,
                "message" => "Login Failed, please check email id",
            ]);
        }
        if (!Hash::check($password, $login->password)) {
            return response()->json([
                "success" => false,
                "message" => "Login Failed, please check password",
            ]);
        }

        if ($login["status"] == "0") {
            return response()->json([
                "success" => false,
                "message" => "user deactive",
            ]);
        }

        if (!empty($login) > 0 && $login["type"] == 1) {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            $data["redirect"] = url("/admin-dashboard");
        } elseif (!empty($login) > 0 && $login["type"] == 2) {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            $data["redirect"] = url("/admin-profile");
        } elseif (!empty($login) > 0 && $login["type"] == "3") {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            $data["redirect"] = url("/admin-profile");
        }elseif (!empty($login) > 0 && $login["type"] == "4") {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            $data["redirect"] = url("/admin-profile");
        } elseif (!empty($login) > 0 && $login["type"] == "8") {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            // $data["redirect"] = url("/new-application-list");
            $data["redirect"] = url("/admin-profile");
        }elseif (!empty($login) > 0 && $login["type"] == "9") {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            // $data["redirect"] = url("/Approved-application-list");
            $data["redirect"] = url("/admin-profile");
        }elseif (!empty($login) > 0) {
            $request->session()->put("Adminnewlogin", [
                "id" => $login["id"],
                "first_name" => $login["first_name"],
                "last_name" => $login["last_name"],
                "phone_number" => $login["phone_number"],
                "image" => $login["image"],
                "type" => $login["type"],
            ]);
            $data["success"] = true;
            $data['email']=$login["email"];
            $data['password']= '123456';
            $data["message"] = "Login Done Successfully";
            $data["redirect"] = url("/admin-profile");
        } else {
            $data["success"] = false;
            $data["message"] = "Invalid email and password";
        }

        $newUser = $request->session()->get('Adminnewlogin');
        $newUser['email'] = $login["email"];
        $request->session()->put("userewlogin", $newUser);
        $userTimezone = $request->input('timezone');
        session(['admin_timezone' => $userTimezone]);
        return response()->json($data);
    }
    //================================================================================
    public function signout(Request $request)
    {
        // Session::flush();
        Session::forget('Adminnewlogin');
        return redirect("/admin");
    }

    public function signuppost(Request $request)
    {

    }

    // forget password
    public function admin_forgot_password(Request $request)
    {
        $this->validate(
            $request,
            ["email" => "required|email"],
            ["email" => "Email Field is required"]
        );

        $email = $request->input("email");
        $obj = new Users();
        $users = Users::where("email", $email)->get();

        $statusCode = 200;

        if (sizeof($users) > 0) {
            if (
                $users[0]["type"] == 1 ||
                $users[0]["type"] == 2 ||
                $users[0]["type"] == 4
            ) {
                $data = [
                    "email" => $users[0]["email"],
                    "subject" => "forgot password",
                    "mailbody" => "forgot",
                    "name" => $users[0]["name"],
                    "id" => $users[0]["id"],
                    "otp" => rand(1000, 9999),
                ];
                $request->session()->put("Otpsession", $data);
                $sert = $users[0]["email"];
                setcookie($sert);

                Mail::send("admin.email-temp.forgotpassword", $data, function (
                    $message
                ) use ($email) {
                    $message->to($email)->subject("Whizara Support");
                    $message->from("<EMAIL>");
                });

                $data["success"] = true;
                $data["message"] =
                    "Otp send successfully, Please check your Email";
            } else {
                $data["success"] = false;
                $data["message"] =
                    "Sorry, no user exists on our system with that email";
                $statusCode = 404;
            }
        } else {
            $data["success"] = false;
            $data["message"] =
                "Sorry, no user exists on our system with that email";
            $statusCode = 404;
        }
        return response()->json($data, $statusCode);
    }

    public function admin_otp_match(Request $request)
    {
        if (!Session::has("Otpsession")) {
            $data["success"] = false;
            $data["redirect"] = url("/admin");
            return response()->json($data, 401);
        }

        $otp = session("Otpsession")["otp"];

        $otp1 = $request->input("otp1");
        $otp2 = $request->input("otp2");
        $otp3 = $request->input("otp3");
        $otp4 = $request->input("otp4");

        $userotp = $otp1 . "" . $otp2 . "" . $otp3 . "" . $otp4;
        $data["success"] = $otp == $userotp;
        $data["message"] =
            $otp == $userotp ? "OTP match successfully" : "Otp does not match";

        $statusCode = $otp == $userotp ? 200 : 422;

        return response()->json($data, $statusCode);
    }

    public function admin_reset_password(Request $request)
    {
        $data["success"] = false;

        if (!Session::has("Otpsession")) {
            return response()->json($data, 401);
        }

        $statusCode = 404;

        $confirm_password = $request->input("confirm_password");
        $new_password = $request->input("new_password");

        if (!$confirm_password || !$new_password) {
            $data["message"] = "All fileds are required";
        }

        if ($confirm_password != $new_password) {
            $data["message"] = "Confirm password is not same";
        }

        if ($confirm_password == $new_password) {
            $user_id = session("Otpsession")["id"];

            $row["password"] = Hash::make($confirm_password);
            // $row["passwordStr"] = $confirm_password;

            $update = Users::where("id", $user_id)->update($row);

            if ($update) {
                $data["success"] = true;
                $data["message"] = "Reset password successfully";
                $data["redirect"] = url("/admin");
                $statusCode = 200;
            } else {
                $data["message"] = "Something went wrong";
            }
        }

        return response()->json($data, $statusCode);
    }
}
