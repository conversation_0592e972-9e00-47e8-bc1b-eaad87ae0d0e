<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{User, Programs, invite_programs, ProgramNote, ProgramNoteAmount, ProgramNoteStudent, MailModel,RosterModel,UserThirdStepModel,UserSecondStepModel,BackgroundMedicalModel,ProgramAdminNote,ReviewModel,Logistics,AssessmentsModel,AvailablityLocationModel,AvailabilityModel,UserEducationModel,UserFirstStepModel,user_interview_slots,FeedbackModel,SettingTermsModel,sub_subjects};
use App\Helpers\DataTableHelper;
use App\Http\Requests\Front\ProgramClassRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use stdClass;

class SchoolProgramDetailsController extends Controller
{

    public function index($encryptedId, Request $request)
    {
       
        $user = auth()->user();
      
        $currentDate = now()->toDateString();

        $user->subProgramNotes;
     

        if (checkAuth($user->status)) {
            return redirect("/logout");
        }
      
            $id = decrypt($encryptedId);
            $program = Programs::with('school','creator')->findOrFail($id);

            $invite_program = new stdClass;
            if ($request->filled('pivot_id')) {
                $invite_program = invite_programs::find($request->pivot_id);
            }
            $day = date('N');
            $payData = generateHourlyPaymentData($user, $program, $day);
            $programNote =  $program->userNotes()->where(['class_date'=>$currentDate])->first();
            return view('school.program.programdetail', compact('program', 'invite_program', 'payData','programNote'));
     
    }

    public function list(Programs $program, Request $request)
    {
        $currentDate = now()->toDateString();
        $user = auth()->user();
        $invite_programs = $request->invite_programs;

        $params = DataTableHelper::getParams($request);

        if ($program->mainAssignedUser &&( $user->id == $program->mainAssignedUser->id)) {
            // echo 1; die;
            $qry = $user->programNotes()->where('program_id', $program->id);
        } elseif ($program->subAssignedUsers && in_array($user->id, $program->subAssignedUsers->pluck('id')->toArray())) {
            // echo 2; die;

            $qry = $user->subProgramNotes()->where('program_id', $program->id);
        } else {
            // echo 3; die;

            $qry = $program->userNotes();
        }

        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy('class_date'), $params['row'], $params['rowperpage']);
        $data = [];
        $i = 1;
        $javascriptVoid = 'javascript:void(0);';

        foreach ($result as $row) {
            $rowscount = ProgramNoteStudent::where('program_id', '=', $program->id)
                ->where('program_note_id', '=', $row->id)
                ->get();

            $action = '<div class="d-flex">';
            $editRoute = route('user.program-list.edit-notes', ['programNote' => $row->id, 'invite_programs' => $invite_programs]);
            $editmRoute = route('user.program-list.edit-attendance-notes', ['programNote' => $row->id, 'invite_programs' => $invite_programs]);
            $editOption = false;

            if ($row->status != '0' && $row->status != '2' && is_null($row->note)) {
                if ($currentDate >= $row->class_date->toDateString()) {
                    $isUserMatch = ($user->id == $row->user_id && in_array( $row->user_sub_requested ,[0,4])) || ($user->id == $row->sub_user_id && in_array( $row->user_sub_requested ,[1,2,3]));

                    $btnAttr = $btnClass = $noteBtnClick = $attendanceBtnClick = '';
                    if (!$isUserMatch) {
                        $btnAttr = 'disabled';
                        $btnClass = 'cursor-not-allowed';
                    } else {
                        $noteBtnClick = "onclick=\"openCommanModal('{$editRoute}')\"";
                        $attendanceBtnClick = "onclick=\"openCommanModal('{$editmRoute}')\"";
                    }
                    if (is_null($row->status) && is_null($row->note) && $row->class_date < $currentDate) {
                        $action .= 'Missed';
                    }else{
                        if (!is_null($row->note)){
                            $action .= 'Pending status';
                        }else{
                            $action .= 'Active class';
                        }
                        
                    }
                   
                } else {
                    $action .= 'Upcoming class';
                }
            } elseif ($row->status == '2') {
                $action .= 'Holiday';
            } elseif ($row->status == '1' || !is_null($row->note)) {
                // $action .= 'Note Entered';
                if ($row->status == '1') {
                    $action .= "<button disabled class='btn btn-primary cursor-not-allowed' title='Enter Note' type='button'>
                <span class='text-white'>
                Completed
                </span>
                </button>";
                }
               
            } elseif ($row->status == '0') {
                $action .= 'Cancelled';
            } elseif (is_null($row->status) && $row->class_date < $currentDate) {
                $action .= 'Missed';
            }
            $action .= '</div>';
            $subUserName = '';
            $SubName = '';
            $encryptedprogramId = encrypt($program->id);
                
            if ($row->subUser && $row->user_sub_requested != '2') {
                $subUserName = $row->subUser->first_name . ' ' . $row->subUser->last_name;
                $encryptedSubId = encrypt($row->subUser->id);
             
                $detailSubRoute = route('school.sub-details.details', ['encryptedSubId' => $encryptedSubId,'encryptedprogramId' => $encryptedprogramId]);
               $SubName= "<a href='{$detailSubRoute}' class='idtextcolor'>" . $subUserName."</a>";
            }
            $MainName= '';
            if ($row->user){
            $encryptedMainId = encrypt($row->user->id);
            $detailMainRoute = route('school.main-details.details', ['encryptedMainId' => $encryptedMainId,'encryptedprogramId' => $encryptedprogramId]);
            $MainName= "<a href='{$detailMainRoute}' class='idtextcolor'>" . $row->user->first_name .' '. $row->user->last_name ."</a>";
            }

            $input = '';
            if (is_null($row->note) && is_null($row->status) && $currentDate <= $row->class_date->toDateString() && $row->user_id == $user->id) {
                $input .= "<input type='checkbox' onchange='syncCheckboxes(this)' form='requestSubForm' value='{$row->id}' name='program_note_id[]'
            class='program_note_id'> <input type='checkbox'  form='cancelSubForm' value='{$row->id}' name='program_note_id[]'
            class='program_note_id' style='display:none;'>";
            }

            $startTime = date('h:i A', strtotime($row->start_time));
            $endTime = date('h:i A', strtotime($row->end_time));
         
            $data[] = [
                'id' => $i,
                'class_date' => '<label>' . $input . date('m-d-Y', strtotime($row->class_date)) . '</label>',
                'class_time' => $startTime . '-' . $endTime,
                'user_id' => $SubName?$SubName:$MainName,
                'sub_user_id' => $SubName,
                'attendance' => count($rowscount),
                'rating' => $row->rating,
                'content_taught' => $row->content_taught,
                'note' => $row->note,
                'action' => $action,
            ];

            $i++;
        }

        return DataTableHelper::generateResponse($params['draw'], $count, $data);
    }



    public function getNotesForm(Programs $program, Request $request)
    {
        $invite_program = new stdClass;
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view("components.modals.notes-for-program", compact('program', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeNotesForm(Programs $program, ProgramClassRequest $request)
    {
        $user = auth()->user();
        $user_id = $user->id;

        $program_id = $program->id;

        $day = date('N');
        $payData = generateHourlyPaymentData($user, $program, $day);

        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }

        $obj = new ProgramNote();
        $obj->fill($request->only('attendance', 'rating', 'content_taught', 'note'));


        $obj->user_id = $user_id;
        $obj->program_id = $program_id;

        $obj->class_date = now()->toDateString();
        $obj->day = $day;
        $obj->start_time = $payData['start_time'];
        $obj->end_time = $payData['end_time'];
        $obj->save();

        if (!empty($payData)) {

            $programNoteAmount = new ProgramNoteAmount();
            $programNoteAmount->program_id = $program_id;
            $programNoteAmount->user_id = $user_id;
            $programNoteAmount->program_note_id = $obj->id;
            $programNoteAmount->hours = $payData['hours'];
            $programNoteAmount->rate = $payData['rate'];
            $programNoteAmount->minutes = $payData['minutes'];
            $programNoteAmount->format = $payData['format'];
            $programNoteAmount->type = getInstructorType(@$invite_program->type ?? '');
            $programNoteAmount->amount = $payData['amount'];
            $programNoteAmount->save();

            /* $students = $request->student;
            $class_ids = $request->class_id;
            foreach ($students as $key => $student) {
                $programNoteStudent = new ProgramNoteStudent();
                $programNoteStudent->program_id = $program_id;
                $programNoteStudent->user_id = $user_id;
                $programNoteStudent->program_note_id = $obj->id;
                $programNoteStudent->student = $student;
                $programNoteStudent->class_id = $class_ids[$key];
                $programNoteStudent->save();
            } */
        }

        return response()->json(['status' => true, 'message' => "Note saved successfully", "resetForm" => true]);
    }

    public function getSchoolBydistrict($id)
    {
        $values = User::where("district", $id)
            ->where("type", 6)
            ->pluck('full_name', 'id');
        $view = view("components.options", compact('values'))->render();

        return response()->json([
            "status" => true,
            "view" => $view,
        ]);
    }
    public function addStudent(Programs $program)
    {
        $program->load('classes');
        $classes = $program->classes;
        $view = view('components.add-student', compact('classes'))->render();
        return response()->json(['view' =>  $view,], 200);
    }

    public function editNotesForm(ProgramNote $programNote, Request $request)
    {
        $invite_program = new stdClass;
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view("components.modals.edit-notes-for-program", compact('programNote', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNotesForm(ProgramNote $programNote, ProgramClassRequest $request)
    {
        if ($programNote->status == '0') {
            return response()->json(['status' => false, 'message' => "Class cancelled", "resetForm" => true]);
        }
        $editor = auth()->user();
        $user = $programNote->user;
        $user_id = $programNote->user_id;

        $program = $programNote->program;
        $program_id = $programNote->program_id;

        $day = $programNote->day;

        $payData = generateHourlyPaymentData($user, $program, $day, $programNote->start_time, $programNote->end_time);

        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }



        $programNote->fill($request->only('attendance', 'rating', 'content_taught', 'note'));
        $programNote->entered_by = $editor->id;
        if ($file = $request->file("file")) {
            $name = time() . "." . $file->getClientOriginalExtension();
            $filename = 'uploads/classdocument/'. $name;
            uploads3image($filename,$file);
             $programNote->document = $name;
        }
        // $programNote->status = 1;
        $programNote->save();

        if (!empty($payData)) {
            /*             $programNote->amount()->delete();
            $programNoteAmount = new ProgramNoteAmount();
            $programNoteAmount->program_id = $program_id;
            $programNoteAmount->user_id = $user_id;
            $programNoteAmount->program_note_id = $programNote->id;
            $programNoteAmount->hours = $payData['hours'];
            $programNoteAmount->rate = $payData['rate'];
            $programNoteAmount->minutes = $payData['minutes'];
            $programNoteAmount->format = $payData['format'];
            $programNoteAmount->type = getInstructorType(@$invite_program->type ?? '');
            $programNoteAmount->amount = $payData['amount'];
            $programNoteAmount->save(); */

            /* $programNote->students()->delete();
            $students = $request->student;
            $class_ids = $request->class_id;

            foreach ($students as $key => $student) {
                $programNoteStudent = new ProgramNoteStudent();
                $programNoteStudent->program_id = $program_id;
                $programNoteStudent->user_id = $user_id;
                $programNoteStudent->program_note_id = $programNote->id;
                $programNoteStudent->student = $student;
                $programNoteStudent->class_id = $class_ids[$key];
                $programNoteStudent->save();
            } */
        }

        return response()->json(['status' => true, 'message' => "Note saved successfully", "resetForm" => true]);
    }


    public function getNoticeForm(Request $request)
    {

        $view = view("components.modals.notice")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNoticeForm(Request $request)
    {
        $request->validate(
            [
                'reason' => 'required',

            ]
        );


        $user = auth()->user();

        $record = MailModel::where("created_by",  $user->id)->where("f_type",  'Notice')->first();
        if ($record) {
            return response()->json(['status' => false, 'message' => "Already Appiled", "resetForm" => true]);
        } else {
            $obj = new MailModel();

            $obj->user_id = $user->id;
            $obj->title = 'Notice';
            $obj->f_type = 'Notice';
            $obj->subject = 'Notice';
            $obj->created_by = $user->id;
            $obj->message = $request->reason;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Notice sent successfully", "resetForm" => true]);
        }
    }


    public function editNotesattendanceForm(ProgramNote $programNote, Request $request)
    {
        $invite_program = new stdClass;
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view("components.modals.edit-attendancenotes-for-program", compact('programNote', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNotesattendanceForm(ProgramNote $programNote, Request $request)
    {
      
        if (!$request->filled('student')) {
            // return response()->json(['status' => false, 'message' => "Please select at least one student", "resetForm" => false]);
         
        }
      
      
        $editor = auth()->user();
        $user = $programNote->user;
        $user_id = $programNote->user_id;

        $program = $programNote->program;
        $program_id = $programNote->program_id;   
        $students=$request->student;
        $userID=$request->user_id;
    
                 $row = ProgramNoteStudent::where("program_id", "=", $program_id)->where("program_note_id", "=", $programNote->id)->where("user_id", "=", $user_id)->where("student", "=", $userID)->first();
 
                  if(isset($row->student)){
                    $row = ProgramNoteStudent::where("program_id", "=", $program_id)->where("program_note_id", "=", $programNote->id)->where("user_id", "=", $user_id)->where("student", "=", $userID)->delete();
                  }else{
                    $programNoteStudent = new ProgramNoteStudent();
                    $programNoteStudent->program_id = $program_id;
                    $programNoteStudent->user_id = $user_id;
                    $programNoteStudent->program_note_id = $programNote->id;
                    $programNoteStudent->student = $userID;
                    $programNoteStudent->save();
                  }
               
     

        return response()->json(['status' => true, 'message' => "Attendance saved successfully", "resetForm" => false]);
    }


    public function studentlist($program,$programNote,Request $request)
    {

  
      
        if ($request->ajax()) {
           
           
            $params = DataTableHelper::getParams($request);
          
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'program_rosters.id';
            }


            $qry = RosterModel::select('program_rosters.*')
                 ->where("program_id", "=", $program)
                 ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
              
                
                
            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });


           


            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
         
          
            foreach ($result as $row) {
              
                $viewButton = "";
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);
                $rows = ProgramNoteStudent::where("program_id", "=", $program)->where("program_note_id", "=", $programNote)->where("student", "=", $row->id)->first();
             
                if($rows){
                $viewButton .= "<input type='checkbox' form='MarkAttendance' onchange='savemark({$row->id})'   value='{$row->id}'  name='student[]' checked='checked'> ";
                }else{
                $viewButton .= "<input type='checkbox' form='MarkAttendance' onchange='savemark({$row->id})'   value='{$row->id}'  name='student[]'> ";

                }
                $data[] = [
                    "id" => $row->id,
                    "student_name" => $row->student_name,
                    "class_id" => $row->class_id,
                    "action" => $viewButton,
                   
                  
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
       
    }



    public function assignuserdetails($encryptedId,$encryptedprogramId,Request $request)
    {
        $user = auth()->user();
        if (checkAuth($user->status)) {
            return redirect("/logout");
        }
     
            $id = decrypt($encryptedId);
            $data['programid'] = decrypt($encryptedprogramId);
            $program_id=decrypt($encryptedprogramId);
             $data['program'] = Programs::where('id',$program_id)
            ->first();
            $data['user'] = $instructor = User::where("id", "=", $id)->firstOrFail();

            $data['user_first_step'] = UserFirstStepModel::where("user_id", $id)
            ->first();

            $data['user_second_step'] = UserSecondStepModel::where("user_id", $id)
            ->first();

            $data['user_third_step'] = UserThirdStepModel::where("user_id", $id)
            ->first();

           
            $data['intro'] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "introduction",
            ])->first();
          
        $data['teaching'] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "teaching",
        ])
            ->orderBy("id", "DESC")
            ->get();
            $data['classroom'] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "classroom",
        ])->get();

        $data['user_education'] = UserEducationModel::where([
            "user_id" => $id,
        ])->get();


        $data['availrangelocation'] = AvailablityLocationModel::where(['user_id' => $id])->get();
        $data['user_interview_slots'] = user_interview_slots::select(
                "tbl_user_interview_slots.*",
                "tbl_schedule_interviews.date as date",
                "tbl_schedule_interviews.time as time",
                "tbl_schedule_interviews.timezone as timezone",
                "tbl_schedule_interviews.end_time as end_time",
                "tbl_schedule_interviews.link as link"
            )
            ->join(
                "tbl_schedule_interviews",
                "tbl_schedule_interviews.id",
                "=",
                "tbl_user_interview_slots.slot_id"
            )
            ->where(["tbl_user_interview_slots.user_id" => $id])
            ->orderBy("tbl_user_interview_slots.id", "DESC")
            ->get();


            $data['availability'] = AvailabilityModel::where(['user_id' => $id])->first();
            
            $data['review_record'] = ReviewModel::where("from_id",  $user->id)->where("to_id",  $id)->where("program_id", $program_id)->first();
            
            $data['avgRating'] = round(ReviewModel::where("to_id",  $id)->avg('rating'));
            
            return view('school.user.userdetails')->with($data);
     
    }

    public function bgcheckstatus($encryptedStrId,Request $request)
    {
       
        $programid = decrypt_str($encryptedStrId);
       
        $background_check = BackgroundMedicalModel::select('tbl_user_docs.*', 'tbl_user_doc_requests.state','tbl_user_doc_requests.label','tbl_user_doc_requests.description','tbl_user_doc_requests.status')
        ->join('tbl_user_docs', 'tbl_user_doc_requests.id', '=', 'tbl_user_docs.back_med_id')
        ->where(['type' => 'background_check', 'program_id' =>$programid])
        // ->whereIn(['tbl_user_docs.status' => '1'])
        ->get();
        $view = view("components.modals.school.bgcheckstatus",compact('background_check'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function medicalcheckstatus($encryptedStrId,Request $request)
    {
       
        $programid = decrypt_str($encryptedStrId);
        
        
        $medical_requirements = BackgroundMedicalModel::select('tbl_user_docs.*', 'tbl_user_doc_requests.state','tbl_user_doc_requests.label','tbl_user_doc_requests.description','tbl_user_doc_requests.status')
        ->join('tbl_user_docs', 'tbl_user_doc_requests.id', '=', 'tbl_user_docs.back_med_id')
        ->where(['type' => 'medical_requirements', 'program_id' =>$programid])
        // ->where(['tbl_user_docs.status' => '1'])
        ->get();

        $view = view("components.modals.school.medicalcheckstatus",compact('medical_requirements'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getTabData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $tab = $request->tab;
        $program_id = $request->program_id;
        $ProgramAdminNote=ProgramAdminNote::where('program_id',$program_id)->first();
        $currentDate = now()->toDateString();
        $requestSub=array();
        $cancelSub=array();
        $invite_program = new stdClass();
            if ($request->filled('pivot_id')) {
                $invite_program = invite_programs::find($request->pivot_id);
            }
            $program = Programs::where("id", $program_id)->first();
            $school_id=$program->school_name;
            if($program->delivery_type=='Online'){
                $doc = SettingTermsModel::where("id", '12')->first();
                $classroomprerequisites=$doc->description;
                if($school_id){
                $classroomprerequisites= str_replace('{{school_name}}', schoolusername($school_id), $classroomprerequisites);
             
                }
            }elseif($program->delivery_type=='In-Person'){
                $doc = SettingTermsModel::where("id", '13')->first();
                $classroomprerequisites=$doc->description;
                if($school_id){
              
                $classroomprerequisites= str_replace('{{school_name}}', schoolusername($school_id), $classroomprerequisites);
                }
            }else{
                $doc=array();
                $classroomprerequisites='';
            }
    
            if($program->sub_subject_id){
                $res = sub_subjects::where("id", $program->sub_subject_id)->first(); 
                  $subname=$res->name;
                if($program->delivery_type=='Online'){
                    $course_prerequisites = $res->online_doc;
                  
                }elseif($program->delivery_type=='In-Person'){
                    $course_prerequisites = $res->inperson_doc;
                 
                }else{
                  
                    $course_prerequisites='';
                }
            }else{
              
                $course_prerequisites='';
            }
    
            $logistics = Logistics::where('program_id', $program_id)->first();

        switch ($tab) {

            case 'overview':

                $program = Programs::where('id',$program_id)
                    ->first();

                break;
            case 'notesandinstructions':

                $program = Programs::where('id',$program_id)
                ->first();

                break;
            case 'logistics':
                $program = Programs::where('id',$program_id)
                ->first();

                break;

            case 'classes':
                $program = Programs::where('id',$program_id)
                ->first();
             

                $requestSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status', 'sub_user_id'])
                    ->exists();
    
                $cancelSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status'])
                    ->where(function ($query) {
                        $query->whereNotNull('sub_user_id')
                            ->orWhereIn('user_sub_requested', [0, 3]);
                    })
                    ->exists();
                break;

            case 'reviews':

                $program = Programs::where('id',$program_id)
                ->first();

        
                break;

                case 'requirements':

                    $program = Programs::where('id',$program_id)
                    ->first();
    
            
                    break;

                    case 'notice':
                      
                        $program = MailModel::where("created_by",  $user_id)->where("program_id",  $program_id)->where("f_type",  'Notice')->first();
                      
                        break;
                        case 'roster':

                            $program = Programs::where('id',$program_id)
                            ->first();
            
                    
                            break;
        

            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }
     
        
        $view = view("school.program.tabs.{$tab}", compact('program','requestSub','cancelSub','invite_program','ProgramAdminNote','logistics','program_id','classroomprerequisites','course_prerequisites','doc'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getReviewData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $program=$request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = ReviewModel::where('program_id', '=', $program)->where('from_id', '=', $user_id)->where('to_id', '=', null)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {
               
                $data[] = [
                    'id' => $row->id,
                    'review' => $row->review,
                    'rating' => $row->rating,
                    'created_at' => getUserTimestamp($row->created_at)
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
      
    }


    public function getFeedbackData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $program=$request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = FeedbackModel::where('program_id', '=', $program)->where('user_id', '=', $user_id)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {
               
                $data[] = [
                    'id' => $row->id,
                    'feedback' => $row->feedback,
                    'rating' => $row->rating,
                    'created_at' => getUserTimestamp($row->created_at)
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
      
    }
    public function getfeedbackForm($encryptedprogramidId,Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $ins_id = $user_id;
        $programid = decrypt_str($encryptedprogramidId);
      
        $view = view("components.modals.school.programfeedback",compact('ins_id','programid'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    
    public function addreview(Request $request)
    {
        $request->validate(
            [
                // 'rating' => 'required',
                'feedback' => 'required',

            ]
        );

        
        $user = auth()->user();
        $ins_id = decrypt_str($request->to_id);
        $program_id = decrypt_str($request->program_id);
       
            $obj = new ReviewModel();

            $obj->from_id = $ins_id;
            $obj->rating = $request->rating?$request->rating:0;
            $obj->review = $request->feedback;
            $obj->program_id = $program_id;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Saved successfully", "resetForm" => true]);
        
    }

    public function addprogramfeedback(Request $request)
    {
        $request->validate(
            [
                // 'rating' => 'required',
                'feedback' => 'required',

            ]
        );

        
        $user = auth()->user();
        $ins_id = decrypt_str($request->to_id);
        $program_id = decrypt_str($request->program_id);
       
            $obj = new FeedbackModel();

            $obj->user_id = $user->id;
            $obj->rating = $request->rating?$request->rating:0;
            $obj->feedback = $request->feedback;
            $obj->program_id = $program_id;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Saved successfully", "resetForm" => true]);
        
    }

    public function getSchoolNoticeForm($program_id,Request $request)
    {

        $view = view("components.modals.school.programnotice",compact('program_id','program_id'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateSchoolNoticeForm(Request $request)
    {
        $request->validate(
            [
                'reason' => 'required',

            ]
        );


        $user = auth()->user();

        $record = MailModel::where("created_by",  $user->id)->where("program_id", $request->program_id)->where("f_type",  'Notice')->first();
        if ($record) {
            return response()->json(['status' => false, 'message' => "Already Appiled", "resetForm" => true]);
        } else {
            $obj = new MailModel();

            $obj->user_id = $user->id;
            $obj->program_id = $request->program_id;
            $obj->title = 'Notice';
            $obj->f_type = 'Notice';
            $obj->subject = 'Notice';
            $obj->created_by = $user->id;
            $obj->message = $request->reason;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Notice sent successfully", "resetForm" => true]);
        }
    }


    public function getRosterData(Request $request)
    {
    
        $program=$request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = RosterModel::select('program_rosters.*')
            ->where('program_id', '=', $program)
            ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {
               
                $data[] = [
                   
                    'class_id' => $row->class_id,
                    'student_name' => $row->student_name
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
      
    }

    
}
