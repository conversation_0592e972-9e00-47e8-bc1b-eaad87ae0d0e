<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Logistics extends Model
{
    protected $table = 'tbl_school_logistics';
    protected $fillable = [
     'title','program_id', 'school_id', 'subject_id', 'sub_subject_id', 'document', 'description', 'status', 'request_type','prerequisites','classroom_prerequisites','course_prerequisites','created_by'
    ];

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

    public function school()
    {
        return $this->belongsTo(User::class, 'school_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function subsubject()
    {
        return $this->belongsTo(SubsubjectModel::class, 'sub_subject_id');
    }
}
