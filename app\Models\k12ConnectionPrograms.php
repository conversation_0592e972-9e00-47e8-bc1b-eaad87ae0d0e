<?php

namespace App\Models;

use App\cbo;
use App\Classes;
use App\invite_programs;
use App\OnboardingInstructor;
use App\program_slots;
use App\Subject;
use App\SubsubjectModel;
use App\User;
use DateTime;
use DateTimeZone;
use Illuminate\Database\Eloquent\Model;

class k12ConnectionPrograms extends Model
{
    protected $table = 'k12_connection_programs';
    protected $fillable = [
        'requirement_id',
        'name',
        'start_date',
        'end_date',
        'program_status',
        'school_id',
        'program_type_id',
        'delivery_type',
        'grade_id',
        'capacity',
        'has_certifications',
        'other_certification_states',
        'background_checks',
        'medical_requirements',
        'address',
        'city',
        'state',
        'timezone_id',
        'zip_code',
        'country',
        'notes_id',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
        'lat',
        'lng',
        'link_id',
        'cbo_id',
        'district_id',
        'subject_area_id',
        'subject_id',
        'is_imported',
        'archived_at',
        'version',
        'managed_under',
        'additional_information',
    ];

    public function requirements()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'requirement_id');
    }

    public function classes()
    {
        return $this->belongsToMany(Classes::class, 'grade_id');
    }

    public function creator()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'created_by');
    }

    public  function createClasses()
    {
        $timezone = new DateTimeZone($this->timezone ?? 'UTC');
        $currentDate = new DateTime('now', $timezone);
        $currentDate->setTime(0, 0, 0);

        $start_date = new DateTime($this->start_date, $timezone);
        $start_date->setTime(0, 0, 0);

        $end_date = new DateTime($this->end_date, $timezone);
        $end_date->setTime(0, 0, 0);

        $noClasses = $this->noClasses;
        while ($start_date <= $end_date) {
            if ($start_date < $currentDate) {
                $start_date->modify('+1 day');
                continue;
            }
            if ($this->is_imported == '0' && !empty($this->noClasses) && $this->shouldSkipDate($start_date, $noClasses)) {

                $start_date->modify('+1 day');
                continue; // skip
            }


            $day = $start_date->format('N');
            $dateSchedule = $this->dateSchedule($day);

            if (empty($dateSchedule->value('start_time')) || empty($dateSchedule->value('end_time'))) {
                $start_date->modify('+1 day');

                continue; // skip
            }

            $obj = new k12ConnectionClasses();

            $obj->main_instructor_id = null;
            $obj->program_id = $this->id;


            $obj->class_date = $start_date;
            $obj->day = $day;
            $obj->status = 'scheduled';
            $obj->start_time = $dateSchedule->first()->start_time;
            $obj->end_time = $dateSchedule->first()->end_time;
            $obj->save();
            $start_date->modify('+1 day');
        }
    }

    private function shouldSkipDate(DateTime $date, $noClasses)
    {
        $currentDate = new DateTime();
        $currentDate->setTime(0, 0, 0);

        if ($date < $currentDate) {
            return true;
        }

        foreach ($noClasses as $noClass) {
            $noClassesStartDate = new DateTime($noClass->start_date);
            $noClassesEndDate = $noClass->end_date ? new DateTime($noClass->end_date) : $noClassesStartDate;

            if (
                $date >= $noClassesStartDate &&
                $date <= $noClassesEndDate
            ) {
                return true;
            }
        }

        return false;
    }

    public function dateSchedule($day)
    {
        return $this->schedules()->where('class_day', $day);
    }

    public function schedules()
    {
        return $this->hasMany(k12ConnectionProgramsSchedule::class, 'program_id');
    }

    public function userNotes()
    {
        return $this->hasMany(k12ConnectionClasses::class, 'program_id')->orderBy('k12_connection_classes.class_date');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function subSubject()
    {
        return $this->belongsTo(SubsubjectModel::class, 'sub_subject_id');
    }

    public function school()
    {
        return $this->belongsTo(User::class, 'school_name');
    }

    public function programNoteAmounts()
    {
        return $this->hasMany(k12ConnectionPaymentLogs::class, 'program_id');
    }

    public function noClasses()
    {
        return $this->hasMany(k12ConnectionNoClassDates::class, 'program_id');
    }

    public function cbo()
    {
        return $this->belongsTo(cbo::class, 'cbo_id');
    }

    public function user()
    {
        return $this->hasOneThrough(
            OnboardingInstructor::class, // Target model
            k12ConnectionInvitePrograms::class, // Intermediate model
            'program_id', // Foreign key on the intermediate table
            'id', // Foreign key on the target table
            'id', // Local key on the parent model
            'user_id' // Local key on the intermediate model
        );
    }

    public function nextCommingProgram(){
        $currentDate = now()->toDateString();
        return $this->hasOne(k12ConnectionClasses::class, 'program_id')
        ->where(function ($qu) use ($currentDate) {
            $qu->where(function ($query) use ($currentDate) {
                $query
                    ->whereNull('makeup_class_id')
                    ->where(function ($q) use ($currentDate) {
                        $q->whereDate('class_date', '=', $currentDate)
                            ->orWhere('class_date', '>', $currentDate);
                    });
            })
            ->orWhere('class_date', '<', $currentDate);
        })
        ->orderByRaw("CASE WHEN class_date >= '{$currentDate}' THEN 0 ELSE 1 END")
        ->orderBy('class_date', 'asc');
    }

    public function mainAssignedUser()
    {
        return $this->hasOneThrough(
            OnboardingInstructor::class,
            k12ConnectionInvitePrograms::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('k12_connection_invite_programs.is_approved', '=', '1')->where(function ($query) {
            $query->where('k12_connection_invite_programs.type', '=', '1')
                ->orWhere('k12_connection_invite_programs.admin_type', '=', '1');
        })->where(function ($query) {
            $query->where('k12_connection_invite_programs.status', '1')
                ->orWhereNull('k12_connection_invite_programs.status');
        })->orderBy('k12_connection_invite_programs.id', 'DESC');
    }

    public function subAssignedUser()
    {
        return $this->hasOneThrough(
            OnboardingInstructor::class,
            k12ConnectionInvitePrograms::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where(function ($query) {
            $query->where('k12_connection_invite_programs.type', '=', '0')
                ->orWhere('k12_connection_invite_programs.admin_type', '=', '0')
                ->whereNull('k12_connection_invite_programs.user_id')
                ->orWhere('k12_connection_invite_programs.user_id', '')
                ->whereNotNull('k12_connection_invite_programs.requested_by')
                ->where('k12_connection_invite_programs.replacement_type', 0);
        })->orderBy('k12_connection_invite_programs.id', 'DESC');
    }

    public function mainUser()
    {
        return $this->hasOneThrough(
            User::class,
            k12ConnectionInvitePrograms::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where(function ($query) {
            $query->where('k12_connection_invite_programs.type', '=', '1')
                ->orWhere('k12_connection_invite_programs.admin_type', '=', '1');
        })->orderBy('k12_connection_invite_programs.id', 'DESC');
    }

    public function subAssignedUsers()
    {
        return $this->hasManyThrough(
            User::class,
            k12ConnectionInvitePrograms::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('k12_connection_invite_programs.is_approved', '=', '1')->where('k12_connection_invite_programs.is_standby', '!=', '1')->where(function ($query) {
            $query->where('k12_connection_invite_programs.type', '=', '0')
                ->orWhere('k12_connection_invite_programs.admin_type', '=', '0');
        })->orderBy('k12_connection_invite_programs.id', 'DESC');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'school_id');
    }
}
