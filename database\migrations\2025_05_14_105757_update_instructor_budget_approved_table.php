<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateInstructorBudgetApprovedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update existing instructor_budget_approved table
        Schema::table('instructor_budget_approved', function (Blueprint $table) {
            $table->dropColumn(['basePayBOI', 'basePayBOE', 'basePayTotal']);
            $table->decimal('in_person', 10, 2)->default(0)->after('user_id');
            $table->decimal('bilingual_inc', 10, 2)->default(0)->after('in_person');
            $table->decimal('case_management', 10, 2)->default(0)->after('bilingual_inc');
            $table->timestamp('status_updated_at')->nullable()->after('case_management');
        });

        // Create new instructor_budget_lines table
        Schema::create('instructor_budget_lines', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('approved_id');
            $table->string('subject_code')->nullable();
            $table->string('subject_title')->nullable();
            $table->decimal('base_pay', 10, 2)->default(0);
            $table->decimal('experience_pay', 10, 2)->default(0);
            $table->decimal('education_pay', 10, 2)->default(0);
            $table->decimal('non_teaching', 10, 2)->default(0);
            $table->decimal('special_education', 10, 2)->default(0);
            $table->decimal('total', 10, 2)->default(0);
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('approved_id')
                  ->references('id')->on('instructor_budget_approved')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert changes to instructor_budget_approved
        Schema::table('instructor_budget_approved', function (Blueprint $table) {
            $table->dropColumn(['in_person', 'bilingual_inc', 'case_management', 'status_updated_at']);
            $table->decimal('basePayBOI', 10, 2)->default(0);
            $table->decimal('basePayBOE', 10, 2)->default(0);
            $table->decimal('basePayTotal', 10, 2)->default(0);
        });

        // Drop instructor_budget_lines table
        Schema::dropIfExists('instructor_budget_lines');
    }
}
