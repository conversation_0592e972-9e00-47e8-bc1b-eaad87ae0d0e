<?php

namespace App\Console\Commands\Reminders;

use App\User;
use Illuminate\Console\Command;

class IncompleteApplicationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:incomplete-application';
    
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminders to instructors with incomplete application';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = "reminder:incomplete-application";

        $instructors = User::active()
            ->where("type", "=", "5")
            ->where("app_notification", "=", "1")
            ->where("profile_status", "=", "1")
            ->pluck('id')->toArray();

        if (!empty($instructors)) {
            foreach ($instructors as $id ) {
                incompleteApplicationRemNotify($id, $signature, "user", "user");
            }
        }

    }
}
