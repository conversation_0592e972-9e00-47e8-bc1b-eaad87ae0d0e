<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\Programs;
use App\Users;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportApplications implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request;
    }

    public function collection()
    {
       
      
            $application = Users::query();
            $application->select('users.*');
            if(session('Adminnewlogin')['type']==4){
            $application->join(
                "tbl_invite_application_recruiters",
                "tbl_invite_application_recruiters.application_id",
                "=",
                "users.id"
            );
           
           $application->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']]);        
            }

            // $application->where("users.profile_status", "=", "1");
            $application->where("users.type", "5");
            $application->orderBy("users.id", "desc");
           
      
        return  $application->get();
    }

 
    public function headings(): array
    {
        return [
            'Status',
            'Instructor Name',
            'Email',
            'format'
        ];
    }

    public function map($row): array
    {

        $profile_status='';
        $teach='';
        if($row->email_verify_status==1){
        if($row->profile_status == null){
            $profile_status= 'Account created';
        }elseif($row->profile_status == 13){
            $profile_status= 'Rejected';
            }else{
            $profile_status= profilestatus($row->profile_status);
            }
         }else{
            $profile_status= 'Account created';
        }
          if($row->profile_status == 12){
            if($row->is_approved == 16){
                $teach='Online';
            }
          
            if($row->is_approved == 17){
                $teach='in-person';
            }
           
            if ($row->is_approved == 20){
                $teach='Hybrid';
            }
            
          }else{
            $teach=$row->teach;
          }
                     
         
   
        return [
            $profile_status ? $profile_status : 'NIL',
            $row->first_name ? ($row->first_name . ' ' . $row->last_name) : '',
            $row->email ? $row->email : '',
            $row->teach ? $row->teach : '',
        
        ];
    }
}
