<?php

namespace App\Http\Controllers\Admin;

use App\EmailTemplate;
use App\Helpers\DataTableHelper;
use App\Helpers\NotificationHelper;
use App\Http\Controllers\Controller;
use App\invite_programs;
use App\ProgramNote;
use App\Programs;
use App\InviteProgramNote;
use App\Notification_content;
use Illuminate\Http\Request;
use Excel;
use App\Exports\Admin\ExportRequest;

class ProgramRequestController extends Controller
{

    public function index(Request $request)
    {
        $sidebarGroup = 'requestMenu';

        $sidebarMenu = 'program-invite-request';
        $status = null;
        if ($request->has('status')) {
            $status = $request->filled('status') ? $request->status : null;
        }
        if ($request->ajax()) {
            $query = invite_programs::with('program', 'user', 'program.school', 'program.schedules');
            $adminSession = session()->get('Adminnewlogin');
            $adminType = $adminSession['type'];

            if ($adminType != '1') {
                $whereInIds = getAdminUserProgramIds();
                if (!empty($whereInIds)) {
                    $query->whereIn('program_id', $whereInIds);
                }
            }

            $params = DataTableHelper::getParams($request);

            $query->where(['status' => 1]);
            $query->whereNotNull('type');
            // if (is_null($status)) {

            // $query->where('deadline', '>=', now()->toDateTimeString());
            // }


            $query->where('is_approved', $status ?? null);
            $query->where('is_auto_invite', 1);


            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            $query->where(function ($que) use ($searchValue) {
                $que->whereHas('program', function ($query) use ($searchValue) {
                    $query->where('id', 'LIKE', "%{$searchValue}%");
                })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    });
            });


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $action =  $this->generateActionButtons($row,[]);
                $viewUser = $viewProgram = $program_type = $delivery_type = $start_date = $end_date = "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a href='{$viewProgramRoute}'>{$row->program->id}</a>";
                    $program_type = $row->program->program_type;
                    $delivery_type = $row->program->delivery_type;
                    $start_date = date('m-d-Y', strtotime($row->program->start_date));
                    $end_date = date('m-d-Y', strtotime($row->program->end_date));
                }
                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a href='{$viewUserRoute}'>{$userName}</a>";
                }

                $data[] = [
                    "id" => $row->id,
                    "name" => $viewProgram,
                    "deadline" => !is_null(@$row->deadline) ? date('m-d-Y h:i A', strtotime(@$row->deadline)) : '',
                    "user_id" => $viewUser,
                    "type" => getInstructorType($row->type ?? $row->admin_type ?? $row->replacement_type),

                    "program_type" => $program_type,
                    "delivery_type" => $delivery_type,
                    "start_date" => $start_date,
                    "end_date" => $end_date,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


        return view("admin.program.requests.list", compact("sidebarMenu", "status", "sidebarGroup"));
    }
    private function generateActionButtons($row,$res = [])
    {
        $id = $row->id;
        $javascriptVoid = "javascript:void(0);";
        $acceptButton = $declineButton = $viewClassButton = '';
        if ($row->notes->isNotEmpty()) {
            $viewClassRoute = route('admin.program.view-invite-classes', ['id' => $id]);

            $viewClassButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=openAdminModal('{$viewClassRoute}')>View Classes</a>  &nbsp;";
        }

        if (is_null($row->is_approved)) {
            $acceptRoute = route('admin.program.update.request', ['id' => $id, 'is_approved' => 1]);

            $acceptButton = "<a href='{$javascriptVoid}' class='btn btn-outline-success btn-rounded' onclick=updateRequest('{$acceptRoute}')>Accept</a>  &nbsp;";

            $declineRoute = route('admin.program.update.request', ['id' => $id, 'is_approved' => 0]);

            $declineButton = "<a href='{$javascriptVoid}' class='btn btn-outline-danger btn-rounded' onclick=updateRequest('{$declineRoute}')>Decline</a>  &nbsp;";
        }


        $html = "<div class='w-100 d-flex justify-content-around align-items-center'>{$viewClassButton}{$acceptButton}{$declineButton}</div>";
        return $html;
    }
    public function updateRequest($id, Request $request)
    {
        // dd($request->all(),$id);
        $obj = invite_programs::findOrFail($id);
        $is_approved = $request->is_approved;
        $message = $is_approved == 1 ? 'Accepted' : 'Declined';
        if ($is_approved == 1) {
            $programlist = ProgramNote::where('program_id', $obj->program_id)->where('user_id', '!=', null)->get();

            if (count($programlist) > 0) {

                $obj->status = 1;
                $obj->is_standby = 1;
                $obj->is_approved = 1;

                $obj->save();
                return response()->json(['status' => true, 'message' => $message . " invite as Stand By successfully"]);
            }
        }


        $obj->is_approved = $is_approved;
        $obj->save();
        // dd($obj);

        if ($is_approved == 1) {
            // if ($obj->is_auto_invite == '1') {
            //     invite_programs::where('is_auto_invite', '1')
            //         ->where('program_id', $obj->program_id)
            //         ->whereNull('is_approved')
            //         ->where('id', '!=', $obj->id)
            //         ->where('type',  $obj->id)
            //         ->delete();
            // }


            $program = Programs::findOrFail($obj->program_id);

            /*$program->program_status = "Upcoming";*/
            $program->save();

            $school_name = schoolusername($program->school_name) ?? '';

            $remainuser = invite_programs::where('program_id',$obj->program_id)->where('is_auto_invite',1)->where('user_id','!=',$obj->user_id)->select('user_id')->get();

            adminAcceptedAppliedProgramNotification($program,$obj->user_id,'24','Main-Instructor',$school_name);
            // dd($remainuser);
            if(count($remainuser) > 0){
                remainInstructorSendInfoNotification($remainuser,$program,'75',$school_name);
            }

            $program->createNotes($obj->user_id, $obj);




            $user = $obj->user;

            if ($user) {
                $subject = "Request Accepted";
                $programName = $program->name;
                $school = $program->school;
                $schoolName =@$school->full_name??'';
                $notificationContent = Notification_content::where("signature", "applied-request-accepted")->first();
                $notificationTemplate = str_replace(['{{schoolName}}', '{{programName}}'], [$schoolName, $programName], @$notificationContent->content??'');
                $notificationTemplate = str_replace(['{{format}}', '{{city_name}}','{{start_date}}'], [$program->delivery_type, $program->city,date('m-d-Y',strtotime($program->start_date))], $notificationTemplate);

                $type = 'request-replacement';

                $template = EmailTemplate::find(26);

                NotificationHelper::sendProgramNotification($user, $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);

            }

        } else {
            $obj->processDeclinedInvite();
        }


        return response()->json(['status' => true, 'message' => $message . " successfully"]);
    }

    public function replacementList(Request $request)
    {
        $sidebarGroup = 'requestMenu';
        $sidebarMenu = 'replacement-requests';
        $query = invite_programs::with('program', 'requester', 'program.school', 'program.schedules', 'notes.programNote');
        $whereInIds = getAdminUserProgramIds();
        if (!empty($whereInIds)) {
            $query->whereIn('program_id', $whereInIds);
        }
        $query->whereNotNull('replacement_type');
        $query->whereNotNull('requested_by');
        $invites = $query->orderBy('created_at', 'DESC')->get();
        return view("admin.program.requests.replacement-list", compact("invites", "sidebarMenu", "sidebarGroup"));
    }


    public function getReplacementForm($id, Request $request)
    {
        $invite = invite_programs::with('program')
            ->where(['id' => $id, 'is_approved' => null])
            ->whereNotNull('replacement_type')->firstOrFail();
        $program = $invite->program;
        $userId = $invite->requested_by;

        $is_sub = $invite->replacement_type;

        /*   $users = CustomHelper::getProgramUsers($program, $userId ,$is_sub);*/

        // $view = view("components.admin.modals.assign-requested-instructor", compact('users', 'invite'))->render();
        $noteIds = $invite->notes->pluck('program_note_id')->toArray();
        if ($noteIds) {

            $schedule = getClassScheduleHtmladmin($program->userNotesWhioutMakup()->findMany($noteIds));
        } else {
            $schedule = getClassScheduleHtmladmin($program->userNotesWhioutMakup);
        }
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $view = view("components.admin.modals.assignfilter.assign-common-main-requested-instructor", compact('schedule', 'invite', 'program', 'userId', 'is_sub', 'timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getReplacementsubForm($id, Request $request)
    {
        $invite = invite_programs::with('program')
            ->where(['id' => $id, 'is_approved' => null])
            ->whereNotNull('replacement_type')->firstOrFail();
        $program = $invite->program;
        $userId = $invite->requested_by;
        $is_sub = $invite->replacement_type;
        $noteIds = $invite->notes->pluck('program_note_id')->toArray();

        if ($noteIds) {
            $schedule = getClassScheduleHtmladmin($program->userNotesWhioutMakup()->findMany($noteIds));
        } else {
            $schedule = getClassScheduleHtmladmin($program->userNotesWhioutMakup);
        }

        $classDate = $program->userNotes()->whereIn('id', $noteIds)->value('class_date');
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $toDate = \Carbon\Carbon::parse($classDate)->timezone($timezone);
        $maxClassDate = $classDate ? $toDate->format('m/d/Y') : null;

        $view = view("components.admin.modals.assignfilter.assign-common-sub-requested-instructor", compact('schedule', 'invite', 'program', 'userId', 'is_sub', 'noteIds', 'timezone', 'maxClassDate'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function view($id, Request $request)
    {
        $invite = invite_programs::with('notes.programNote')->findOrFail($id);
        $notes = $invite->notes;

        $view = view("components.admin.modals.view-invite-classes", compact('notes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    private function jsonResponse($data, $status = true, $reload = true)
    {

        return response()->json(array_merge([
            "status" => $status,
            "reload" => $reload,
        ], $data), 200);
    }

    public function allrequests(Request $request)
    {

        $sidebarGroup = 'requestMenu';

        $sidebarMenu = 'program-invite-request';
        $status = null;
        if ($request->has('status')) {
            $status = $request->filled('status') ? $request->status : null;
        }
        if ($request->ajax()) {
            $query = invite_programs::with('program', 'user', 'program.school', 'program.schedules');
            $adminSession = session()->get('Adminnewlogin');
            $adminType = $adminSession['type'];

            if ($adminType != '1') {
                $whereInIds = getAdminUserProgramIds();
                if (!empty($whereInIds)) {
                    $query->whereIn('program_id', $whereInIds);
                }
            }

            $params = DataTableHelper::getParams($request);

            $query->where(['status' => 1]);
            $query->where(['program_id' => $request->program_id]);
            $query->whereNotNull('type');
            // if (is_null($status)) {

            // $query->where('deadline', '>=', now()->toDateTimeString());
            // }


            // $query->where('is_approved', $status ?? null);
            $query->where('is_auto_invite', 1);


            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            $query->where(function ($que) use ($searchValue) {
                $que->whereHas('program', function ($query) use ($searchValue) {
                    $query->where('name', 'LIKE', "%{$searchValue}%");
                })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    });
            });


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {
                if (is_null($row->is_approved)) {

                    $action =  $this->generateActionButtons($row,[]);
                } else {
                    if ($row->is_approved == 1) {
                        $action = 'Accepted';
                    }
                    if ($row->is_approved == 2) {
                        $action = 'Declined';
                    }
                    if ($row->is_approved == 0) {
                        $action = 'Declined';
                    }
                }
                $viewUser = $viewProgram = $program_type = $delivery_type = $start_date = $end_date = "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a href='{$viewProgramRoute}'>{$row->program->name}</a>";
                    $program_type = $row->program->program_type;
                    $delivery_type = $row->program->delivery_type;
                    $start_date = date('m-d-Y', strtotime($row->program->start_date));
                    $end_date = date('m-d-Y', strtotime($row->program->end_date));
                }
                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a href='{$viewUserRoute}'>{$userName}</a>";
                }

                $data[] = [
                    "id" => $row->id,
                    "name" => $viewProgram,
                    "deadline" => !is_null(@$row->deadline) ? date('m-d-Y h:i A', strtotime(@$row->deadline)) : '',
                    "user_id" => $viewUser,
                    "type" => getInstructorType($row->type ?? $row->admin_type ?? $row->replacement_type),

                    "program_type" => $program_type,
                    "delivery_type" => $delivery_type,
                    "start_date" => $start_date,
                    "end_date" => $end_date,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }


    public function cancelinviterequest($id, Request $request)
    {
        $invite = invite_programs::with('program.school','user')->findOrFail($id);
        $is_approved = $request->is_approved;
        $message = $is_approved == 1 ? 'Accepted' : 'Cancelled';
        $status = 0;
        $invite->status = $status;
        $invite->cancelled_by = 1;
        $invite->save();

        $program  = $invite->program;
        $delivery_type = $program->delivery_type;
        $school = $program->school;
        $schoolName = $school->full_name;

        $subject = "Invite Cancelled";
        $instructorType = getInstructorType($invite->admin_type);

        $notificationContent = Notification_content::where("signature", "invite-cancelled")->first();
        $notificationTemplate = str_replace(['{{instructorType}}','{{delivery_type}}','{{schoolName}}'], [$instructorType,$delivery_type,$schoolName], @$notificationContent->content??'');

        $type = 'invite_cancelled';

        $template = EmailTemplate::find(26);
        $user = $invite->user;
        if ($user) {
            NotificationHelper::sendProgramNotification($user, $template, $invite->program_id, $schoolName, $notificationTemplate, $subject, $type);
        }

        return response()->json(['status' => true, 'reload' => true, 'message' => $message . " successfully"]);
    }

    public function storeReplacementForm($id, Request $request)
    {

        if (!$request->filled('instructor')) {

            return $this->jsonResponse(["message" => "Please select instructor"], false, false);
        }



        $request->validate(
            [
                'instructor' => 'required',
                'deadline' => 'required_unless:ins_type,standBy',
            ],
            [
                'instructor.required' => 'Instructor field is required',
                'deadline.required_unless' => 'Deadline field is required',
            ]
        );

        /**
         * 1 == main
         * 0 == sub
         */

        $user_id = $request->instructor;
        $obj = invite_programs::where(['id' => $id, 'is_approved' => null, 'user_id' => null])->firstOrFail();

        //

        if(invite_programs::where(['parent_id'=>$obj->id,'user_id'=>$user_id])->first() != null){
            return response()->json(['status' => false, 'message' => "Instructor Already Invited", 'reload' => false]);
        }

        $assign_type = $request->assign_type;

        $replacement_start_date = $obj->replacement_start_date;

        $program_id = $obj->program_id;
        $program = $obj->program;

        $school_name = @$program->school->full_name ?? '';






        $objmain = new  invite_programs();
        $noteIds = $obj->notes->pluck('program_note_id')->toArray();
        $programNoteQuery1 = ProgramNote::whereIn('id', $noteIds)->where('program_id', $program_id);

        if ($request->standBy == 'standBy') {
            $obj->has_requested = 2;
            $obj->user_id = $user_id;
            if ($obj->notes->isNotEmpty()) {


                if (!empty($replacement_start_date)) {
                    $currentdate = $replacement_start_date->format('Y-m-d');
                } else {
                    $currentdate = now()->toDateString();
                }
                $noteIds = $obj->notes->pluck('program_note_id')->toArray();

                if ($assign_type == 1) {

                    $programNoteQuery1 = $programNoteQuery1->where('class_date', '>=', $currentdate)
                        ->whereNull(['note', 'status', 'sub_user_id']);
                    $programNoteQuery1->update(['user_id' => $user_id]);
                } else if ($assign_type == 0) {

                    $programNoteQuery1 = $programNoteQuery1
                        ->where('class_date', '>=', $currentdate)
                        ->whereNull(['note', 'status']);
                    $programNoteQuery1
                        ->update(['sub_user_id' => $user_id, 'user_sub_requested' => 0]);
                }
            } elseif (!empty($replacement_start_date)) {

                if ($assign_type == 1) {

                    $programNoteQuery1 = $programNoteQuery1
                        ->where('class_date', '>=', $replacement_start_date->format('Y-m-d'))
                        ->whereNull(['note', 'status', 'sub_user_id']);
                    $programNoteQuery1
                        ->update(['user_id' => $user_id]);


                } else if ($assign_type == 0) {

                    $programNoteQuery = InviteProgramNote::where('invite_program_id', $obj->id);
                    $program_note_ids = $programNoteQuery->pluck('program_note_id')->toArray();


                    $programNoteQuery1 = $programNoteQuery1
                        ->when(!empty($program_note_ids), function ($qry) use ($program_note_ids) {
                            $qry->whereIn('id', $program_note_ids);
                        })
                        ->where('class_date', '>=', $replacement_start_date->format('Y-m-d'))
                        ->whereNull(['note', 'status']);
                    $programNoteQuery1
                        ->update(['sub_user_id' => $user_id, 'user_sub_requested' => 0]);
                }
            }

            $objmain->status = 1;
            $obj->admin_type = $obj->replacement_type;

            invite_programs::where('id', $obj->parent_id)->update(['has_requested' => 2]);
        } else {

            $classes = ($programNoteQuery1->pluck('class_date')->toArray());


            if ($assign_type == "1") {
                createProgramCommanNotification($user_id, $request->deadline, $program, '9', 'user', 'user', $school_name, $classes);
            } elseif ($assign_type == "0") {
                createProgramCommanNotification($user_id, $request->deadline, $program, '18', 'user', 'user', $school_name, $classes);
            }
        }

        if ($assign_type == "1" || $assign_type == "0") {

            $objmain->user_id = $user_id;
            $objmain->replacement_type = $assign_type;
            $objmain->program_id = $program_id;
            $objmain->replacement_start_date = $replacement_start_date;
            $objmain->deadline = $request->deadline;
            $objmain->is_approved = 1;
            $objmain->parent_id = $obj->id;
            $objmain->admin_type = $assign_type;
            $objmain->is_sub_only = !$assign_type;
            $objmain->type = $assign_type;
            $objmain->save();
            $obj->save();


            if ($assign_type == "0") {
                $programNoteQuery = InviteProgramNote::where('invite_program_id', $obj->id);
                $program_note_ids = $programNoteQuery->pluck('program_note_id')->toArray();
                foreach ($program_note_ids as $program_note_id) {

                    $inviteProgramNote = new InviteProgramNote();
                    $inviteProgramNote->invite_program_id = $objmain->id;
                    $inviteProgramNote->program_note_id = $program_note_id;
                    $inviteProgramNote->save();
                }
            }
        }

        return response()->json(['status' => true, 'message' => "Instructor invited successfully", 'reload' => true]);
    }

    public function export(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Request'.time().'.xlsx';
            return Excel::download(new ExportRequest($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}
