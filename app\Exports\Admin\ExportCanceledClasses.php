<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{AvailabilityModel, ProgramNote, Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportCanceledClasses implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->searchInput;
    }

    public function collection()
    {
        $classesQry = ProgramNote::with(['user', 'program'])
        ->whereNotNull(['program_notes.status'])
        ->where('program_notes.status',0);
        $this->applyFilters($classesQry);

        return $classesQry->orderBy('updated_at', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = ['id', 'class_date','class_time', 'cancel_date_time', 'makeup_class_date', 'makeup_class_time','cancellation_reason'];

        if (!empty($this->requestFilters)) {
            $query->where(function ($q) use ($filters) {
                foreach ($filters as $filter) {
                    switch ($filter) {
                        case 'id':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('program_id', $this->requestFilters);
                            }
                            break;

                        case 'class_date':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('class_date', $this->requestFilters);
                            }
                            break;

                        case 'class_time':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('start_time', $this->requestFilters)->orWhere('end_time', $this->requestFilters);
                            }
                            break;

                        case 'cancel_date_time':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('cancelled_time', $this->requestFilters)->orWhere('cancelled_date', $this->requestFilters);
                            }
                            break;

                        // case 'makeup_class_date':
                        //     if (!empty($this->requestFilters)) {
                        //         $q->orWhereHas('makupClass', function ($subQuery) {
                        //             $subQuery->where('class_date', $this->requestFilters);
                        //         });
                        //     }
                        //     break;

                        // case 'makeup_class_time':
                        //     if (!empty($this->requestFilters)) {
                        //         $q->orWhereHas('makupClass', function ($subQuery) {
                        //             $subQuery->orWhere('start_time', $this->requestFilters)->orWhere('end_time', $this->requestFilters);
                        //         });
                        //     }
                        //     break;

                        case 'cancellation_reason':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('class_date', 'LIKE', '%' . $this->requestFilters . '%');
                            }
                            break;
                    }
                }
            });
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Class Date',
            'Class Time',
            'Cancellation Date Time',
            'Marked Complete TimeStamp',
            'MakeUp  Class Date',
            'MakeUp Class Time',
            'Cancellation Reason',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        $makeUpClass = $row->makupClass();
        return [
            $row->program->id ? $row->program->id : 'NIL',
            $row->class_date ? date('m-d-Y', strtotime($row->class_date)) : '',
            $row->start_time ? date('h:i A', strtotime($row->start_time)).'-'.date('h:i A', strtotime($row->end_time)) : '',
            $row->cancelled_time ? date('m-d-Y', strtotime($row->cancelled_date)).' '.date('h:i A', strtotime($row->cancelled_time)) : '',
            $makeUpClass ? date('h:i A', strtotime($makeUpClass->start_time)).' '.date('h:i A', strtotime($makeUpClass->end_time)) : '',
            $row->cancellation_reason ? $row->cancellation_reason : '',

        ];
    }
}
