<?php

namespace App\Console\Commands\Reminders;

use App\User;
use Illuminate\Console\Command;

class TomorrowInstructorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:tomorrow-instructors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminders to instructors teaching the next day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = $this->signature;
        $class_date = now()->addDay()->format('m/d/Y');
        $users = User::active()
            ->where("type", "=", "5")
            ->where("app_notification", "=", "1")
            ->where("profile_status", "=", "12")
            ->withCount(['tomorrowClasses', 'tomorrowSubClasses'])
            ->where(function ($query) {
                $query->whereHas('tomorrowClasses')
                    ->orWhereHas('tomorrowSubClasses');
                })
                ->get();

        // Merge counts into a single array
        $result = $users->mapWithKeys(function ($user) {
            return [
                $user->id => ($user->tomorrow_classes_count + $user->tomorrow_sub_classes_count),
            ];
        })->toArray();

        if (!empty($result)) {

            foreach ($result as $id => $class_count) {
                tomInstructorRemNotify($id, $class_count, $class_date, $signature, "user", "user");
            }
        }
    }
}
