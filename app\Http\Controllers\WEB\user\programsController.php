<?php
namespace App\Http\Controllers\WEB\User;
use App\Http\Controllers\Controller;
use Session;

use DB;
use App\Http\Requests;
use App\User;
use App\UserFirstStepModel;
use App\UserSecondStepModel;
use App\StateModel;
use App\UserSubjectsModel;
use App\UserThirdStepModel;
use App\UserFourthStepModel;
use App\GradeLevelModel;
use App\Classes;
use App\Subject;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\QuestionsModel;
use App\EducationListModel;
use App\AvailabilityModel;
use App\AvailabilityRangeModel;
use App\AvailabilityDayTimeSlotsModel;
use Validator;
use View;
use URL;
use DateTime;
use Auth;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
DB::enableQueryLog();

class programs<PERSON><PERSON>roller extends Controller
{

public function index()
    {
        if(Auth::user()->profile_status==12){
        return view('web.user.program.myprogram');
        }
    }

    public function newprogramalerts()
    {
        if(Auth::user()->profile_status==12){
        return view('web.user.program.newprogramalerts');
       }
    }

    public function programdetail()
    {
        if(Auth::user()->profile_status==12){
        return view('web.user.program.programdetail');
       }
    }

    public function teachingpreferences()
    {

        $data['id']=Auth::user()->id;
        $data['state']=StateModel::where(['country_id'=>'239'])->get();
        $data['grade']=GradeLevelModel::get();
        $data['subject']=Subject::get();
        $data['question']=QuestionsModel::get();
        $data['class']=Classes::get();
        $id=Auth::user()->id;
        $user=User::where('id',$id)->first();
        $data['first']=UserFirstStepModel::where(['user_id'=>$id])->first();
        $data['second']=UserSecondStepModel::where(['user_id'=>$id])->first();
        $data['third']=UserThirdStepModel::where(['user_id'=>$id])->first();
        $data['four']=UserFourthStepModel::where(['user_id'=>$id])->first();
        $data['intro']=AssessmentsModel::where(['user_id'=>$id,'type'=>'introduction'])->first();
        $data['teachings']=AssessmentsModel::where(['user_id'=>$id,'type'=>'teaching'])->orderBy('id', 'DESC')->get();
        $data['classroom']=AssessmentsModel::where(['user_id'=>$id,'type'=>'classroom'])->get();
        
        $data['five']=AssessmentsModel::where(['user_id'=>$id])->first();
        $data['quiz']=UserQuizModel::where(['user_id'=>$id])->first();
        $data['education_list']=EducationListModel::get();
        return view('web.user.preferences.teachingpreferences')->with($data);
    }

    public function myavailability()
    {
        $data['availability']=AvailabilityModel::where(['user_id'=>Auth::user()->id])->first();
        return view('web.user.preferences.myavailability')->with($data);
    }

    public function payments()
    {
        return view('web.user.payments.payments');
    }


    public function administrativeinformation()
    {
        return view('web.user.settings.administrativeinformation');
    }


    public function profileinformation()
    {
        return view('web.user.settings.profileinformation');
    }

    public function notificationssettings()
    {
      
        return view('web.user.settings.notificationssettings');
    }

    
    public function passwordmanagement()
    {
        return view('web.user.settings.passwordmanagement');
    }


    public function contract()
    {
        return view('web.user.settings.contract');
    }

    public function status()
    {
        return view('web.user.settings.status');
    }

    public function message()
    {
        return view('web.user.message.message');
    }

    public function notification()
    {
        return view('web.user.notification.notification');
    }

    public function faq()
    {
        return view('web.user.faq.faq');
    }

  
    public function submitAvailability(Request $request){
      


        $validator = Validator::make($request->all(), [
            'teach_minimum' => 'required',
            'teach_maximum' => 'required',
            // 'teach_online_timezone' => 'required',
          

        ]);

        if ($validator->fails()) {
            return response()
                ->json([
                    'success' => false, 'error' => "Please enter valid details",
                    'message' => $validator->errors(),
                ], 400);
        }
        $data = $request->except([
            '_token',
            'from_date',
            'to_date',
            'day',
            'from_time',
            'to_time',  
            'teach_in_person_location_same',
            'teach_in_person_timezone_same',
            'teach_online_timezone'
        ]); 
     
        $data['teach_in_person_timezone']=$request->teach_in_person_timezone_same;
        $data['teach_in_person_location']=$request->teach_in_person_location_same;

        $data['user_id']=Auth::user()->id;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        $first=AvailabilityModel::where(['user_id'=>Auth::user()->id])->first();
        if(!empty($first)){
                AvailabilityModel::where(['user_id'=>Auth::user()->id])->delete();
        }
        $result=AvailabilityModel::insertGetId($data);
        if($result) { 
       
            $availabilityrangearray=[];
            $availabilityrangearrayy=[];
            $tdated=$request->to_date;
            if($fdate=$request->from_date){
                if(!empty(array_filter($fdate['online']))){
                    foreach($fdate['online'] as $key=>$fdates){
                       
                        $availabilityrangearray[]=['availability_id'=>$result,'type'=>'online','from_date'=>$fdates,'to_date'=>$tdated['online'][$key],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
                        

                    }
                }
      
                if(!empty(array_filter($fdate['inperson']))){
                    foreach($fdate['inperson'] as $key=>$infdates){
                       
                        $availabilityrangearrayy[]=['availability_id'=>$result,'type'=>'inperson','from_date'=>$infdates,'to_date'=>$tdated['inperson'][$key],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
                    }
                }
    
            }
          
            $rangeData=array_merge($availabilityrangearray,$availabilityrangearrayy);
      
            $rangeid=AvailabilityRangeModel::insert($rangeData);
            $day=$request->day;   
            $from_time=$request->from_time;
            $to_time=$request->to_time;

            $teach_in_person_location=$request->teach_in_person_location;
            $teach_in_person_timezone=$request->teach_in_person_timezone;
            $teach_online_timezone=$request->teach_online_timezone;



            $availabilitytimesloatrray=[];
            $availabilitytimesloatrrayy=[];
           
         
            if($day=$request->day){
                $arr12 = array_values(array_filter($day['online']));
                $arr123=array_combine(range(1, count($arr12)), $arr12);
                if(count($arr123)>0){
                    $fo1 = array_values(array_filter($from_time['online']));
                    $fo12=array_combine(range(1, count($fo1)), $fo1);
        
                    $to1 = array_values(array_filter($to_time['online']));
                    $to12=array_combine(range(1, count($to1)), $to1);
                   
                    foreach($arr123 as $key=>$ondayrow){
                    
                        foreach(array_filter($ondayrow) as $keys=> $rowonlinedata){
                            
                           
                            $availabilitytimesloatrray[]=['user_id'=>Auth::user()->id,'position'=>$key,'type'=>'online','day'=>$rowonlinedata,'from_time'=>$fo12[$key][$keys],'to_time'=>$to12[$key][$keys],'location'=>null,'timezone'=>$teach_online_timezone[$key][$keys],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
                           
                            
                            }
                       
                    }
                }
                $arr1 = array_values(array_filter($day['inperson']));
                $arr=array_combine(range(1, count($arr1)), $arr1);

               

                if(count($arr)>0){
                    $fi1 = array_values(array_filter($from_time['inperson']));
                    $fi12=array_combine(range(1, count($fi1)), $fi1);
    
                    $ti1 = array_values(array_filter($to_time['inperson']));
                    $ti12=array_combine(range(1, count($ti1)), $ti1);
                  
                    foreach($arr as $keyy=>$indayrow){
                        foreach(array_filter($indayrow) as $keyys=> $rowinpersondata){
                        $availabilitytimesloatrrayy[]=['user_id'=>Auth::user()->id,'position'=>$keyy,'type'=>'inperson','day'=>$rowinpersondata,'from_time'=>$fi12[$keyy][$keyys],'to_time'=> $ti12[$keyy][$keyys],'location'=>$teach_in_person_location[$keyy][$keyys],'timezone'=>$teach_in_person_timezone[$keyy][$keyys],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
                      
                    } 
                    }
                }
    
            }
        
            $rangeDatas=array_merge($availabilitytimesloatrray,$availabilitytimesloatrrayy);
          
            $already= AvailabilityDayTimeSlotsModel::where(['user_id'=>Auth::user()->id])->get();
            if(count($already)>0){
                AvailabilityDayTimeSlotsModel::where(['user_id'=>Auth::user()->id])->delete();
            }
            AvailabilityDayTimeSlotsModel::insert($rangeDatas);
            return response()->json(['success' => true, 'message' => 'Submitted successfully']);
        }else{
            return response()->json(['success' => false, 'error' => 'Something went wrong']);
        }
    }
  
    public function getLocation(Request $request){
        $text=$request->text;
        $details=str_replace(' ','+',$text);
        $data=location($details);
       
    }


    
    
}