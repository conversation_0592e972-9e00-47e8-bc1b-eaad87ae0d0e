@extends('school-marketplace.layouts.masterDashboard')

@section('content')

@include('school-marketplace.layouts.instructor-public-profile')


<style>
    .search_bar_budget {
        border-radius: 33px !important;
        padding-left: 33px;
    }

    .search_bar_icon_budget {
        top: 8px;
        left: 3%;

    }

    .sort_button {
        background-color: white;
        color: #004CBD;
        border: 1px solid #004CBD;
        border-radius: 3px;
    }

    .drop_down_button {
        background-color: white;
        padding: 2% 11%;
        border-radius: 10px;
        border: 1px solid #004CBD;
        color: #004CBD;



    }

    .shadow-box {
        border: 1px solid #ddd;
        /* Light gray border */
        border-radius: 12px;
        /* Rounded corners on all sides */
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        /* Subtle shadow */
    }

    .basic_color {
        color: #004CBD;
    }
</style>
<div class="container mt-4">
    <!-- <div class="d-flex justify-content-between align-items-center"> -->

    <div>
        <a style="font-size:14px; color:#004CBD;">Manage</a> &gt;
        <a style="font-size:14px; color:#004CBD;">Saved Budgets</a>


    </div>
    <div class="d-flex justify-content-between my-5">
        <div>
            <h4 class="basic_color">Saved Budgets</h4>
        </div>
        <div class="btn btn-primary" id="calculate_budget" style="color:white;background-color:#004CBD;border-radius:30px">
            + Calculate a new budget
        </div>

    </div>

    <div class="d-flex justify-content-between">
        <div class="position-relative">
            <input style="font-size:15px;width:100%" class="search_bar_budget" placeholder="Search" />
            <svg class="position-absolute search_bar_icon_budget" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.28734 12.5747C4.53011 12.5747 3.04307 11.9659 1.82623 10.7484C0.609389 9.53096 0.000645367 8.04393 5.1179e-07 6.28734C-0.000644344 4.53075 0.608099 3.04372 1.82623 1.82623C3.04436 0.608743 4.5314 0 6.28734 0C8.04328 0 9.53064 0.608743 10.7494 1.82623C11.9682 3.04372 12.5766 4.53075 12.5747 6.28734C12.5747 6.99668 12.4618 7.66572 12.2361 8.29445C12.0104 8.92319 11.7041 9.47937 11.3172 9.96302L16.734 15.3798C16.9113 15.5571 17 15.7828 17 16.0569C17 16.331 16.9113 16.5567 16.734 16.734C16.5567 16.9113 16.331 17 16.0569 17C15.7828 17 15.5571 16.9113 15.3798 16.734L9.96302 11.3172C9.47937 11.7041 8.92319 12.0104 8.29445 12.2361C7.66572 12.4618 6.99668 12.5747 6.28734 12.5747ZM6.28734 10.6401C7.49644 10.6401 8.52434 10.2171 9.37104 9.37104C10.2177 8.52499 10.6408 7.49709 10.6401 6.28734C10.6395 5.07759 10.2164 4.05001 9.37104 3.20461C8.52563 2.3592 7.49773 1.93586 6.28734 1.93457C5.07695 1.93328 4.04937 2.35662 3.20461 3.20461C2.35985 4.05259 1.9365 5.08017 1.93457 6.28734C1.93263 7.49451 2.35598 8.52241 3.20461 9.37104C4.05324 10.2197 5.08082 10.6427 6.28734 10.6401Z" fill="black" />
            </svg>

        </div>
        <div>

            <div class="dropdown me-3">
                <button class="dropdown-toggle drop_down_button" type="button"
                    id="dropdownMenuButton" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    Sort <svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0.944448 12.1666C0.676855 12.1666 0.452707 12.0759 0.272003 11.8946C0.0912997 11.7133 0.000632886 11.4891 3.2567e-06 11.2221C-0.000626373 10.9552 0.0900404 10.731 0.272003 10.5497C0.453966 10.3684 0.678114 10.2777 0.944448 10.2777H4.72223C4.98982 10.2777 5.21428 10.3684 5.39561 10.5497C5.57695 10.731 5.6673 10.9552 5.66667 11.2221C5.66604 11.4891 5.57537 11.7136 5.39467 11.8955C5.21397 12.0775 4.98982 12.1678 4.72223 12.1666H0.944448ZM0.944448 7.44436C0.676855 7.44436 0.452707 7.3537 0.272003 7.17236C0.0912997 6.99103 0.000632886 6.76688 3.2567e-06 6.49992C-0.000626373 6.23296 0.0900404 6.00881 0.272003 5.82747C0.453966 5.64614 0.678114 5.55547 0.944448 5.55547H10.3889C10.6565 5.55547 10.8809 5.64614 11.0623 5.82747C11.2436 6.00881 11.334 6.23296 11.3333 6.49992C11.3327 6.76688 11.242 6.99134 11.0613 7.17331C10.8806 7.35527 10.6565 7.44562 10.3889 7.44436H0.944448ZM0.944448 2.72214C0.676855 2.72214 0.452707 2.63147 0.272003 2.45014C0.0912997 2.26881 0.000632886 2.04466 3.2567e-06 1.7777C-0.000626373 1.51073 0.0900404 1.28659 0.272003 1.10525C0.453966 0.923919 0.678114 0.833252 0.944448 0.833252H16.0556C16.3232 0.833252 16.5476 0.923919 16.7289 1.10525C16.9103 1.28659 17.0006 1.51073 17 1.7777C16.9994 2.04466 16.9087 2.26912 16.728 2.45109C16.5473 2.63305 16.3232 2.7234 16.0556 2.72214H0.944448Z" fill="#004CBD" />
                    </svg>

                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li><a class="dropdown-item" href="#">Budget: Highest to Lowest</a>
                        <hr style="margin:0rem 0rem!important">
                    </li>
                    <li><a class="dropdown-item" href="#">Budget: Lowest to Highest</a>
                        <hr style="margin:0rem 0rem!important">
                    </li>
                    <li><a class="dropdown-item" href="#">Subject A–Z</a>
                        <hr style="margin:0rem 0rem!important">
                    </li>
                    <li><a class="dropdown-item" href="#">Subject Z-A</a>
                        <hr style="margin:0rem 0rem!important">
                    </li>
                    <li><a class="dropdown-item" href="#">Newest</a>
                        <hr style="margin:0rem 0rem!important">
                    </li>
                    <li><a class="dropdown-item" href="#">Oldest</a>

                    </li>


                </ul>
            </div>


        </div>
    </div>
   
<x-all_budget.all_budget_screen :subjectArea="$subjectArea" />

</div>






</div>


@endsection