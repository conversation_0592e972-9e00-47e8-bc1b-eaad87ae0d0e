<?php
    $notifyCount = 0;
?>
<?php if(auth()->guard()->check()): ?>
    <?php
        $notifyCount = auth()->user()->unreadNotifications();
    ?>
    <style>
        .header-notify-badge {
            bottom: 14px;
            position: relative;
            background-color: #FF7F00;
            color: white;
            padding: 2px 6px;
            border-radius: 50%;
            font-size: 12px;
        }

        .header-notify-mobile-badge {
            bottom: 14px;
            position: relative;
            background-color: #FF7F00;
            color: white;
            padding: 1px 4px;
            border-radius: 50%;
            font-size: 10px;
        }
    </style>
<?php endif; ?>
<!-- headar section start -->
<header style="<?php if(Auth::check() &&  Auth::user()->type==5 && request()->segment(1) !='schools'): ?> <?php echo e(''); ?> <?php else: ?> <?php echo e('display:none'); ?> <?php endif; ?>">
    <div class="headerarea headerarea__2  header__sticky header__area">
        <div class="container desktop__menu__wrapper">
            <div class="row">
                <div class="col-xl-2 col-lg-2 col-md-6">
                    <div class="headerarea__left">
                        <div class="headerarea__left__logo">

                            <a href="#"><img loading="lazy" src="<?php echo e(asset('website/img/logo/t-logo.png')); ?>"
                                    alt="logo" style="    height: 89px;"></a>
                        </div>

                    </div>
                </div>
                <div class="col-xl-8 col-lg-7 main_menu_wrap">
                    <div class="headerarea__main__menu">
                        <nav>
                            <ul class="text-center">

                                <?php if(request()->segment(1) == 'onboarding-step' ||
                                        request()->segment(1) == 'onboarding-re-step' ||
                                        request()->segment(1) == 'verify'): ?>
                                <?php else: ?>
                                    <?php if(Auth::check()): ?>
                                        <?php if(Auth::user()->profile_status == 12): ?>
                                            <li class="mega__menu position-static">
                                                <a class="headerarea__has__dropdown"
                                                    href="<?php echo e(url('/web-dashboard')); ?>">Home</a>
                                            </li>

                                            <?php if(Auth::user()->profile_status == 4 ||
                                                    Auth::user()->profile_status == 8 ||
                                                    Auth::user()->profile_status == 12 ||
                                                    Auth::user()->profile_status == 14): ?>
                                                <li class="mega__menu position-static">
                                                    <a class="headerarea__has__dropdown"
                                                        href="<?php echo e(url('/notifications')); ?>">Notifications

                                                        <?php if($notifyCount>0): ?>
                                                        <span class="header-notify-badge"><?php echo e($notifyCount); ?></span>
                                                        <?php endif; ?>

                                                        </a>
                                                </li>
                                            <?php else: ?>
                                                <li class="mega__menu position-static">
                                                    <a class="headerarea__has__dropdown"
                                                        href="<?php echo e(url('/web-dashboard')); ?>">Notifications

                                                        <?php if($notifyCount>0): ?>
                                                        <span class="header-notify-badge"><?php echo e($notifyCount); ?></span>
                                                        <?php endif; ?>

                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <?php if(Auth::user()->profile_status == 4 ||
                                                    Auth::user()->profile_status == 8 ||
                                                    Auth::user()->profile_status == 12 ||
                                                    Auth::user()->profile_status == 14): ?>
                                                <li class="mega__menu position-static">
                                                    <a class="headerarea__has__dropdown"
                                                        href="<?php echo e(url('/messages')); ?>">Messages</a>
                                                </li>
                                            <?php else: ?>
                                                <li class="mega__menu position-static">
                                                    <a class="headerarea__has__dropdown"
                                                        href="<?php echo e(url('/web-dashboard')); ?>">Messages</a>
                                                </li>
                                            <?php endif; ?>


                                            

                                            <li class="mega__menu position-static">
                                                <a class="headerarea__has__dropdown"
                                                    href="<?php echo e(url('/teacher-faq')); ?>">FAQ</a>
                                            </li>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <!-- <li class="mega__menu position-static">
                                            <a class="headerarea__has__dropdown" href="<?php echo e(url('/')); ?>">Home</a>
                                        </li>
                                        <li class="mega__menu position-static">
                                            <a class="headerarea__has__dropdown" href="javascript:void(0)">Courses</a>
                                        </li>
                                        <li class="mega__menu position-static">
                                            <a class="headerarea__has__dropdown" href="javascript:void(0)">About us</a>
                                        </li>
                                        <li class="mega__menu position-static">
                                            <a class="headerarea__has__dropdown" href="javascript:void(0)">Contact</a>
                                        </li> -->
                                    <?php endif; ?>

                                    <!-- <li class="mega__menu position-static">
                                            <a class="headerarea__has__dropdown"  href="<?php echo e(url('/faq')); ?>">FAQ</a>
                                        </li> -->

                                <?php endif; ?>


                            </ul>
                        </nav>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-3 col-md-6">
                    <div class="headerarea__right">
                        <!-- <div class="headerarea__button login">
                                    <a href="<?php echo e(url('/sign-in')); ?>">Login</a>
                                </div> -->

                        <?php if(Auth::check()): ?>

                            <!-- <div class="headerarea__button Meeting">
                                    <a href="javascript:void(0);">Hi, <?php if(strlen(Auth::user()->first_name) > 10): ?>
<?php echo substr(Auth::user()->first_name, 0, 5); ?>..
<?php else: ?>
<?php echo e(Auth::user()->first_name); ?>

<?php endif; ?>
                                        <span class="iser-dropdown">
                                            <svg width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 1L7 7L13 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>

                                        </span></a>
                                </div> -->
                            <!-- <div class="headerarea__button login">
                                    <a href="<?php echo e(url('/logout')); ?>">Logout</a>
                                </div> -->
                            <div class="dropdown login-drop">
                                <button class="btn btn-secondary dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                    Hi,<?php if(strlen(Auth::user()->first_name) > 10): ?>
                                        <?php echo substr(Auth::user()->first_name, 0, 5); ?>..
                                    <?php else: ?>
                                        <?php echo e(Auth::user()->first_name); ?>

                                    <?php endif; ?>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if(Auth::user()->profile_status == 4 ||
                                            Auth::user()->profile_status == 12 ||
                                            Auth::user()->profile_status == 14 ||
                                            Auth::user()->profile_status == 8): ?>
                                        <!-- <li><a class="dropdown-item" href="<?php echo e(url('public-profile/' . encrypt_str(Auth::user()->id))); ?>">View profile</a></li> -->

                                        <li><a class="dropdown-item" href="<?php echo e(url('account-settings')); ?>">Account
                                                settings</a></li>
                                    <?php endif; ?>
                                    <li><a class="dropdown-item" href="<?php echo e(url('/logout')); ?>">Logout</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <div class="headerarea__button login">
                                <?php if(request()->segment(1) == 'teachers'): ?>
                                    <a class="headerarea__has__dropdown" href="<?php echo e(url('/teacher-faq')); ?>">FAQ</a>
                                <?php elseif(request()->segment(1) == 'teacher-faq'): ?>
                                    <a href="<?php echo e(url('/teachers')); ?>">Create Account</a>
                                <?php else: ?>
                                    <a class="headerarea__has__dropdown" href="<?php echo e(url('/teacher-faq')); ?>">FAQ</a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>


        <div class="container-fluid mob_menu_wrapper">
            <div class="row align-items-center">
                <div class="col-6">
                    <div class="mobile-logo">
                        <a class="logo__dark" href="<?php echo e(url('/')); ?>"><img loading="lazy"
                                src="<?php echo e(asset('website/img/logo/t-logo.png')); ?>" alt="logo"></a>
                    </div>
                </div>
                <div class="col-6">
                    <div class="header-right-wrap">

                        <div class="headerarea__right">

                        </div>

                        <div class="mobile-off-canvas">
                            <a class="mobile-aside-button" href="#"><i class="icofont-navigation-menu"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<!-- headar section start -->


<!-- Mobile Menu Start Here -->
<div class="mobile-off-canvas-active">
    <a class="mobile-aside-close"><i class="icofont  icofont-close-line"></i></a>
    <div class="header-mobile-aside-wrap">
        <?php if(request()->segment(1) == 'onboarding-step' ||
                request()->segment(1) == 'onboarding-re-step' ||
                request()->segment(1) == 'verify'): ?>
        <?php else: ?>
            <?php if(Auth::check()): ?>
                <?php if(Auth::user()->profile_status == 12): ?>
                    <div class="mobile-menu-wrap headerarea">
                        <div class="mobile-navigation">
                            <nav>
                                <ul class="mobile-menu">
                                    <li class="menu-item-has-children"><a href="<?php echo e(url('/web-dashboard')); ?>">Home</a>
                                    </li>
                                    <li class="menu-item-has-children"><a
                                            href="<?php echo e(url('/notifications')); ?>">Notifications

                                            <?php if($notifyCount>0): ?>
                                            <span class="header-notify-mobile-badge"><?php echo e($notifyCount); ?></span>
                                            <?php endif; ?>

                                            </a></li>
                                    <li class="menu-item-has-children"><a href="<?php echo e(url('/messages')); ?>">Messages</a>
                                    </li>
                                    <li class="menu-item-has-children"><a
                                            href="<?php echo e(url('/resources')); ?>">Resources</a>

                                    </li>
                                    <li class="menu-item-has-children"><a href="<?php echo e(url('/teacher-faq')); ?>">FAQ</a>
                                    </li>
                                </ul>
                            </nav>

                        </div>

                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
        <div class="mobile-curr-lang-wrap">


            <?php if(Auth::check()): ?>
                <div class="single-mobile-curr-lang">
                    <a class="mobile-account-active" href="#">My Account <i class="icofont-thin-down"></i></a>
                    <div class="lang-curr-dropdown account-dropdown-active">
                        <div class="dropdown login-drop">
                            <button class="btn btn-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                Hi,<?php if(strlen(Auth::user()->first_name) > 10): ?>
                                    <?php echo substr(Auth::user()->first_name, 0, 5); ?>..
                                <?php else: ?>
                                    <?php echo e(Auth::user()->first_name); ?>

                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu" style="">

                                <?php if(Auth::user()->profile_status == 4 ||
                                        Auth::user()->profile_status == 12 ||
                                        Auth::user()->profile_status == 14 ||
                                        Auth::user()->profile_status == 8): ?>
                                    <!-- <li><a class="dropdown-item" href="<?php echo e(url('public-profile/' . encrypt_str(Auth::user()->id))); ?>">View profile</a></li> -->
                                    <li><a class="dropdown-item" href="<?php echo e(url('account-settings')); ?>">Account
                                            settings</a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="<?php echo e(url('/logout')); ?>">Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<!-- Mobile Menu end Here -->
<?php /**PATH D:\whizara\whizara\resources\views/web/layouts/header.blade.php ENDPATH**/ ?>