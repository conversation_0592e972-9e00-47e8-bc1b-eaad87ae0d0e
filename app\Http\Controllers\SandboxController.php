<?php

namespace App\Http\Controllers;

use App\Models\k12ConnectionClasses;
use App\Models\k12ConnectionClassLogs;
use App\Models\k12ConnectionProctorFeedback;
use App\Models\PlatformSchoolProctor;
use App\Models\PlatformSchoolRequirements;
use App\OnboardingInstructor;
use Illuminate\Http\Request;
use App\User;

class SandboxController extends Controller
{
    public function getHireDetails($school_id, Request $request)
    {
        // Request se "req name" lena
        $reqName = $request->input('req_name');
        
        // Agar req name nahi diya gaya toh error return karein
        if (!$reqName) {
            return response()->json(['error' => 'Request name is required'], 400);
        }

        // "platform_school_requirements" table se req id find karna
        $requirement = PlatformSchoolRequirements::where('requirement_name', $reqName)->where('school_id', $school_id)->first();

        // Agar requirement nahi mili toh error return karein
        if (!$requirement) {
            return response()->json(['error' => 'Requirement not found'], 404);
        }

        // Helper function call karna (ye function aapke helpers me define hoga)
        $hireDetails = getHiresDetails($requirement, $school_id);

        // Response return karna
        return response()->json($hireDetails);
    }

    public function processInvite($school_id, Request $request)
    {
        // Query Parameters lena
        $proposedAmount = $request->proposed_amount;
        $instructEmail = $request->instruct_email;
        $reqName = $request->req_name;
        $schoolId = $school_id;
        $status = $request->status;

        // Validation
        if (!$proposedAmount || !$instructEmail || !$reqName || !$schoolId || !$status) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        $instructor = OnboardingInstructor::where('email', $instructEmail)->first();
        if (!$instructor) {
            return response()->json(['error' => 'User not found'], 500);
        }

        $requirement = PlatformSchoolRequirements::where('requirement_name', $reqName)->where('school_id', $schoolId)->first();
        if (!$requirement) {
            return response()->json(['error' => 'Requirement not found'], 500);
        }

        // Accept Invite Helper Function Call
        $inviteObject = acceptInvite($instructor, $requirement, $schoolId, $status);

        if (!$inviteObject) {
            return response()->json(['error' => 'Invite not found or could not be updated or may be invite is accepted or declined'], 404);
        }
        if (!empty($status) && $status != 'declined') {
            // Send Proposal Route Call
            $proposal = sendProposal($proposedAmount, $requirement, $instructor);

            if (!$proposal) {
                return response()->json(['error' => 'Proposal could not be created or Proposal already sent'], 500);
            }

            // return $proposal;
            return response()->json([
                'message' => 'Proposal sent successfully',
                'proposal' => $proposal,
            ]);
        } else if ($status == 'declined') {
            return response()->json([
                'message' => 'Invitation Declined',
                'instructor' => $instructor,
                'requirement' => $requirement
            ]);
        }
    }

    public function sendProposal(Request $request)
    {
        $proposedAmount = $request->proposed_amount;
        $instructEmail = $request->instruct_email;
        $reqName = $request->req_name;
        $schoolId = $request->school_id;
        if (!empty($request->req_name) && !empty($request->instruct_email)) {
            $instructor = OnboardingInstructor::where('email', $instructEmail)->first();
            if (!$instructor) {
                return response()->json(['error' => 'Instructor not found'], 404);
            }
    
            $requirement = PlatformSchoolRequirements::where('requirement_name', $reqName)->where('school_id', $schoolId)->first();
            if (!$requirement) {
                return response()->json(['error' => 'Requirement not found'], 404);
            }
        }
        
        // Proposal Helper Function Call
        $proposal = sendProposal($proposedAmount, $requirement, $instructor);

        if (!$proposal) {
            return response()->json(['error' => 'Proposal could not be created or Proposal already sent'], 500);
        }

        return response()->json([
            'message' => 'Proposal sent successfully',
            'proposal' => $proposal,
            'instructor' => $proposal->instructor,
            'requirement' => $proposal->requirement
        ]);
    }

    public function acceptOffer(Request $request)
    {
        $status = $request->status;
        $instructEmail = $request->instructor_email;
        $reqName = $request->req_name;
        $schoolId = $request->school_id;

        if (!$status || !$instructEmail || !$reqName || !$schoolId) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        $instructor = OnboardingInstructor::where('email', $instructEmail)->first();
        if (!$instructor) {
            return response()->json(['error' => 'User not found'], 500);
        }

        $requirement = PlatformSchoolRequirements::where('requirement_name', $reqName)->where('school_id', $schoolId)->first();
        if (!$requirement) {
            return response()->json(['error' => 'Requirement not found'], 500);
        }

        $school = User::find($schoolId);
        $offer = offerStatusUpdate($schoolId, $instructor, $requirement, $status, $school);
        if (!$offer) {
            return response()->json(['error' => 'Offer could not be created or Offer is already updated'], 500);
        }
        return response()->json([
            'message' => 'Offer sent successfully',
            'offer' => $offer,
            'instructor' => $instructor,
            'requirement' => $requirement
        ], 200);
    }

    public function educatorFeedback(Request $request)
    {
        try {
            $request->validate([
                'program_id'     => 'required|exists:k12_connection_programs,id',
                'class_id'       => 'required|exists:k12_connection_classes,id',
                'entered_by'     => 'required|exists:new_onboarding_instructor,email',
                'class_rating'   => 'required|integer|min:1|max:5',
                'note'           => 'nullable|string',
            ]);
            $educator = OnboardingInstructor::where('email', $request->entered_by)->first();

            if (!$educator) {
                return response()->json(['error' => 'Educator not found'], 404);
            }

             // Check if program_id and class_id exist in k12ConnectionClasses
             $class = k12ConnectionClasses::where('id', $request->class_id)
             ->where('program_id', $request->program_id)
             ->first();
 
             if (!$class) {
                 return response()->json(['error' => 'Invalid program_id or class_id'], 400);
             } 

            // Check if feedback already exists for this educator and class
            $feedback = k12ConnectionClassLogs::where('class_id', $request->class_id)
                ->where('entered_by', $educator->id)
                ->first();

            if ($feedback) {
                // Update existing feedback
                $feedback->update([
                    'program_id'   => $request->program_id,
                    'class_rating' => $request->class_rating,
                    'note'         => $request->note,
                ]);

                $message = 'Feedback updated successfully';
            } else {
                $feedback = k12ConnectionClassLogs::create([
                    'program_id'   => $request->program_id,
                    'class_id'     => $request->class_id,
                    'entered_by'   => $educator->id,
                    'class_rating' => $request->class_rating,
                    'note'         => $request->note,
                ]);

                $message = 'Feedback submitted successfully';
            }

            $status = is_null($class->proctor_log_id) ? 'under review' : 'completed';

            $class->update([
                'class_log_id' => $feedback->id,
                'status'         => $status
            ]);

            return response()->json(['message' => $message, 'data' => $feedback], $feedback->wasRecentlyCreated ? 201 : 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Something went wrong. Please try again.'], 500);
        }
    }

    public function proctorFeedback(Request $request)
    { 
        try {
            $request->validate([
                'program_id'     => 'required|exists:k12_connection_programs,id',
                'class_id'       => 'required|exists:k12_connection_classes,id',
                'entered_by'     => 'required|exists:platform_school_proctors,email',
                'proctor_rating' => 'required|integer|min:1|max:5',
                'note'           => 'nullable|string',
            ]);

            $proctor = PlatformSchoolProctor::where('email', $request->entered_by)->first();

            if (!$proctor) {
                return response()->json(['error' => 'Proctor not found'], 404);
            }

            // Check if program_id and class_id exist in k12ConnectionClasses
            $class = k12ConnectionClasses::where('id', $request->class_id)
            ->where('program_id', $request->program_id)
            ->first();

            if (!$class) {
                return response()->json(['error' => 'Invalid program_id or class_id'], 400);
            }

            // Check if feedback already exists for this proctor and class
            $feedback = k12ConnectionProctorFeedback::where('class_id', $request->class_id)
                ->where('entered_by', $proctor->id)
                ->first();

            if ($feedback) {
                // Update existing feedback
                $feedback->update([
                    'program_id'     => $request->program_id,
                    'proctor_rating' => $request->proctor_rating,
                    'note'           => $request->note,
                ]);

                $message = 'Feedback updated successfully';
            } else {
                $feedback = k12ConnectionProctorFeedback::create([
                    'program_id'     => $request->program_id,
                    'class_id'       => $request->class_id,
                    'entered_by'     => $proctor->id,
                    'proctor_rating' => $request->proctor_rating,
                    'note'           => $request->note,
                ]);

                $message = 'Feedback submitted successfully';
            }
            
            $status = is_null($class->class_log_id) ? 'under review' : 'completed';
            $class->update([
                'proctor_log_id' => $feedback->id,
                'status'         => $status
            ]);

            return response()->json(['message' => $message, 'data' => $feedback], $feedback->wasRecentlyCreated ? 201 : 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['error' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Something went wrong. Please try again.'], 500);
        }
    }

}