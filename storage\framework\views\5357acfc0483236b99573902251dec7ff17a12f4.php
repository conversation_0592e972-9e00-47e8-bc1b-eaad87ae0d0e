

<?php $__env->startSection('title'); ?> Permission Page | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- MAIN SECTION START -->
<main class="content">
<div class="container-fluid p-0">
<!-- BREADCRUMB START -->
<nav aria-label="breadcrumb">
<ol class="breadcrumb">
<li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary">Dashboard</a></li>
<li class="breadcrumb-item active" aria-current="page">Manage Role</li>
<li class="breadcrumb-item active" aria-current="page">Set Permission</li>

</ol>
</nav>
<!-- BREADCRUMB END -->
<?php $res = get_permission($user_id); ?>

<!-- EDIT PROFILE SECTION START -->
 <form  id="permission_form" enctype='multipart/form-data' >
 <div class="row justify-content-center">
                        <div class="col-lg-10 col-md-9">
                            <div class="card">
                                <!-- <div class="card-header border-bottom">
                                    <h5 class="mb-0">Permissions</h5>
                                </div> -->
                                <div class="card-body">
                                    <?php echo e(csrf_field()); ?>

                                     <input type="hidden" name="userid" id="userid" value="<?php echo e($user_id); ?>">
                                    <h6>Management</h6>
                                    <div class="form-group mb-4">
                                    <ul class="list-group">
                                    <?php if(!empty($modules) && $modules->count()): ?>
                                    <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    
    <li class="list-group-item px-0">
        <div class="row mx-0">
            <div class="col-sm-3"><?php echo e($data->title); ?></div>

            <div class="col-sm">
                <div class="d-flex justify-content-start flex-wrap">
               

                <?php $__currentLoopData = json_decode($data->mod_action); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="form-check mr-3">
                        <label class="form-check-label" for="flexCheckChecked">
                        <?php if(array_key_exists($data->type,$res)): ?>
                          <?php if(in_array($val,json_decode($res[$data->type] ,true))): ?>
                               <input class="form-check-input" type="checkbox" value="<?php echo e($val); ?>" 
                                name="<?php echo e($data->type); ?>[]" checked="checked">
                                <?php else: ?>
                                <input class="form-check-input" type="checkbox" value="<?php echo e($val); ?>"
                                name="<?php echo e($data->type); ?>[]" >
                                <?php endif; ?>
                            <?php else: ?>
                            <input class="form-check-input" type="checkbox" value="<?php echo e($val); ?>" 
                                name="<?php echo e($data->type); ?>[]" >
                            <?php endif; ?>

                            <?php echo e(ucfirst($val)); ?>&nbsp;
                        </label>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                 

                </div>
            </div>
        </div>
    </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
    

   

</ul>
                                    </div>
                                
                                <div class="mb-3" id="permissionform"></div>

                                    <div class="d-flex justify-content-md-end justify-content-between mt-3 pt-3">
                                        <button type="button" class="btn btn-secondary mr-2">Cancel</button>
                                        <button type="Submit" id="submitpermissionbutton" class="btn btn-primary">Submit</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
 </form>
<!-- EDIT PROFILE SECTION END -->
</div>
</main>
<!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/permission-activity/permissionpage.blade.php ENDPATH**/ ?>