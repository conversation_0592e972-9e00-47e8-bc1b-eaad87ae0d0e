<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateWithoutFKChecks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:without-fk';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run migrations with foreign key checks temporarily disabled';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $this->call('migrate', ['--force' => true]);

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->info('Migrations completed with foreign key checks disabled.');
    }
}
