<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolClassBudgetsTable extends Migration
{
    public function up()
    {
        Schema::create('school_class_budgets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('school_id')->constrained('users')->onDelete('cascade');
            $table->string('name')->default('Class Budget');
            $table->enum('requirement_type', ['Class', 'Case Management', 'Speech therapy', 'Other']);
            $table->foreignId('subject_id')->constrained('subjects_v1')->onDelete('cascade');
            $table->enum('educator_profile', ['Credentialed', 'Non-credentialed']);
            $table->boolean('special_education_certification')->default(false);
            $table->enum('delivery_mode', ['Online', 'Hybrid', 'In-person']);
            $table->enum('years_of_experience', ['0-3', '3-6', '6-10', '10+']);
            $table->integer('instructional_days');
            $table->integer('class_duration_hours');
            $table->integer('non_instructional_hours');
            $table->string('language_of_instruction');
            $table->integer('expected_class_size');
            $table->boolean('school_curriculum_provided')->default(false);
            $table->decimal('calculated_budget', 10, 2);
            $table->text('tags')->nullable(); // Using TEXT for JSON-like data
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('school_class_budgets');
    }
}
