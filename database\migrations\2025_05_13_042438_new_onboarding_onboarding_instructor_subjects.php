<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class NewOnboardingOnboardingInstructorSubjects extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('onboarding_instructor_subjects', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('user_id')->nullable();
    $table->foreign('user_id')->references('id')->on('new_onboarding_instructor')->onDelete('cascade');
    
    $table->integer('step_id')->nullable();

    // Fix: Use unsignedBigInteger to match foreign key references
    $table->unsignedBigInteger('subject')->nullable(); // This should be unsignedBigInteger, not string
    $table->foreign('subject')->references('id')->on('subject_area_v1');
    $table->string('proficiency')->nullable();
    $table->string('other')->nullable();
    $table->unsignedBigInteger('sub_subject')->nullable(); // This should also be unsignedBigInteger
    $table->foreign('sub_subject')->references('id')->on('subjects_v1');
    $table->timestamps();
});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
