<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSubjectBudgetV1TableDropColumnsAndAddStateId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            // Drop existing columns
            if (Schema::hasColumn('subject_budget_v1', 'bilingual_inc')) {
                $table->dropColumn('bilingual_inc');
            }
            if (Schema::hasColumn('subject_budget_v1', 'sped_rec_comp')) {
                $table->dropColumn('sped_rec_comp');
            }

            // Add state_id foreign key with comment
            if (!Schema::hasColumn('subject_budget_v1', 'state_id')) {
                $table->unsignedBigInteger('state_id')
                      ->nullable()
                      ->after('subject_id')
                      ->comment('Foreign key referencing budget_states.id');

                $table->foreign('state_id')
                      ->references('id')
                      ->on('budget_states')
                      ->onDelete('cascade');

                $table->index('state_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['state_id']);
            $table->dropIndex(['state_id']);
            $table->dropColumn('state_id');

            // Re-add previously dropped columns
            $table->decimal('bilingual_inc', 8, 2)->nullable();
            $table->decimal('sped_rec_comp', 8, 2)->nullable();
        });
    }
}
