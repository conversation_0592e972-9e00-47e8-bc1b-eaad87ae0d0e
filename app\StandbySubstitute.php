<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class StandbySubstitute extends Model
{
    protected $table = 'standby_substitutes';

    protected $fillable = [
        'program_id','program_note_id', 'user_id', 'status',
    ];

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function programNote()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    
}
