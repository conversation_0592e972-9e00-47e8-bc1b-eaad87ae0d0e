<?php

namespace App\Helpers;

use Illuminate\Database\Eloquent\Builder;

class DataTableHelper
{
    public static function getParams($request)
    {
        return [
            'draw' => $request->draw,
            'row' => $request->start,
            'rowperpage' => $request->length,
            'columnIndex' => $request->input('order.0.column'),
            'columnName' => $request->input('columns.' . $request->input('order.0.column') . '.data'),
            'columnSortOrder' => $request->input('order.0.dir'),
            'searchValue' => $request->input('search.value'),
            'columns' => $request->columns,
        ];
    }

    public static function applySearchFilter(Builder $query, $searchValue, $columns)
    {
        if (!is_array($columns)) {
            // Log or handle the error accordingly
            return;
        }
        $query->where(function ($que) use ($searchValue, $columns) {
            foreach ($columns as $column) {
                if (isset($column['searchable']) && $column['searchable'] === 'true') {
                    $que->orWhere($column['data'], 'LIKE', "%{$searchValue}%");
                }
            }
        });
    }

    public static function applySearchFilterMail(Builder $query, $searchValue, $columns)
    {
        if (!is_array($columns)) {
            // Log or handle the error accordingly
            return;
        }
        $query->where(function ($que) use ($searchValue, $columns) {
            foreach ($columns as $column) {
                if (isset($column['searchable']) && $column['searchable'] === 'true') {
                    if ($column['data'] == 'id' || empty($column['data'])) {
                        $que->orWhere('users.id', 'LIKE', "%{$searchValue}%");
                    }else{
                        $que->orWhere($column['data'], 'LIKE', "%{$searchValue}%");
                    }
                    
                }
                
            }
        });
    }

    public static function applyPagination($query, $row, $rowperpage)
    {
        $count = $query->count();
        if ($row) {
            $query->offset($row);
        }
        $result = $query->take($rowperpage)->get();

        return [$count, $result];
    }

   
    public static function applyCustomPagination($collection, $row, $rowperpage)
    {
        $count = $collection->count();
        
        if ($row && $row < $count) {
            $result = $collection->slice($row)->take($rowperpage)->values();
        } else {
            $result = $collection->take($rowperpage)->values();
        }
    
        return [$count, $result];
    }
    
    public static function generateResponse($draw, $count, $data, $additional_options = [], $chartData = [])
    {
        $response = [
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data,
        ];
        if (!empty($additional_options)) {
            $response = array_merge($response, $additional_options);
        }
        
        if (!empty($chartData)) {
            $response["chartData"] = $chartData;
        }
        
        return response()->json($response);
    }


}
