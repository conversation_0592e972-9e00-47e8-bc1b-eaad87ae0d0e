<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\CourseCategoryModel;
use Hash;
use Mail;

DB::enableQueryLog();

class CourseController extends Controller
{
    /**
     * Display a listing of the Course.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managesubject','view')!=true){
            return redirect("/no-permission");
        }  
        $course = CourseCategoryModel::all();
        return view("admin.course_category.listcourse", compact("course"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view("admin.course_category.addcourse");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $course_category_name = $request->course_category_name;
        if ($course_category_name != "") {
            $where = ["course_category_name" => $course_category_name];
            $alredyExits = CourseCategoryModel::get_single_record($where);
            if ($alredyExits) {
                return response()->json([
                    "success" => false,
                    "message" => "Course category name already exits",
                ]);
            } else {
                $data["course_category_name"] = $request->course_category_name;
                $data["status"] = $request->status;
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");
                $save = CourseCategoryModel::insert($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Course category successfully added",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = decrypt_str($request->id);
        $course_category_name = $request->course_category_name;

        if ($course_category_name != "") {
            $alreadyExits = CourseCategoryModel::where(
                "course_category_name",
                "=",
                $course_category_name
            )
                ->where("id", "!=", $id)
                ->get();
            if (count($alreadyExits)) {
                return response()->json([
                    "success" => false,
                    "message" => "Course category already exits",
                ]);
            } else {
                $data["course_category_name"] = $request->course_category_name;
                $data["status"] = $request->status;
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");
                $save = CourseCategoryModel::where("id", $id)->update($data);
                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Details successfully updated",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    }

    public function statuschange(Request $request)
    {
        $data["status"] = $request->id1;
        $id = $request->id;
        $record = CourseCategoryModel::where("id", $id)->first();
        if ($record) {
            $res = CourseCategoryModel::where("id", $id)->update($data);
            if ($res) {
                return response()->json([
                    "success" => true,
                    "message" => "Status Successfully Changed",
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went worng",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Record not found",
            ]);
        }
    }

    public function deletecoursecategory(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = CourseCategoryModel::where("id", $id)->first();
            if ($record) {
                $res = CourseCategoryModel::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewcoursecategory(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $course_id = $request->id;
        $id = decrypt_str($course_id);
        $where = ["id" => $id];
        $course = CourseCategoryModel::get_all_record($where);
        return view(
            "admin.course_category.editcourse",
            compact("course", "course_id")
        );
    }
}
