<?php

namespace App\Console\Commands\Feedbacks;

use App\EmailTemplate;
use App\Programs;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class FirstMonthCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feedback:first-month';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ask feedback from instructors after first month of an program';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $template = EmailTemplate::find(20);
        $body =  $template->description;


        $today = Carbon::now();
        $thirtyDaysFromToday = $today->copy()->subDays(30);

        // Query to get programs
        $programs = Programs::where('start_date', $thirtyDaysFromToday->toDateString())
            ->where('end_date', '>=', $today->toDateString())
            ->with('mainAssignedUser')
            ->whereHas('mainAssignedUser')
            ->get();

        foreach ($programs as $program) {
            if($program->mainAssignedUser->email_notification==1){
            $full_name = $program->mainAssignedUser->first_name . ' ' . $program->mainAssignedUser->last_name;
            $email = $program->mainAssignedUser->email;

            $body = str_replace('{{ NAME }}', $full_name, $body);
            $body = str_replace('{{ program_name }}', $program->name, $body);

            $subject = $template->subject;
            $data = array('template' => $body);
            Mail::send('template', $data, function (
                $message
            ) use ($email, $subject) {
                $message->to($email)->subject($subject);
            });
        }
    }
    }
}
