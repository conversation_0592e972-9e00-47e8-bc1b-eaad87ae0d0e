<?php
namespace App;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
DB::enableQueryLog();

class CommomModel extends Model {

  public static function get_single_record($table,$data){
      $value=DB::table($table)->where($data)->first();
      return $value;
  }

   public static function get_last_record($table,$orderby){
      $value=DB::table($table)->orderBy($orderby, 'desc')->first();
      return $value;
  }


  public static function get_all_record($table){
      $value=DB::table($table)->paginate(20);
      return $value;
  }

  public static function get_all_recordywhere($table,$data){
      $value=DB::table($table)->where($data)->paginate(20);
      return $value;
  }

public static function get_all_records($table){
      $value=DB::table($table)->get();
      return $value;
  }

   public static function insertData($table,$data){
   
       return DB::table($table)->insert($data);
     
  }

  public static function updateData($table,$data,$where){
   return DB::table($table)
      ->where($where)
      ->update($data);
  }

  public static function deleteData($table,$where){
    DB::table($table)->where($where)->delete();
  }
  public static function get_allrecords($table,$data){
      $value=DB::table($table)->where($data)->get();
      return $value;
  }
}

  ?>