<?php

namespace App\View\Components\Marketplace\School;

use Illuminate\View\Component;

class InstructorsProfile extends Component
{
    public $type;
    public $invitedInstructorIds;
    public $instructor;
    public $requirement;
    public $whizaraContracts;
    public $marketplaceContracts;
    public $buttonText;
    public $buttonColor;
    public $schoolRequirement;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($instructor, $requirement, $whizaraContracts, $marketplaceContracts, $buttonText, $buttonColor, $type=null, $invitedInstructorIds=null, $schoolRequirement=null)
    {
        $this->type = $type;
        $this->invitedInstructorIds = $invitedInstructorIds;
        $this->instructor = $instructor;
        $this->requirement = $requirement;
        $this->whizaraContracts = $whizaraContracts;
        $this->marketplaceContracts = $marketplaceContracts;
        $this->buttonText = $buttonText;
        $this->buttonColor = $buttonColor;
        $this->schoolRequirement=$schoolRequirement;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.marketplace.school.instructors-profile');
    }
}
