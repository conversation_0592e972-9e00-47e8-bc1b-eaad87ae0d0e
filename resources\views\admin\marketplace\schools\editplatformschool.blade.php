@extends('admin.layouts.master')

@section('title') Edit Platform School | Whizara @endsection

@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url('admin/k12connections/manage-platform-schools') }}">{{ __('messages.list_platform_institute') }}</a>
                </li>
                <li class="breadcrumb-item active">Edit Platform School</li>
            </ol>
        </nav>

        <form method="post" enctype="multipart/form-data" id="edit_platform_institute_details">
            <div class="row justify-content-center">
                <input type="hidden" name="user_id" value="{{$user_list[0]->id}}">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="change_profile_img mx-auto mb-4">
                        <div class="avatar-edit">
                            <input type="file" name="profile_upload" class="profileImgUploadBtnd" id="imageUpload" accept=".png,.jpg,.jpeg" />
                            <label for="imageUpload">
                                <span class="btn btn-primary btn-block dbdb">Upload Photo</span>
                            </label>
                        </div>
                        <div class="avatar-preview">
                            <div id="imagePreview" style="background-image: url('{{ old('profile', $user_list[0]->profile ?? default_user_placeholder()) }}')"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-10 col-md-9">
                    <div class="card">
                        <div class="card-header border-bottom"><h5 class="mb-0">{{ __('messages.institute_details') }}</h5></div>
                        <div class="card-body">
                            <input type="hidden" name="latitude" id="latitude" value="{{ old('latitude', $user_list[0]->latitude ?? '') }}">
                            <input type="hidden" name="longitude" id="longitude" value="{{ old('longitude', $user_list[0]->longitude ?? '') }}">

                            <div class="row">
                                <!-- Organization Type -->
                                <div class="col-md-4 form-group">
                                    <label>Organization Type</label>
                                    <select name="organizationtype" id="organizationtype" class="form-control select2">
                                        <option value="">Select</option>
                                        @foreach(['Public School','Charter school','Private school','Independent school','Community Based Organizations'] as $type)
                                            <option value="{{ $type }}" {{ old('organizationtype', $user_list[0]->organizationtype)==$type ? 'selected' : '' }}>{{ $type }}</option>
                                        @endforeach
                                    </select>
                                    <span id="organizationtype_error" class="err"></span>
                                </div>

                                <!-- School Name dynamic search -->
                                <div class="col-md-4 form-group">
                                    <label>School Name</label>
                                    <select id="full_name" name="full_name" class="form-control select2">
                                        @if(old('full_name', $user_list[0]->full_name??false))
                                            <option value="{{ old('full_name', $user_list[0]->full_name) }}" selected>{{ $user_list[0]->full_name }}</option>
                                        @endif
                                    </select>
                                    <input type="hidden" id="nces_cource_code" name="nces_cource_code" value="{{ old('nces_cource_code', $user_list[0]->nces_cource_code ?? '') }}">
                                    <span id="full_name_error" class="err"></span>
                                </div>

                                <!-- Other School -->
                                <div id="other_full_name_section"
                                     class="col-md-4 form-group {{ in_array(old('full_name', $user_list[0]->full_name ?? ''), ['Other']) ? '' : 'd-none' }}">
                                    <label>School Name</label>
                                    <input type="text" id="other_full_name" name="other_full_name" class="form-control" placeholder="Full name" value="{{ old('other_full_name', $user_list[0]->other_full_name ?? '') }}">
                                    <span id="other_full_name_error" class="err"></span>
                                </div>

                                <!-- Work Email -->
                                <div class="col-md-4 form-group">
                                    <label>Work Email</label>
                                    <input type="email" id="email" name="email" class="form-control" placeholder="Work email" value="{{ old('email', $user_list[0]->email ?? '') }}">
                                    <span id="email_error" class="err"></span>
                                </div>

                                <!-- Cust Type (Platform, read-only) -->
                                <div class="col-md-4 form-group">
                                    <label>Customer Type</label>
                                    <input type="text" name="cust_type" class="form-control" value="Platform" readonly>
                                </div>

                                <!-- CBO -->
                                <div class="col-md-4 form-group">
                                    <label>CBO</label>
                                    <select name="cbo" id="cbo" class="form-control select2">
                                        <option value="">Select CBO</option>
                                        @foreach($cbo as $c)
                                            <option value="{{ $c->id }}" {{ old('cbo', $user_list[0]->cbo)==$c->id ? 'selected' : '' }}>
                                                {{ $c->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <span id="cbo_error" class="err"></span>
                                </div>

                                <!-- Grades -->
                                @php $selGrades = explode(',', old('grade', $user_list[0]->grade ?? '')); @endphp
                                <div class="col-md-4 form-group">
                                    <label>Grade Levels</label>
                                    <select name="grade[]" id="grade" class="form-control select2" multiple>
                                        @foreach($gradeLavel as $g)
                                            <option value="{{ $g->id }}" {{ in_array($g->id, $selGrades) ? 'selected' : '' }}>
                                                {{ $g->class_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Website & Org Name & District -->
                                <div class="col-md-4 form-group">
                                    <label>{{ __('messages.website') }}</label>
                                    <input type="text" name="website" id="website" class="form-control"
                                           value="{{ old('website', $user_list[0]->website ?? '') }}">
                                </div>
                                <div class="col-md-4 form-group">
                                    <label>Organization Name</label>
                                    <input type="text" name="organizationname" id="organizationname" class="form-control"
                                           value="{{ old('organizationname', $user_list[0]->organizationname ?? '') }}">
                                </div>
                                <div class="col-md-4 form-group">
                                    <label>District</label>
                                    <select name="district" id="district" class="form-control select2">
                                        <option value="">Select District</option>
                                        @foreach($district as $d)
                                            <option value="{{ $d->id }}" {{ old('district', $user_list[0]->district)==$d->id ? 'selected' : '' }}>
                                                {{ $d->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Contact Info: dynamic rows -->
                                <div class="col-md-12"><label>Contact info</label></div>
                                @foreach($school_contact_info as $k => $info)
                                    <div class="row after-add-more">
                                        <div class="col-md-2">
                                            <input type="text" name="job_title[]" class="form-control"
                                                value="{{ old('job_title.' . $k, $info->job_title) }}" placeholder="Job Title">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" name="first_name[]" class="form-control"
                                                value="{{ old('first_name.' . $k, $info->first_name) }}" placeholder="First name">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" name="last_name[]" class="form-control"
                                                value="{{ old('last_name.' . $k, $info->last_name) }}" placeholder="Last name">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="email" name="cemail[]" class="form-control"
                                                value="{{ old('cemail.' . $k, $info->email) }}" placeholder="Email">
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" name="phone[]" class="form-control"
                                                value="{{ old('phone.' . $k, $info->phone) }}" placeholder="Phone">
                                        </div>
                                        <div class="col-md-1 change">
                                            @if($k == 0)
                                                <a class="btn btn-success add-more">+</a>
                                            @else
                                                <a class="btn btn-danger remove">-</a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Address -->
                                <div class="col-md-12"><label>{{ __('messages.address') }}</label></div>
                                <div class="col-md-12 mb-3">
                                    <textarea name="address" id="address" class="form-control">{{ old('address', $user_list[0]->address ?? '') }}</textarea>
                                </div>

                                <!-- City, State, ZIP, Country -->
                                <div class="col-md-3 form-group"><input type="text" name="city" id="city"
                                    class="form-control" placeholder="City" value="{{ old('city', $user_list[0]->city ?? '') }}"></div>
                                <div class="col-md-3 form-group"><input type="text" name="state" id="state"
                                    class="form-control" placeholder="State" value="{{ old('state', $user_list[0]->state ?? '') }}"></div>
                                <div class="col-md-3 form-group"><input type="text" name="zipcode" id="zipcode"
                                    class="form-control" placeholder="Zipcode" value="{{ old('zipcode', $user_list[0]->zipcode ?? '') }}"></div>
                                <div class="col-md-3 form-group"><input type="text" name="country" id="country"
                                    class="form-control" placeholder="Country" value="{{ old('country', $user_list[0]->country ?? '') }}"></div>

                                <!-- Notes -->
                                <div class="col-md-12 form-group">
                                    <label>Notes</label>
                                    <textarea name="notes" class="form-control">{{ old('notes', $user_list[0]->notes ?? '') }}</textarea>
                                </div>

                                <!-- Buttons -->
                                <div class="col-md-12 d-flex justify-content-end">
                                    <a href="{{ url()->previous() }}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                    <button type="submit" id="updatePlatformInstitute" class="btn btn-primary">{{ __('messages.update') }}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</main>
@endsection

@section('scripts')
<script>
    function add_more() {
        var html = `<div class="row after-add-more" style="margin-left:0 !important;">
                        <div class="col-md-2 form-group">
                            <input type="text" class="form-control" name="job_title[]" id="first_name" placeholder="Job Title">
                            <span id="name_error" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="text" class="form-control" name="first_name[]" id="first_name" placeholder="First name">
                            <span id="name_error" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="text" id="last_name" class="form-control" name="last_name[]" placeholder="{{ __('messages.last_name') }}">
                            <span id="p_last_name_error" class="err"></span>
                        </div>

                        <div class="col-md-3 form-group">
                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                            <span id="email_errors" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="number" class="form-control" name="phone[]" id="phone" placeholder="Phone">
                            <span id="phone_error" class="err"></span>
                        </div>

                        <div class="col-md-1 form-group change">
                            <a class='btn btn-danger remove'>-</a>
                        </div>
                    </div>`;

        $(".after-add-more").last().after(html);
        $(html).find(".change").html("<a class='btn btn-danger remove'>-</a>");
    }
    $("body").on("click",".remove",function(){
        $(this).parents(".after-add-more").remove();
    });
</script>

<script>
    $(document).ready(function () {
        let schoolData = [];
        let debounceTimer = null;

        const $dropdown = $('#full_name');
        const $otherInput = $('#other_full_name');
        const $otherSection = $('#other_full_name_section');
        const $gradeSelect = $('#grade');

        const gradeMap = {
            G_PK_OFFERED: 'Tk',   // Mapping "Pre-K" to "Tk"
            G_KG_OFFERED: 'K',
            G_1_OFFERED: '1',
            G_2_OFFERED: '2',
            G_3_OFFERED: '3',
            G_4_OFFERED: '4',
            G_5_OFFERED: '5',
            G_6_OFFERED: '6',
            G_7_OFFERED: '7',
            G_8_OFFERED: '8',
            G_9_OFFERED: '9',
            G_10_OFFERED: '10',
            G_11_OFFERED: '11',
            G_12_OFFERED: '12'
        };

        function handleSchoolSelection() {
            const selectedName = $dropdown.val();
            if (selectedName.toLowerCase() === 'other') {
                $otherSection.removeClass('d-none');
                $('#nces_cource_code').val('');
                $gradeSelect.val(null).trigger('change');
            } else {
                $otherSection.addClass('d-none');
                const selectedSchool = schoolData.find(s => s.SCH_NAME === selectedName);
                if (selectedSchool) {
                    $('#website').val(selectedSchool.WEBSITE || '');
                    $('#phone').val(selectedSchool.PHONE || '');
                    $('#address').val(selectedSchool.LSTREET1 || selectedSchool.LSTREET2 || selectedSchool.LSTREET3 || '');
                    $('#city').val(selectedSchool.LCITY || '');
                    $('#state').val(selectedSchool.STATENAME || '');
                    $('#zipcode').val(selectedSchool.LZIP || '');
                    $('#nces_cource_code').val(selectedSchool.NCESSCH || '');

                    // ===== Grade Level Auto Selection =====
                    let selectedGrades = [];
                    for (const [apiKey, label] of Object.entries(gradeMap)) {
                        if (selectedSchool[apiKey] === 'Yes') {
                            const matchingOption = $gradeSelect.find('option').filter(function () {
                                return $(this).text().trim() === label;
                            }).val();
                            if (matchingOption) {
                                selectedGrades.push(matchingOption);
                            }
                        }
                    }
                    $gradeSelect.val(selectedGrades).trigger('change');
                }
            }
        }

        // === select2 for full_name with dynamic search ===
        $dropdown.select2({
            placeholder: 'Type at least 4 characters to search',
            minimumInputLength: 4,
            ajax: {
                delay: 500,
                transport: function (params, success, failure) {
                    const searchTerm = params.data.q || '';
                    $.ajax({
                        url: `https://app.whizara.com/node/nces/schools?SCH_NAME=${encodeURIComponent(searchTerm)}`,
                        method: 'GET',
                        dataType: 'json',
                        success: function (response) {
                            schoolData = response;
                            let results = response.map(school => {
                                const schoolType = school.CHARTER_TEXT?.toLowerCase() === 'no' ? 'Public' : 'Independent';
                                const street = school.LSTREET1 || school.LSTREET2 || school.LSTREET3 || '';
                                const label = `${school.SCH_NAME} - ${schoolType}, ${street} (${school.LCITY}, ${school.LSTATE})`;
                                return { id: school.SCH_NAME, text: label };
                            });
                            results.push({ id: 'Other', text: 'Other' });
                            success({ results });
                        },
                        error: function (xhr, status, error) {
                            console.error("Request error:", status, error);
                            failure();
                        }
                    });
                },
                processResults: function (data) {
                    return data;
                }
            }
        });
        $dropdown.on('change', handleSchoolSelection);
        if ($.fn.select2) {
            $gradeSelect.select2();
        }
    });
</script>
@endsection