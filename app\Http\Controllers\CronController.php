<?php

namespace App\Http\Controllers;
use App\Users;
use Illuminate\Http\Request;

class CronController extends Controller
{
    public function index()
    {
       $data='cronjob';
        return $data;
    }

    public function incompleteApplication (){
        $application = Users::where("profile_status", "=", "1")
                ->orwhere("profile_status", "=", "2")
                ->orwhere("profile_status", "=", null)
                ->where("type", "=", "5")
                ->orderBy("id", "desc")
                ->get();
                foreach ($application as $key => $value) {

                    createCronUserNotification($value,'1','user','user','user');

                }
                return;

    }
}
