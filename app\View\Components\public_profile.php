<?php

namespace App\View\Components;

use Illuminate\View\Component;

class public_profile extends Component
{
    public $data;
    public $instructor;
    public $type;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($instructor, $type, $data = null)
    {
        $this->instructor = $instructor;
        $this->type = $type;
        $this->data = $data;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.public_profile');
    }
}
