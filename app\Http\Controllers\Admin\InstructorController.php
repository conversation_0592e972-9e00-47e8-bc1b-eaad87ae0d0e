<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\Permission;
use App\Users;
use App\UserFirstStepModel;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\scheduledInterview;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\ViewClassroomModel;
use App\UserUponCompletionNoteModel;
use App\ProfileStatusHistoryModel;
use App\rubric;
use App\user_references;
use App\UserEducationModel;
use App\user_interview_slots;
use App\Programs;
use App\ProgramNote;

use App\District;
use App\user_contract;
use App\document_form;
use App\StateModel;
use App\Subject;
use App\LastChatModel;
use App\AdministrativeInfoModel;
use App\AvailablityLocationModel;


use Hash;
use Mail;
use Auth;
use App\AvailabilityModel;
use App\UserThirdStepModel;
use App\Helpers\DataTableHelper;
use App\QuestionsModel;
use App\User;
use Illuminate\Support\Facades\Crypt;
use PhpParser\Node\Stmt\Else_;
use App\Exports\Admin\ExportInstructor;
use Excel;

class InstructorController extends Controller
{
    private function generateChatLink($encryptedId)
    {
        $chatRoute = url('instructor-chat/' . $encryptedId);
        return "<a href='{$chatRoute}'><i class='fas fa-comment fa-lg'></i></a>";
    }

    private function generateActionButtons($encryptedStrId,$res)
    {
        $viewRoute = url('viewinstructordetails/step1/' . $encryptedStrId);
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $mailButton = '';

        if (isset($res['manageinstructor'])) :
            if (array_key_exists('manageinstructor', $res)) :
                if (in_array('update', json_decode($res['manageinstructor'], true))) :
                    $editRoute = url('edit-instructor/' . $encryptedStrId);

                    $editButton = "<a href='{$editRoute}'><button type='button' class='btn btn-rounded btn-block btn-xs btn-outline-secondary' title='Edit'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>";
                endif;

                if (in_array('delete', json_decode($res['manageinstructor'], true))) :
                    $deleteButton = "<a class='admin_delete'  href='{$actionUrl}' data-id='{$encryptedStrId}'><button type='button' title='Delete' class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;

                if (in_array('mail', json_decode($res['manageinstructor'], true))) :
                    $mailButton = "<a  href='{$viewMailRoute}' ><button type='button' title='Mail' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-envelope' aria-hidden='true'></i></button></a>";

                endif;

            endif;
        endif;




        $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";


        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$viewButton}{$deleteButton}{$mailButton}</div>";
    }

    public function index(Request $request)
    {

        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'manageinstructor','view')!=true){
            return redirect("/no-permission");
        }

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }



            $qry = User::select('users.*')
                ->where("users.type", "=", "5")
                ->whereIn("profile_status", ['1','12','16','17','20']);
            if ($params['columnName'] == 'availibility') {
                $qry->addSelect([
                    'availibility' => AvailabilityModel::selectRaw('COUNT(*)')
                    ->whereColumn('tbl_user_availabilities.user_id', 'users.id')
                    ->limit(1)
                ]);
            } elseif ($params['columnName'] == 'prefrence') {
                $qry->addSelect([
                    'prefrence' => UserThirdStepModel::selectRaw('COUNT(tbl_user_teaching_preferences.i_prefer_to_teach)')
                    ->whereColumn('tbl_user_teaching_preferences.user_id', 'users.id')
                    ->limit(1)
                ]);
            } elseif ($params['columnName'] == 'approvals') {
                $params['columnName'] = 'is_sub';
            } elseif ($params['columnName'] == 'format') {
                $params['columnName'] = 'is_approved';
            }

            if ($params['columnName'] == 'status') {
                $params['columnName'] = 'status';
            }
            $qry->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->with('programs');

                if(session('Adminnewlogin')['type']==4){
                    $qry->join(
                        "tbl_invite_application_recruiters",
                        "tbl_invite_application_recruiters.application_id",
                        "=",
                        "users.id"
                    );
                    $qry->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']]);
                    $qry->orderBy("tbl_invite_application_recruiters.id", "desc");
                }


                $qry->where(function ($query) use ($params) {
                    $searchValue = $params['searchValue'];
                    $query->where(function ($que) use ($params) {
                        DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                    })
                    ->orWhere(function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                        $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                    });
                });


            $this->applyFilters($qry, $request);



            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);

                ///
                $chat = "";
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $chat = $this->generateChatLink($encryptedId);
                        endif;
                    endif;
                endif;
                ///
                $checboxButton  = '';
                $action = $this->generateActionButtons($encryptedStrId,$res);
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                $checboxButton  = "<input type='checkbox'  value='{$row->id}' form='chatprogram' name='instructor[]'><input type='hidden'  value='{$row->email}' form='chatprogram' id='email{$row->id}'><input type='hidden'  value='{$row->first_name} {$row->last_name}' form='chatprogram' id='to_name{$row->id}'><input type='hidden'  value='{$row->image}' form='chatprogram' id='img{$row->id}'> ";
            endif;
        endif;
    endif;
    if($row->is_approved==16){
        $status = "Online";
    }

    if($row->is_approved==17){
        $status = "In-person";
    }

    if($row->is_approved==20){
        $status = "Both (Online & In-person)";
    }
    if($row->is_sub == 1){
        $aval = 'Sub';
    }
    else{
        $aval = 'Main/Sub';
    }

    $prefernces = DB::table("tbl_user_teaching_preferences")
            ->where("user_id",$row->id)
            ->first();
    $user_interview_slots = AvailabilityModel::where(['user_id' => $row->id])->first();
    $availibility = !empty($user_interview_slots) ? '<a target="_blank" href="' . url('viewinstructordetails/step12/' . encrypt_str($row->id)) . '">Availability</a>' : 'null';
    $prefrence = !empty($prefernces) && !empty($prefernces->i_prefer_to_teach) ? '<a target="_blank" href="' . url('viewinstructordetails/step3/' . encrypt_str($row->id)) . '">Teaching preferences</a>' : 'null';

    // for main instructor
    $programsList = $row->mainInsProgram->map(function ($program) {
        return [
            'id' => $program->id,
            'name' => $program->name,
        ];
    })->groupBy('id')->toArray();

    $programLinks = collect($programsList)->map(function ($group) {
        $program = $group[0];
        return '<a target="_blank" href="' . url('view-program/step1/' . encrypt_str($program['id'])) . '">' . $program['name'] . '</a>';
    })->implode('&comma;<br>');
    // end main instructor

    // for stand instructor
    $programsListforstandby = $row->programs->filter(function ($program) use ($row) {
        return $program->pivot->status == 1 &&
               ($program->pivot->user_id == $row->id);
            })->map(function ($program) {
                return [
                    'id' => $program->id,
                    'name' => $program->name,
                ];
            })->unique('id')
            ->groupBy('id')
            ->toArray();

        $programLinksforstandby = collect($programsListforstandby)->map(function ($group) {
            return collect($group)->map(function ($program) {
                return '<a target="_blank" href="' . url('view-program/step1/' . encrypt_str($program['id'])) . '">' . $program['name'] . '</a>';
            })->implode('');
        })->implode('&comma;<br>');
    // end standby

    ///
    $statusButton ='';
    if (isset($res['manageinstructor'])) :
        if (array_key_exists('manageinstructor', $res)) :
            if (in_array('update', json_decode($res['manageinstructor'], true))) :
                $statusButton = $this->generateStatusButton($row->status, $row->id);
            endif;
        endif;
    endif;
                $data[] = [
                    "id" =>$checboxButton.' '. $row->id,
                    // "id" =>$row->id,
                    "first_name" => $row->first_name.' '.$row->last_name,
                    "email" => $row->email,
                    "mainprograms" => '<div class="program_names">'.$programLinks.'</div>' ?? '',
                    "standbyprograms" => '<div class="program_names">'.$programLinksforstandby.'</div>' ?? '',
                    "onlinerate" => $row->onlinerate,
                    "inpersonrate" => $row->inpersonrate,
                    "availibility" => $availibility ?? '',
                    "prefrence" => $prefrence ?? '',
                    "approvals" =>  $aval ?? '',
                    "format" => $status ?? '',
                    "state" => $row->state,
                    "city" => $row->city,
                    "status" => $statusButton,
                    "chat" => $chat,
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["subject"] = Subject::get();
        $data["district"] = District::where(["status" => "1"])->get();
        $data["status"] = DB::table("tbl_profile_status")->where(['status_type'=>'Instructor'])->get();
        return view("admin.instructor.instructor_list",$data);
    }

    private function applyFilters($q, Request $request)
    {

        if ($request->input('is_sub')) {
            $q->where('users.is_sub', 1);
        }

        if ($request->input('background_check')) {

            $q->whereHas('backgroundVerifications', function ($query)  {
                $query->where(["type" => "background_check","status" => "1"]);
            });
        }
        if ($request->input('medical_requirements')) {
            $q->whereHas('backgroundVerifications', function ($query)  {
                $query->where(["type" => "medical_requirements","status" => "1"]);
            });
        }


        if ($request->filled(['location', 'lat', 'lng'])) {
            $lat = $request->lat;
            $lng = $request->lng;

            $q->whereHas('availableLocations', function ($query) use ($lat, $lng) {
                $query->selectRaw(
                    '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                    [$lat, $lng, $lat]
                )
                ->orderByRaw('distance');
            });
        }
        //tbl_user_doc_requests

        if ($request->input('state')) {
            $q->where('state', 'LIKE', "%{$request->input('state')}%");
        }

        if ($request->input('city')) {
            $q->where('city', 'LIKE', "%{$request->input('city')}%");
        }

        if ($request->input('district')) {

            $programslist = Programs::where('district',$request->input('district'))->where('program_status','!=','Draft')->pluck('id')->toArray();

                $ProgramNotelist = ProgramNote::whereIn('program_id',$programslist)->where('user_id','!=',null)->pluck('user_id')->toArray();

                $q->whereIn('users.id', $ProgramNotelist);

        }

        if ($request->filled('subject')) {
            $q->join('tbl_user_teaching_preferences', 'tbl_user_teaching_preferences.user_id', '=', 'users.id')
                ->join('tbl_user_subjects', 'tbl_user_subjects.step_id', '=', 'tbl_user_teaching_preferences.id')
                ->where('tbl_user_subjects.subject', trim($request->subject));

            if ($request->filled('sub_subject')) {
                $q->where('tbl_user_subjects.sub_subject', trim($request->sub_subject));
            }
        }


        if ($request->input('certified_teacher')) {
            $q->join('tbl_user_experiences', 'tbl_user_experiences.user_id', '=', 'users.id')
                ->where('tbl_user_experiences.profile_type', 'LIKE', "%{$request->input('certified_teacher')}%");
        }


        if ($request->input('format')) {
            $q->join('tbl_user_teaching_preferences as third', 'third.user_id', '=', 'users.id');

            $str = '';
            $i = 1;

            foreach ($request->input('format') as $key => $value) {
                $str .= 'FIND_IN_SET("' . $value . '" ,third.format)';
                if ($i < count($request->input('format'))) {
                    $str .= ' OR ';
                }
                $i++;
            }

            $q->whereRaw($str);
        }

        if ($request->input('profile_status')) {


            if($request->input('profile_status')=="3"){

                $q->where('users.status',"0");
            }else{
                $q->where('users.status', $request->input('profile_status'));
            }
        }

    }

    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<button type="button" onclick="status_update(' . $id . ', 1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $id . '">' . __('messages.deactive') . '</button>';
            case 1:
                return '<button type="button" onclick="status_update(' . $id . ', 0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $id . '">' . __('messages.active') . '</button>';
            case 2:
                return '<button type="button" data-data="0" class="btn btn-danger btn-rounded">Account Deleted</button>';
            case 3:
                return '<button type="button" data-data="0" class="btn btn-danger btn-rounded">Account Deactivated</button>';
            default:
                return '';
        }
    }

    public function subinstructorlist(Request $request)
    {
        $admin = Users::where("type", "=", "7")
            ->orderBy("id", "desc")
            ->get();
        return view("admin.instructor.subinstructor_list", compact("admin"));
    }

    public function add_instructor(Request $request)
    {
        $role = DB::table("tbl_roles")
            ->orderBy("id", "asc")
            ->get();
        return view("admin.instructor.add_instructor", compact("role"));
    }
    public function save_admin(Request $request)
    {
        $email = $request->email;
        $userExits = Users::where("email", "=", $email)->get();
        if (count($userExits)) {
            return response()->json([
                "success" => false,
                "message" => "Email already exits",
            ]);
        }
        $obj = [];
        $length = 6;
        $randpassword = substr(
            str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $user_id = substr(str_shuffle("0123456789"), 1, $length);
        $obj["user_id"] = $user_id;
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["email"] = $request->input("email");
        $obj["gender"] = $request->input("gender");
        $obj["dob"] = date("Y-m-d", strtotime($request->input("dob")));
        $obj["type"] = $request->input("type");
        $obj["about"] = $request->input("about");
        $obj["password"] = Hash::make($randpassword);
        // $obj["passwordStr"] = $randpassword;
        $obj["status"] = "1";
        $obj["created_at"] = date("Y-m-d H:i:s");
        $obj["updated_at"] = date("Y-m-d H:i:s");
        if ($request->hasfile("file_data")) {
            $file = $request->file("file_data");
            $extension = $file->getClientOriginalExtension(); // getting image extension
            $logopic = "image-" . time() . "." . $extension;
            $destinationPath = public_path("/uploads/institute/");
            $file->move($destinationPath, $logopic);
            $obj["image"] = url("/uploads/institute/" . $logopic);
        }
        $save = Users::insertGetId($obj);
        if ($save) {
            $managment = [
                "dashboard",
                "profile",
                "role",
                "staff",
                "manageschool",
                "manageinstructor",
                "recruiter",
                "manageprogram",
                "generalsetting",
            ];

            foreach ($managment as $key => $value) {
                $objP["user_id"] = $save;
                $objP["module"] = $value;
                $objP["permission_setting"] = "[]";
                $saveP = Permission::insertGetId($objP);
            }
        }
        $dataEmail = [
            "email" => $request->email,
            "subject" => "Created Client",
            "mailbody" => "New client",
            "first_name" => $request->first_name,
            "last_name" => $request->last_name,
            "randpassword" => $randpassword,
            "redirect" => url("/admin"),
        ];

        // Mail::send('admin.email-temp.admin_sinup', $dataEmail, function ($message) use ($email) {
        //     $message->to($email)->subject('Whizara Support');
        //     $message->from('<EMAIL>');
        // });
        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Admin  successfully added",
                "redirect" => url("/add-admin-management"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }
    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Users::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }
    public function delete_admin(Request $request)
    {

        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = Users::where("id", $id)->first();
            if ($record) {
                $res = Users::where("id", "=", $id)->delete();
                $res1 = Permission::where("user_id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function editinstructor(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $admin = Users::where("id", $id)->first();

        $state = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
        $first = UserFirstStepModel::where(["user_id" => $id])->first();
        return view("admin.instructor.editinstructor", [
            "admin" => $admin,
            "state" => $state,
            "first" => $first,
        ]);
    }

    public function instructorupdate(Request $request)
    {
        $obj = [];
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["is_approved"] = $request->input("profilestatusedit");

        if ($request->input("profilestatusedit") == '16' || $request->input("profilestatusedit") == '20') {
            $obj['onlinerate'] = $request->input('onlinerate');
        } else {
            $obj['onlinerate'] = 0;
        }
        if ($request->input("profilestatusedit") == '17' || $request->input("profilestatusedit") == '20') {
            $obj["inpersonrate"] = $request->input("inpersonrate");
        } else {
            $obj['inpersonrate'] = 0;
        }

        $obj["country"] = "United States";
        // $obj["state"] = $request->input("state");
        // $obj["city"] = $request->input("city");
        // $obj["zipcode"] = $request->input("zipcode");
        $id = $request->input("id");

        // $obj1["state"] = $request->input("state");
        // $obj1["city"] = $request->input("city");
        // $obj1["zip_code"] = $request->input("zipcode");
        $obj1["specify_you_work_authorization"] = $request->input("specify");
        $obj1["i_am_authorized"] = 'yes';
        $obj1["reside_united_states"] = 'yes';
        $obj1["user_id"] = $id;


        Users::where("id", $id)->update($obj);

        $first = UserFirstStepModel::where([
            "user_id" => $id,
        ])->first();
        if (!empty($first)) {
            UserFirstStepModel::where("user_id", $id)->update($obj1);
        } else {
            UserFirstStepModel::insert($obj1);
        }


        return response()->json([
            "success" => true,
            "message" => "Successfully update",
        ]);
    }

    public function viewinstructordetails($id, $id2)
    {
        $application_id = decrypt_str($id2);

        $users = Users::where("users.id", $application_id)
            ->firstOrFail();

        if ($users->profile_status == 2) {
            $datas["profile_status"] = 3;
            Users::where("id", $application_id)->update($datas);
        }
        $user = User::find($application_id);
        $pStatus = DB::table("tbl_profile_status")
            ->where("status_type", "Instructor")
            ->where("status", "1")
            ->get();
        $user_first_step = DB::table("tbl_user_work_authorizations")
            ->where("user_id", $application_id)
            ->first();
        $user_second_step = DB::table("tbl_user_experiences")
            ->where("user_id", $application_id)
            ->first();
        $user_third_step = DB::table("tbl_user_teaching_preferences")
            ->where("user_id", $application_id)
            ->first();
        $user_fourth_step = DB::table("tbl_user_hourly_rates")
            ->where("user_id", $application_id)
            ->first();
        $intro = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "introduction",
        ])->first();
        $teaching = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "teaching",
        ])
            ->orderBy("id", "DESC")
            ->get();
        $classroom = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "classroom",
        ])->get();
        $scheduledInterview = scheduledInterview::where([
            "user_id" => $application_id,
        ])->get();
        $five = AssessmentsModel::where(["user_id" => $application_id])->first();
        $quiz = UserQuizModel::where(["user_id" => $application_id])->first();
        $profilehistory = ProfileStatusHistoryModel::where([
            "user_id" => $application_id,
        ])
            ->orderBy("id", "DESC")
            ->get();
            if(request()->segment(2) =='step101'){
                $reqid=$_GET['reqid'];
        $background = BackgroundMedicalModel::select(
            "tbl_user_docs.*",
            "tbl_user_doc_requests.state"
        )
            ->join(
                "tbl_user_docs",
                "tbl_user_doc_requests.id",
                "=",
                "tbl_user_docs.back_med_id"
            )
            ->where(["type" => "background_check", "user_id" => $application_id, "back_med_id" => $reqid])
            ->get();
            }else{
                $background=array();
            }
            if(request()->segment(2) =='step101'){
                $reqid=$_GET['reqid'];
        $medical_requirements = BackgroundMedicalModel::select(
            "tbl_user_docs.*",
            "tbl_user_doc_requests.state"
        )
            ->join(
                "tbl_user_docs",
                "tbl_user_doc_requests.id",
                "=",
                "tbl_user_docs.back_med_id"
            )
            ->where([
                "type" => "medical_requirements",
                "user_id" => $application_id,
                "back_med_id" => $reqid
            ])
            ->get();
        }else{
            $medical_requirements=array();
        }
        $viewclassroom = ViewClassroomModel::select(
            "tbl_training_videos.video",
            "tbl_training_videos.title",
            "tbl_user_view_videos.*"
        )
            ->join(
                "tbl_training_videos",
                "tbl_training_videos.id",
                "=",
                "tbl_user_view_videos.classroom_video_id"
            )
            ->where(["tbl_user_view_videos.user_id" => $application_id])
            ->get();
        $rubric = rubric::where(["user_id" => $application_id])->first();
        $user_references = user_references::where([
            "user_id" => $application_id,
        ])->get();
        $user_education = UserEducationModel::where([
            "user_id" => $application_id,
        ])->get();


        $availrangelocation = AvailablityLocationModel::where(['user_id' => $application_id])->get();
        $user_interview_slots = DB::table("tbl_user_interview_slots")
            ->select(
                "tbl_user_interview_slots.*",
                "tbl_schedule_interviews.date as date",
                "tbl_schedule_interviews.time as time",
                "tbl_schedule_interviews.timezone as timezone",
                "tbl_schedule_interviews.end_time as end_time",
                "tbl_schedule_interviews.link as link"
            )
            ->join(
                "tbl_schedule_interviews",
                "tbl_schedule_interviews.id",
                "=",
                "tbl_user_interview_slots.slot_id"
            )
            ->where(["tbl_user_interview_slots.user_id" => $application_id])
            ->orderBy("tbl_user_interview_slots.id", "DESC")
            ->get();

        $user_contract = user_contract::where([
            "user_id" => $application_id,
        ])->first();

        $availability = AvailabilityModel::where(['user_id' => $application_id])->first();
        $quiz = UserQuizModel::where(['user_id' => $application_id])->first();

        $question = QuestionsModel::orderBy("question_id", "asc")->get();
        $form = document_form::where([
            "status" => 1,
        ])->get();

        $school = Users::where(["type" => 6, "status" => "1"])->get();
        $state = StateModel::where(["country_id" => "239"])->get();
        $medicalform = BackgroundMedicalModel::where([
            "type" => "background_check",
            "application_id" => $application_id,
        ])->get();

        $background_medical = BackgroundMedicalModel::where([
            "type" => "medical_requirements",
            "application_id" => $application_id,
        ])->get();
        $admin = AdministrativeInfoModel::where(['user_id' => $application_id])->first();
        return view(
            "admin.instructor.viewinstructor",
            compact(
                "question",
                "admin",
                "quiz",
                "availability",
                "user_education",
                "profilehistory",
                "viewclassroom",
                "user",
                "application_id",
                "pStatus",
                "user_first_step",
                "user_second_step",
                "user_third_step",
                "user_fourth_step",
                "intro",
                "teaching",
                "classroom",
                "five",
                "quiz",
                "scheduledInterview",
                "background",
                "medical_requirements",
                "rubric",
                "user_references",
                "user_interview_slots",
                "user_contract",
                "form",
                "school",
                "state",
                'background_medical',
                'medicalform',
                'availrangelocation'
            )
        );
    }
    public function viewsubinstructordetails($id)
    {
        $user_id = decrypt_str($id);

        $user_list = Users::where("users.id", $user_id)
            ->where("users.type", 7)
            ->first();

        return view(
            "admin.instructor.viewsubinstructor",
            compact("user_list", "user_id")
        );
    }

    public function change_password($id)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $user_id = decrypt_str($id);
        return view(
            "admin.admin-management.changepassword",
            compact("user_id")
        );
    }

    public function updateChangePassword(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $new_password = $request->input("new_password");
        $confirm_password = $request->input("confirm_password");
        $id = $request->input("userid");

        $login = Users::where("id", $id)->first();

        if (!empty($login)) {
            if ($new_password == $confirm_password) {
                $insertpwd = bcrypt($new_password);
                Users::where("id", $id)->update([
                    "password" => $insertpwd,
                    // "passwordStr" => $new_password,
                ]);

                return response()->json([
                    "success" => true,
                    "message" => "Password successfully changed",
                    "redirect" => url("/viewdetails/" . encrypt_str($id)),
                ]);
            } else {
                return response()->json([
                    "success" => true,
                    "message" =>
                    "New password and Confirm password does not matchd",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Password does not exist",
            ]);
        }
    }


    public function sendMsg(Request $request)
    {
        $lastmsg = LastChatModel::where(function ($query) use ($request) {
            $query->where(['from_id' => $request->from_id, 'to_id' => $request->to_id]);
        })->orWhere(function ($query) use ($request) {
            $query->where(['from_id' => $request->to_id, 'to_id' => $request->from_id]);
        })->orderBy('id', 'asc')->first();


        // LastChatModel::where(['from_id'=>$request->from_id,'to_id'=>$request->to_id])->first();
        if (!empty($lastmsg)) {
            $lastmsg->delete();
            LastChatModel::insert(['from_id' => $request->from_id, 'to_id' => $request->to_id, 'message' => $request->message, 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')]);
        } else {
            LastChatModel::insert(['from_id' => $request->from_id, 'to_id' => $request->to_id, 'message' => $request->message, 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')]);
        }
    }
    public function chat($id)
    {
        $id = decrypt($id);

        $rows = Users::where("id", $id)->first();

        return view("admin.instructor.chat", compact("rows"));
    }



    public function sendinsmsg(Request $request)
    {
        if (!$request->filled('instructor')) {
            return $this->jsonErrorResponse(["message" => "Please select at least one instructor"], false);
        }

        $view = view("components.admin.modals.sendmsgmodel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function export(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Instructor'.time().'.xlsx';
            return Excel::download(new ExportInstructor($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

}
