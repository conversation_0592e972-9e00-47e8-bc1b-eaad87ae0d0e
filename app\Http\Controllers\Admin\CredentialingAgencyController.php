<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\CredentialingAgencyModel;
use App\AgencyCertificatesModel;
use Illuminate\Support\Facades\Crypt;

class CredentialingAgencyController extends Controller
{
    public function listAgency()
    {
        $agency = CredentialingAgencyModel::get();
        return view('admin.agency.index',compact('agency'));
    }

    public function CreateAgency()
    {
        return view('admin.agency.create');
    }

    public function storeAgency(Request $request)
    {
        $request->validate([
            'agency' => 'required'
        ]);
        $agency = new CredentialingAgencyModel;
        $agency->agency = $request->agency;
        $agency->save();

        return redirect()->route('agency')->with('success', 'Agency Stored Successfully');
    }

    public function DeleteAgency(Request $request)
    {
        try {
            $agencyId = decrypt_str($request->id); // Decrypt the ID
            $agency = CredentialingAgencyModel::find($agencyId);
    
            if (!$agency) {
                return response()->json(['success' => false, 'message' => "Not Found"]);
            }
    
            $agency->delete();
    
            return response()->json(['success' => true, 'message' => "Agency Deleted Successfully"]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => "invalid Id"]);
        }
    }

    public function editAgency($id)
    {
        $agency = CredentialingAgencyModel::find(decrypt_str($id));

        if (!$agency) {
            return redirect()->route('agency')->with('success', 'Agency not found.');
        }

        return view('admin.agency.edit', compact('agency'));
    }


    public function updateAgency(Request $request, $id)
    {
        $request->validate([
            'agency' => 'required|string|max:255'
        ]);

        $agency = CredentialingAgencyModel::find(decrypt_str($id));
        $agency->update([
            'agency' => $request->agency
        ]);

        return redirect()->route('agency')->with('success', 'Agency updated successfully.');
    }
    public function AddCertificates($id)
    {
        $decryptId = decrypt_str($id);
        $agency = CredentialingAgencyModel::with(['certificates'])->find($decryptId);
        return view('admin.agency.add-certificates',compact('agency'));
    }

    public function StoreCertificates(Request $request)
    {
        $request->validate([
            'certificates' => 'required|array|max:255'
        ]);
        $datas = [];
        if (!empty($request->certificates) && count($request->certificates) > 0) {
            foreach ($request->certificates as $key => $value) {
                $certificateId = $request->certificate_ids[$key] ?? null;

                if ($certificateId) {
                    // Update existing certificate
                    $existingCertificate = AgencyCertificatesModel::where('id', $certificateId)
                        ->where('agency_id', $request->agency_id)
                        ->first();
    
                    if ($existingCertificate) {
                        $existingCertificate->certificate = $value; // Update certificate text
                        $existingCertificate->updated_at = now();
                        $existingCertificate->save();
                    }
                } else {
                    $datas[] = [
                        'certificate' => $value,
                        'agency_id' => $request->agency_id,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }
            }
            
        }

        if (!empty($datas)) {
            $certificate = AgencyCertificatesModel::insert($datas);
            return redirect()->route('agency')->with(['success' => true, 'message' => 'Certificates Add Successfully']);
        }else {
            return redirect()->route('agency')->with(['success' => false, 'message' => 'Certificates Not Added']);
        }
        
    }

    public function deleteCertificates(Request $request)
    {
        $certificate = AgencyCertificatesModel::find($request->id);
        if (!empty($certificate)) {
            $certificate->delete();
            return response()->json(['success' => true, 'message' => 'Deleted Successfully']);
        } else {
            return response()->json(['success' => false, 'message' => 'Certificate Not Found']);
        }

    }
}
