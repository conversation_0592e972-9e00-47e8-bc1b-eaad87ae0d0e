<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class CKeditorimguploadController extends Controller
{
    public function upload(Request $request)
    {
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');

            $request->validate([
                'upload' => 'required|image|mimes:jpg,jpeg,png,gif|max:2048'
            ]);

            $fileName = time() . '-' . $file->getClientOriginalName();
            $file->move(public_path('uploads'), $fileName);
            $url = asset('uploads/' . $fileName);

            return response()->json([
                'uploaded' => 1,
                'url' => $url
            ]);
        }

        return response()->json([
            'uploaded' => 0,
            'error' => ['message' => 'Failed to upload image.']
        ]);
    }
}
