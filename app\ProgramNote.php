<?php

namespace App;

use App\Casts\TimeCast;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTime;
use DateTimeZone;

class ProgramNote extends Model
{
    use SoftDeletes;
    public static $validRatings = ['good', 'ok', 'bad'];
    public static $ratingColors = ['good' => 'tr-bg-green', 'ok' => 'tr-bg-yellow', 'bad' => 'tr-bg-orange'];

    protected $table = 'program_notes';
    protected $dates = ['class_date'];
        protected $casts = [
        'start_time' => TimeCast::class,
        'end_time' => TimeCast::class,
    ];

    protected $fillable = [
        'program_id', 'user_id', 'attendance', 'rating', 'content_taught', 'other_feedback', 'comment', 'note', 'class_date', 'day', 'start_time', 'end_time', 'commented_by', 'meeting_id', 'status', 'cancellation_reason','makup_class_id','document', 'cancelled_by', 'is_reassigned', 'reassigned_to', 'parent_id', 'invite_id', 'invited_by', 'deleted_at', 'sub_user_id', 'is_sub_requested', 'entered_by', 'user_sub_requested','is_replacement','is_makeup_class','completed_class_log_timestamp','admin_complete_datetime','delete_payment_reason','cancelled_date'
    ];

    private function getUserTimezone()
    {
        $timezone = config('app.timezone', 'America/Los_Angeles');
        $user = request()->user();
        $isAdmin = in_array('CheckSession', request()->route()->middleware());
        $isSchool = in_array('CheckSchoolSession', request()->route()->middleware());

        if ($user  && !$isAdmin && !$isSchool) {
            $timezone = optional(@$user->availability)->teach_in_person_timezone ?? $timezone;
        } elseif ($isAdmin || $isSchool) {
            $timezone = $this->program->timezone ?? $timezone;
        }

        return $timezone;
    }

    public function getStartTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }

    public function getEndTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }


    public function getStartTimeUtcAttribute()
    {
        return $this->attributes['start_time']; //  raw attribute in the database
    }

    public function getEndTimeUtcAttribute()
    {
        return $this->attributes['end_time']; // raw attribute in the database
    }

    public function subUser()
    {
        return $this->belongsTo(User::class, 'sub_user_id');
    }

    /*public function subUsers()
    {
        return $this->hasMany(ProgramNoteSub::class, 'program_note_id');
    }*/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function parent()
    {
        return $this->belongsTo(ProgramNote::class, 'parent_id');
    }
    public function reassignedUser()
    {
        return $this->belongsTo(User::class, 'reassigned_to');
    }

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
    public function amount()
    {
        return $this->hasOne(ProgramNoteAmount::class);
    }
    public function programNoteAmounts()
    {
        return $this->hasMany(ProgramNoteAmount::class, 'program_note_id');
    }
    public function commentor()
    {
        return $this->belongsTo(User::class, 'commented_by');
    }

    public function canceller()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    public function meetings()
    {
        return $this->belongsToMany(Meeting::class, 'meeting_program_note', 'program_note_id', 'meeting_id');
    }
    public function meeting()
    {
        return $this->belongsTo(Meeting::class);
    }
    public function makupClass()
    {
        return $this->belongsTo(ProgramNote::class, 'makup_class_id')->first();
    }

    public function students()
    {
        return $this->hasMany(ProgramNoteStudent::class);
    }
    public function inviter()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeCompleted(Builder $query): void
    {
        $query->where('status', "1");
    }
    /**
     * Scope a query to only include active users.
     */
    public function scopeCancelled(Builder $query): void
    {
        $query->where('status', "0");
    }

    /*
    *Note entered by
    *
    */
    public function editor()
    {
        return $this->belongsTo(User::class, 'entered_by');
    }
}
