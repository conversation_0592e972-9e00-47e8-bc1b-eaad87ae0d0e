

<?php $__env->startSection('title'); ?>
    Instructor List | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php $res = get_permission(session('Adminnewlogin')['type']); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <!-- BREADCRUMB START -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                <?php if(isset($res['manageinstructor'])): ?>
                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/ALL')); ?>" class="<?php echo e(request()->segment(4) == 'ALL' ? 'text-primary' : ''); ?>">All</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/InProgress')); ?>" class="<?php echo e((request()->segment(4) == 'InProgress') ?'text-primary':''); ?>">Profile Incomplete</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/UnderReview')); ?>" class="<?php if (request()->segment(4) == 'UnderReview') {
                                                                                                                    echo 'text-primary';
                                                                                                                } ?>">Under Review</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/ChangeRequested')); ?>" class="<?php if (request()->segment(4) == 'ChangeRequested') {
                                                                                                                        echo 'text-primary';
                                                                                                                    } ?>">Change Requested</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/Approved')); ?>" class="<?php if (request()->segment(4) == 'Approved') {
                                                                                                                    echo 'text-primary';
                                                                                                                } ?>">Approved</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/Active')); ?>" class="<?php if (request()->segment(4) == 'Active') {
                                                                                                                        echo 'text-primary';
                                                                                                                    } ?>">Active</a></li>
                <?php endif; ?>

                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-instructor/Declined')); ?>" class="<?php if (request()->segment(4) == 'Declined') {
                                                                                                                    echo 'text-primary';
                                                                                                                } ?>">Black Listed</a></li>
                <?php endif; ?>
                
                <?php if(array_key_exists('manageinstructor', $res)): ?>
                <li class="breadcrumb-item " aria-current="page">
                    <form id="marketplace-applicants-excel-form" action="<?php echo e(route('admin.marketplace-applicant-export')); ?>" method="POST" autocomplete="off">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="searchText" id="searchText" value="">
                        <input type="hidden" name="status" id="status" value="<?php echo e(request()->segment(4)); ?>">
                        <button type="submit" class="btn p-0" style="padding: 11px 17px;"><i class="fa fa-download px-2" aria-hidden="true"></i>Download</button>
                    </form>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                </ol>

            </nav>
            <!-- BREADCRUMB END -->
            <form id="chatprogram" method="post" enctype='multipart/form-data'>
                <?php if(isset($res['manageinstructor'])): ?>
                <?php if(array_key_exists('manageinstructor', $res)): ?>
                    <?php if(in_array('chat', json_decode($res['manageinstructor'], true))): ?>
                <div class="container">

                    <div class="row mt-4 mb-2 d-flex justify-content-between  align-items-center">

                        <div class=" d-flex justify-content-between  align-items-center ">
                            <div class="mr-1">
                                <button type="button" class="btn btn-success btn-rounded mb-2" form="chatprogram"
                                    onclick='openAdminModalIfCheckedOnboardingIns("chatprogram","<?php echo e(route('admin.onboardinginstructor.sendmsg')); ?>")'>
                                    Send message</button>
                            </div>


                        </div>
                    </div>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>
            <!-- MONTHLY INVOICES SECTION START -->
            <div class="table-responsive filterdata">
                <table id="dataTable" class="table table-striped" style="width:100%">
                    <thead class="thead-dark">
                        <tr>
                            <th class="d-none">Application Submitted Date</th>
                            <th class="d-none">id</th>
                            <th>Email Verify</th>
                            <th>Application Start Date</th>
                            <th><?php echo e(__('messages.name')); ?></th>
                            <th><?php echo e(__('messages.email')); ?></th>
                            
                            <th>Expected Rate <small>(online)</small></th>
                            <th>Expected Rate <small>(in-person)</small></th>
                            
                            
                            <th>Format</th>
                            
                            <th>State</th>
                            <th>City</th>
                            <th><?php echo e(__('Chat')); ?></th>
                            <th style="min-width: 160px"><?php echo e(__('messages.status')); ?></th>
                            <th>Subject</th>
                            <th>Approved Rate <small>(online)</small></th>
                            <th>Approved Rate <small>(in-person)</small></th>
                            <th>Application Submitted Date</th>
                            <th>Approved / Declined Date</th>
                            <th>Notes</th>
                            <th><?php echo e(__('messages.action')); ?></th>
                            
                            <th>Step-1</th>
                            <th>Step-3</th>
                            <th>Step-4</th>
                            <th>Step-5</th>
                            <th>Step-6</th>
                        </tr>
                    </thead>
                    <tbody>





                    </tbody>
                    <tfoot style="display:none;">

                        <tr>
                            <th>User Id</th>
                            <th><?php echo e(__('messages.name')); ?></th>
                            <th><?php echo e(__('messages.email')); ?></th>
                            <th>State</th>
                            <th>City</th>
                            
                            
                            
                            
                            

                        </tr>
                    </tfoot>
                </table>
            </div>
            <!-- END -->
            </form>
            <!-- EDIT PROFILE SECTION END -->
        </div>
    </main>
    <!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatables.min.css')); ?>">
    <script src="<?php echo e(asset('js/datatables.min.js')); ?>"></script>

    <script async defer
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCjbhs5R7IoIq8x7SE5AfQ6bx1gylGrcLI&loading=async&callback=initAutocompletemap&libraries=places&v=weekly">
</script>
<script>
        $(function() {

            if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
                dataTable.destroy();
            }
            window.dataTable = initializeAdminDataTable2("#dataTable", "<?php echo e(route('admin.new-instructor.manage-instructor', ['id' => request()->segment(4)])); ?>", [
                {
                    data: 'applicationSubmittedDate',
                    visible: false,
                },
                {
                    data: 'id',
                    visible: false,
                },
                {
                    data: 'email_verify_status',
                },
                {
                    data: 'applicationStartDate',
                    name: 'applicationStartDate'
                },
                {
                    data: 'first_name',
                },
                {
                    data: 'email',
                },
                {
                    data: 'onlineRate'
                },
                {
                    data: 'inpersonRate'
                },
                {
                    data: 'format',
                    searchable: false,
                },
                {
                    data: 'state',
                },
                {
                    data: 'city'
                },
                {
                    data: 'chat',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'status',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'subjects',
                },
                {
                    data: 'onlinerate',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'inpersonrate',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'applicationSubmittedDate',
                    name: 'applicationSubmittedDate',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'approvedDeclinedDate',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'notes',
                    searchable: false,
                    orderable: false,
                },
                {
                    data: 'action',
                    searchable: false,
                    orderable: false,
                },
                // {
                //     data: 'pending_onboarding_steps',
                //     searchable: false,
                //     orderable: false,
                // },
                { 
                    data: 'step_1', 
                    title: 'Step 1',
                    searchable: false,
                    orderable: false,

                },
                { 
                    
                    data: 'step_3', 
                    title: 'Step 3',
                    searchable: false,
                    orderable: false,
                },
                { 
                    data: 'step_4', 
                    title: 'Step 4',
                    searchable: false,
                    orderable: false,
                },
                { 
                    data: 'step_5', 
                    title: 'Step 5',
                    searchable: false,
                    orderable: false, 
                },
                { 
                    data: 'step_6', 
                    title: 'Step 6',
                    searchable: false,
                    orderable: false, 
                },
            ]);
        });

        setTimeout(function() {
            $('#dataTable_filter input').on('keyup', function() {
                let searchText = $(this).val();
                // console.log("Search Text:", searchText);
                $('#searchText').val(searchText);
            });
        }, 500);

        // function status_update(id) {
        //     var url = base_url + 'admin-status-changes';

        //     var status = $('.changestatuscls-' + id).data('data');

        //     if (status == 0)
        //         confirm_message = 'Are you sure you want to Deactivate ?';
        //     else
        //         confirm_message = 'Are you sure you want to activate ?';
        //     update_status(id, url, confirm_message);
        // }

    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/new-instructor/instructor_list.blade.php ENDPATH**/ ?>