<?php

namespace App\Http\Requests\Front;

use App\ProgramNote;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProgramClassRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'attendance' => 'required|integer|gt:0',
            'rating' => ['required', Rule::in(ProgramNote::$validRatings)],
            'content_taught' => 'required',
            'note' => 'required',
            'student.*' => 'required',
            'class_id.*' => 'required',
        ];
    }


    public function attributes()
    {
        return [

            'student.*' => 'student',
            'class_id.*' => 'grade',
        ];
    }
}
