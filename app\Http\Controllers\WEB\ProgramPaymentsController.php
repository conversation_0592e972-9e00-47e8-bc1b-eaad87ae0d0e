<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{Programs, ProgramNote, ProgramNoteAmount, ProgramNoteStudent, Reimbursement};
use App\Exports\Front\ExportPayments;
use App\Exports\Front\ExportReimbursements;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Excel;



class ProgramPaymentsController extends Controller
{

    public function getTabData(Request $request)
    {

        $user = auth()->user();
        $user->load('reimbursements', 'programNoteAmounts');

        $tab = $request->tab;

        $totalAmount = $user->reimbursements()->whereStatus('1')->sum('amount');
        $totalPayment = $user->programNoteAmounts()->sum('amount');



        $view = view("components.tabs.payments.{$tab}", compact('totalAmount', 'user', 'totalPayment'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function list(Request $request)
    {
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        // $qry = $user->programNoteAmounts()->whereHas('note')->with('note.meeting');
        $qry = $user->programNoteAmounts()->whereHas('note')->with('note');

        if ($request->program_id) {
            $qry->where('program_id', $request->program_id);
        }

        if ($request->daterange && strpos($request->daterange, ' TO ') !== false && strpos($request->daterange, ' date ') == false) {
            $separator = ' TO ';
            $dateRange = explode($separator, $request->daterange);
            $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
            $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

            $qry->whereHas('note', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('class_date', [$startDate, $endDate]);
            });
        }

        if ($request->status == 'paid') {
            $qry->whereNotNull('payment_date_updated');
        } elseif ($request->status == 'unpaid') {
            $qry->whereNull('payment_date_updated');
        }

        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id', 'DESC')->get();

        $data = array();
        $i = 1;
        $javascriptVoid = 'javascript:void(0);';

        foreach ($result as $row) {
            $viewRoute = route('user.payment.view', ['id' => $row->id]);
            $action = $this->generateActionButtons($viewRoute);

            // $meetingData = getMeetingTimeHtml(@$row->note->meeting);
            // $link = @$row->note->meeting->link??'';
            // $meeting_type = @$row->note->meeting->meeting_type??'';
            // $date = @$row->note->meeting->date??'';

            $encryptedId = encrypt($row->program_id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
            $view = $row->program ? "<a class='idtextcolor' href='{$detailUrl}'>{$row->program->id}</a>" : 'NIL';

            $paymentDateTime = new DateTime($row->payment_date_updated);
            $paymentDate = $paymentDateTime->format('Y-m-d');
            $paymentTime = $paymentDateTime->format('H:i:s');
            $newPaymentDate = '<whiz-date date="'.$paymentDate.'"></whiz-date>';
            $newPaymentTime = '<whiz-time date="'.$paymentDate.'" set-time="'.$paymentTime.'" format="hh:mm A" convert-from="local""></whiz-time>';

            if($row->payment_date_updated){
                $payment_updated_date = $newPaymentDate . ' ' . $newPaymentTime;
                $payment_status = 'Paid';
            }
            else{
                $payment_updated_date = '';
                $payment_status = '';
            }

            $data[] = array(
                "id" => $i,
                "school_id" => @$row->program->school->full_name ?? 'NIL',
                "program_id" => $view,
                // "date" =>  $date?date('m-d-Y', strtotime($date)):'',
                "format" => @$row->format ?? '',
                // "meeting_type" => $meeting_type,
                // "program_id" => getClassScheduleHtml($row->program->dateSchedule(date('N',strtotime($row->created_at)))->get()),
                // "meeting_time" => $meetingData,
                "class_date" => ($row->note->class_date)->format('m-d-Y'),
                "class_time" => date('h:i A', strtotime($row->note->start_time)) . '-' . date('h:i A', strtotime($row->note->end_time)),
                "hours" => $row->hours,
                "minutes" => $row->minutes,
                "rate" => '$' . $row->rate,
                "amount" => '$' . $row->amount,
                "payment_status" => $payment_status,
                "payment_date_updated" =>  $payment_updated_date,
                // "link" => "<a href='{$link}' target='_blank'>Click Here</a>" ,
                "action" => $action,


            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }
    public function paymentHistoryList(Request $request)
    {
        $user = auth()->user();

        // $qry = $user->programNoteAmounts()->whereHas('note')->with('note.meeting');
        $qry = $user->programNoteAmounts()->whereHas('note')->whereNotNull('payment_date_updated')->with('note');

        if ($request->program_id) {
            $qry->where('program_id', $request->program_id);
        }
        $count = $qry->count();

        $result = $qry->orderBy('id', 'DESC')->get();

        $data = array();
        $i = 1;
        $javascriptVoid = 'javascript:void(0);';

        foreach ($result as $row) {
            $encryptedId = encrypt($row->program_id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
            $view = $row->program ? "<a class='idtextcolor' href='{$detailUrl}'>{$row->program->id}</a>" : 'NIL';
            $data[] = array(
                "id" => $i,
                "school_id" => @$row->program->school->full_name ?? 'NIL',
                "program_id" => $view,
                "class_date" => ($row->note->class_date)->format('m-d-Y'),
                "class_time" => date('h:i A', strtotime($row->note->start_time)) . '-' . date('h:i A', strtotime($row->note->end_time)),
                "hours" => $row->hours,
                "minutes" => $row->minutes,
                "rate" => '$' . $row->rate,
                "amount" => '$' . $row->amount,
                "payment_status" => 'Paid',
                "payment_date_updated" =>  date('m-d-Y h:i A', strtotime($row->payment_date_updated)),


            );

            $i++;
        }


        ## Response
        $response = array(
            "aaData" => $data
        );

        return json_encode($response);
    }


    public function getNotesForm(Programs $program, Request $request)
    {
        $view = view("components.modals.notes-for-program", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeNotesForm(Programs $program, Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;

        $program_id = $program->id;

        $day = date('N');
        $payData = generateHourlyPaymentData($user, $program, $day);


        $request->validate(
            [
                'attendance' => 'required|integer|gt:0',
                'rating' => 'required|integer|between:1,5',
                'content_taught' => 'required',
                'note' => 'required',
                'student.*' => 'required',
                'class_id.*' => 'required',
            ]
        );

        $obj = new ProgramNote();
        $obj->fill($request->only('attendance', 'rating', 'content_taught', 'note'));
        $obj->user_id = $user_id;
        $obj->program_id = $program_id;
        $obj->save();

        if (!empty($payData)) {

            $programNoteAmount = new ProgramNoteAmount();
            $programNoteAmount->program_id = $program_id;
            $programNoteAmount->user_id = $user_id;
            $programNoteAmount->program_note_id = $obj->id;
            $programNoteAmount->hours = $payData['hours'];
            $programNoteAmount->rate = $payData['rate'];
            $programNoteAmount->minutes = $payData['minutes'];
            $programNoteAmount->format = $payData['format'];
            $programNoteAmount->amount = $payData['amount'];
            $programNoteAmount->save();

            $class_ids = $request->class_id;
            $students = $request->student;

            foreach ($students as $key => $student) {

                $programNoteStudent = new ProgramNoteStudent();
                $programNoteStudent->program_id = $program_id;
                $programNoteStudent->user_id = $user_id;
                $programNoteStudent->class_id = $class_ids[$key];
                $programNoteStudent->student = $student;
                $programNoteStudent->save();
            }
        }

        return response()->json(['status' => true, 'message' => "Note saved successfully", "resetForm" => true]);
    }

    public function storeReimbursementRequest(Request $request)
    {

        $user = auth()->user();
        $user_id = $user->id;

        $request->validate(
            [
                'type' => 'required',
                'receipt' => 'required|mimes:jpeg,png,jpg,PDF,pdf|file|max:2048',
                'program_id' => 'nullable',
                'amount' => 'required|numeric|gt:0',
                'description' => 'required|min:10|max:255',
            ],
            [
                'receipt.max' => "Maximum file size to upload is 2MB (2048 KB)."
            ]
        );

        if ($request->hasFile("receipt")) {
            $image = $request->file("receipt");
            $filename = 'uploads/reimbursement/' . uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename, $image);
        }
        $obj = new Reimbursement();
        $obj->fill($request->only('type', 'program_id', 'description', 'amount'));
        $obj->user_id = $user_id;
        $obj->receipt = $filename;
        $obj->save();

        return response()->json(['status' => true, 'message' => "Request sent successfully", "resetForm" => true]);
    }

    public function reimbursementsReqList(Request $request)
    {
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $qry = $user->reimbursements();

        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id', 'DESC')->get();

        $data = array();
        $i = 1;

        foreach ($result as $row) {
            $encryptedId = encrypt($row->program_id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
            $view = $row->program ? "<a href='{$detailUrl}' class='idtextcolor'>{$row->program->id}</a>" : 'NIL';
            $receiptUrl =  generateSignedUrl($row->receipt);
            $data[] = array(
                "id" => $i,
                "created_at" => date('m-d-Y', strtotime($row->created_at)),
                "type" => $row->type,
                "program_id" => $view,
                "description" => $row->description,
                "amount" => '$' . $row->amount,
                "receipt" => "<a href='{$receiptUrl}' target='_blank' title='Receipt'>Receipt</a>",
                "status" => getReimbursementStatus($row->status),
                "updated_at" => date('m-d-Y', strtotime($row->updated_at)),
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function reimbursementsList(Request $request)
    {
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $qry = $user->reimbursements()
            ->where('user_id', $user->id)
            ->where(function ($query) {
                $query->where('status', '1')
                    ->orWhere('status', '3');
            });

        if ($request->program_id) {
            $qry->where('program_id', $request->program_id);
        }

        if ($request->daterange && strpos($request->daterange, ' TO ') !== false && strpos($request->daterange, ' date ') == false) {
            $separator = ' TO ';
            $dateRange = explode($separator, $request->daterange);
            $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
            $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

            $qry->whereBetween('updated_at', [$startDate, $endDate]);
        }

        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id', 'DESC')->get();

        $data = array();
        $i = 1;

        foreach ($result as $row) {

            $viewRoute = route('user.reimbursement.view', ['id' => $row->id]);

            // $action = $this->generateActionButtons($viewRoute);

            $newPaymentDate = '<whiz-datetime date="'.$row->updated_time.'" convert-to="local"></whiz-datetime>';

            if($row->updated_time){
                $payment_updated_date = $newPaymentDate;
                $payment_status = 'Paid';
            }
            else{
                $payment_updated_date = '';
                $payment_status = '';
            }


            $encryptedId = encrypt($row->program_id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
            $view = $row->program ? "<a href='{$detailUrl}' class='idtextcolor'>{$row->program->id}</a>" : 'NIL';
            $data[] = array(
                "id" => $i,
                "school" => @$row->program->school->full_name ?? 'NIL',
                "program_id" => $view,
                "updated_at" => date('m-d-Y', strtotime($row->updated_at)),
                "description" => $row->description,
                "amount" => '$' . $row->amount,
                "payment_status" => $payment_status,
                "payment_updated_date" => $payment_updated_date,
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function exportPayments(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Payments-' . time() . '.xlsx';
            info($request->all());
            return Excel::download(new ExportPayments($request), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

    public function exportReimbursements(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Reimbursements-' . time() . '.xlsx';
            info($request->all());

            return Excel::download(new ExportReimbursements($request), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

    public function viewPayment($id, Request $request)
    {

        $info = ProgramNoteAmount::with('program.school', 'note')->findOrFail($id);
        $encryptedId = encrypt($info->program_id);
        $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
        $programView  = $info->program ? "<a href='{$detailUrl}'>{$info->program->name}</a>" : 'NIL';


        $schoolName = @$info->program->school->full_name ?? 'NIL';
        $view = view('components.modals.payment-details', compact('info', 'programView', 'schoolName'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function viewReimbursement($id, Request $request)
    {

        $info = Reimbursement::with('program.school')->findOrFail($id);
        $encryptedId = encrypt($info->program_id);
        $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
        $programView  = $info->program ? "<a href='{$detailUrl}'>{$info->program->name}</a>" : 'NIL';


        $schoolName = @$info->program->school->full_name ?? 'NIL';
        $view = view('components.modals.reimbursement-details', compact('info', 'programView', 'schoolName'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    private function generateActionButtons($viewRoute)
    {
        $javascriptVoid = 'javascript:void(0);';

        $viewBtnClick = "onclick=\"openCommanModal('{$viewRoute}','#infoModal')\"";

        $html = "";
        $html .= "<button type='button' href='{$javascriptVoid}' class='btn btn-primary'  {$viewBtnClick}>
        <span class='text-white'>
            View
        </span>
    </button>";
        return $html;
    }
}
