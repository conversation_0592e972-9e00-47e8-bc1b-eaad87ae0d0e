<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{AvailabilityModel, ProgramNote, Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportClasses implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->searchInput;
    }

    public function collection()
    {
        $classesQry = ProgramNote::with(['user', 'program','students'])->whereNotNull(['program_notes.note', 'program_notes.attendance', 'program_notes.status']);
        $this->applyFilters($classesQry);

        return $classesQry->orderBy('program_notes.updated_at', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = ['id', 'class_date','completed_class_log_timestamp', 'marked_complete', 'marked_complete_time', 'how_did_it_go', 'main_instructor', 'sub_instructor', 'school_name', 'sub_subject','grade', 'notes_submitted_by', 'content_taught', 'notes', 'other_class_feedback', 'attendence','document'];

        if (!empty($this->requestFilters)) {
            $query->where(function ($q) use ($filters) {
                foreach ($filters as $filter) {
                    switch ($filter) {
                        case 'id':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('program_id', $this->requestFilters);
                            }
                            break;

                        case 'class_date':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('class_date', $this->requestFilters);
                            }
                            break;

                        case 'completed_class_log_timestamp':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('completed_class_log_timestamp', $this->requestFilters);
                            } else {
                                $q->orWhere('updated_at', $this->requestFilters);
                            }
                            break;

                        case 'marked_complete_time':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('admin_complete_datetime', $this->requestFilters);
                            } else {
                                $q->orWhere('updated_at', $this->requestFilters);
                            }
                            break;

                        case 'how_did_it_go':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('rating', $this->requestFilters);
                            }
                            break;

                        case 'main_instructor':
                            if (!empty($this->requestFilters)) {
                                $q->orWhereHas('user', function ($subQuery) {
                                    $subQuery->where('first_name', 'LIKE', '%' . $this->requestFilters . '%')
                                             ->orWhere('last_name', 'LIKE', '%' . $this->requestFilters . '%');
                                });
                            }
                            break;

                        case 'sub_instructor':
                            if (!empty($this->requestFilters)) {
                                $q->orWhereHas('subUser', function ($subQuery) {
                                    $subQuery->where('first_name', $this->requestFilters)
                                             ->orWhere('last_name', $this->requestFilters);
                                });
                            }
                            break;

                        case 'school_name':
                            if (!empty($this->requestFilters)) {
                                $q->orWhereHas('program.school', function ($subQuery) {
                                    $subQuery->where('full_name', 'LIKE', '%' . $this->requestFilters . '%');
                                });
                            }
                            break;

                        case 'sub_subject':
                            if (!empty($this->requestFilters)) {
                                $q->orWhereHas('program.subSubject', function ($subQuery) {
                                    $subQuery->where('name', 'LIKE', '%' . $this->requestFilters . '%');
                                });
                            }
                            break;

                        case 'notes_submitted_by':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('sub_user_id', $this->requestFilters);
                            }

                        case 'content_taught':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('content_taught', $this->requestFilters);
                            }
                            break;

                        case 'notes':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('note', $this->requestFilters);
                            }
                            break;

                        case 'other_class_feedback':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('other_feedback', $this->requestFilters);
                            }
                            break;

                        case 'attendance':
                            if (!empty($this->requestFilters)) {
                                $q->withCount('students')->having('program_note_id', '=', $this->requestFilters);
                            }
                            break;

                        case 'document':
                            if (!empty($this->requestFilters)) {
                                $q->orWhere('document', 'LIKE', '%' . $this->requestFilters . '%');
                            }
                            break;

                        // case 'grade':
                        //     if (!empty($this->requestFilters)) {
                        //         $q->orWhereHas('program', function ($subQuery) {
                        //             $subQuery->where('formatted_classes', 'LIKE', '%' . $this->requestFilters . '%');
                        //         });
                        //     }
                        //     break;
                    }
                }
            });
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Class Date',
            'Notes Submission',
            'Marked Complete',
            'Marked Complete TimeStamp',
            'How Did It Go',
            'Main Instructor Name',
            'Sub Instructor Name',
            'School Name',
            'Sub Subject',
            'Grade',
            'Notes Submitted By',
            'Content Taught',
            'Notes',
            'Documents',
            'Other Class Feedback',
            'Attendence',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        if($row->completed_class_log_timestamp){
            $completed_class_log_timestamp = date('m-d-Y h:i A', strtotime($row->completed_class_log_timestamp));
        }else{
            $completed_class_log_timestamp = date('m-d-Y h:i A', strtotime($row->updated_at));
        }

        $mark_complete = '';
        $admin_complete_datetime = '';
        if ($row->status == '0') {
            $mark_complete = 'Cancelled';
            $admin_complete_datetime = '';
        } elseif ($row->status == '1') {
            $mark_complete = 'Completed';
            $admin_complete_datetime = $row->admin_complete_datetime ? $row->admin_complete_datetime : $row->updated_at;
        }



        return [
            $row->program->id ? $row->program->id : 'NIL',
            $row->class_date ? date('m-d-Y', strtotime($row->class_date)) : '',
            $completed_class_log_timestamp ? $completed_class_log_timestamp : '',
            $mark_complete ? $mark_complete : '',
            $admin_complete_datetime ? $admin_complete_datetime : '',
            $row->rating ? ucwords($row->rating) : '',
            $row->user ? $row->user->first_name.' '.$row->user->last_name : '',
            $row->subUser ? $row->subUser->first_name  . ' ' . $row->subUser->last_name : '',
            $row->program->school_name ? schoolusername($row->program->school_name) : '',
            $row->program->sub_subject_id ? subsubjectname($row->program->sub_subject_id) : '',
            $row->program->formatted_classes ? $row->program->formatted_classes : '',
            $row->sub_user_id ? 'Sub Instuctor' : 'Main Instuctor',
            $row->content_taught ? $row->content_taught : '',
            $row->note ? $row->note : '',
            $row->document ? $row->document : '',
            $row->id ? totalmarkstudent($row->id) : '0',

        ];
    }
}
