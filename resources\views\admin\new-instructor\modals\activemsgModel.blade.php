<style>
    .col-12.activate-wrng_main label {
        /* margin-bottom: 0; */
    }

    .modal-dialog {
        max-width: 90% !important;
    }

    .col-12.activate-wrng_main {
        display: flex;
        justify-content: space-between;
        align-items: end;
        padding: 0;
    }

    .activate-wrng input {
        padding: 0px 5px;
        width: 14rem;
        height: 35px;
        border-radius: 4px;
        border: 1.3px solid #bbbbbb;
    }

    .activate-wrng label,
    .activate-wrng_main label {
        font-size: 12px;
    }

    .modal-footer.activate-wrng_ftr_btn button {
        font-size: 14px;
    }

    .modal-footer.activate-wrng_ftr_btn .close {
        background-color: transparent;
        padding: 10px;
        border: 1px solid #000000;
        color: #000000;
        opacity: 0.4;
    }

    @media (max-width: 600px) {

        .activate-wrng input {
            padding: 0px 5px;
            width: 100%;
            height: 35px;
            border-radius: 4px;
            border: 1.3px solid #bbbbbb;
        }

        .col-12.activate-wrng_main {
            display: block;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: end;
            padding: 0;
        }

    }

    .label_width {
        white-space: nowrap;
        /* Keep label text in one line */
        width: auto;
        /* Let width fit the content */
        margin-right: 10px;
        /* Add space between label and input */
        display: inline-block;
        /* Allow sizing based on content */
        margin-bottom: 0px;
        padding-bottom: 0px;
    }


    .col_md_new_subject {
        width: 22% !important;
    }
</style>

@php
    $sum=0;
@endphp

<div class="modal-content">
    <form method="POST" novalidate autocomplete="off">
        <div class="modal-header">
            <h5 class="modal-title" id="common-admin-modal">
                Activate Instructor
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">X</span>
            </button>
        </div>
        <div class="modal-body">
            <div class="row loginarea__wraper">
                <div class="col-xl-12">
                    <div class="inner-form video Classroom">

                        <!-- Base Pay Section -->
                        <div class="col-12 mt-3 activate-wrng_main justify-content-around">
                            @php
                                $bilingualTotal = $bilingual ?? 0;
                                $specialEducationAmmount = 0;
                                $specialEducationAmmount = $sped ?? 0;
                                $specialAmount = $sped ?? 0;
                            @endphp
                            <div class="w-100 budget-container px-5">

                                <div class="row">
                                    <div class="col-md-6 pt-2 d-flex align-items-center">
                                        <label class="label_width" for="bilengue">Bi Lengue.</label>
                                        <input type="number" name="bilengue" class="form-control biLengue budget_input" placeholder="Enter" value="{{ $bilingualTotal }}">
                                        @php $sum+=$bilingualTotal @endphp
                                    </div>

                                    <div class="col-md-6 pt-2 d-flex align-items-center">
                                        <label class="label_width" for="case_management">Case Management</label>
                                        <input type="checkbox" name="case_managemnet_status" style="max-width: 16px; margin-right: 10px;" class="form-control case_management budget_input" placeholder="Enter case management" @if($user->step3->case_management) checked @endif>
                                        <input type="number" name="case_managemnet" class="form-control case_management budget_input" placeholder="Enter case management" value="{{ $case_management ?? 0 }}">
                                        @php $sum+=$case_management @endphp
                                    </div>

                                    {{-- <div class="col-md-6 pt-2 d-flex align-items-center">
                                        <label class="label_width" for="sped">Special Education</label>
                                        <input type="number" name="sped" class="form-control sped budget_input" placeholder="Enter" value="{{ $specialAmount }}">
                                        @php $sum+=$specialAmount @endphp
                                    </div> --}}
                                </div>
                                @if(!empty($specialEducation))
                                    <div class="row">
                                        <div class="col-12 pt-2 d-flex flex-column align-items-start">
                                            <div>
                                                <input type="checkbox" id="sped_status" name="sped_status" style="max-width: 16px; margin-right: 10px;" checked class="form-control sped_edu budget_input">
                                                Special Education Applicable for:
                                            </div>
                                            <ul class="ml-5">
                                                @foreach($specialEducation as $key => $value)
                                                    <li> {{$key}} </li>                                                   </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>



                        <div>
                            <div class="row d-flex justify-content-center mt-2 px-5">
                                <table class="table table-bordered budget_table">
                                    <thead>
                                        <tr>
                                            <th>Check</th>
                                            <th>Subject</th>
                                            <th>Base Pay</th>
                                            <th>Experience Inc.</th>
                                            <th>Education Inc.</th>
                                            <th>Non Teaching Inc.</th>
                                            <th>Special Education</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody class="budget_table">
                                        @foreach ($subjects as $index => $subject)
                                            @php
                                                $experience = $user->step2->total_experience;

                                                if ($experience < 3) {
                                                    $payColumn='none' ;
                                                } elseif ($experience < 6) {
                                                    $payColumn='pay_3_6' ;
                                                } elseif ($experience < 10) {
                                                    $payColumn='pay_6_10' ;
                                                } else {
                                                    $payColumn='pay_10_plus' ;
                                                }
                                                $basePay=$subject->subjectBudget->$payColumn ?? 0;
                                                $experiencePay = $basePay ?? 0;
                                                $educationPay = payScale($user->step2, $subject->subjectBudget);
                                                $nonTeaching = $subject->subjectBudget->non_tech_time ?? 0;
                                                $specialEd = $specialEducationAmmount ?? 0;
                                                $subSum = ($specialEd > 0 ? $specialEd : $subject->subjectBudget->base_pay_0_3) + $experiencePay + $educationPay + $nonTeaching;
                                                $sum += $subSum;
                                                $modifiedCode = strlen($subject->subject_code) == 4 ? '0' . $subject->subject_code : $subject->subject_code;
                                            @endphp

                                            <tr>
                                                <td class="text-center align-middle">
                                                    <input type="checkbox"
                                                        name="selected[{{ $index }}]"
                                                        id="selected_{{ $index }}"
                                                        checked
                                                        style="width:20px; height:15px;">
                                                </td>
                                                <td>
                                                    <input type="text"
                                                        name="code_title[{{ $index }}]"
                                                        id="code_title_{{ $index }}"
                                                        class="form-control"
                                                        value="{{ $modifiedCode . ': ' }} {{ $subject->title }}"
                                                        readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="base_pay[{{ $index }}]"
                                                        id="base_pay_{{ $index }}"
                                                        class="form-control"
                                                        data-field="base_pay"
                                                        value="{{ $subject->subjectBudget->base_pay_0_3 }}">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="experience_pay[{{ $index }}]"
                                                        id="experience_pay_{{ $index }}"
                                                        class="form-control"
                                                        data-field="experience_pay"
                                                        value="{{ $experiencePay }}">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="education_pay[{{ $index }}]"
                                                        id="education_pay_{{ $index }}"
                                                        class="form-control"
                                                        data-field="education_pay"
                                                        value="{{ $educationPay }}">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="non_teaching[{{ $index }}]"
                                                        id="non_teaching_{{ $index }}"
                                                        class="form-control"
                                                        data-field="non_teaching"
                                                        value="{{ $nonTeaching }}">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="special_education[{{ $index }}]"
                                                        id="special_education_{{ $index }}"
                                                        class="form-control"
                                                        data-field="special_education"
                                                        value="{{ $specialEd }}">
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="total[{{ $index }}]"
                                                        id="total_{{ $index }}"
                                                        class="form-control"
                                                        data-field="total"
                                                        value="{{ $subSum }}"
                                                        readonly>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            {{-- <div class="col-md-3 col_md_new mt-3  ">
                                <label class="label_width" for="Experience">Total</label>
                                <input type="number" name="total" class="form-control base_pay" id="total_budget_checked" value="{{ $sum }}" placeholder="Total">
                            </div> --}}

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="from_id" value='{{session("Adminnewlogin")["id"]}}' id="from_id">

        <input type="hidden" value='{{session("Adminnewlogin")["first_name"]}}' id="authname">

        <input type="hidden" value='{{useremail(session("Adminnewlogin")["id"])}}' id="from_email">
        <input type="hidden" value="{{userimg(session('Adminnewlogin')['id'])}}" id="from_img">
        <div class="modal-footer activate-wrng_ftr_btn">
            <button type="button" class="close btn btn-lg" data-dismiss="modal" aria-label="Close"> Cancel</button>
            <button type="button" id="activeOnboardingMsg" class="btn btn-info btn-lg activate-wrng_ftr_btn_active">Confirm</button>
        </div>
    </form>
</div>

<script>
    function updateRowTotal($row) {

        const get = (field) => parseFloat($row.find(`input[data-field="${field}"]`).val()) || 0;

        const base = get('base_pay');
        const exp = get('experience_pay');
        const edu = get('education_pay');
        const nonTech = get('non_teaching');
        const sped = get('special_education');
        const spedApplicable = $('#sped_status').is(':checked');

        // console.log(spedApplicable);

        const total = (spedApplicable ? sped : base) + exp + edu + nonTech;
        console.log("total");
        console.log(total);
        $row.find('input[data-field="total"]').val(total.toFixed(2));
    }

    $('.budget_table tr').each(function() {
        const $row = $(this);
        $row.find('input[data-field="base_pay"], input[data-field="experience_pay"], input[data-field="education_pay"], input[data-field="non_teaching"], input[data-field="special_education"]')
            .on('input', function() {
                updateRowTotal($row);
            });
    });
    $(document).on("change", "#sped_status", function () {
        $('.budget_table tr').each(function () {
            updateRowTotal($(this)); // Just recalculate using current values
        });
    });
</script>