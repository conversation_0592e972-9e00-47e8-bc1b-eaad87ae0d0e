<?php

namespace App\Http\Controllers\Admin;

use App\EmailTemplate;
use App\Helpers\NotificationHelper;
use App\Http\Controllers\Controller;
use App\invite_programs;
use App\ProgramNote;
use App\Programs;
use App\InviteProgramNote;
use App\User;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\MakeupClassRequest;
use App\Notification_content;
use DB;


class MakeupClassController extends Controller
{

    public function cancelClass(ProgramNote $programNote, Request $request)
    {
        $view = view("components.admin.modals.add-cancel-comment", compact('programNote'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeCancelClass(ProgramNote $programNote, Request $request)
    {
        $request->validate(
            [
                'cancellation_reason' => 'required',
            ]
        );
        $current_date = now()->toDateString();
        $current_datetime = now()->toTimeString();

        $programNote->cancellation_reason = $request->cancellation_reason;
        $programNote->cancelled_by = session()->get('Adminnewlogin')['id'];
        $programNote->cancelled_date = $current_date;
        $programNote->cancelled_time = $current_datetime;
        $programNote->status = 0;
        $programNote->save();


        $userId = $programNote->user_id;
        $mainUser = User::find($userId);
        $subUserId = $programNote->sub_user_id;
        $subUser = User::find($subUserId);

        $programId = $programNote->program_id;
        $program = $programNote->program;
        $schoolId = $program->school_name;
        $school = User::find($schoolId);
        $schoolName = $school->full_name ?? '';
        $classDate = $programNote->class_date->format('m-d-Y');

        $title = "Class Cancelled";

        $notificationContent = Notification_content::where("signature", "class-cancelled")->first();
        $notificationTemplate = str_replace(['{{schoolName}}', '{{classDate}}'], [$schoolName, $classDate], @$notificationContent->content??'');

        $type = 'class_cancelled';

        $template = EmailTemplate::find(24);

        $recipients = [
            'recruiter' => getOwnerDeatils($programId, 'Recruiter'),
            // 'school' => $school,
            'mainUser' => $mainUser,
            'subUser' => $subUser,
        ];

        foreach ($recipients as $key => $recipient) {
            if ($recipient) {
                NotificationHelper::sendProgramNotification($recipient, $template, $programId, $schoolName, $notificationTemplate, $title, $type);
            }
        }

        return response()->json(['status' => true, 'message' => "Class cancelled successfully", 'reload' => true]);
    }




    public function add(ProgramNote $programNote, Request $request)
    {
        // if ($programNote->is_reassigned !== 0) {
        //     return response()->json(['status' => true, 'message' => "Class already assigned",], 422);
        // }

        $program = Programs::find($programNote->program_id);

        $is_approved = getApprovedArray($program->delivery_type);
        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $programNote->program_id)
            ->get();
        $programIds = [];
        $programIds[] = $programNote->program_id;
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        // $view = view("components.admin.modals.re-assign-makeup-class", compact( 'programNote'))->render();
        $view = view("components.admin.modals.re-assign-common-makeup-class", compact('programNote', 'program', 'slots', 'programIds', 'timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function store(ProgramNote $programNote, MakeupClassRequest $request)
    {
        // if ($programNote->is_reassigned !== 0) {
        //     return response()->json(['status' => true, 'message' => "Class already assigned",], 422);
        // }

        if (!$request->filled('standBy')) {
            $request->validate(
                [

                    'deadline' => 'required',
                ]
            );
        }
        $program_id = $programNote->program_id;
        $program = $programNote->program;
        $user_id = $request->instructor;

        $class_date = date('Y-m-d', strtotime($request->class_date));

        $exitsclass = ProgramNote::where([
            'program_id' => $program_id,
            'class_date' => $class_date,
            'reassigned_to' => $user_id,
            'start_time' => formatTimeAdminTimezone($request->start_time, $program->timezone),
            'end_time' => formatTimeAdminTimezone($request->end_time, $program->timezone)
        ])->first();
        if ($exitsclass) {
            return response()->json(['status' => false, 'message' => "Same class already created."]);
        }

        $program = Programs::with('school')->find($program_id);
        $delivery_type = $program->delivery_type;
        $school = $program->school;
        $schoolName = $school->full_name;
        if ($request->filled('standBy')) {

            $invite = invite_programs::create([
                'program_id' => $program_id,
                'user_id' => $user_id,
                'status' => 1,
                'is_approved' => 1,
                'type' =>  $request->admin_type,
                'deadline' => $request->deadline,
                'timezone' => $request->timezone,
                'admin_type' => $request->admin_type,
                'is_makeup' => 1,
            ]);
        } else {
            $user = User::find($user_id);
            $deadline = $request->deadline;
            $timezone = $request->timezone;
            $invite = invite_programs::create([
                'program_id' => $program_id,
                'user_id' => $user_id,
                'deadline' => $deadline,
                'timezone' => $timezone,
                'admin_type' => $request->admin_type,
                'is_makeup' => 1,
            ]);
            $formattedclass_date = date('m-d-Y', strtotime($request->class_date));
            $link = url("/new-program-alerts/");

            $subject = "Class Request";
            $notificationContent = Notification_content::where("signature", "make-up-class")->first();
            $notificationTemplate = str_replace(['{{delivery_type}}', '{{schoolName}}', '{{formattedclass_date}}', '{{deadline}}','{{link}}'], [$delivery_type, $schoolName,$formattedclass_date,$invite->deadline->format('m-d-Y'),$link], @$notificationContent->content??'');
            sendMakeupClassScheduledAdded($program,$programNote->cancelled_date,$user);

            $type = 'class_request';

            $template = EmailTemplate::find(26);

            if ($user) {
                NotificationHelper::sendProgramNotification($user, $template, $program_id, $schoolName, $notificationTemplate, $subject, $type);
            }
        }

        $obj = new ProgramNote();
        if ($request->filled('standBy')) {
            $obj->user_id = $user_id;
            // $obj->status = 1;
            // $obj->is_approved = 1;
            // $obj->type = $request->admin_type;
            $programNote->is_reassigned = 2;
        } else {
            $programNote->is_reassigned = 1;
        }
        $obj->program_id = $program_id;
        $obj->class_date = $class_date;
        $obj->day = date('N', strtotime($request->class_date));
        $obj->start_time = formatTimeAdminTimezone($request->start_time, $program->timezone);
        $obj->end_time = formatTimeAdminTimezone($request->end_time, $program->timezone);

        $obj->is_makeup_class = 1;
        $obj->is_reassigned = 0;
        $obj->parent_id = $programNote->id;
        if (!$request->filled('standBy')) {
            $obj->invite_id =  $invite->id;
        }
        $obj->invited_by = session()->get('Adminnewlogin')['id'];
        $obj->reassigned_to = $user_id;
        $obj->save();
        $programNote->makup_class_id = $obj->id;
        $programNote->save();
        if (!$request->filled('standBy')) {
            $inviteProgramNote = new InviteProgramNote();
            $inviteProgramNote->invite_program_id = $invite->id;
            $inviteProgramNote->program_note_id = $obj->id;
            $inviteProgramNote->save();
        }

        return response()->json([
            "success" => true,
            "reload" => true,
            "message" => "Successfully invited"
        ]);
    }
}
