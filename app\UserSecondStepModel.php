<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserSecondStepModel extends Model
{
    protected $table = 'tbl_user_experiences';

    protected $fillable = [
        'user_id', 'certification', 'profile_type', 'specify', 'teaching_certification_year', 'teaching_certification_states', 'certified_special_education', 'teaching_since', 'experience_teaching_ages', 'highest_level_of_education', 'month_and_year_graduation', 'GPA', 'certified_other', 'resume',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
