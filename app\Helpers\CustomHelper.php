<?php
namespace App\Helpers;
use App\AvailabilityRangeModel;
use App\invite_programs;
use App\ProgramNote;
use App\Programs;
use App\User;
use App\InviteProgramNote;
use App\Models\k12ConnectionClasses;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;

class CustomHelper
{

    public static $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    public static function getCalendarEvents($events, $tab)
    {

        return ($tab === 'today-classes')
            ? static::getTodayClassesEvents($events, $tab)
            : static::getMultiDayEvents($events, $tab);
    }

    private static function getTodayClassesEvents($events, $tab)
    {
        $classIds  = [];

        return $events->map(function ($event) use ($tab, &$classIds) {
            $start_date = date('Y-m-d');
            $eventData = static::createEventData($event, $start_date, $tab);
            if (!empty($eventData)) {
                $programNote = ProgramNote::where(['program_id' => $event->id, 'class_date' => $start_date])->first();
                $classId = @$programNote->id ?? '';
                if (!in_array($classId, $classIds)) {
                    $eventsForDays = $eventData;

                    $classIds[] = $classId;
                }
            }
            return @$eventsForDays ?? [];
        });
    }

    private static function getMultiDayEvents($events, $tab)
    {

        $classIds  = [];
        return $events->flatMap(function ($event) use ($tab, &$classIds) {

            $start_date = new DateTime($event->start_date);
            $end_date = new DateTime($event->end_date);

            while ($start_date <= $end_date) {
                $eventData = static::createEventData($event, $start_date->format('Y-m-d'), $tab);
                if (!empty($eventData)) {
                    $programNote = ProgramNote::where(['program_id' => $event->id, 'class_date' => $start_date->format('Y-m-d')])->first();
                    $classId = @$programNote->id ?? '';
                    if (!in_array($classId, $classIds)) {
                        $eventsForDays[$classId] = $eventData;
                        $classIds[] = $classId;
                    }
                }
                $start_date->modify('+1 day');
            }
            return $eventsForDays ?? [];
        });
    }

    private static function createEventData($event, $start_date, $tab)
    {

        $user_id  = auth()->user()->id;
        $type  = auth()->user()->type;
        $limitChars = 14;
        $encryptedId = encrypt($event->id);

        if ($tab === 'new-program') {
            $program = Programs::findOrFail($event->id);
            $errors = static::checkInstructorBackground($program, $user_id);
            if (!empty($errors)) {

                return [];
            }
        }



        $url = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$event->pivot->id]);


        $subuserId = $event->dateAllClassSchedulealert($user_id, $start_date)->value('sub_user_id');
        if($subuserId && $subuserId != $user_id){
            return [];
        }
        if ($subuserId) {
            $isSub = 0;
        } else {
            $isSub = 1;
        }
        $start_time = '';
        $end_time = '';
        if ($tab === 'new-program') {

            $start_time = $event->dateAllClassSchedulealert($user_id, $start_date)->value('start_time');

            $end_time = $event->dateAllClassSchedulealert($user_id, $start_date)->value('end_time');
            $status = $event->dateAllClassSchedulealert($user_id, $start_date)->value('status');

            if ($status === 0 || $status === 4 || $status === 2) {
                return [];
            }
        } else {

            if ((@$event->pivot->is_sub_only == 1 || @$event->pivot->is_makeup == 1) &&  @$event->pivot->is_standby == 0) {

                $id = $event->dateAllClassSchedulealertsub($user_id, $start_date)->value('id');
                $note = InviteProgramNote::where('program_note_id', $id)->where('invite_program_id', $event->pivot->id)->first();

                if (!empty($note)) {

                    $start_time = $event->dateAllClassSchedulealertsub($user_id, $start_date)->value('start_time');

                    $end_time = $event->dateAllClassSchedulealertsub($user_id, $start_date)->value('end_time');
                    $isSub = 0;
                } else {
                    return [];
                }
            } else {

                if ($type == 5) {
                    if ($tab == 'in-progress') {

                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } elseif ($tab == 'upcoming') {
                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } elseif ($tab == 'completed') {
                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } else {

                        $start_time = $event->dateAllClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateAllClassSchedulealert($user_id, $start_date)->value('end_time');
                    }
                } else {

                    if ($tab == 'in-progress') {

                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } elseif ($tab == 'upcoming') {
                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } elseif ($tab == 'completed') {
                        $start_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('end_time');
                    } else {
                        $start_time = $event->dateAllClassSchedule($user_id, $start_date)->value('start_time');

                        $end_time = $event->dateAllClassSchedule($user_id, $start_date)->value('end_time');
                    }
                }

                if ($tab == 'in-progress') {

                    $status = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('status');
                } elseif ($tab == 'upcoming') {
                    $status = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('status');
                } elseif ($tab == 'completed') {
                    $status = $event->dateinprogressClassSchedulealert($user_id, $start_date)->value('status');
                } else {

                    $status = $event->dateAllClassSchedulealert($user_id, $start_date)->value('status');
                }

                if ($status === 0 || $status === 4 || $status === 2) {
                    return [];
                }
            }
        }


        # if there are no classes
        if (!$start_time || !$end_time) {

            return [];
        }

        $program = Programs::findOrFail($event->id);
        $start_time_24 = date('H:i', strtotime($start_time));
        $end_time_24 = date('H:i', strtotime($end_time));
        $start_time = '<whiz-time date='. $start_date.' set-time='.$start_time.' format="hh:mm A" convert-to="local" convert-from='.$program->timezone.'></whiz-time>';
        $end_time = '<whiz-time date='. $start_date.' set-time='.$end_time.' format="hh:mm A" convert-to="local" convert-from='.$program->timezone.'></whiz-time>';
        // $start_time = date('g:iA', strtotime($start_time));
        // $end_time = date('g:iA', strtotime($end_time));
        // dd('ji');
        // dd('hi');


        $end_time_24 = Carbon::createFromFormat('H:i', $end_time_24)->addHour()->format('H:i');

        $subjectName = $event->subject ? $event->subject->subject_name : 'Unknown Subject';

        return [
            // 'title' => Str::limit($event->name, $limitChars, ' ...'),
            'title' => '#' . $event->id,
            'format' => $event->delivery_type,
            'grade' => $event->formatted_classes,
            'start' => $start_date . ' ' . $start_time_24,
            'end' => $start_date . ' ' . $end_time_24,
            'url' => $url,
            'description' => Str::limit($event->address, $limitChars, ' ...'),
            'timeSlot' => $start_time . '-' . $end_time,
            'subjectName' => $subjectName,
            'isSub' => $isSub,
            'isMakeup' => $event->pivot->is_makeup ?? '',
            'isStandby' => $event->pivot->is_standby ?? '',
        ];
    }

    public static function checkUserBackground(Programs $program, $user_id): array
    {
        $errors = [];

        if ($user_id) {
            $user = User::find($user_id);

            if (!static::checkUserClasses($user, $program)) {
                $errors[] = 'Instructor already has active classes during this program.';
            }
        }
        return $errors;
    }
    public static function checkInstructorBackground(Programs $program, $user_id): array
    {
        $errors = [];

        if ($user_id) {
            $user = User::find($user_id);

            if (!static::checkUserClasses($user, $program)) {
                $errors[] = 'You already have active classes during this program.';
            }
        }
        return $errors;
    }

    public static function checkInstructorSubClass(Programs $program, $user_id, $id): array
    {
        $errors = [];

        if ($user_id) {
            $user = User::find($user_id);

            if (!static::checkUserSubClasses($user, $program, $id)) {
                $errors[] = 'You already have active classes during this program.';
            }
        }
        return $errors;
    }
    public static function getAllEventsData($startRange, $endRange)
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];

        // Define the base query for programs
        $qry = Programs::with([
            'schedules',
            'userNotes' => function($query) use ($startRange, $endRange) {
                $query->whereBetween('class_date', [$startRange, $endRange]);
            }
        ])->select(
            'id', 'end_date', 'start_date', 'name', 'address',
            'program_status', 'delivery_type', 'timezone',
            'school_name', 'subject_id', 'sub_subject_id','address'
        );

        // Add the date filtering logic for programs
        $qry->where(function($query) use ($startRange, $endRange) {
            $query->whereBetween('start_date', [$startRange, $endRange])
                  ->orWhereBetween('end_date', [$startRange, $endRange])
                  ->orWhere(function($subQuery) use ($startRange, $endRange) {
                      $subQuery->where('start_date', '<=', $startRange)
                               ->where('end_date', '>=', $endRange);
                  });
        });

        // Check if adminType is not '1', restrict to specific program IDs
        if ($adminType != '1') {
            $whereInIds = getAdminUserProgramIds();
            $qry->whereIn('id', $whereInIds);
        }

        // Execute the query and fetch results
        $programs = $qry->get();

        // Process the fetched programs and filter valid userNotes
        return $programs->map(function ($event) {
            $eventsForDays = [];

            foreach ($event->userNotes as $class) {
                // Only process the classes with status !== 0
                if ($class->status !== 0 && $class->status !== 4 && $class->status !== 2) {
                    $eventsData = static::getAdminEventsData($event, $class);

                    // Only add events data if it's not empty
                    if (!empty($eventsData)) {
                        $eventsForDays[] = $eventsData;
                    }
                }
            }

            return $eventsForDays;
        });
    }


    private static function getAdminEventsData($program, $class)
    {
        $encryptedStrId = encrypt_str($program->id);

        $limitChars = 18;
        $url = url('view-program/step1/' . $encryptedStrId);

        $startDate = $class->class_date;



        $start_time = $class->start_time;

        $end_time = $class->end_time;

        $currentDate = date('Y-m-d');
        if ($currentDate > $program->end_date && $program->program_status !== 'Draft') {
            $status = 'Completed';
        } else {
            $status = $program->program_status;
        }

        $start_time_24 = Carbon::parse($start_time)->format('H:i');
        $end_time_24 = Carbon::parse($end_time)->format('H:i');
        // $start_time = '<whiz-time date="${classItem.class_date}" set-time="${classItem.startTime}"></whiz-time>'
        $start_time = '<whiz-time date='.$startDate.' set-time='.$start_time.' format="hh:mm A" convert-to="local" convert-from='.$program->timezone.'></whiz-time>';
        $end_time = '<whiz-time date='.$startDate.' set-time='.$end_time.' format="hh:mm A" convert-to="local" convert-from='.$program->timezone.'></whiz-time>';

        // $end_time = date('h:i A', strtotime($end_time));
        // "start":"2016-10-08T12:00:00+02:00",
        $startDate_string = $startDate->toDateString();
        if($program->school_name){
            $schoolname = schoolusername($program->school_name);
        }
        else{
            $schoolname = '';
        }
        if($program->subject_id){
            $subject = subjectName($program->subject_id);
        }
        else{
            $subject = '';
        }
        if(isset($program->userNotesWhioutMakupdesc->user_id)){
            $inst = username($program->userNotesWhioutMakupdesc->user_id);
            $checkformaininst = 'yes';
        }
        else{
            $inst = '';
            $checkformaininst = 'no';
        }
        if($program->sub_subject_id){
            $subsubject = subsubjectname($program->sub_subject_id);
        }
        else{
            $subsubject = '';
        }
        $missingStandby = $program->missingStandBy()->exists();
        $missingSubInstructor = $program->missingSubInstructor()->exists();
        return [
            // 'title' => Str::limit($program->name, $limitChars, ' ...'),
            'title' => '#' . $program->id,
            'program' => 'Program Name -' . $program->name,
            'start' => $startDate_string . ' ' . $start_time_24,
            'end' => $startDate_string . ' ' . $end_time_24,
            'status' => $status,
            'end_date' => $program->end_date,
            'currentDate' => $currentDate,
            'url' => $url,
            'description' => Str::limit($program->address, $limitChars, ' ...'),
            'timeSlot' => $start_time . '-' . $end_time,
            'delivery_type' => $program->delivery_type,
            'timezone' => $program->timezone,
            'schoolname' => $schoolname,
            'subject' => $subject,
            'instructor' => $inst,
            'checkformaininst' => $checkformaininst,
            'missingStandby' => $missingStandby,
            'missingSubInstructor' => $missingSubInstructor,
            'subsubject' => $subsubject,
            'address' => $program->address,
        ];
    }
    private static function getEventsData($event, $startDate)
    {
        $encryptedStrId = encrypt_str($event->id);

        $limitChars = 18;
        $url = url('view-program/step1/' . $encryptedStrId);

        $start_time = $event->dateClassAdminSchedule($startDate->format('Y-m-d'))->value('start_time');

        $end_time = $event->dateClassAdminSchedule($startDate->format('Y-m-d'))->value('end_time');
        # if there are no classes
        if (!$start_time || !$end_time) {
            return [];
        }

        $start_time = date('h:i A', strtotime($start_time));
        $end_time = date('h:i A', strtotime($end_time));
        $isSub = '500';

        return [
            'title' => Str::limit($event->name, $limitChars, ' ...'),
            'start' => $startDate,
            'url' => $url,
            'description' => Str::limit($event->address, $limitChars, ' ...'),
            'timeSlot' => $start_time . '-' . $end_time,
            'isSub' => $isSub,
        ];
    }

    public static function getUserAvailProgramsold(User $user)
    {
        $delivery_type = getUserDeliveryType($user->is_approved);

        $qry = Programs::query();
        $qry->published()
            ->whereDoesntHave('mainAssignedUser')
            ->whereDoesntHave('subAssignedUser');
        if ($delivery_type) {

            $qry->where('tbl_programs.delivery_type', $delivery_type);
        }
        $programs = $qry->where('tbl_programs.end_date', '>=', now()->toDateString());

        $teachingPreference = optional($user->teachingPreference);
        if ($teachingPreference && !empty($teachingPreference->i_prefer_to_teach)) {

            $qry->whereHas('classes', function ($query) use ($teachingPreference) {
                $query->whereIn('tbl_classes.id', explode(',', $teachingPreference->i_prefer_to_teach));
            });
        }
        if ($user->subjects->isNotEmpty()) {

            $subjects = $user->subjects()->pluck('subject')->toArray();
            $subSubjects = $user->subjects()->pluck('sub_subject')->toArray();

            if (!empty($subjects)) {
                $qry->whereIn('subject_id', $subjects);
            }
            if (!empty($subSubjects)) {
                $qry->whereIn('sub_subject_id', $subSubjects);
            }
        }


        $programs = $qry->get();

        $filteredPrograms = $programs->filter(function ($program) use ($user) {
            $declinedQry = invite_programs::query()
                ->where([
                    'program_id' => $program->id,
                    'user_id' => $user->id,
                ]);
            $declined = $declinedQry->where(function ($dQuery) {
                $dQuery->where('status', 0)
                    ->orWhereNotNull('admin_type');
            })
                ->exists();
            if ($declined) {
                return false;
            }
            return static::checkUserAvalibility($user, $program);
        });

        return $filteredPrograms;
    }
    public static function getUserAvailPrograms(User $user, $request = null)
    {
        $qry = Programs::query();
        $qry->active()->published()->whereDoesntHave('mainAssignedUser');
        $qry->where('tbl_programs.end_date', '>=', now()->toDateString());
        $qry->orderBy('tbl_programs.id', 'desc');
        $programs = $qry->get();
        return $programs->filter(function ($program) use ($user) {
            $declinedQry = invite_programs::query()
                ->where([
                    'program_id' => $program->id,
                    'user_id' => $user->id,
                ])
                ->where(function ($dQuery) {
                    $dQuery->where('status', 0)
                        ->orWhereNotNull('admin_type')
                        ->orWhere('is_approved', 0);
                });
            return !$declinedQry->exists();
        });
    }

    public static function getArchivedPrograms(User $user, $request = null)
    {
        $qry = Programs::query();
        $qry->active()->published()->whereHas('mainAssignedUser');

        // Fetch all programs
        $programs = $qry->orderBy('id', 'Desc')->get();

        // Filter the programs based on the given conditions
        return $programs->filter(function ($program) use ($user) {
            // Check if the program is declined by the user or has certain other conditions
            $declinedQry = invite_programs::query()
                    ->where([
                        'program_id' => $program->id,
                        'user_id' => $user->id,
                    ])->where(function ($dQuery) {
                        $dQuery->where('status', 0)
                            ->orWhereNotNull('admin_type')
                            ->orWhere('is_approved', 0);
                    });

            // Check if the program is assigned to another user
            $assignedToOtherUser = invite_programs::query()
                ->where('program_id', $program->id)
                ->where('admin_type', 1)
                ->where('status', 1)
                ->orderBy('created_at', 'desc')  // Get the latest invite
                ->first();  // Check if there is any invite other than the current user

            $isAssignedToOtherUser = $assignedToOtherUser && $assignedToOtherUser->user_id !== $user->id;

            // Check if the program has an end date within the last 3 months using Carbon
            $threeMonthsAgo = Carbon::now()->subMonths(1);
            $isEndDateWithinThreeMonths = $program->end_date && Carbon::parse($program->end_date)->greaterThanOrEqualTo($threeMonthsAgo);

            // Return true if any of the conditions are met
            return $isEndDateWithinThreeMonths && ($declinedQry->exists() || $isAssignedToOtherUser);
        });
    }


    public static function checkUserAvalibilityWithDate($user, $program, $request)
    {
        $currentDate = now()->toDateString();

        $startProgramDate = $program->start_date;
        /**
         * check if its first invite
         * @var boolean
         */
        $instructorNotExists = ProgramNote::where('program_id', $program->id)->whereNull(['user_id'])->exists();

        if ($instructorNotExists) {
            $minClassDate = getMinProgramClassDate(null, $program->id, $currentDate);
            $startProgramDate = $minClassDate ? $minClassDate->format('Y-m-d') : $currentDate;
        } elseif ($currentDate > $startProgramDate) {
            $startProgramDate = $currentDate;
        }


        $availability = $user->availability;
        if (!$availability) {
            return false;
        }

        if (@$request->invite_id) {
            $invite = invite_programs::where(['id' => $request->invite_id])->first();
            if ($invite->replacement_start_date) {
                $startProgramDate = $invite->replacement_start_date;
            }
        } else if (@$request->replacement_start_date) {
            $startProgramDate = date('Y-m-d', strtotime($request->replacement_start_date));
        }


        $same_as_online  = false;
        $availableRanges = optional($user->availability)->ranges();


        if ($program->delivery_type == 'In-Person' && $user->availableLocations()->count() > 0) {


            $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
            $same_as_online = in_array('1', $same_as_onlineArr);

            if (!$availableRanges) {
                return false;
            }
            $avRanges =  $availableRanges->where('from_date', '<=', $startProgramDate)
                ->where('to_date', '>=', $program->end_date)
                ->where(function ($subQuery) use ($same_as_online) {

                    if ($same_as_online) {
                        $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                    } else {
                        $subQuery->where('type', '=', 'inperson');
                    }
                })->get();

            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $query = AvailabilityRangeModel::query();
                $query->where('availability_id', $availability->id)->with('timeSlots');
                if ($same_as_online) {
                    $query->where(function ($qry) {
                        $qry->where('type', '=', 'online')
                            ->orWhere('type', '=', 'inperson');
                    })

                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) <= ?', [$availability->id,  $startProgramDate])
                        ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) >= ?', [$availability->id,  $program->end_date]);
                } else {
                    $query->where('type', '=', 'inperson')
                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'inperson', $startProgramDate])
                        ->whereRaw(
                            '(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?',
                            [$availability->id, 'inperson', $program->end_date]
                        );
                }

                $query->orderBy('from_date', 'ASC');
                $avRanges = $query->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        } elseif ($program->delivery_type == 'Online') {


            if (!$availableRanges) {
                return false;
            }

            $avRanges =  $availableRanges
                ->where('from_date', '<=', $startProgramDate)
                ->where('to_date', '>=', $program->end_date)
                ->where('type', '=', 'online')
                ->get();


            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $avRanges = AvailabilityRangeModel::where('availability_id', $availability->id)
                    ->with('timeSlots')
                    ->where('type', '=', 'online')
                    ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'online', $startProgramDate])
                    ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?', [$availability->id, 'online', $program->end_date])
                    ->orderBy('from_date', 'ASC')
                    ->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        }

        if (!isset($avRanges)) {
            return false;
        }


        return static::checkAllTimes($program->userNoteswhitoutmakup, $avRanges, $isMultipleRange);
    }

    public static function checkUserAvalibility($user, $program)
    {

        $availability = $user->availability;
        if (!$availability) {
            return false;
        }


        $same_as_online  = false;
        $availableRanges = optional($user->availability)->ranges();


        if ($program->delivery_type == 'In-Person' && $user->availableLocations()->count() > 0) {


            $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
            $same_as_online = in_array('1', $same_as_onlineArr);

            if (!$availableRanges) {
                return false;
            }
            $avRanges =  $availableRanges->where('from_date', '<=', $program->start_date)
                ->where('to_date', '>=', $program->end_date)
                ->where(function ($subQuery) use ($same_as_online) {

                    if ($same_as_online) {
                        $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                    } else {
                        $subQuery->where('type', '=', 'inperson');
                    }
                })->get();

            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $query = AvailabilityRangeModel::query();
                $query->where('availability_id', $availability->id)->with('timeSlots');
                if ($same_as_online) {
                    $query->where(function ($qry) {
                        $qry->where('type', '=', 'online')
                            ->orWhere('type', '=', 'inperson');
                    })

                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) <= ?', [$availability->id,  $program->start_date])
                        ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) >= ?', [$availability->id,  $program->end_date]);
                } else {
                    $query->where('type', '=', 'inperson')
                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'inperson', $program->start_date])
                        ->whereRaw(
                            '(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?',
                            [$availability->id, 'inperson', $program->end_date]
                        );
                }

                $query->orderBy('from_date', 'ASC');
                $avRanges = $query->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        } elseif ($program->delivery_type == 'Online') {


            if (!$availableRanges) {
                return false;
            }

            $avRanges =  $availableRanges
                ->where('from_date', '<=', $program->start_date)
                ->where('to_date', '>=', $program->end_date)
                ->where('type', '=', 'online')
                ->get();


            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $avRanges = AvailabilityRangeModel::where('availability_id', $availability->id)
                    ->with('timeSlots')
                    ->where('type', '=', 'online')
                    ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'online', $program->start_date])
                    ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?', [$availability->id, 'online', $program->end_date])
                    ->orderBy('from_date', 'ASC')
                    ->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        }

        if (!isset($avRanges)) {
            return false;
        }


        return static::checkAllTimes($program->userNoteswhitoutmakup, $avRanges, $isMultipleRange);
    }


    public static function checkAllTimes($userNotes,  $avRanges, $isMultipleRange)
    {

        $checkedClasses = [];

        foreach ($avRanges as $avRange) {
            $timeSlots = $avRange->timeSlots;



            if (!static::hasOverlap($userNotes, $timeSlots, $avRange, $isMultipleRange, $checkedClasses)) {

                return false;
            }
        }



        if ($isMultipleRange && (count($userNotes) > count(array_unique($checkedClasses)))) {
            $checkedClasses = [];

            return false;
        } else {
            $checkedClasses = [];
        }

        return true;
    }
    public static function hasOverlap($userNotes, $timeSlots, $avRange, $isMultipleRange, &$checkedClasses)
    {

        foreach ($userNotes as $userNote) {
            $userDay = $userNote->day;
            $classDate = $userNote->class_date->toDateString();


            if (in_array($classDate, $checkedClasses)) {
                continue;
            }
            $classStartTime = $userNote->start_time_utc;
            $classEndTime = $userNote->end_time_utc;


            $timeSlot = $timeSlots->firstWhere('day', $userDay);


            if (!$timeSlot) {

                if (!$isMultipleRange) {

                    return false;
                }
                continue;
            } else if ($isMultipleRange && (($avRange->from_date_raw <= $classDate) && ($avRange->to_date_raw >= $classDate))) {

                array_push($checkedClasses, $classDate);
            }


            $fromTime = $timeSlot->from_time_utc;
            $toTime = $timeSlot->to_time_utc;


            // check time range crosses midnight
            $midnightClass = $classStartTime > $classEndTime;
            $midnightAvalibility = $fromTime > $toTime;





            $isWithinRange = (
                // When both time ranges cross midnight
                ($midnightClass && $midnightAvalibility &&
                    ($classStartTime >= $fromTime && $classEndTime <= $toTime)
                ) ||



                // When the class's time range crosses midnight
                (!$midnightAvalibility && $midnightClass &&
                    ($classStartTime >= $fromTime && $classEndTime >= $toTime)
                    // 17:00:00  >= 17:00:00   || 02:00:00  <= 22:00:00
                ) ||

                // When the avalibility time range crosses midnight
                ($midnightAvalibility && !$midnightClass &&
                    ($classStartTime >= $fromTime || $classEndTime <= $toTime)
                )

                ||
                // When neither time range crosses midnight
                (!$midnightClass && !$midnightAvalibility &&
                    ($classStartTime >= $fromTime && $classEndTime <= $toTime)
                )
            );


            if (!$isWithinRange) {

                return false;
            }
        }


        return true;
    }

    public static function checkUserSubAvalibility($user, $program, $request)
    {

        $availability = $user->availability;
        if (!$availability) {
            return false;
        }

        if (@$request->Classes[0]) {
            $classnote =  ProgramNote::whereIn('id', $request->Classes)->pluck('class_date')->toArray();



            if (!empty($classnote)) {

                $timestamps = array_map('strtotime', $classnote);
                $minTimestamp = min($timestamps);
                $maxTimestamp = max($timestamps);
                $startProgramDate = date("Y-m-d", $minTimestamp);
                $endProgramDate = date("Y-m-d", $maxTimestamp);
            } else {
                $startProgramDate = $program->start_date;
                $endProgramDate = $program->end_date;
            }
        } else {
            $startProgramDate = $program->start_date;
            $endProgramDate = $program->end_date;
        }



        $same_as_online  = false;
        $availableRanges = optional($user->availability)->ranges();


        if ($program->delivery_type == 'In-Person' && $user->availableLocations()->count() > 0) {


            $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
            $same_as_online = in_array('1', $same_as_onlineArr);

            if (!$availableRanges) {
                return false;
            }
            $avRanges =  $availableRanges->where('from_date', '<=', $startProgramDate)
                ->where('to_date', '>=', $endProgramDate)
                ->where(function ($subQuery) use ($same_as_online) {

                    if ($same_as_online) {
                        $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                    } else {
                        $subQuery->where('type', '=', 'inperson');
                    }
                })->get();

            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $query = AvailabilityRangeModel::query();
                $query->where('availability_id', $availability->id)->with('timeSlots');
                if ($same_as_online) {
                    $query->where(function ($qry) {
                        $qry->where('type', '=', 'online')
                            ->orWhere('type', '=', 'inperson');
                    })

                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) <= ?', [$availability->id,  $startProgramDate])
                        ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) >= ?', [$availability->id,  $endProgramDate]);
                } else {
                    $query->where('type', '=', 'inperson')
                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'inperson', $startProgramDate])
                        ->whereRaw(
                            '(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?',
                            [$availability->id, 'inperson', $endProgramDate]
                        );
                }

                $query->orderBy('from_date', 'ASC');
                $avRanges = $query->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        } elseif ($program->delivery_type == 'Online') {


            if (!$availableRanges) {
                return false;
            }

            $avRanges =  $availableRanges
                ->where('from_date', '<=', $startProgramDate)
                ->where('to_date', '>=', $endProgramDate)
                ->where('type', '=', 'online')
                ->get();


            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $avRanges = AvailabilityRangeModel::where('availability_id', $availability->id)
                    ->with('timeSlots')
                    ->where('type', '=', 'online')
                    ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'online', $startProgramDate])
                    ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?', [$availability->id, 'online', $endProgramDate])
                    ->orderBy('from_date', 'ASC')
                    ->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        }

        if (!isset($avRanges)) {
            return false;
        }


        $clasessList = $program->userNotes()->whereIn('id', $request->Classes)->get();
        if (@$request->Classes) {
            return static::checkAllsubTimes($clasessList, $avRanges, $isMultipleRange);
        } else {

            return false;
        }
    }

    public static function checkAllsubTimes($userNotes,  $avRanges, $isMultipleRange)
    {

        $checkedClasses = [];

        foreach ($avRanges as $avRange) {
            $timeSlots = $avRange->timeSlots;


            if (!static::hasSubOverlap($userNotes, $timeSlots, $avRange, $isMultipleRange, $checkedClasses)) {

                return false;
            }
        }



        if ($isMultipleRange && (count($userNotes) > count(array_unique($checkedClasses)))) {
            $checkedClasses = [];

            return false;
        } else {
            $checkedClasses = [];
        }

        return true;
    }


    public static function hasSubOverlap($userNotes, $timeSlots, $avRange, $isMultipleRange, &$checkedClasses)
    {

        foreach ($userNotes as $userNote) {
            $userDay = $userNote->day;
            $classDate = $userNote->class_date->toDateString();


            if (in_array($classDate, $checkedClasses)) {
                continue;
            }
            $classStartTime = $userNote->start_time_utc;
            $classEndTime = $userNote->end_time_utc;


            $timeSlot = $timeSlots->firstWhere('day', $userDay);


            if (!$timeSlot) {

                if (!$isMultipleRange) {

                    return false;
                }
                continue;
            } else if ($isMultipleRange && (($avRange->from_date_raw <= $classDate) && ($avRange->to_date_raw >= $classDate))) {

                array_push($checkedClasses, $classDate);
            }


            $fromTime = $timeSlot->from_time_utc;
            $toTime = $timeSlot->to_time_utc;


            // check time range crosses midnight
            $midnightClass = $classStartTime > $classEndTime;
            $midnightAvalibility = $fromTime > $toTime;





            $isWithinRange = (
                // When both time ranges cross midnight
                ($midnightClass && $midnightAvalibility &&
                    ($classStartTime >= $fromTime && $classEndTime <= $toTime)
                ) ||



                // When the class's time range crosses midnight
                (!$midnightAvalibility && $midnightClass &&
                    ($classStartTime >= $fromTime && $classEndTime >= $toTime)
                    // 17:00:00  >= 17:00:00   || 02:00:00  <= 22:00:00
                ) ||

                // When the avalibility time range crosses midnight
                ($midnightAvalibility && !$midnightClass &&
                    ($classStartTime >= $fromTime || $classEndTime <= $toTime)
                )

                ||
                // When neither time range crosses midnight
                (!$midnightClass && !$midnightAvalibility &&
                    ($classStartTime >= $fromTime && $classEndTime <= $toTime)
                )
            );


            if (!$isWithinRange) {

                return false;
            }
        }


        return true;
    }

    public static function getProgramsUsers($programIds, $isSub = false)
    {
        // Fetch all programs at once
        $programs = Programs::whereIn('id', $programIds)->get();

        $userIds = $programs->flatMap(function ($program) use ($isSub) {
            return $isSub ? optional($program->subUser)->id : optional($program->mainUser)->id;
        })->filter()->toArray();

        // Fetch users for all programs
        $allUsers = self::getMultipleProgramUsers($programs, $userIds, $isSub);

        return $allUsers->unique('id');
    }

    public static function getMultipleProgramUsers($programs, $userIds = null, $is_sub = null)
    {
        $usersQry = User::query()
            ->active()
            ->where('type', '5')
            ->where('profile_status', '12');

        if ($userIds) {
            $usersQry->whereNotIn('id', $userIds);
        }



        $isApprovedArr = getApprovedArray($programs->first()->delivery_type);

        if (!in_array(16, $isApprovedArr)) {
            $usersQry->whereHas('availableLocations', function ($query) use ($programs) {
                $query->selectRaw(
                    '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                    [$programs->min('lat'), $programs->max('lng'), $programs->min('lat')]
                )->addSelect('radius')
                    ->havingRaw('distance <=  (radius * ?)', [1609.34]);
            })->with('availableLocations');
        }
        $users = $usersQry->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

        $filteredUsers = $users->filter(function ($user) use ($programs) {

            $same_as_online  = false;
            if ($user->availableLocations()->count() > 0) {
                $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
                $same_as_online = in_array('1', $same_as_onlineArr);
            }
            $availableRanges = optional($user->availability)->ranges();
            if ($availableRanges) {
                return $availableRanges->where('from_date', '<=', $programs->min('start_date'))
                    ->where('to_date', '>=', $programs->max('end_date'))
                    ->where(function ($subQuery) use ($programs, $same_as_online) {
                        $types = $programs->pluck('delivery_type')->toArray();
                        if ($same_as_online) {
                            $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                        } elseif (!empty($types)) {

                            $type[] = in_array('Online', $types) ? 'online' : '';
                            $type[] = in_array('In-Person', $types) ? 'inperson' : '';
                            $subQuery->whereIn('type',  $type);
                        } else {
                            $type[] = 'online';
                            $type[] =  'inperson';
                            $subQuery->whereIn('type',  $type);
                        }
                    })->exists();
            } else {
                return false;
            }
        });

        return $filteredUsers;



        return $users;
    }
    public static function checkProgramsBackground($programs, $user_id = null): string
    {
        $errors = '';



        // $uniqueErrors[] = 'Assigned Successfully';
        if ($programs instanceof Programs) {

            //$programs = Programs::find($programs->id, ['certifications', 'background_checks', 'medicalrequirements']);
            $errors .= static::checkBackVerifications($programs, $user_id);
        } else {
            foreach ($programs as $pid) {
                $program = Programs::find($pid);

                $errors .= static::checkBackVerifications($program, $user_id);
            }
        }


        return $errors;
    }

    private  static function checkBackVerifications($programs, $user_id = null)
    {
        $uniqueErrors = [];

        if ($user_id) {

            $user = User::find($user_id);
            if (!static::checkUserClasses($user, $programs)) {

                $uniqueErrors[] = 'Instructor already has active classes during this program.';
            }
        }

        $errors = implode('<br>', array_unique($uniqueErrors));

        return $errors;
    }

    public static  function sendNotification($user_id, $title = 'New Program Alert', $notification = 'New Program Alert', $type = 'program', $program_id = null)
    {
        if (!$user_id) {
            return true;
        }

        $data['user_id'] = $user_id;
        $data['program_id'] = $program_id;
        $data['title'] = $title;
        $data['notification'] = $notification;
        $data['type'] = $type;
        $data['is_read'] = 0;
        createNotification($data);
    }

    public static function checkUserClasses($user, $program)
    {



        $programClassDates = optional($program->userNotes())->get(['class_date', 'start_time', 'end_time']);


        if (!$programClassDates) {
            return true; // No program class dates, so no overlap
        }


        $checkOverlap = function ($userClasses) use ($programClassDates) {
            foreach ($programClassDates as $pdate) {
                $porgClassDate = $pdate->class_date->toDateString();
                // DB::enableQueryLog();

                $overlappingClasses = $userClasses
                    ->where('class_date', $porgClassDate)
                    ->whereHas('program', function ($query) {
                        $query->where('status', '!=', 0);  // Only include programs with status = 1
                    })
                    ->where(function ($query) use ($pdate) {
                        $start_time_dt = new DateTime($pdate->start_time_utc);
                        $end_time_dt = new DateTime($pdate->end_time_utc);

                        // Clone and adjust the times
                        $start_time_adjusted = (clone $start_time_dt)->modify('+5 minutes')->format('H:i:s');
                        $end_time_adjusted = (clone $end_time_dt)->modify('-5 minutes')->format('H:i:s');
                        $query->whereBetween('start_time', [$pdate->start_time_utc, $end_time_adjusted])
                            ->orWhereBetween('end_time', [$start_time_adjusted, $pdate->end_time_utc])
                            ->orWhere(function ($query) use ($pdate) {
                                $query->where('start_time', $pdate->start_time_utc)
                                    ->orWhere('end_time',  $pdate->end_time_utc);
                            });
                    })->exists();


                if ($overlappingClasses) {

                    return false; // Overlapping classes found
                }
            }

            return true; // No overlap
        };


        if ($user->activeClasses->isNotEmpty() && $programClassDates->isNotEmpty()) {


            if (!$checkOverlap($user->activeClasses())) {

                return false; // Overlapping classes found
            }
        }



        if ($user->activeSubClasses->isNotEmpty() && $programClassDates->isNotEmpty()) {

            if (!$checkOverlap($user->activeSubClasses())) {
                return false; // Overlapping classes found
            }
        }

        return true; // No overlap
    }

    public static function checkUserSubClasses($user, $program, $id)
    {

        $invite = invite_programs::with('notes.programNote')->findOrFail($id);
        $notes = $invite->notes;

        $programClassDates = optional($program->userNotes())->get(['class_date', 'start_time', 'end_time']);


        if (!$programClassDates) {
            return true; // No program class dates, so no overlap
        }


        $checkOverlap = function ($userClasses) use ($programClassDates) {
            foreach ($programClassDates as $pdate) {
                $porgClassDate = $pdate->class_date->toDateString();


                $overlappingClasses = $userClasses
                    ->where('class_date', $porgClassDate)
                    ->where('status', null)
                    ->whereHas('program', function ($query) {
                        $query->where('status', '!=', 0);  // Only include programs with status = 1
                    })
                    ->where(function ($query) use ($pdate) {
                        $start_time_dt = new DateTime($pdate->start_time_utc);
                        $end_time_dt = new DateTime($pdate->end_time_utc);

                        // Clone and adjust the times
                        $start_time_adjusted = (clone $start_time_dt)->modify('+5 minutes')->format('H:i:s');
                        $end_time_adjusted = (clone $end_time_dt)->modify('-5 minutes')->format('H:i:s');
                        $query->whereBetween('start_time', [$pdate->start_time_utc, $end_time_adjusted])
                            ->orWhereBetween('end_time', [$start_time_adjusted, $pdate->end_time_utc])
                            ->orWhere(function ($query) use ($pdate) {
                                $query->where('start_time', $pdate->start_time_utc)
                                    ->orWhere('end_time',  $pdate->end_time_utc);
                            });
                    })->exists();

                if ($overlappingClasses) {

                    return false; // Overlapping classes found
                }
            }

            return true; // No overlap
        };

        $checksubOverlap = function ($userClasses) use ($notes) {
            foreach ($notes as $pdate) {

                $porgClassDate = $pdate->programNote->class_date->toDateString();


                $overlappingClasses = $userClasses
                    ->where('class_date', $porgClassDate)
                    ->where('status', null)
                    ->whereHas('program', function ($query) {
                        $query->where('status', '!=', 0);  // Only include programs with status = 1
                    })
                    ->where(function ($query) use ($pdate) {

                        $query->whereBetween('start_time', [$pdate->programNote->start_time_utc, $pdate->end_time_utc])
                            ->orWhereBetween('end_time', [$pdate->programNote->start_time_utc, $pdate->end_time_utc]);
                    })
                    ->exists();
                if ($overlappingClasses) {

                    return false; // Overlapping classes found
                }
            }

            return true; // No overlap
        };

        if ($user->activeSubClasses->isNotEmpty() && $programClassDates->isNotEmpty()) {
            if (!$checksubOverlap($user->activeSubClasses())) {

                return false; // Overlapping classes found
            }
        }



        if ($user->activeClasses->isNotEmpty() && $programClassDates->isNotEmpty()) {
            if (!$checkOverlap($user->activeClasses())) {
                return false; // Overlapping classes found
            }
        }



        if ($user->activeSubClasses->isNotEmpty() && $programClassDates->isNotEmpty()) {
            if (!$checksubOverlap($user->activeSubClasses())) {

                return false; // Overlapping classes found
            }
        }

        return true; // No overlap
    }


    public static function getProgramsUsersCommon($programIds, $isSub = null)
    {
        // Fetch all programs at once
        $programs = Programs::whereIn('id', $programIds)->get();

        $userIds = $programs->flatMap(function ($program) use ($isSub) {
            return $isSub ? optional($program->subUser)->id : optional($program->mainUser)->id;
        })->filter()->toArray();
        // Fetch users for all programs
        $allUsers = self::getMultipleProgramUsersCommon($programs, $userIds, $isSub);

        return $allUsers->unique('id');
    }

    public static function getMultipleProgramUsersCommon($programs, $userIds = null, $is_sub = null)
    {
        $usersQry = User::query()
            ->active()
            ->where('type', '5')
            ->where('profile_status', '12');

        if ($userIds) {
            $usersQry->whereNotIn('id', $userIds);
        }



        $isApprovedArr = getApprovedArray($programs->first()->delivery_type);

        if (!in_array(16, $isApprovedArr)) {
            $usersQry->whereHas('availableLocations', function ($query) use ($programs) {
                $query->selectRaw(
                    '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                    [$programs->min('lat'), $programs->max('lng'), $programs->min('lat')]
                )->addSelect('radius')
                    ->havingRaw('distance <=  (radius * ?)', [1609.34]);
            })->with('availableLocations');
        }
        $users = $usersQry->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

        $filteredUsers = $users->filter(function ($user) use ($programs) {
            $same_as_online  = false;
            if ($user->availableLocations()->count() > 0) {
                $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
                $same_as_online = in_array('1', $same_as_onlineArr);
            }
            $availableRanges = optional($user->availability)->ranges();
            if ($availableRanges) {
                return $availableRanges->where('from_date', '<=', $programs->min('start_date'))
                    ->where('to_date', '>=', $programs->max('end_date'))
                    ->where(function ($subQuery) use ($programs, $same_as_online) {
                        $types = $programs->pluck('delivery_type')->toArray();
                        if ($same_as_online) {
                            $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                        } elseif (!empty($types)) {

                            $type[] = in_array('Online', $types) ? 'online' : '';
                            $type[] = in_array('In-Person', $types) ? 'inperson' : '';
                            $subQuery->whereIn('type',  $type);
                        } else {
                            $type[] = 'online';
                            $type[] =  'inperson';
                            $subQuery->whereIn('type',  $type);
                        }
                    })->exists();
            } else {
                return false;
            }
        });

        return $filteredUsers;


        return $users;
    }

    public static function canUpdateProgram($user, $program, $request)
    {
        $availability = $user->availability;
        $endDate = date('Y-m-d', strtotime($request->datesingle1));

        if (!$availability) {
            return false;
        }
        $programTimezone =   $program->timezone ?? 'America/Los_Angeles';

        $same_as_online  = false;
        $availableRanges = optional($user->availability)->ranges();

        if ($program->delivery_type == 'In-Person' && $user->availableLocations()->count() > 0) {


            $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
            $same_as_online = in_array('1', $same_as_onlineArr);

            if (!$availableRanges) {
                return false;
            }
            $avRanges =  $availableRanges->where('from_date', '<=', $program->start_date)
                ->where('to_date', '>=', $endDate)
                ->where(function ($subQuery) use ($same_as_online) {

                    if ($same_as_online) {
                        $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                    } else {
                        $subQuery->where('type', '=', 'inperson');
                    }
                })->get();
            if ($avRanges->isEmpty()) {
                return false;
            }
        } elseif ($program->delivery_type == 'Online') {


            if (!$availableRanges) {
                return false;
            }



            $avRanges =  $availableRanges->where('from_date', '<=', $program->start_date)
                ->where('to_date', '>=', $endDate)
                ->where('type', '=', 'online')->get();
            if ($avRanges->isEmpty()) {
                return false;
            }
        }


        if (!isset($avRanges)) {
            return false;
        }
        return static::checkUpdateTimes($request, $programTimezone, $avRanges);
    }

    public static function checkUpdateTimes($request, $programTimezone, $avRanges)
    {
        foreach ($avRanges as $avRange) {
            $timeSlots = $avRange->timeSlots;

            if (!static::hasUpdateOverlap($request, $programTimezone, $timeSlots)) {

                return false;
            }
        }
        return true;
    }
    public static function hasUpdateOverlap($request, $programTimezone, $timeSlots)
    {


        $daysOfWeek = static::$daysOfWeek;

        foreach ($daysOfWeek as $index => $day) {
            if (isset($request->$day[1]) && isset($request->$day[2])) {
                $userDay = ($index + 1);

                $userStartTime = formatTimeForTimezone($request->$day[1], $programTimezone);
                $userEndTime = formatTimeForTimezone($request->$day[2], $programTimezone);

                $timeSlot = $timeSlots->firstWhere('day', $userDay);

                if (!$timeSlot) {
                    return false;
                }

                $fromTime = $timeSlot->from_time_utc;
                $toTime = $timeSlot->to_time_utc;

                // check time range crosses midnight
                $midnightClass = $userStartTime > $userEndTime;
                $midnightAvalibility = $fromTime > $toTime;


                $isWithinRange = (
                    // When both time ranges cross midnight
                    ($midnightClass && $midnightAvalibility &&
                        ($userStartTime >= $fromTime && $userEndTime <= $toTime)
                    ) ||
                    // When the user's time range crosses midnight
                    (!$midnightAvalibility && $midnightClass &&
                        ($userStartTime >= $fromTime || $userEndTime <= $toTime)
                    ) ||
                    // When the provided time range crosses midnight
                    ($midnightAvalibility && !$midnightClass &&
                        ($userStartTime >= $fromTime || $userEndTime <= $toTime)
                    ) ||
                    // When neither time range crosses midnight
                    (!$midnightClass && !$midnightAvalibility &&
                        ($userStartTime >= $fromTime && $userEndTime <= $toTime)
                    )
                );

                if (!$isWithinRange) {
                    return false;
                }
            }
        }

        return true;
    }
    public static function checkAndGetUserClasses($user, $program)
    {

        $programIds = [];
        $programClassDates = optional($program->userNotes())->get(['class_date', 'start_time', 'end_time']);

        if (!$programClassDates) {
            return $programIds;
        }

        foreach ($programClassDates as $pdate) {
            $porgClassDate = $pdate->class_date->toDateString();

            $overlappingClasses = $user->activeClasses()
                ->where('class_date', $porgClassDate)
                ->whereNotIn('status', [0, 4])
                ->whereHas('program', function ($query) {
                    $query->where('status', '!=', 0);  // Only include programs with status = 1
                })
                ->where(function ($query) use ($pdate) {


                    if ($pdate->start_time_utc > $pdate->end_time_utc) {
                        $start_time_dt = new DateTime($pdate->start_time_utc);
                        $end_time_dt = new DateTime($pdate->end_time_utc);

                        // Clone and adjust the times
                        $start_time_adjusted = (clone $start_time_dt)->modify('+5 minutes')->format('H:i:s');
                        $end_time_adjusted = (clone $end_time_dt)->modify('-5 minutes')->format('H:i:s');
                        $query->whereBetween('start_time', [$pdate->end_time_utc, $start_time_adjusted])
                            ->orWhereBetween('end_time', [$pdate->start_time_utc, $end_time_adjusted]);
                    } else {
                        $start_time_dt = new DateTime($pdate->start_time_utc);
                        $end_time_dt = new DateTime($pdate->end_time_utc);

                        // Clone and adjust the times
                        $start_time_adjusted = (clone $start_time_dt)->modify('+5 minutes')->format('H:i:s');
                        $end_time_adjusted = (clone $end_time_dt)->modify('-5 minutes')->format('H:i:s');
                        $query->whereBetween('start_time', [$pdate->start_time_utc, $end_time_adjusted]) // end_time_utc - 5 min
                            ->orWhereBetween('end_time', [$start_time_adjusted, $pdate->end_time_utc]); // start_time_utc + 5min
                    }
                })->pluck('program_id')->toArray();


            $overlappingSubClasses = $user->activeSubClasses()
                ->where('class_date', $porgClassDate)
                ->where('status', null)
                ->whereHas('program', function ($query) {
                    $query->where('status', '!=', 0);  // Only include programs with status = 1
                })
                ->where(function ($query) use ($pdate) {
                    if ($pdate->start_time_utc > $pdate->end_time_utc) {
                        $query->whereBetween('start_time', [$pdate->end_time_utc, $pdate->start_time_utc])
                            ->orWhereBetween('end_time', [$pdate->start_time_utc, $pdate->end_time_utc]);
                    } else {
                        $query->whereBetween('start_time', [$pdate->start_time_utc, $pdate->end_time_utc])
                            ->orWhereBetween('end_time', [$pdate->start_time_utc, $pdate->end_time_utc]);
                    }
                })->pluck('program_id')->toArray();

            $programIds = array_merge($programIds, $overlappingClasses, $overlappingSubClasses);
        }


        return $programIds;
    }


    public static function generateClassPayment(User $user, ProgramNote $programNote)
    {

        $startDateTime = Carbon::parse($programNote->start_time);
        $endDateTime = Carbon::parse($programNote->end_time);

        $timeDifference = $startDateTime->diff($endDateTime);
        $hours = $timeDifference->h;
        $minutes = $timeDifference->i;

        $delivery_type = $programNote->program->delivery_type;
        $hourlyRate = ($delivery_type == 'In-Person') ? $user->inpersonrate : $user->onlinerate;

        $amount = number_format(($hours + ($minutes / 60)) * $hourlyRate, 2, '.', '');

        return [
            'hours' => $hours,
            'minutes' => $minutes,
            'rate' => $hourlyRate,
            'format' => $delivery_type,
            'amount' => $amount,
        ];
    }

    public static function alertProgramUsers($program, $status)
    {
        $usersQry = User::query();
        $usersQry->active()
            ->whereNotNull('users.is_approved')
            ->where("type", "=", "5")
            ->where("profile_status", "=", "12"); {
            $delivery_type = $program->delivery_type;


            // if (!empty($program->background_checks)) {
            //     $usersQry->whereHas('backgroundVerifications', function ($query) {
            //         $query->where(["type" => "background_check", "status" => "1"]);
            //     });
            // }
            // if (!empty($program->medicalrequirements)) {
            //     $usersQry->whereHas('backgroundVerifications', function ($query) {
            //         $query->where(["type" => "medical_requirements", "status" => "1"]);
            //     });
            // }

            // $isApprovedArr = getApprovedArray($delivery_type);
            // if (!in_array(16, $isApprovedArr)) {
            //     $usersQry->whereHas('availableLocations', function ($query) use ($program) {
            //         $query->selectRaw(
            //             '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
            //             [$program->lat, $program->lng, $program->lat]
            //         )->addSelect('radius')
            //             ->havingRaw('distance <=  (radius * ?)', [1609.34])->orderByRaw('distance');
            //     });
            // }


            // if ($delivery_type) {

            //     if ($delivery_type == 'Online') {
            //         $usersQry->whereIn('users.is_approved', array('16', '20'));
            //     } elseif ($delivery_type == 'In-Person') {
            //         $usersQry->whereIn('users.is_approved', array('17', '20'));
            //     }
            // }


            $classs = $program->classes->pluck('id')->toArray();
            // if (!empty($classs)) {

            //     $usersQry->join('tbl_user_teaching_preferences as third', 'third.user_id', '=', 'users.id');

            //     $str = '';
            //     $i = 1;

            //     foreach ($classs as  $value) {

            //         $str .= 'FIND_IN_SET("' . $value . '" ,third.i_prefer_to_teach)';
            //         if ($i < count($classs)) {
            //             $str .= ' AND ';
            //         }

            //         $i++;
            //     }

            //     $usersQry->whereRaw($str);
            // }

            // if ($program->subject_id) {
            //     $usersQry->with('subjects');
            //     $subject_id = $program->subject_id;
            //     $sub_subject_id = $program->sub_subject_id;

            //     $usersQry->whereHas('subjects', function ($query) use ($subject_id, $sub_subject_id) {
            //         $query->where('tbl_user_subjects.subject', '=', $subject_id);
            //         if ($sub_subject_id) {
            //             $query->where('tbl_user_subjects.sub_subject', '=',  $sub_subject_id);
            //         }
            //     });
            // }

            // if ($program->certificates->isNotEmpty()) {

            //     $certificates = $program->certificates;

            //     foreach ($certificates as $certificate) {

            //         $usersQry->whereHas('certificates', function ($query) use ($certificate) {

            //             $query->where("certification", "yes")
            //                 ->where("teaching_certification_states", $certificate->state)
            //                 ->whereRaw("FIND_IN_SET(?, certified_special_education)", [$certificate->certificate]);
            //         });
            //     }
            // }
        }

        $usersQry->select('users.*');

        $result = $usersQry->get();


        $filteredUsers = $result->toArray();
        // ->filter(function ($user) use ($program) {
        //     if (self::checkUserAvalibility($user, $program)) {
        //         return empty(self::checkAndGetUserClasses($user, $program));
        //     }
        // });

        if (!empty($filteredUsers)) {
            $delivery_type = $program->delivery_type;
            $subsubject = subsubjectname($program->sub_subject_id);
            if($delivery_type == 'In-Person'){
                $user = User::find($program->school_name);
                $location = $user->address ?? '';
                $options = [
                    'subsubject' => $subsubject,
                    'location' => $location,
                    'delivery_type' => $delivery_type
                ];
            }
            else{
                $options = [
                    'subsubject' => $subsubject,
                    'location' => '',
                    'delivery_type' => $delivery_type
                ];
            }





            $title = "New Program Alert";

            foreach ($filteredUsers as $user) {
                try {
                    newProgramAlertNotify($user, $program->id, $options, $title, "user", "user", $status);
                } catch (\Exception $e) {
                    dd($e);
                }
            }
        }
    }

    public static function checkUserAvalibilityforMakupClass($user, $program, $request)
    {

        $makupClaasDate = date('Y-m-d', strtotime($request->class_date));


        $availability = $user->availability;
        if (!$availability) {
            return false;
        }


        $same_as_online  = false;
        $availableRanges = optional($user->availability)->ranges();


        if ($program->delivery_type == 'In-Person' && $user->availableLocations()->count() > 0) {


            $same_as_onlineArr = $user->availableLocations()->pluck('same_as_online')->toArray();
            $same_as_online = in_array('1', $same_as_onlineArr);

            if (!$availableRanges) {
                return false;
            }
            $avRanges =  $availableRanges->where('from_date', '<=', $makupClaasDate)
                ->where('to_date', '>=', $makupClaasDate)
                ->where(function ($subQuery) use ($same_as_online) {

                    if ($same_as_online) {
                        $subQuery->where('type', '=', 'online')->orWhere('type', '=', 'inperson');
                    } else {
                        $subQuery->where('type', '=', 'inperson');
                    }
                })->get();

            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $query = AvailabilityRangeModel::query();
                $query->where('availability_id', $availability->id)->with('timeSlots');
                if ($same_as_online) {
                    $query->where(function ($qry) {
                        $qry->where('type', '=', 'online')
                            ->orWhere('type', '=', 'inperson');
                    })

                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) <= ?', [$availability->id,  $makupClaasDate])
                        ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ?) >= ?', [$availability->id,  $makupClaasDate]);
                } else {
                    $query->where('type', '=', 'inperson')
                        ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'inperson', $makupClaasDate])
                        ->whereRaw(
                            '(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?',
                            [$availability->id, 'inperson', $makupClaasDate]
                        );
                }

                $query->orderBy('from_date', 'ASC');
                $avRanges = $query->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        } elseif ($program->delivery_type == 'Online') {


            if (!$availableRanges) {
                return false;
            }

            $avRanges =  $availableRanges
                ->where('from_date', '<=', $makupClaasDate)
                ->where('to_date', '>=', $makupClaasDate)
                ->where('type', '=', 'online')
                ->get();


            $isMultipleRange = false;
            if ($avRanges->isEmpty()) {

                $avRanges = AvailabilityRangeModel::where('availability_id', $availability->id)
                    ->with('timeSlots')
                    ->where('type', '=', 'online')
                    ->whereRaw('(SELECT min(from_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) <= ?', [$availability->id, 'online', $program->start_date])
                    ->whereRaw('(SELECT max(to_date) FROM tbl_user_availability_ranges WHERE availability_id = ? AND type = ?) >= ?', [$availability->id, 'online', $program->end_date])
                    ->orderBy('from_date', 'ASC')
                    ->get();

                if ($avRanges->isEmpty()) {

                    return false;
                } else {
                    $isMultipleRange = true;
                }
            }
        }

        if (!isset($avRanges)) {
            return false;
        }


        return static::checkMakupTimes($program->userNoteswhitoutmakup, $avRanges, $isMultipleRange, $request, $program);
    }

    public static function checkMakupTimes($userNotes,  $avRanges, $isMultipleRange, $request, $program)
    {


        $checkedClasses = [];

        foreach ($avRanges as $avRange) {
            $timeSlots = $avRange->timeSlots;

            if (!static::hasOverlapMakupClass($userNotes, $timeSlots, $avRange, $isMultipleRange, $checkedClasses, $request, $program)) {

                return false;
            }
        }

        if ($isMultipleRange && (count($userNotes) > count(array_unique($checkedClasses)))) {
            $checkedClasses = [];

            return false;
        } else {
            $checkedClasses = [];
        }

        return true;
    }

    public static function hasOverlapMakupClass($userNotes, $timeSlots, $avRange, $isMultipleRange, &$checkedClasses, $request, $program)
    {



        $classStartTime = formatTimeForTimezone($request->start_time, $program->timezone);
        $classEndTime = formatTimeForTimezone($request->end_time, $program->timezone);


        $makupClaasDate = date('Y-m-d', strtotime($request->class_date));
        $makupClaasDates = date('N', strtotime($request->class_date));
        $userDay = $makupClaasDates;

        $classDate = $makupClaasDate;


        $timeSlot = $timeSlots->firstWhere('day', $userDay);


        if (!$timeSlot) {

            if ($isMultipleRange) {

                $checkedClasses = [];
            }
            return false;
        } else if ($isMultipleRange && (($avRange->from_date_raw <= $classDate) && ($avRange->to_date_raw >= $classDate))) {

            array_push($checkedClasses, $classDate);
        }


        $fromTime = $timeSlot->from_time_utc;
        $toTime = $timeSlot->to_time_utc;


        // check time range crosses midnight
        $midnightClass = $classStartTime > $classEndTime;
        $midnightAvalibility = $fromTime > $toTime;


        $isWithinRange = (
            // When both time ranges cross midnight
            ($midnightClass && $midnightAvalibility &&
                ($classStartTime >= $fromTime && $classEndTime <= $toTime)
            ) ||

            // When the class's time range crosses midnight
            (!$midnightAvalibility && $midnightClass &&
                ($classStartTime >= $fromTime && $classEndTime >= $toTime)
                // 17:00:00  >= 17:00:00   || 02:00:00  <= 22:00:00
            ) ||

            // When the avalibility time range crosses midnight
            ($midnightAvalibility && !$midnightClass &&
                ($classStartTime >= $fromTime || $classEndTime <= $toTime)
            )

            ||
            // When neither time range crosses midnight
            (!$midnightClass && !$midnightAvalibility &&
                ($classStartTime >= $fromTime && $classEndTime <= $toTime)
            )
        );





        if (!$isWithinRange) {

            return false;
        }



        return true;
    }

    public static function allProgramUsers($program_id)
    {
        $currentDate = now()->toDateString();
        $uniqueUserIds = ProgramNote::where('program_id', $program_id)
        ->whereNull(['sub_user_id', 'note', 'status'])
        ->where('class_date','>=', $currentDate)
        ->select('user_id')
            ->union(
                ProgramNote::where('program_id', $program_id)->whereNull(['note', 'status'])->where('class_date','>=', $currentDate)->select('sub_user_id')
            )
            ->distinct()
            ->get()
            ->pluck('user_id')->toArray();
        return  $uniqueUserIds;
    }

    public static function checkInstructorMakeupClass($makeupNote, $user_id): array
    {
        $errors = [];

        if ($user_id) {
            $user = User::find($user_id);

            if (!static::checkUserMakeupClasses($user, $makeupNote)) {
                $errors[] = 'You already have active classes during this program.';
            }
        }
        return $errors;
    }

    public static function checkUserMakeupClasses($user, $makeupNote)
    {

        if (!$makeupNote) {
            return true; // No program class dates, so no overlap
        }
        $pdate = ProgramNote::find($makeupNote->program_note_id);

        $checkOverlap = function ($userClasses) use ($pdate) {

            $porgClassDate = $pdate->class_date->toDateString();


            $overlappingClasses = $userClasses
                ->where('class_date', $porgClassDate)
                ->where('status', null)
                ->whereHas('program', function ($query) {
                    $query->where('status', '!=', 0);  // Only include programs with status = 1
                })
                ->where(function ($query) use ($pdate) {
                    $start_time_dt = new DateTime($pdate->start_time_utc);
                    $end_time_dt = new DateTime($pdate->end_time_utc);

                    // Clone and adjust the times
                    $start_time_adjusted = (clone $start_time_dt)->modify('+5 minutes')->format('H:i:s');
                    $end_time_adjusted = (clone $end_time_dt)->modify('-5 minutes')->format('H:i:s');
                    $query->whereBetween('start_time', [$pdate->start_time_utc, $end_time_adjusted])
                        ->orWhereBetween('end_time', [$start_time_adjusted, $pdate->end_time_utc])
                        ->orWhere(function ($query) use ($pdate) {
                            $query->where('start_time', $pdate->start_time_utc)
                                ->orWhere('end_time',  $pdate->end_time_utc);
                        });
                })->exists();

            if ($overlappingClasses) {

                return false; // Overlapping classes found
            }


            return true; // No overlap
        };


        if ($user->activeSubClasses->isNotEmpty() && $pdate) {
            if (!$checkOverlap($user->activeSubClasses())) {

                return false; // Overlapping classes found
            }
        }



        if ($user->activeClasses->isNotEmpty() && $pdate) {
            if (!$checkOverlap($user->activeClasses())) {
                return false; // Overlapping classes found
            }
        }
        return true; // No overlap
    }

    public static function checkAndGetUserClass($user, $class)
    {
        $programIds = [];

        if (empty($class)) {
            return $programIds;
        }
        $class_date = $class['class_date'];
        $start_time = $class['start_time'];
        $end_time = $class['end_time'];

        $overlappingClasses = $user->activeClasses()
            ->where('class_date', $class_date)
            ->whereNotIn('status', [0, 4, null])
            ->where(function ($query) use ($start_time, $end_time) {

                if ($start_time > $end_time) {
                    $query->whereBetween('start_time', [$end_time, $start_time])
                        ->orWhereBetween('end_time', [$start_time, $end_time]);
                } else {
                    $query->whereBetween('start_time', [$start_time, $end_time])
                        ->orWhereBetween('end_time', [$start_time, $end_time]);
                }
            })->pluck('program_id')->toArray();


        $overlappingSubClasses = $user->activeSubClasses()
            ->where('class_date', $class_date)
            ->where('status', null)
            ->where(function ($query) use ($start_time, $end_time) {

                if ($start_time > $end_time) {
                    $query->whereBetween('start_time', [$end_time, $start_time])
                        ->orWhereBetween('end_time', [$start_time, $end_time]);
                } else {
                    $query->whereBetween('start_time', [$start_time, $end_time])
                        ->orWhereBetween('end_time', [$start_time, $end_time]);
                }
            })->pluck('program_id')->toArray();

        $programIds = array_merge($programIds, $overlappingClasses, $overlappingSubClasses);

        return $programIds;
    }

    public static function allk12ConnectionProgramUsers($program_id)
    {
        $currentDate = now()->toDateString();
        $uniqueUserIds = k12ConnectionClasses::where('program_id', $program_id)
        ->whereNull(['sub_instructor_id'])
        ->where('class_date','>=', $currentDate)
        ->select('main_instructor_id')
            ->union(
                k12ConnectionClasses::where('program_id', $program_id)->where('class_date','>=', $currentDate)->select('sub_instructor_id')
            )
            ->distinct()
            ->get()
            ->pluck('main_instructor_id')->toArray();
        return  $uniqueUserIds;
    }
}
