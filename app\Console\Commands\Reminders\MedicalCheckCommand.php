<?php

namespace App\Console\Commands\Reminders;

use App\notification;
use App\Notification_content;
use App\ProgramNote;
use App\User;
use Illuminate\Console\Command;

class MedicalCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:medical-check {--type= : The type of reminder (oneday or everytwodays)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to All instructors One day before the medical check is due or every two days?";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = $this->signature;
        $type = $this->option('type');


        $users = User::active()
            ->where("type", "=", "5")
            ->where("app_notification", "=", "1")
            ->where("profile_status", "=", "12")
            ->whereHas("backgroundVerifications", function ($query)use  ($type)  {
                $query->whereNull('status')
                    ->where('type', '=', 'medical_requirements');
                    if ($type === 'oneday' ) {
                        $query->where('deadline', '=', now()->addDay()->toDateString());
                    } elseif ($type === 'everytwodays') {
                        $query->where('deadline', '=', now()->addDay()->toDateString());
                        $query->where('deadline', '!=', now()->toDateString());
                    }else{
                        $query->where('deadline', '=', now()->addDay()->toDateString());

                    }

            })
            ->pluck('id')->toArray();
            logger()->info($users);die;

        $link = url('/onboarding-details');
        if (!empty($users)) {
            foreach ($users as $user) {


                $template = Notification_content::where("signature", $signature)->first();
                $body =  @$template->content;

                $body = str_replace('{{link}}', $link, $body);

                notification::insert([
                    'title' => 'notification',
                    'user_id' => $user->id,
                    'program_id' => null,
                    'notification' => $body,
                    'type' => "user",
                    'user_type' =>  "user",
                ]);
            }
        }
    }
}
