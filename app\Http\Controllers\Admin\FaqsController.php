<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\FaqsModel;
use Hash;
use Mail;

DB::enableQueryLog();

// addactivitycategory.blade.php
class FaqsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'generalsetting','view')!=true){
            return redirect("/no-permission");
        }
        $faqs = FaqsModel::get();
        return view("admin.faqs.listfaqs", compact("faqs"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view("admin.faqs.addfaqs");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $time = date("is");
        $date = date("dmy");
        $comid = $time . $date;
        $data["question"] = $request->question;
        $data["description"] = $request->description;
        $data["status"] = $request->status;
        $data["type"] = $request->type;

        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = FaqsModel::insert($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Faqs successfully added",
                "redirect" => url("/faqs-list"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = decrypt_str($request->id);
        $data["question"] = $request->question;
        $data["description"] = $request->description;
        $data["type"] = $request->type;

        // $data["status"] = $request->status;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = FaqsModel::where(["id" => $id])->update($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Faqs successfully updated",
                "redirect" => url("/faqs-list"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    }

    public function statuschange(Request $request)
    {
        $id = $request->id;
        $record = FaqsModel::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = FaqsModel::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = FaqsModel::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function deletefaqs(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = FaqsModel::where("id", $id)->first();
            if ($record) {
                $res = FaqsModel::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewfaqs(Request $request)
    {
        $faqs_id = $request->id;
        $id = decrypt_str($faqs_id);

        $faqs = DB::table("tbl_faqs")
            ->where("id", $id)
            ->first();
        return view("admin.faqs.editfaqs", compact("faqs", "faqs_id"));
    }

    // // Function to generate the url for image.
    // public function uploadImage(Request $request)
    // {
    //     if ($request->hasFile('image') && $request->file('image')->isValid()) {
    //         $validated = $request->validate([
    //             'image' => 'required|mimes:jpeg,png,jpg,gif|max:2048',
    //         ]);
    //         $fileName = time() . '.' . $request->image->extension();
    //         $path = $request->image->move(public_path('uploads/faq_images'), $fileName);
    //         $url = asset('uploads/faq_images/' . $fileName);
    //         return response()->json(['url' => $url], 200);
    //     }
    //     return response()->json(['error' => 'Invalid Image Upload'], 400);
    // }
}
