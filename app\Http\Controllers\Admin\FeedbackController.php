<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\FeedbackModel;
use App\Helpers\DataTableHelper;
use Mail;
class FeedbackController extends Controller
{

    public function index($id,Request $request)
    {

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'tbl_program_feedbacks.id';
            }
            $qry = FeedbackModel::where("program_id", "=", $id)->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');


            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });




            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);

            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);
                $viewUser ='';
                $action = $this->generateActionButtons($encryptedStrId,$res);
                if(strlen($row->feedback) > 50){
                    $mess=substr($row->feedback, 0, 50).'...';

                }else{
                   $mess= $row->feedback;
                }
                if ($row->user) {
                    if($row->user->type==5){
                        $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    }else{
                        $viewUserRoute =  url('viewschooldetails/' . encrypt_str($row->user_id));
                    }

                    if($row->user->first_name){
                        $userName = $row->user->first_name . ' ' . $row->user->last_name;

                    }else{
                        $userName = $row->user->full_name ;

                    }
                    $viewUser = " <a target='_blank' href='{$viewUserRoute}'>{$userName}</a>";
                }

                $data[] = [
                    "id" => $i,
                    "user_id" => $viewUser,
                    "rating" => $row->rating,
                    "feedback" => $mess,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


    }

    private function generateActionButtons($encryptedStrId,$res)
    {
        $viewRoute = "javascript:void(0);";
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton =  '';


        $deleteRoute = route('admin.program.feedback.delete', ['eid' => $encryptedStrId]);



        $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";




        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$deleteButton}</div>";
    }

    public function delete($id,Request $request)
    {
        $did = decrypt_str($id);

        if (isset($id)) {
            $record = FeedbackModel::where("id", $did)->first();
            if ($record) {
                $res = FeedbackModel::where("id", "=", $did)->delete();

                if ($res) {
                    return response()->json([
                        "status" => true,
                        "message" => "Successfully deleted",
                        'reload' => false
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
