<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeDatatypeOnPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            //
        });

        DB::statement('ALTER TABLE platform_school_requirements MODIFY regular_days LONGTEXT NULL');
        DB::statement('ALTER TABLE platform_school_requirements MODIFY schedule_1_days LONGTEXT NULL');
        DB::statement('ALTER TABLE platform_school_requirements MODIFY schedule_2_days LONGTEXT NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            //
        });
        DB::statement('ALTER TABLE platform_school_requirements MODIFY regular_days LONGTEXT NULL');
        DB::statement('ALTER TABLE platform_school_requirements MODIFY schedule_1_days LONGTEXT NULL');
        DB::statement('ALTER TABLE platform_school_requirements MODIFY schedule_2_days LONGTEXT NULL');
    }
}
