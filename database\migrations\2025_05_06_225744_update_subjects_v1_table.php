<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSubjectsV1Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Step 1: Drop columns
        Schema::table('subjects_v1', function (Blueprint $table) {
            $table->dropColumn(['subject_code', 'title', 'description']);
        });

        // Step 2: Recreate columns as nullable
        Schema::table('subjects_v1', function (Blueprint $table) {
            $table->string('subject_code', 6)->nullable()->unique()->after('subject_area_id');
            $table->string('title')->nullable()->after('subject_code');
            $table->text('description')->nullable()->after('title');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subjects_v1', function (Blueprint $table) {
            // Drop the nullable columns
            $table->dropColumn(['subject_code', 'title', 'description']);
        });
        
        Schema::table('subjects_v1', function (Blueprint $table) {
            
            // Recreate the columns as NOT NULL (original state)
            $table->string('subject_code', 6)->unique()->after('subject_area_id'); // Not nullable
            $table->string('title')->after('subject_code'); // Not nullable
            $table->text('description')->after('title'); // Not nullable
    
            $table->index('subject_code'); // Optional, may be redundant
        });
    }
}
