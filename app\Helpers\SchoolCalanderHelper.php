<?php

namespace App\Helpers;

use App\Models\k12ConnectionClasses;
use App\ProgramNote;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;

class SchoolCalanderHelper
{

    public static $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    public static function getCalendarEvents($events, $tab)
    {

        return ($tab === 'today-classes')
            ? static::getTodayClassesEvents($events, $tab)
            : static::getMultiDayEvents($events, $tab);
    }

    private static function getTodayClassesEvents($events, $tab)
    {
        $classIds  = [];

        $start_date = date('Y-m-d');
        return $events->map(function ($event) use ($start_date,$tab, &$classIds) {
            $eventData = static::createEventData($event, $start_date, $tab);

            if (!empty($eventData)) {
                $programNote = ProgramNote::where(['program_id' => $event->id, 'class_date' => $start_date])->first();
                $classId = @$programNote->id ?? '';
                if (!in_array($classId, $classIds)) {
                    $eventsForDays = $eventData;
                    $classIds[] = $classId;
                }
            }
            return @$eventsForDays ?? [];
        });
    }

    private static function getMultiDayEvents($events, $tab)
    {

        $classIds  = [];
        return $events->flatMap(function ($event) use ($tab, &$classIds) {

            $start_date = new DateTime($event->start_date);
            $end_date = new DateTime($event->end_date);

            while ($start_date <= $end_date) {
                $eventData = static::createEventData($event, $start_date->format('Y-m-d'), $tab);
                if (!empty($eventData)) {
                    $programNote = ProgramNote::where(['program_id' => $event->id, 'class_date' => $start_date->format('Y-m-d')])->first();
                    $classId = @$programNote->id ?? '';
                    if (!in_array($classId, $classIds)) {
                        $eventsForDays[$classId] = $eventData;
                        $classIds[] = $classId;
                    }
                }
                $start_date->modify('+1 day');
            }
            return $eventsForDays ?? [];
        });
    }

    private static function createEventData($event, $start_date, $tab)
    {
        $limitChars = 14;
        $encryptedId = encrypt($event->id);

        $url = route('school.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$event->pivot->id]);

        $class = $event->userNotes()
            ->where('class_date', $start_date)
            ->whereNull('status')
        ->first();

        # if there are no classes
        if (!$class) {
            return [];
        }

        $isSub = $class->sub_user_id;

        $start_time = $class->start_time;
        $end_time = $class->end_time;

        $start_time = date('g:iA', strtotime($start_time));
        $end_time = date('g:iA', strtotime($end_time));

        $start_time_24 = date('H:i', strtotime($start_time));
        $end_time_24 = date('H:i', strtotime($end_time));

        return [
            'title' => 'Program Id - ' . $event->id,
            'start' => $start_date . ' ' . $start_time_24,
            'end' => $start_date . ' ' . $end_time_24,
            'url' => $url,
            'description' => Str::limit($event->address, $limitChars, ' ...'),
            'timeSlot' => $start_time . '-' . $end_time,
            'isSub' => $isSub,
        ];
    }

    public static function getClassCalendarEvents($events)
    {
        return static::getClassesEvents($events);
    }

    private static function getClassesEvents($events)
    {
        $classIds  = [];

        return $events->flatMap(function ($event) use ($classIds) {

            $start_date = new DateTime($event->start_date);
            $end_date = new DateTime($event->end_date);

            while ($start_date <= $end_date) {
                $eventData = static::createClassEventData($event, $start_date->format('Y-m-d'));
                if (!empty($eventData)) {
                    $class = k12ConnectionClasses::where(['program_id' => $event->id, 'class_date' => $start_date->format('Y-m-d')])->first();
                    $classId = $class->id ?? '';
                    if (!in_array($classId, $classIds)) {
                        $eventsForDays[$classId] = $eventData;
                        $classIds[] = $classId;
                    }
                }
                $start_date->modify('+1 day');
            }
            return $eventsForDays ?? [];
        });
    }

    private static function createClassEventData($event, $start_date)
    {
        $limitChars = 16;
        // $encryptedId = encrypt($event->id);

        // $url = route('school.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$event->pivot->id]);
        $currentDate = Carbon::now()->toDateString();
        $tab = '';
        $class = $event->userNotes()->where('class_date', $start_date)->first();

        # if there are no classes
        if (!$class) {
            return [];
        }
        // dd($currentDate , $start_date, $currentDate < $start_date);
        if ($currentDate == $start_date) {
            $tab = 'today';
        } elseif ($currentDate < $start_date) {
            $tab = 'upcoming';
        } elseif ($currentDate > $start_date) {
            $tab = 'previous';
        }

        if ($class->status === 'completed') {
            $tab = 'completed';
        }

        $userImage = static::getUserProfile($class->main_instructor_id);

        $start_time = $class->start_time;
        $end_time = $class->end_time;

        $start_time = date('g:iA', strtotime($start_time));
        $end_time = date('g:iA', strtotime($end_time));

        $start_time_24 = date('H:i', strtotime($start_time));
        $end_time_24 = date('H:i', strtotime($end_time));

        return [
            // 'title' => 'Program Id - ' . $event->id,
            'title' => Str::limit($event->name, $limitChars, ' ...'),
            'programName' => $event->name,
            'start' => $start_date . ' ' . $start_time_24,
            'end' => $start_date . ' ' . $end_time_24,
            // 'description' => Str::limit($event->address, $limitChars, ' ...'),
            'timeSlot' => $start_time . '-' . $end_time,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'tab' => $tab,
            'userImage' => $userImage,
            'userName' => Str::limit(k12username($class->main_instructor_id), 8, ' ...'),
            'userFullName' => k12username($class->main_instructor_id),
            'userId' => encrypt_str($class->main_instructor_id),
            'reqId' => encrypt_str($event->requirement_id),
            'proposalId' => userProposalId($class->main_instructor_id,$event->requirement_id),
        ];
    }

    private static function getUserProfile($userId)
    {
        $userImage = generateSignedUrl(k12userImg($userId));
        return $userImage;
    }
}
