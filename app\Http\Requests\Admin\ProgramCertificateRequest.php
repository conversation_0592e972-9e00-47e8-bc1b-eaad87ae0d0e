<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ProgramCertificateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'certificate.*' => 'required|max:255',
            'state.*' => 'required|max:255',
        ];
    }

    public function attributes()
    {
        return [

            'certificate.*' => 'certificate',
            'state.*' => 'state',
        ];
    }
}
