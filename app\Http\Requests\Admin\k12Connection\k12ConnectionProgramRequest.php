<?php

namespace App\Http\Requests\Admin\k12Connection;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;

class k12ConnectionProgramRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

        $rules = [
            'program_status' => 'required',
        ];
        $programId = $this->input('id');

        if ($this->input('program_status') === 'Publish') {
            $requiredFields = [
                'timezone' => 'required',
                'program_name' => [
                                    'required',
                                    Rule::unique('k12_connection_programs', 'name')
                                    ->ignore($programId),
                                    ],
                // 'address' => 'required',
                // 'city' => 'required',
                // 'state' => 'required',
                // 'zipcode' => 'required',
                // 'country' => 'required',
                //  'garde.*' => 'required',
                // 'cbo_id' => 'required',
                // 'district' => 'required',
                'school_name' => 'required',

                'subject_id' => 'required',
                'sub_subject_id' => 'required',
                'capacity' => 'required',
                'program_type' => 'required',
                'delivery_type' => 'required',
                'job_title.*' => 'required',
                'first_name.*' => 'required',
                'last_name.*' => 'nullable',
                'notes' => 'nullable',
                'cemail.*' => 'required|email',
                'phone.*' => 'required',
                'datesingle1' => 'required|date:Y-m-d|after_or_equal:datesingle',



            ];

            if ($this->input('is_imported') === '1') {

                $requiredFields['class_schedule'] = 'required|file|mimes:xlsx,xls|max:2048';
            }else{
                // Custom rule for at least one day required
                $requiredFields['days'] = ['sometimes', function ($attribute, $value, $fail)use($weekdays) {

                    foreach ($weekdays as $day) {
                        if (!empty($this->input($day . '.1')) || !empty($this->input($day . '.2'))) {
                            return;
                        }
                    }

                    $fail("At least one time slot's From and To fields are required.");
                }];
                foreach ($weekdays as $weekday) {
                    $requiredFields[$weekday . '.1'] = "nullable|required_with:{$weekday}.2";
                    $requiredFields[$weekday . '.2'] = "nullable|after_or_equal:{$weekday}.1|required_with:{$weekday}.1";
                }
            }



            if (!$this->input('id')) {
                $requiredFields['datesingle'] = 'required|date:Y-m-d';
            }


            if ($this->input('is_past') === '1') {
                $requiredFields['past_date'] = 'required|date:Y-m-d';
            }

            if (!$this->has('grade') && $this->input('program_status') === 'Publish') {
                // 'grade' is missing, add an error message to the validation error bag

                $requiredFields['grade.'] = 'required';
            }

            if ($this->input('delivery_type') == 'In-Person') {
                // $requiredFields['link'] = 'required|url';
                $requiredFields['address'] = 'required';
                $requiredFields['city'] = 'required';
                $requiredFields['state'] = 'required';
                $requiredFields['zipcode'] = 'required';
                $requiredFields['country'] = 'required';
            }

            $rules = array_merge($rules, $requiredFields);
        }
        if ($this->input('certifications') == '1') {
            $requiredFields['certifications_states'] = 'required';

            $rules = array_merge($rules, $requiredFields);
        }
        
        if ($this->input('certifications_states') == 'Other') {
            $requiredFields['other_certification_states'] = 'required';

            $rules = array_merge($rules, $requiredFields);
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'monday.1.required_with' => 'From time is required',
            'monday.2.required_with' => 'To time is required',
            'tuesday.1.required_with' => 'From time is required',
            'tuesday.2.required_with' => 'To time is required',
            'wednesday.1.required_with' => 'From time is required',
            'wednesday.2.required_with' => 'To time is required',

            'thursday.1.required_with' => 'From time is required',
            'thursday.2.required_with' => 'To time is required',

            'friday.1.required_with' => 'From time is required',
            'friday.2.required_with' => 'To time is required',

            'saturday.1.required_with' => 'From time is required',
            'saturday.2.required_with' => 'To time is required',

            'sunday.1.required_with' => 'From time is required',
            'sunday.2.required_with' => 'To time is required',
        ];
    }

    public function attributes()
    {
        return [
            'datesingle' => 'Start date',
            'datesingle1' => 'End date',
            'first_name.*' => 'first name',
            'job_title.*' => 'job title',
            'last_name.*' => 'last name',
            'cemail.*' => 'email',
            'phone.*' => 'phone',
            'grade.' => 'grade level',
            // 'cbo_id' => 'cbo',
            'subject_id' => 'subject',
            'sub_subject_id' => 'sub subject',
            'monday.1' => 'monday from',
            'monday.2' => 'monday to',
            'tuesday.1' => 'tuesday from',
            'tuesday.2' => 'tuesday to',
            'wednesday.1' => 'wednesday from',
            'wednesday.0' => 'wednesday from',
            'wednesday.2' => 'wednesday to',
            'thursday.1' => 'thursday from',
            'thursday.2' => 'thursday to',
            'friday.1' => 'friday from',
            'friday.2' => 'friday to',
            'saturday.1' => 'saturday from',
            'saturday.2' => 'saturday to',
            'sunday.1' => 'sunday from',
            'sunday.2' => 'sunday to',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     */
    protected function failedValidation(Validator $validator)
    {
        // Manually check if 'grade' is present in the request
        if (!$this->has('grade') && $this->input('program_status') === 'Publish') {
            // 'grade' is missing, add an error message to the validation error bag
            // $validator->errors()->add('grade.', 'The grade field is required.');
        }

        parent::failedValidation($validator);
    }
}
