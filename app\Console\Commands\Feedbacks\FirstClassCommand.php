<?php

namespace App\Console\Commands\Feedbacks;

use App\EmailTemplate;
use App\ProgramNote;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class FirstClassCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feedback:first-class';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ask feedback from instructors after first class of an program';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $class_date = now()->toDateString();
        $current_time = now('America/Los_Angeles')->toTimeString();

        // Subquery to find the earliest class_date for each program
        $subquery = ProgramNote::select(DB::raw('MIN(class_date) as first_class_date'), 'program_id')
            ->groupBy('program_id');

        // Join with the original table to get the full details of the first class
        $firstClasses = ProgramNote::joinSub($subquery, 'first_classes', function ($join) {
            $join->on('program_notes.program_id', '=', 'first_classes.program_id')
                ->on('program_notes.class_date', '=', 'first_classes.first_class_date');
        })
            ->where('class_date', '=', $class_date) // Ensure the class_date is greater than the current time
            ->where('end_time', '>', $current_time) // Ensure the class_date is today
            ->where('program_notes.feedback_sent', false)
            ->with('user', 'program')
            ->whereHas('user')
            ->dd();

            $template = EmailTemplate::find(19);
        $body =  $template->description;

            foreach ($firstClasses as $class) {
                if($class->user->email_notification==1){

                $full_name = $class->user->first_name . ' ' . $class->user->last_name;
                $email = $class->user->email;

                $body = str_replace('{{ NAME }}', $full_name, $body);
                $body = str_replace('{{ program_name }}', @$class->program->name, $body);

                $subject = $template->subject;
                $data = array('template' => $body);
                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
                $class->feedback_sent = true;
                $class->save();
            }

        }
    }
}
