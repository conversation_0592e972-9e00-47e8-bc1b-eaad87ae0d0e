<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Carbon\Carbon;

class TimeCast implements CastsAttributes
{
    public function get($model, $key, $value, $attributes)
    {
        // info (config('app.timezone'));
        // return $value ;
        return $value ? Carbon::parse($value)->format('H:i:00') : null;
        // return $value ? Carbon::parse($value)->timezone(config('app.timezone'))->format('H:i:00') : null;
    }

    public function set($model, $key, $value, $attributes)
    {
        return $value ? Carbon::createFromFormat('H:i:s', $value)->setTimezone('America/Los_Angeles')->format('H:i:00') : null;
    }
}
