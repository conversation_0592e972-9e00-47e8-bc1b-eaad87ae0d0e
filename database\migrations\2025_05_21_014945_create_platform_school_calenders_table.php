<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlatformSchoolCalendersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('platform_school_calenders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('school_id')->nullable();
            $table->foreign('school_id')->references('id')->on('users')->onDelete('cascade');
            $table->unsignedBigInteger('district_id')->nullable();
            $table->enum('type', ['school', 'district'])->default('school');
            $table->string('calender_url')->nullable();
            $table->boolean('active')->default(true);
            $table->string('lastUpdatedFrom')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('platform_school_calenders');
    }
}
