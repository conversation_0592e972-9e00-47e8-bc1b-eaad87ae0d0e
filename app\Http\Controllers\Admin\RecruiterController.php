<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\Users;
use App\Permission;

use Session;
use Hash;
use Mail;
use Illuminate\Support\Facades\Crypt;

DB::enableQueryLog();
class RecruiterController extends Controller
{
    public function index(Request $request)
    {
        $admin =Users::where("type", "=", "4")
            ->orderBy("id", "desc")
            ->get();

        return view("admin.recruiter.recruiter_list", compact("admin"));
    }

    public function add_recruiter(Request $request)
    {
        $role = DB::table("tbl_roles")
            ->orderBy("id", "asc")
            ->get();
        return view("admin.recruiter.add_recruiter", compact("role"));
    }
    public function save_admin(Request $request)
    {
        $email = $request->email;
        $userExits = Users::where("email", "=", $email)->get();
        if (count($userExits)) {
            return response()->json([
                "success" => false,
                "message" => "Email already exits",
            ]);
        }
        $obj = [];
        $length = 6;
        $randpassword = substr(
            str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $user_id = substr(str_shuffle("0123456789"), 1, $length);
        $obj["user_id"] = $user_id;
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["email"] = $request->input("email");
        $obj["gender"] = $request->input("gender");
        $obj["dob"] = date("Y-m-d", strtotime($request->input("dob")));
        $obj["type"] = $request->input("type");
        $obj["about"] = $request->input("about");
        $obj["password"] = Hash::make($randpassword);
        // $obj["passwordStr"] = $randpassword;
        $obj["status"] = "1";
        $obj["created_at"] = date("Y-m-d H:i:s");
        $obj["updated_at"] = date("Y-m-d H:i:s");
        if ($request->hasfile("file_data")) {
            $file = $request->file("file_data");
            $extension = $file->getClientOriginalExtension(); // getting image extension
            $logopic = "image-" . time() . "." . $extension;
            $destinationPath = public_path("/uploads/institute/");
            $file->move($destinationPath, $logopic);
            $obj["image"] = url("/uploads/institute/" . $logopic);
        }
        $save = Users::insertGetId($obj);
        if ($save) {
            $managment = [
                "dashboard",
                "profile",
                "role",
                "staff",
                "manageschool",
                "manageinstructor",
                "recruiter",
                "manageprogram",
                "generalsetting",
            ];

            foreach ($managment as $key => $value) {
                $objP["user_id"] = $save;
                $objP["module"] = $value;
                $objP["permission_setting"] = "[]";
                $saveP = Permission::insertGetId($objP);
            }
        }
        $dataEmail = [
            "email" => $request->email,
            "subject" => "Created Client",
            "mailbody" => "New client",
            "first_name" => $request->first_name,
            "last_name" => $request->last_name,
            "randpassword" => $randpassword,
            "redirect" => url("/admin"),
        ];

        // Mail::send('admin.email-temp.admin_sinup', $dataEmail, function ($message) use ($email) {
        //     $message->to($email)->subject('Whizara Support');
        //     $message->from('<EMAIL>');
        // });
        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Admin  successfully added",
                "redirect" => url("/add-admin-management"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }
    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Users::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }
    public function delete_admin(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = Users::where("id", $id)->first();
            if ($record) {
                $res = Users::where("id", "=", $id)->delete();
                $res1 = Permission::where("user_id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
    public function edit_admin(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $admin = Users::where("id", $id)->first();
        $role = DB::table("tbl_roles")
            ->orderBy("id", "asc")
            ->get();
        return view("admin.admin-management.edit_admin", [
            "admin" => $admin,
            "role" => $role,
        ]);
    }
    public function admin_update(Request $request)
    {
        $obj = [];
        $obj["first_name"] = $request->input("first_name");
        $obj["last_name"] = $request->input("last_name");
        $obj["email"] = $request->input("email");
        $obj["gender"] = $request->input("gender");
        $obj["dob"] = date("Y-m-d", strtotime($request->input("dob")));
        $obj["type"] = $request->input("type");
        $obj["about"] = $request->input("about");
        $id = $request->input("id");
        Users::where("id", $id)->update($obj);
        return response()->json([
            "success" => true,
            "message" => "Successfully update",
        ]);
    }
    public function adminimagechange(Request $request)
    {
        if ($request->hasFile("filedata")) {
            $image = $request->file("filedata");
            $name = time() . "." . $image->getClientOriginalExtension();
            $destinationPath = public_path("/uploads/institute");
            $image->move($destinationPath, $name);
            $destinationPathname = url("/uploads/institute/" . $name);
        }
        $id = $request->input("id");
        $obj = [];
        $obj["image"] = $destinationPathname;
        $result = Users::where("id", $id)
            ->update($obj);
        if ($result) {
            $data["success"] = true;
            $data["message"] = "Image Updated successfully";
            $data["img"] = $name;
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }

    public function viewdetails($id)
    {
        $user_id = decrypt_str($id);

        $user_list = Users::where("users.id", $user_id)
            ->first();

        return view(
            "admin.admin-management.viewdetails",
            compact("user_list", "user_id")
        );
    }

    public function change_password($id)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $user_id = decrypt_str($id);
        return view(
            "admin.admin-management.changepassword",
            compact("user_id")
        );
    }

    public function updateChangePassword(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $new_password = $request->input("new_password");
        $confirm_password = $request->input("confirm_password");
        $id = $request->input("userid");

        $login = Users::where("id", $id)->first();

        if (!empty($login)) {
            if ($new_password == $confirm_password) {
                $insertpwd = bcrypt($new_password);
                Users::where("id", $id)->update([
                    "password" => $insertpwd,
                    // "passwordStr" => $new_password,
                ]);

                return response()->json([
                    "success" => true,
                    "message" => "Password successfully changed",
                    "redirect" => url("/viewdetails/" . encrypt_str($id)),
                ]);
            } else {
                return response()->json([
                    "success" => true,
                    "message" =>
                        "New password and Confirm password does not matchd",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Password does not exist",
            ]);
        }
    }
}
