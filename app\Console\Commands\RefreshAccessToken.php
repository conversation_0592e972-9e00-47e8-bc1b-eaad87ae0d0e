<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;
use Carbon\Carbon;
use QuickBooksOnline\API\DataService\DataService;

class RefreshAccessToken extends Command
{
    protected $signature = 'quickbooks:check-token';
    protected $description = 'Check quickbooks token for expiration and regenrate';
    public function handle()
    {
        $credentials = DB::table('quickbooks_credentials')->where('id', 1)->first();

        if (Carbon::parse($credentials->token_expires_at)->diffInMinutes(now()) <= 15) {
            $this->generateNewToken();
        }
        
    }

    public function generateNewToken()
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);

        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $newAccessTokenObj = $OAuth2LoginHelper->refreshToken();

        DB::table('quickbooks_credentials')
            ->where('id', 1)
            ->update([
                'access_token' => $newAccessTokenObj->getAccessToken(),
                'refresh_token' => $newAccessTokenObj->getRefreshToken(),
                // 'token_expires_at' => Carbon::now()->addSeconds($newAccessTokenObj->getExpiresIn())
                'token_expires_at' => Carbon::createFromTimestamp($newAccessTokenObj->getAccessTokenExpiresAt())
            ]);

        $this->info('QuickBooks access token refreshed successfully.');
    }
}