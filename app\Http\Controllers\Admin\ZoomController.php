<?php
// app/Http/Controllers/ZoomController.php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ZoomService;
use App\Programs;
use App\ZoomModel;
use App\ZoomlinkModel;
use App\program_slots;
use App\ProgramNote;

use Carbon\Carbon;
use DateTime;

class ZoomController extends Controller
{
    protected $zoomService;

    public function __construct(ZoomService $zoomService)
    {
        $this->zoomService = $zoomService;
    }

    public function createSubAccount(Request $request)
    {
        $data = [
            'action' => 'create',
            'user_info' => [
                'email' => '<EMAIL>',
                'type' => 2, // Type 2 indicates a subaccount
            ],
        ];

        $response = $this->zoomService->createSubAccount($data);

        return response()->json($response);
    }

    public function createMeeting(Request $request)
    {
        $request->validate(
            [
                'zoom_account' => 'required',
                'meeting_title' => 'required',
                'duration' => 'required',
                'start_time' => 'required',
                'end_time' => 'required',

            ]
        );

        $res = ZoomModel::where("id", $request->zoom_account)->first();
        $zoomAccount['account_key'] = $res->account_key;
        $zoomAccount['app_key'] = $res->app_key;
        $zoomAccount['secret_key'] = $res->secret_key;
        $zoomlink = ZoomlinkModel::where("zoom_id", $request->zoom_account)->where("start_date", $request->datesingle)->where("start_time", $request->end_time)->first();

        if (!empty($zoomlink)) {

            return response()->json(['status' => false, 'message' => "Meeting link already created in this account"]);
        } else {

            $link["zoom_id"] = $request->zoom_account;
            $link["start_date"] = $request->datesingle;
            $link["start_time"] = $request->end_time;

            ZoomlinkModel::insertGetId($link);
        }
       
        $timestamp = strtotime($request->datesingle . ' ' . $request->start_time);
        $new_date_format = date("Y-m-d\TH:i:s", $timestamp);
        $endtimestamp = strtotime($request->datesingle1 . ' ' . $request->end_time);
        $enddate = date("Y-m-d\TH:i:s", $endtimestamp);


        $totalduration = $request->duration;
        $data = [
            'topic' => $request->meeting_title,
            'type' => 2, // 2 for scheduled meeting
            'start_time' => $new_date_format, // Use the desired start time in ISO 8601 format
            'end_time' => $enddate, // Use the desired end time in ISO 8601 format
            'duration' => $totalduration, // Meeting duration in minutes
            'timezone' => 'UTC', // Specify the timezone
            'password' => '', // No password for the meeting
            // 'settings' => array(
            //     'host_video' => true,
            //     'participant_video' => true,
            //     'join_before_host' => true,
            //     'auto_recording' => 'local', 
            // ),
        ];

        $response = $this->zoomService->createMeeting($data, $zoomAccount);
        return response()->json(['status' => true, 'message' => "Meeting link created successfully", 'data' => $response]);
    }
    public function getAcoountId(Request $request)
    {
        $data = [];

        $response = $this->zoomService->getAcoountId($data);

        return response()->json($response);
    }

    public function editMeeting(Request $request)
    {
        $request->validate(
            [

                'link_type' => 'required',

            ]
        );

        if ($request->link_type == 'Zoom') {

            $request->validate(
                [
                    'zoom_account' => 'required',

                ]
            );
        }


        if ($request->link_type == 'Manual') {

            $request->validate(
                [
                    'meeting_link' => 'required',

                ]
            );

            $datares["link"] = $request->meeting_link;
            Programs::where("id", $request->program_id)->update($datares);
            return response()->json(['status' => true, 'message' => "Meeting link created successfully", 'reload' => true]);
        }



        $currentDate = now()->toDateString();

        $firstClass = ProgramNote::where('class_date', '>=', $currentDate)
            // ->whereNull(['note', 'status'])
            ->where('program_id', $request->program_id)
            ->first();



        /*         $maxTime = program_slots::where("program_id", $request->program_id)->orderBy('end_time', 'desc')->first();
                $minTime = program_slots::where("program_id", $request->program_id)
                    ->orderBy('start_time', 'asc')->first();
        */


        if (!empty($firstClass)) {
            $zoomAccount['zoom_account'] = $request->zoom_account;
            $res = ZoomModel::where("id", $request->zoom_account)->first();
            $zoomAccount['account_key'] = $res->account_key;
            $zoomAccount['app_key'] = $res->app_key;
            $zoomAccount['secret_key'] = $res->secret_key;
            $programdetails = Programs::where("id", $request->program_id)->first();

            $zoomlink = ZoomlinkModel::where("zoom_id", $request->zoom_account)->where("start_date", $firstClass->class_date)->where("start_time", $firstClass->start_time_utc)->first();

            if (!empty($zoomlink)) {

                return response()->json(['status' => false, 'message' => "Meeting link already created in this account", 'data' => 'Manual']);
            } else {


                $link["zoom_id"] = $request->zoom_account;
                $link["start_date"] = $firstClass->class_date;
                $link["start_time"] = $firstClass->start_time_utc;

                ZoomlinkModel::insertGetId($link);
            }

            $program_id = $request->program_id;


            $timestamp = strtotime($firstClass->class_date->toDateString() . ' ' . $firstClass->start_time_utc);
            $new_date_format = date("Y-m-d\TH:i:s", $timestamp);

            $endtimestamp = strtotime($programdetails->end_date . ' ' . $firstClass->end_time_utc);
            $enddate = date("Y-m-d\TH:i:s", $endtimestamp);

            $days = program_slots::where("program_id", $request->program_id)->pluck('slot_day')->implode(',');

            $startTime = Carbon::parse($programdetails->start_date . ' ' . $firstClass->start_time_utc);
            $endTime = Carbon::parse($programdetails->start_date . ' ' . $firstClass->end_time_utc);
            $durationInMinutes = $endTime->diffInMinutes($startTime);

            $data = [
                'topic' => $programdetails->name,
                'type' => 2, // 2 for scheduled meeting
                'start_time' => $new_date_format, // Use the desired start time in ISO 8601 format
                // "start_time"=> "2024-05-09T09:00:00",
                'duration' => $durationInMinutes, // Meeting duration in minutes
                'timezone' => $programdetails->timezone, // Specify the timezone
                'recurrence' => [
                    'type' => 2,
                    'repeat_interval' => 1,
                    'weekly_days' => $days,
                    'end_date_time' => $enddate,
                    // 'end_date_time' => "2024-06-09T09:00:00",
                    // 'end_times' => 10

                ]
            ];

            $response = $this->zoomService->createMeeting($data, $zoomAccount);

            $datares["link"] = $response['start_url'];
            $datares["joinlink"] = $response['join_url'];

            Programs::where("id", $program_id)->update($datares);
            return response()->json(['status' => true, 'message' => "Meeting link created successfully", 'data' => $response, 'reload' => true]);
        } else {
            return response()->json(['status' => false, 'message' => "Please create first slots"]);
        }
    }
}
