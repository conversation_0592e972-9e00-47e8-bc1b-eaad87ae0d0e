<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\Roles;
use App\Permission;
use Session;
use Hash;
use Mail;
use Illuminate\Support\Facades\Crypt;

DB::enableQueryLog();
class RoleController extends Controller
{
    public function index(Request $request)
    {
        $admin = DB::table("tbl_roles")
            ->where("id", "!=", "5")
            ->where("id", "!=", "6")
            ->where("id", "!=", "7")
            ->where("deleted_status", "!=", 1)
            ->orderBy("id", "asc")

            ->get();
        return view("admin.role.rolelist", compact("admin"));
    }

    public function addrole(Request $request)
    {
        return view("admin.role.add_role");
    }

    public function save_role(Request $request)
    {
      
        $name = $request->name;
        $status = $request->input("status");
        if ($name != "") {
            $districtExits = Roles::where("name", "=", $name)->get();
            if (count($districtExits)) {
                return response()->json([
                    "success" => false,
                    "message" => "Role already exits",
                ]);
            } else {
                $data["name"] = $request->name;

                $data["status"] = $request->input("status");
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = Roles::insertGetId($data);

                if ($save) {
                    $managment = DB::table("tbl_modules")
                        ->orderBy("sort_order", "asc")
                        ->get();
                    foreach ($managment as $key => $value) {
                        $objP["user_id"] = $save;
                        $objP["module"] = $value->type;
                        $objP["permission_setting"] = "[]";
                        $saveP = Permission::insertGetId($objP);
                    }
                }

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Role successfully created",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    public function edit_role(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $admin = Roles::where("id", $id)->first();
        return view("admin.role.edit_role", ["admin" => $admin]);
    }

    public function role_update(Request $request)
    {
        $obj = [];
        $obj["name"] = $request->input("name");
        $obj["status"] = $request->input("status");

        $id = $request->input("id");
        Roles::where("id", $id)->update($obj);
        return response()->json([
            "success" => true,
            "message" => "Successfully update",
        ]);
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Roles::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Roles::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Roles::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function roledelete(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = Roles::where("id", $id)->first();
            if ($record) {
                $data["deleted_status"] = "1";
                $data["status"] = "0";

                $res = Roles::where("id", $id)->update($data);
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
