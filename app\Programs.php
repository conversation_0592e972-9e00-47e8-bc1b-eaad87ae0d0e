<?php

namespace App;

use App\Helpers\CustomHelper;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Facade\FlareClient\Stacktrace\File;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;

class Programs extends Model
{
    protected $table = 'tbl_programs';

    protected $fillable = [
        'name', 'start_date', 'end_date', 'program_status', 'district', 'school_name', 'program_type', 'delivery_type', 'grade', 'capacity', 'certifications', 'background_checks', 'medicalrequirements', 'address', 'city', 'state', 'zip_code', 'timezone', 'country', 'notes', 'status', 'created_by', 'lat', 'lng', 'link', 'cbo_id', 'subject_id', 'sub_subject_id', 'is_imported', 'no_classes_start_date', 'no_classes_end_date', 'past_date', 'is_past'
    ];
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function subSubject()
    {
        return $this->belongsTo(SubsubjectModel::class, 'sub_subject_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'tbl_invite_programs', 'program_id', 'user_id')->withPivot('id', 'status', 'is_approved', 'admin_type', 'type', 'replacement_type', 'has_requested', 'parent_id', 'is_makeup', 'deadline', 'is_sub_only','is_standby','requested_by');
    }
    public function invite()
    {
        return $this->hasOne(invite_programs::class, 'program_id');
    }

    public function invites()
    {
        return $this->hasMany(invite_programs::class, 'program_id');
    }

    public function contacts()
    {
        return $this->hasOne(program_contact_info::class, 'program_id')->orderBy('tbl_program_contact_info.id', 'DESC');
    }

    public function user()
    {
        return $this->hasOneThrough(User::class, invite_programs::class, 'program_id', 'id', 'id', 'user_id');
    }
    public function mainUser()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '1')
                ->orWhere('tbl_invite_programs.admin_type', '=', '1');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }
    public function subUser()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '0')
                ->orWhere('tbl_invite_programs.admin_type', '=', '0');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }
    public function standByUsers()
    {
        return $this->hasManyThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_approved', '=', '1')
            ->where('tbl_invite_programs.is_standby', '=', '1')
            ->where(function ($query) {
                $query->where('tbl_invite_programs.type', '=', '0')
                    ->orWhere('tbl_invite_programs.admin_type', '=', '0');
            })->orderBy('tbl_invite_programs.id', 'DESC');
    }

    public function missingStandBy()
    {
        return $this->hasMany(invite_programs::class, 'program_id')->where('is_standby', 1)->whereNull('is_approved')->where('deadline', '>=', now())->whereNull('status');
    }

    public function missingSubInstructor()
    {
        return $this->hasMany(invite_programs::class, 'program_id')->where('is_standby', 0)->where('is_makeup', 0)->where('admin_type', 0)->whereNull('is_approved')->where('deadline', '>=', now())->whereNull('status');
    }

    public function standByUsersActive()
    {
        return $this->hasManyThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_standby', '=', '1')
        // ->where('tbl_invite_programs.status', '=', '1')
            ->where(function ($query) {
                // $query->where('tbl_invite_programs.type', '=', '0')
                //     ->orWhere('tbl_invite_programs.admin_type', '=', '0');
            })->orderBy('tbl_invite_programs.id', 'DESC');
    }
    public function standByUser()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_standby', '=', '1')
        ->where('tbl_invite_programs.status', '=', '1');
    }

    public function mainAssignedUser()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_approved', '=', '1')->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '1')
                ->orWhere('tbl_invite_programs.admin_type', '=', '1');
        })->where(function ($query) {
            $query->where('tbl_invite_programs.status', '1')
                ->orWhereNull('tbl_invite_programs.status');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }

    public function mainAssignedUserName()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_approved', '=', '1')->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '1')
                ->where('tbl_invite_programs.admin_type', '=', '1');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }

    public function mainAssignedUserNameReq()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_approved', '=', '1')->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '1');
                // ->Where('tbl_invite_programs.admin_type', '=', '1');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }
    public function subAssignedUser()
    {
        return $this->hasOneThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '0')
                ->orWhere('tbl_invite_programs.admin_type', '=', '0')
                ->whereNull('tbl_invite_programs.user_id')
                ->orWhere('tbl_invite_programs.user_id', '')
                ->whereNotNull('tbl_invite_programs.requested_by')
                ->where('tbl_invite_programs.replacement_type', 0);
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }
    public function subAssignedUsers()
    {
        return $this->hasManyThrough(
            User::class,
            invite_programs::class,
            'program_id',
            'id',
            'id',
            'user_id'
        )->where('tbl_invite_programs.is_approved', '=', '1')->where('tbl_invite_programs.is_standby', '!=', '1')->where(function ($query) {
            $query->where('tbl_invite_programs.type', '=', '0')
                ->orWhere('tbl_invite_programs.admin_type', '=', '0');
        })->orderBy('tbl_invite_programs.id', 'DESC');
    }


    public function school()
    {
        return $this->belongsTo(User::class, 'school_name');
    }

    public function cbo()
    {
        return $this->belongsTo(cbo::class, 'cbo_id');
    }

    public function schedules()
    {
        return $this->hasMany(program_slots::class, 'program_id');
    }
    public function certificates()
    {
        return $this->hasMany(ProgramCertificate::class, 'program_id');
    }

    public static function getList($user, $status = null, $request = null)
    {

        if (!auth()->check()) {
            $user = new User();
        }
        $qry = $user->programs();
        if ($status) {
            if (is_array($status)) {
                $qry->wherePivotIn('status', $status);
            } else {
                $qry->wherePivot('status', $status);
            }
        } else {
            $qry->wherePivot('status', null);
        }
        if ($request) {
            $qry->filterList($request);
        }
        $qry->groupBy('tbl_invite_programs.program_id');
        $qry->where('program_status', '!=', 'Draft');

        return $qry;
    }

    public static function getListInvite($user, $status = null, $request = null)
    {
        if (!auth()->check()) {
            $user = new User();
        }
        $qry = $user->programs();
        if ($status) {
            if (is_array($status)) {
                $qry->wherePivotIn('status', $status);
            } else {
                $qry->wherePivot('status', $status);
            }
        } else {
            $qry->wherePivot('status', null);
        }
        if ($request) {
            $qry->filterList($request);
        }
        // $qry->groupBy('tbl_invite_programs.program_id');
        $qry->where('program_status', '!=', 'Draft');

        return $qry;
    }


    public function scopeFilterList($qry, $request)
    {
        $searchValue = $request->input('search.value');
        $columns = $request->columns;
        $qry->with('school', 'schedules');

        $qry->where(function ($que) use ($searchValue, $columns, $request) {
            if (!empty($searchValue)) {

                // $que->whereHas('school', function ($query) use ($searchValue) {
                //     $query->orWhere('full_name', 'like', "%{$searchValue}%");
                // });

                foreach ($columns as $column) {
                    if (isset($column['searchable']) && $column['searchable'] == 'true') {
                        $que->orWhere($column['data'], 'like', "%{$searchValue}%");
                    }
                }
            }

            if ($request->filled('district')) {
                $que->where('district', $request->district);
            }

            if ($request->filled('school_name')) {
                $que->where('school_name', $request->school_name);
            }

            if ($request->filled('delivery_type')) {
                $que->where('delivery_type', $request->delivery_type);
            }
            if ($request->filled('state')) {
                $que->where('state', 'like', "%{$request->state}%");
            }
            if ($request->filled('city')) {
                $que->orWhere('city', 'like', "%{$request->city}%");
            }
            if ($request->filled('name')) {
                // $que->orWhere('sub_subject_id', 'like', "%{$request->name}%");
                $name = $request->input('name');

                $subsubjectid = sub_subjects::where('tbl_sub_subjects.name', 'like', "%{$name}%")->pluck('id')->toArray();
                if(!empty($subsubjectid)){
                    $que->whereIn('sub_subject_id',$subsubjectid);
                }

            }
            if ($request->filled('grade')) {
                $grade = $request->input('grade');

                $que->whereHas('classes', function ($query) use ($grade) {
                    $query->where('program_class.class_id', $grade);
                });
            }

            if ($request->filled('start_time')) {
                $searchVal = formatTime($request->input('start_time'));
                $que->whereHas('userNotes', function ($query) use ($searchVal) {
                    $query->where('start_time',  $searchVal);
                });
            }
            if ($request->filled('end_time')) {
                $searchVal = formatTime($request->input('end_time'));

                $que->whereHas('userNotes', function ($query) use ($searchVal) {
                    $query->where('end_time', $searchVal);
                });
            }
            if ($request->filled('current_date')) {
                $current_date = $request->current_date;

                $que->whereDate('start_date', '<=', $current_date)
                    ->whereDate('end_date', '>=', $current_date);
            }

            if ($request->filled('start_date')) {
                // $start_date = $request->start_date;
                $start_date =   date("Y-m-d", strtotime($request->start_date));

                $que->whereDate('start_date', '=', $start_date);
            }

            if ($request->filled('end_date')) {
                // $end_date = $request->end_date;
                $end_date =   date("Y-m-d", strtotime($request->end_date));
                $que->whereDate('end_date', '=', $end_date);
            }
        });

        return $qry;
    }

    public static function getEvents($data, $user)
    {
        $qry = $user->programs();
        if (isset($data['status'])) {
            if (is_array($data['status'])) {
                $qry->wherePivotIn('status', $data['status']);
            } else {

                $qry->wherePivot('status', $data['status']);
            }
        } else {
            $qry->wherePivot('status', null);
        }


        return $qry;
    }

    public function classes()
    {
        return $this->belongsToMany(Classes::class, 'program_class', 'program_id', 'class_id');
    }


    public function getFormattedClassesAttribute()
    {
        $classes = $this->classes()->pluck('class_name');

        return $classes->isNotEmpty() ? $classes->implode(', ') : 'No classes';
    }

    public function dateSchedule($day)
    {
        return $this->schedules()->where('slot_day', $day);
    }
    public function dateClassSchedule($user_id, $class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date)
            ->where(function ($q) use ($user_id) {
                $q->where('user_id', $user_id)
                    ->orWhere('sub_user_id', $user_id);
            });
    }


    public function dateAllClassSchedulealertsub($user_id, $class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date)
            ->whereNull('status');
    }
    public function dateClassScheduleschool($class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date);
    }

    public function dateAllClassSchedule($user_id, $class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date)


            ->where(function ($q) use ($user_id) {
                $q->where('user_id', $user_id)
                  ->orWhere('sub_user_id', $user_id);
            });
    }

    public function dateAllClassSchedulealert($user_id, $class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date);


            // ->where(function ($q) use ($user_id) {
            //     $q->where('user_id', $user_id)
            //       ->orWhere('sub_user_id', $user_id);
            // });
    }

    public function dateinprogressClassSchedulealert($user_id, $class_date)
    {
        return $this->userNotes()
            ->where('class_date', $class_date)

            ->where(function ($q) use ($user_id) {
                $q->where('user_id', $user_id)
                  ->orWhere('sub_user_id', $user_id);
            });
    }
    public function dateClassAdminSchedule($class_date)
    {
        return $this->userNotes()->where(['class_date' => $class_date]);
    }
    public function dateClassUniqueSchedule($user_id)
    {
        return $this->userNotes()
        ->groupBy('program_notes.day');
    }

    public function dateClassUniqueSchedulenotes($notes)
    {
        return $this->userNotes()->where(function ($query) use ($notes) {
            $query->whereIn('program_notes.id', $notes);
        })->groupBy('program_notes.day');
    }

    public function userNotes()
    {
        return $this->hasMany(ProgramNote::class, 'program_id')->where(function ($query) {
            $query->whereNull('program_notes.status')
                ->orWhereNotIn('program_notes.status', [2,4]);
        })->orderBy('program_notes.class_date');
    }

    public function userNotesmakup()
    {
        return $this->hasMany(ProgramNote::class, 'program_id')->where(function ($query) {
            $query->whereNull('program_notes.status')
                ->orWhereNotIn('program_notes.status', [0,2,4]);
        })->orderBy('program_notes.class_date');
    }

    public function userNoteswhitoutmakup()
    {
        return $this->hasMany(ProgramNote::class, 'program_id')->where(function ($query) {
            $query->whereNull('program_notes.status')
                ->orWhereNotIn('program_notes.status', [0,2,4]);
        })->where('program_notes.is_makeup_class', null)->orderBy('program_notes.class_date');
    }

    public function userNotesWhioutMakup()
    {
        return $this->hasMany(ProgramNote::class, 'program_id')->where(function ($query) {
            $query->whereNull('program_notes.status')
                ->orWhereNotIn('program_notes.status', [0,2,4]);
        })->orderBy('program_notes.class_date');
    }

    public function userNotesWhioutMakupdesc()
    {
        $currentDate = now()->toDateString();
        return $this->hasOne(ProgramNote::class, 'program_id')->where('class_date','>=',$currentDate)->where('program_notes.is_makeup_class', null)->orderBy('program_notes.id','desc');
    }

    public function meetings()
    {
        return $this->hasMany(Meeting::class, 'program_id');
    }

    public function programNoteAmounts()
    {
        return $this->hasMany(ProgramNoteAmount::class, 'program_id');
    }

    public function programNoteStudents()
    {
        return $this->hasMany(ProgramNoteStudent::class, 'program_id');
    }

    public function reimbursements()
    {
        return $this->hasMany(Reimbursement::class, 'program_id');
    }


    public function programAdminNotes()
    {
        return $this->hasMany(ProgramAdminNote::class, 'program_id');
    }

    public function nextCommingProgram(){
        $currentDate = now()->toDateString();
        return $this->hasOne(ProgramNote::class, 'program_id')
        ->whereNull('is_makeup_class')
        ->where(function ($qu) {
            $qu->where('status', '!=', 4)
            ->orWhereNull('status');
        })
        ->orderByRaw("CASE 
            WHEN class_date = ? THEN 1
            WHEN class_date > ? THEN 2
            ELSE 3 END", [$currentDate, $currentDate])
        ->orderByRaw("
            CASE 
                WHEN class_date <= ? THEN class_date
                ELSE NULL
            END DESC, 
            CASE 
                WHEN class_date > ? THEN class_date
                ELSE NULL
            END ASC
        ", [$currentDate, $currentDate]);;
    }

    public function activeMainUser()
    {
        $programNote = $this->nextCommingProgram()
            ->with('user')
            ->first();

        return $programNote ? $programNote->user : null;
    }

    public function activeUser()
    {
        return $this->hasOneThrough(User::class, invite_programs::class, 'program_id', 'id', 'id', 'user_id')
            ->where(['tbl_invite_programs.has_requested' => '0', 'tbl_invite_programs.status' => '1', 'tbl_invite_programs.is_approved' => '1'])
            ->whereNotNull('tbl_invite_programs.type');
    }

    public  function createNotes($user_id, $invite = null)
    {
        if ($this->userNotesWhioutMakup->isNotEmpty()) {
            if ($invite->is_makeup == '1') {
                $newNote = ProgramNote::firstWhere('invite_id', $invite->id);
                if ($newNote === null) {
                    return true;
                }

                $note = ProgramNote::find($newNote->parent_id);
                $note->reassigned_to = $user_id;
                $note->is_reassigned = 2;
                if ($invite->type == '1') {

                    $note->user_id = $user_id;
                } else {
                    $note->sub_user_id = $user_id;
                }
                $note->save();
            } else {

                if ($invite->is_sub_only == '1') {
                    foreach ($this->userNotesWhioutMakup as $programNote) {

                        if ($invite->notes()->where('program_note_id', $programNote->id)->exists()) {
                            if($invite->is_replace==1){
                                $programNote->is_replacement = 1;
                            }

                            /*$noteSub = new ProgramNoteSub();
                            $noteSub->program_note_id = $programNote->id;
                            $noteSub->user_id = $user_id;
                            $noteSub->save();*/
                           // if ($programNote->user_sub_requested == 1) {
                                $programNote->sub_user_id = $user_id;
                            //}
                            $programNote->save();
                        }
                    }
                } elseif ($invite->notes->isNotEmpty()) {
                    foreach ($this->userNotesWhioutMakup as $programNote) {

                        if($invite->is_replace==1){
                            $programNote->is_replacement = 1;
                        }

                        if ($invite->notes()->where('program_note_id', $programNote->id)->exists()) {
                            if ($invite->type == '1') {

                                $programNote->user_id = $user_id;
                            } else {
                                /*$noteSub = new ProgramNoteSub();
                                $noteSub->program_note_id = $programNote->id;
                                $noteSub->user_id = $user_id;
                                $noteSub->save();*/

                                if ($programNote->user_sub_requested == 1) {
                                    $programNote->sub_user_id = $user_id;
                                }
                            }
                            if($programNote->class_date && $invite->replacement_start_date) {
                                if($programNote->class_date >= $invite->replacement_start_date){
                                    $programNote->save();
                                }
                            } else {
                                $programNote->save();
                            }

                        }
                    }
                } else {

                    foreach ($this->userNotesWhioutMakup as $programNote) {

                        if($invite->is_replace==1){
                            $programNote->is_replacement = 1;
                        }

                        if ($invite->type == '1') {

                            $programNote->user_id = $user_id;
                        } else {
                            /*$noteSub = new ProgramNoteSub();
                            $noteSub->program_note_id = $programNote->id;
                            $noteSub->user_id = $user_id;
                            $noteSub->save();*/
                            if ($programNote->user_sub_requested == 1) {
                                $programNote->sub_user_id = $user_id;
                            }
                        }

                        if($programNote->class_date && $invite->replacement_start_date){
                            if($programNote->class_date  >= $invite->replacement_start_date){
                                $programNote->save();
                            }
                        } else {
                            $programNote->save();
                        }
                        // $programNote->user_id = $user_id;
                    }
                }
            }
        } elseif ($this->userNotesWhioutMakup->isEmpty()) {
            // echo 52;die;
            $currentDate = new DateTime();
            $currentDate->setTime(0, 0, 0);

            $start_date = new DateTime($this->start_date);
            $start_date->setTime(0, 0, 0);

            $end_date = new DateTime($this->end_date);
            $end_date->setTime(0, 0, 0);


            $noClasses = $this->noClasses;

            while ($start_date <= $end_date) {

                if ($start_date < $currentDate) {
                    $start_date->modify('+1 day');
                    continue;
                }
                if ($this->is_imported == '0' && $noClasses->isNotEmpty() && $this->shouldSkipDate($start_date, $noClasses)) {

                    $start_date->modify('+1 day');
                    continue; // skip
                }

                $day = $start_date->format('N');
                $dateSchedule = $this->dateSchedule($day);
                if (empty($dateSchedule->value('start_time')) || empty($dateSchedule->value('end_time'))) {
                    $start_date->modify('+1 day');

                    continue;       //skip
                }
                $obj = new ProgramNote();


                $obj->program_id = $this->id;


                $obj->class_date = $start_date;
                $obj->day = $day;
                $obj->start_time = $dateSchedule->first()->start_time_utc;
                $obj->end_time = $dateSchedule->first()->end_time_utc;
                $obj->save();
                $start_date->modify('+1 day');
            }
        }
    }

    /**
     * create Classes on program creation
     *
     * @return void
     */
    public  function createClasses()
    {
        $timezone = new DateTimeZone($this->timezone ?? 'UTC');
        $currentDate = new DateTime('now', $timezone);
        $currentDate->setTime(0, 0, 0);

        $start_date = new DateTime($this->start_date, $timezone);
        $start_date->setTime(0, 0, 0);

        $end_date = new DateTime($this->end_date, $timezone);
        $end_date->setTime(0, 0, 0);

        $noClasses = $this->noClasses;
        while ($start_date <= $end_date) {
            if ($start_date < $currentDate) {
                $start_date->modify('+1 day');
                continue;
            }
            if ($this->is_imported == '0' && $noClasses->isNotEmpty() && $this->shouldSkipDate($start_date, $noClasses)) {

                $start_date->modify('+1 day');
                continue; // skip
            }


            $day = $start_date->format('N');
            $dateSchedule = $this->dateSchedule($day);

            if (empty($dateSchedule->value('start_time')) || empty($dateSchedule->value('end_time'))) {
                $start_date->modify('+1 day');

                continue; // skip
            }

            $obj = new ProgramNote();

            $obj->user_id = null;
            $obj->program_id = $this->id;


            $obj->class_date = $start_date;
            $obj->day = $day;
            $obj->start_time = $dateSchedule->first()->start_time_utc;
            $obj->end_time = $dateSchedule->first()->end_time_utc;
            $obj->save();
            $start_date->modify('+1 day');
        }
    }
    /**
     * update Classes on program update
     *
     * @return void
     */
    public function updateClasses()
    {
        $currentDate = new DateTime();
        $currentDate->setTime(0, 0, 0);

        $startDate = new DateTime($this->start_date);
        $startDate->setTime(0, 0, 0);

        $endDate = new DateTime($this->end_date);
        $endDate->setTime(0, 0, 0);


        $noClasses = $this->noClasses;

        $dateRange = [];
        while ($startDate <= $endDate) {
            $skipDate = false;
            if ($this->is_imported == '0' && $noClasses->isNotEmpty()) {
                foreach ($noClasses as $noClass) {
                    $noClassesStartDate = new DateTime($noClass->start_date);
                    $noClassesEndDate = $noClass->end_date ? new DateTime($noClass->end_date) : $noClassesStartDate;
                    if ($startDate >= $noClassesStartDate && $startDate <= $noClassesEndDate) {
                        $skipDate = true;

                        $deleted = $this->userNotes()->where(['is_reassigned' => '0', 'class_date' => ($startDate)->format('Y-m-d')])
                            ->whereNull(['parent_id', 'status'])
                            ->forceDelete();
                        break;
                    }
                }
            }


            if (!$skipDate) {
                $dateRange[] = clone $startDate;
            }

            $startDate->modify('+1 day');
        }

        $oldProgramNoteUser = ProgramNote::select('user_id')
            ->where(['program_id' => $this->id, 'is_reassigned' => '0'])
            ->where(function ($query) {
                $query->whereNull(['status'])
                    ->orWhere('status', '=', '2');
            })
            ->first();

        $existingClassDates = ProgramNote::where(['program_id' => $this->id])
            ->pluck('class_date');

        $updatedNoteIds = [];
        $updatedUserIds = [];

        foreach ($dateRange as $currentDate) {


            $dayOfWeek = $currentDate->format('N');
            $dateSchedule = $this->dateSchedule($dayOfWeek);

            if (empty($dateSchedule->value('start_time')) || empty($dateSchedule->value('end_time'))) {
                continue; // skip
            }

            $existingNote = ProgramNote::where(['program_id' => $this->id, 'class_date' => $currentDate->format('Y-m-d'), 'day' => $dayOfWeek, 'is_reassigned' => '0'])->first();

            if ($existingNote) {

                $existingNote->start_time = $dateSchedule->first()->start_time_utc;
                $existingNote->end_time = $dateSchedule->first()->end_time_utc;

                $existingNote->save();
                $updatedNoteIds[] = $existingNote->id;
                $updatedUserIds[] = $existingNote->user_id;
            } elseif (!in_array($currentDate->format('Y-m-d'), $existingClassDates->toArray())) {
                $newProgramNote = new ProgramNote();

                $newProgramNote->user_id = $oldProgramNoteUser->user_id ?? null;
                $newProgramNote->program_id = $this->id;

                $newProgramNote->class_date = $currentDate->format('Y-m-d');
                $newProgramNote->day = $dayOfWeek;
                $newProgramNote->start_time = $dateSchedule->first()->start_time_utc;
                $newProgramNote->end_time = $dateSchedule->first()->end_time_utc;
                $newProgramNote->save();

                $updatedNoteIds[] = $newProgramNote->id;
                $updatedUserIds[] = $newProgramNote->user_id;
            }
        }

        if (!empty($updatedNoteIds)) {
            ProgramNote::whereNull(['status', 'parent_id'])
                ->where(['program_id' => $this->id, 'is_reassigned' => '0'])
                ->whereNotIn('id', $updatedNoteIds)
                ->update(['status' => 4]);
        }
    }

    public function latestProgramOwner()
    {
        return $this->belongsToMany(User::class, 'tbl_invite_program_owners', 'program_id', 'user_id')
            ->latest('tbl_invite_program_owners.id')
            ->limit(1)
            ->withPivot(['deadline']);
    }



    public function programOwners()
    {
        return $this->belongsToMany(User::class, 'tbl_invite_program_owners', 'program_id', 'user_id')->withPivot(['deadline']);
    }

    public function noClasses()
    {
        return $this->hasMany(ProgramNoClassDate::class, 'program_id');
    }


    private function shouldSkipDate(DateTime $date, $noClasses)
    {
        $currentDate = new DateTime();
        $currentDate->setTime(0, 0, 0);

        if ($date < $currentDate) {
            return true;
        }

        foreach ($noClasses as $noClass) {
            $noClassesStartDate = new DateTime($noClass->start_date);
            $noClassesEndDate = $noClass->end_date ? new DateTime($noClass->end_date) : $noClassesStartDate;

            if (
                $date >= $noClassesStartDate &&
                $date <= $noClassesEndDate
            ) {
                return true;
            }
        }

        return false;
    }


/*     public static function boot()
    {
        parent::boot();

        self::created(function ($model) {
            if ($model->program_status == 'Publish') {
                CustomHelper::alertProgramUsers($model);
            }
        });
    } */


    public function roster()
    {
        return $this->hasMany(RosterModel::class, 'program_id');
    }

    /**
     * Scope a query to only include Publish programs.
     */
    public function scopePublished(Builder $query): void
    {
        $query->where('tbl_programs.program_status', 'Publish');
    }

    /**
     * Scope a query to only include active programs.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('tbl_programs.status', 1);
    }
}
