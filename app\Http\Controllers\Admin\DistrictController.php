<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\District;
use App\district_contact_info;

use Hash;
use Mail;
use Crypt;
use App\CommomModel;
DB::enableQueryLog();

class DistrictController extends Controller
{
    /**
     * Display a listing of the District.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managedistrict','view')!=true){
            return redirect("/no-permission");
        }  
        $where = [];
        $district = District::all();
        return view("admin.district.district", compact("district"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function adddistrict()
    {
        $country = DB::table("tbl_countries")->get();
        return view("admin.district.adddistrict", compact("country"));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function savedistrict(Request $request)
    {
        $name = $request->name;

        if ($name != "") {
            $districtExits = District::where("name", "=", $name)->get();
            if (count($districtExits)) {
                return response()->json([
                    "success" => false,
                    "message" => "District already exits",
                ]);
            } else {
                $data["name"] = $request->name;
                $data["cust_type"] = $request->cust_type;
                $data["website"] = $request->website;
                $data["country"] = $request->country;
                $data["state"] = $request->state;
                $data["address"] = $request->address;
                $data["zipcode"] = $request->zipcode;
                $data["city"] = $request->city;
                $data["notes"] = $request->notes;
                $data["status"] = "1";
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = District::insertGetId($data);

                if ($save) {
                    for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                        $data1["job_title"] = $_POST["job_title"][$i];
                        $data1["email"] = $_POST["cemail"][$i];
                        $data1["first_name"] = $_POST["first_name"][$i];
                        $data1["last_name"] = $_POST["last_name"][$i];
                        $data1["phone"] = $_POST["phone"][$i];
                        $data1["district_id"] = $save;
                        district_contact_info::insertGetId($data1);
                    }

                    return response()->json([
                        "success" => true,
                        "message" => "District  successfully created",
                        "redirect" => url("/manage-district"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $district = District::where("id", $id)->first();
        $country = DB::table("tbl_countries")->get();
        $district_contact_info = DB::table("tbl_district_contact_info")
            ->where("district_id", $id)
            ->get();
        return view("admin.district.edit_district", [
            "district" => $district,
            "country" => $country,
            "district_contact_info" => $district_contact_info,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = $request->id;
        $name = $request->name;
        $data["name"] = $request->name;
        $data["cust_type"] = $request->cust_type;
        $data["website"] = $request->website;
        $data["country"] = $request->country;
        $data["state"] = $request->state;
        $data["address"] = $request->address;
        $data["zipcode"] = $request->zipcode;
        $data["city"] = $request->city;
        $data["notes"] = $request->notes;
        $data["status"] = "1";
        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = District::where("id", $id)->update($data);

        if ($save) {
            $res = district_contact_info::where(
                "district_id",
                "=",
                $id
            )->delete();
            for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                $data1["job_title"] = $_POST["job_title"][$i];
                $data1["email"] = $_POST["cemail"][$i];
                $data1["first_name"] = $_POST["first_name"][$i];
                $data1["last_name"] = $_POST["last_name"][$i];
                $data1["phone"] = $_POST["phone"][$i];
                $data1["district_id"] = $id;
                district_contact_info::insertGetId($data1);
            }
            return response()->json([
                "success" => true,
                "message" => "Details successfully updated",
                "redirect" => url("/manage-district"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
       
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = District::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = District::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = District::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = District::where("id", $id)->first();
            if ($record) {
                $res = District::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
