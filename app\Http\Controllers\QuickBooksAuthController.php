<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use QuickBooksOnline\API\Core\HttpClients\CurlHttpClient;
use QuickBooksOnline\API\Core\ServiceContext;
use QuickBooksOnline\API\DataService\DataService;
use QuickBooksOnline\API\Facades\Vendor;
use QuickBooksOnline\API\Facades\Bill;
use QuickBooksOnline\API\Facades\BillPayment;
use QuickBooksOnline\API\Facades\Deposit;
use QuickBooksOnline\API\Facades\Account;

class QuickBooksAuthController extends Controller
{

    public function connect()
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);

        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $authorizationUrl = $OAuth2LoginHelper->getAuthorizationCodeURL();
        // return redirect()->away($authorizationUrl);
        return redirect()->to($authorizationUrl);
    }

    public function callback(Request $request)
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);

        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $accessTokenObj = $OAuth2LoginHelper->exchangeAuthorizationCodeForToken($request->code, $request->realmId);

        $refreshToken = $accessTokenObj->getRefreshToken();
        $accessToken = $accessTokenObj->getAccessToken();
        // dd($accessTokenObj->getAccessTokenExpiresAt());

        session()->put('accessToken', $accessTokenObj);

        file_put_contents(
            app()->environmentFilePath(),
            str_replace(
                'QUICKBOOKS_REFRESH_TOKEN=' . env('QUICKBOOKS_REFRESH_TOKEN'),
                'QUICKBOOKS_REFRESH_TOKEN=' . $refreshToken,
                file_get_contents(app()->environmentFilePath())
            )
        );

        file_put_contents(
            app()->environmentFilePath(),
            str_replace(
                'QUICKBOOKS_REALM_ID=' . env('QUICKBOOKS_REALM_ID'),
                'QUICKBOOKS_REALM_ID=' . $request->realmId,
                file_get_contents(app()->environmentFilePath())
            )
        );

        return redirect()->route('admin.manage-payments.instructorPayments');
    }

    public function contractor($email)
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);
        $accessTokenObj = session()->get('accessToken');

        if (!$accessTokenObj || !method_exists($accessTokenObj, 'getRealmID')) {
            // return (['message' => 'Access token not found or invalid. Please authenticate first.']);
            return (['errorMessage' => 'The QuickBooks token has expired.']);
        }

        // Set the access token in the DataService
        $dataService->updateOAuth2Token($accessTokenObj);

        // $query = "SELECT * FROM Vendor WHERE PrimaryEmailAddr.Address LIKE '%{$email}%'";

        $query = "SELECT * FROM Vendor";
        $vendors = $dataService->Query($query);
        if (empty($vendors)) {
            $error = $dataService->getLastError();
            return (['errorMessage' => $error->getResponseBody()]);
        }

        // Filter vendors by email
        // $matchingVendors = array_filter($vendors, function ($vendor) use ($email) {
        //     return isset($vendor->PrimaryEmailAddr) && strpos($vendor->PrimaryEmailAddr->Address, $email) !== false;
        // });

        // Filter vendor by email
        $matchingVendor = null;
        foreach ($vendors as $vendor) {
            if (isset($vendor->PrimaryEmailAddr) && strpos($vendor->PrimaryEmailAddr->Address, $email) !== false) {
                $matchingVendor = $vendor;
                break;
            }
        }

        // Step 4: If no vendor found, return message
        if (!$matchingVendor) {
            return ['errorMessage' => 'No vendor found with this email'];
        }

        // Step 5: Retrieve the vendor’s account number
        $vendorAccountNumber = $matchingVendor->AcctNum ?? null;

        if (!$vendorAccountNumber) {
            return ['errorMessage' => 'Vendor does not have an account number'];
        }

        // Step 6: Query QuickBooks to get all accounts (filtered to bank accounts)
        $bankAccounts = $dataService->Query("SELECT * FROM Account");

        if (empty($bankAccounts)) {
            $error = $dataService->getLastError();
            return ['errorMessage' => $error ? $error->getResponseBody() : 'No bank accounts found'];
        }

        // Step 7: Match the vendor's account number with QuickBooks bank accounts
        $matchingBankAccount = null;
        foreach ($bankAccounts as $account) {
            if (isset($account->AcctNum) && $account->AcctNum === $vendorAccountNumber) {
                $matchingBankAccount = $account;
                break;
            }
        }
        // Step 8: Return results if found, or message if not
        if ($matchingBankAccount) {
            return [
                'vendor' => $matchingVendor,
                'bank_account' => $matchingBankAccount,
            ];
        } else {
            return ['errorMessage' => 'No bank account matches the vendor’s account number'];
        }
    }

    public function billPayment($contractor_id, $total_amount, $description)
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);
        $accessTokenObj = session()->get('accessToken');
        $accessToken = $accessTokenObj->getAccessToken();
        if (!$accessTokenObj || !method_exists($accessTokenObj, 'getRealmID')) {
            return (['error' => 'Access token not found or invalid. Please authenticate first.']);
        }

        $dataService->updateOAuth2Token($accessTokenObj);

        $deposit = Deposit::create([
            "DepositToAccountRef" => [
                // "name" => "Savings",
                // "value" => "36"
                "name" => "Contractor Pay",
                "value" => "95"
            ],
            "Line" => [
                "Description" => $description,
                "DetailType" => "DepositLineDetail",
                "Amount" => $total_amount,
                "DepositLineDetail" => [
                    "AccountRef" => [
                        "name" => "Commissions & fees",
                        "value" => "9"
                    ],
                    // "PaymentMethodRef" => [
                    //     "value" => "8"
                    // ],
                ],
            ],
            
        ]);
        $createdDeposit = $dataService->Add($deposit);

        // $bill = Bill::create([
        //     "VendorRef" => [
        //         "value" => $contractor_id, // Vendor ID from QuickBooks
        //         // "value" => "62", // Vendor ID from QuickBooks
        //     ],
        //     "Line" => [
        //         [
        //             "Amount" => $total_amount,
        //             "DetailType" => "AccountBasedExpenseLineDetail",
        //             "AccountBasedExpenseLineDetail" => [
        //                 "AccountRef" => [
        //                     "value" => "8" // Expense account ID in QuickBooks
        //                 ]
        //             ]
        //         ]
        //     ]
        // ]);

        // $createdBill = $dataService->Add($bill);
        // $createdBillId = $createdBill->Id;

        // $billPayment = BillPayment::create([
        //     "VendorRef" => [
        //         "value" => $contractor_id // Contractor's Vendor ID from QuickBooks
        //     ],
        //     "TotalAmt" => $total_amount,
        //     "PayType" => "BankTransfer", // Or other types like "Check, CreditCard, Cash, BankTransfer, DebitCard, Other"
        //     "Line" => [
        //         [
        //             "Description" => $description,
        //             "Amount" => $total_amount,
        //             "LinkedTxn" => [
        //                 [
        //                     "TxnId" => $createdBillId, // ID of the created Bill
        //                     "TxnType" => "Bill"
        //                 ]
        //             ]
        //         ]
        //     ],
        //     "CheckPayment" => [
        //         "BankAccountRef" => [
        //             "value" => "35" // Bank account ID in QuickBooks
        //         ]
        //     ]
        // ]);

        // $billPaymentResult = $dataService->Add($billPayment);

        if (!$createdDeposit || isset($createdDeposit->Fault)) {
            $error = $dataService->getLastError();
            return (['error' => 'Error creating invoice: ' . $error->getResponseBody()]);
        }

        return (['message' => 'Pay successfully', 'createdDeposit' => $createdDeposit]);
    }

    public function account()
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);
        $accessTokenObj = session()->get('accessToken');

        if (!$accessTokenObj || !method_exists($accessTokenObj, 'getRealmID')) {
            return response()->json(['message' => 'Access token not found or invalid. Please authenticate first.'], 401);
        }

        // Set the access token in the DataService
        $dataService->updateOAuth2Token($accessTokenObj);

        $query = "SELECT * FROM Account";
        // $query = "SELECT * FROM PaymentMethod";
        // $query = "SELECT * FROM Project";
        $accounts = $dataService->Query($query);
        return response()->json(['Account' => $accounts]);
    }

    public function createContractor()
    {
        $dataService = DataService::Configure([
            'auth_mode' => 'oauth2',
            'ClientID' => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI' => env('QUICKBOOKS_REDIRECT_URI'),
            'scope' => 'com.intuit.quickbooks.accounting com.intuit.quickbooks.payment',
            'baseUrl' => env('QUICKBOOKS_ENVIRONMENT') == 'sandbox' ? "https://sandbox-quickbooks.api.intuit.com" : "https://quickbooks.api.intuit.com",
        ]);
        $accessTokenObj = session()->get('accessToken');

        if (!$accessTokenObj || !method_exists($accessTokenObj, 'getRealmID')) {
            return response()->json(['message' => 'Access token not found or invalid. Please authenticate first.'], 401);
        }

        // Set the access token in the DataService
        $dataService->updateOAuth2Token($accessTokenObj);

        $contractorData = [
            "PrimaryPhone" => [
                "FreeFormNumber" => "**********"
            ],
            "PrimaryEmailAddr" => [
                "Address" => "<EMAIL>"
            ],
            "BillAddr" => [
                "City" => "Millbrae",
                "Country" => "U.S.A",
                "Line3" => "654 Mustang Ave.",
                "Line2" => "Zohini Dianne",
                "Line1" => "Sandbox Shop",
                "PostalCode" => "94030",
                "CountrySubDivisionCode" => "CA"
            ],
            // "Title" => "Ms.",
            "GivenName" => "Zeera",
            "FamilyName" => "Shaw",
            "DisplayName" => "Zeera 5 Shaw",
            "Vendor1099" => true,
            "AcctNum" => "64654465151515612054",
            "CompanyName" => "Sandbox Company_US_1"
        ];

        $contractor = Vendor::create($contractorData);
        $createdContractor = $dataService->Add($contractor);

        // $existingVendor = $dataService->FindById('Vendor', "64");        // for update
        // $existingVendor->AcctNum = "64654465151515612054";
        // $createdContractor = $dataService->Update($existingVendor);

        if (!$createdContractor || isset($createdContractor->Fault)) {
            $error = $dataService->getLastError();
            return response()->json(['error' => 'Error creating invoice: ' . $error->getResponseBody()]);
        }

        return response()->json(['message' => 'Created successfully', 'contractor' => $createdContractor]);
    }
}
