<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\FaqsModel;
use App\MailModel;
use App\school_contact_info;
use App\ReviewModel;

use Hash;
use Mail;
use Auth;

DB::enableQueryLog();


class SchoolController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (Auth::user() && Auth::user()->type == 6) {
            if (Auth::user()->cust_type == 'Platform') {
                return redirect()->route('new-school.dashboard');
            } else {
                return view('school.index');
            }
        } else {
            return redirect("/schools");
        }
    }

    public function faq()
    {

        $data['faqs'] = FaqsModel::where("type", 'School')->where("status", '1')->get();
        return view('web.user.faq.faq')->with($data);
    }



    public function logout()
    {
        Auth::logout();
        Session::forget('schoolloginsession');
        return redirect("/schools");
    }



    public function search_instructor()
    {
        $data['instructor'] = array();
        return view('school.instructor.instructor')->with($data);
    }

    public function newprogram()
    {
        $data['instructor'] = array();
        return view('school.program.newprogram')->with($data);
    }

    public function rate_instructor()
    {
        $data['instructor'] = array();
        return view('school.instructor.rate_instructor')->with($data);
    }

    public function school_payments()
    {
        $data['instructor'] = array();
        return view('school.payment.school_payment')->with($data);
    }

    public function update_contact_details()
    {
        $data['user'] = Users::where(['id' => Auth::user()->id])->first();
        $data['school_contact_info'] = DB::table("tbl_school_contact_info")
            ->where("school_id", Auth::user()->id)
            ->get();
        return view('school.setting.contact_details')->with($data);
    }


    public function school_profile_information()
    {
        $data['users'] = Users::where(['id' => Auth::user()->id])->first();
        return view('school.setting.profile')->with($data);
    }

    public function school_notifications_settings()
    {
        $data['user'] = Users::where(['id' => Auth::user()->id])->first();
        return view('school.setting.notifications_settings')->with($data);
    }

    public function messages()
    {
        $data['instructor'] = array();
        return view('school.messages.messages')->with($data);
    }

    public function  acoountsetting()
    {

        $data['user'] = Users::where(['id' => Auth::user()->id])->first();
        $data['notice'] = MailModel::where("created_by",  Auth::user()->id)->where("f_type",  'Notice')->first();
        return view('school.setting.acoountsetting')->with($data);
    }


    public function notification(Request $request)
    {
        $value = $request->value;
        $notification = $request->notification;

        if ($notification == 'true') {

            $not = '1';
        } else {
            $not = '0';
        }

        if ($value == 'app_notification') {
            Users::where(["id" => Auth::user()->id])->update(['app_notification' => $not]);
        } else {
            Users::where(["id" => Auth::user()->id])->update(['email_notification' => $not]);
        }
        return response()->json([
            "success" => true,
            "message" => "Updated successfully",
        ]);
    }

    public function schoolupdateAccountStatus(Request $request)
    {

        $user = auth()->user();
        $user->status = $request->status;
        $user->save();
        return response()->json([
            "success" => true,
            "message" => "Account status changed successfully",
        ]);
    }


    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "old_password" => "required",
            "password" => "required|string|confirmed",
        ]);

        $user = auth()->user();
        if (!Hash::check($request->old_password, $user->password)) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => 'Old password is wrong',
                ],
                200
            );
        };
        if ($request->old_password == $request->password) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => 'Old password and new password cannot be same',
                ],
                200
            );
        };
        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors()->all(),
                ],
                400
            );
        }
        $password = $request->input("password");

        $user = auth()->user();
        $user->password = Hash::make($password);
        $user->save();
        return response()->json([
            "success" => true,
            "message" => "Password updated successfully",
        ]);
    }

    public function deleteAccount(Request $request)
    {

        $user = auth()->user();
        $user->status = "2";
        $user->save();

        Auth::logout();
        Session::forget('userewlogin');


        return response()->json([
            "success" => true,
            "message" => "Account deleted successfully",
            "redirect" => url("/schools"),
        ]);
    }

    public function getSchoolNoticeForm(Request $request)
    {

        $view = view("components.modals.school.notice")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateSchoolNoticeForm(Request $request)
    {
        $request->validate(
            [
                'reason' => 'required',

            ]
        );


        $user = auth()->user();

        $record = MailModel::where("created_by",  $user->id)->where("f_type",  'Notice')->first();
        if ($record) {
            return response()->json(['status' => false, 'message' => "Already Appiled", "resetForm" => true]);
        } else {
            $obj = new MailModel();

            $obj->user_id = $user->id;
            $obj->title = 'Notice';
            $obj->f_type = 'Notice';
            $obj->subject = 'Notice';
            $obj->created_by = $user->id;
            $obj->message = $request->reason;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Notice sent successfully", "resetForm" => true]);
        }
    }

    public function saveschoolprofile(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "address" => "required",
            "full_name" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "profile_image",
            "first_name",
            "address"

        ]);

        if ($request->hasFile("profile_image")) {
            $image = $request->file("profile_image");
            $name = time() . "." . $image->getClientOriginalExtension();
            // $destinationPath = public_path("/uploads/institute");
            // $image->move($destinationPath, $name);
            $filename = 'uploads/admin/' . uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename, $image);
            $destinationPath =  $filename;

            $data["image"] = $filename;
        }
        $data["first_name"] = $request->full_name;
        $data["address"] = $request->address;
        Users::where(['id' => Auth::user()->id])->update($data);


        return response()->json([
            "success" => true,
            "message" => "Updated successfully",
        ]);
    }


    public function updatecontactdeatils(Request $request)
    {
        $request->validate(
            [
                'job_title.*' => 'required',
                'first_name.*' => 'required',
                'last_name.*' => 'required',
                'cemail.*' => 'required|email',
                'phone.*' => 'required'

            ]
        );


        $user = auth()->user();


        if (count($_POST["job_title"]) > 0) {
            $res = school_contact_info::where(
                "school_id",
                "=",
                $user->id
            )->delete();

            for ($i = 0; $i < count($_POST["job_title"]); $i++) {

                $data1["job_title"] = $_POST["job_title"][$i];
                $data1["email"] = $_POST["cemail"][$i];
                $data1["first_name"] = $_POST["first_name"][$i];
                $data1["last_name"] = $_POST["last_name"][$i];
                $data1["phone"] = $_POST["phone"][$i];
                $data1["school_id"] = $user->id;
                school_contact_info::insertGetId($data1);
            }

            return response()->json([
                "success" => true,
                "message" => "Contact Details successfully updated",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function getReveiwForm($encryptedStrId, $encryptedprogramidId, Request $request)
    {

        $ins_id = decrypt_str($encryptedStrId);
        $programid = decrypt_str($encryptedprogramidId);

        $view = view("components.modals.school.review", compact('ins_id', 'programid'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function addreveiwins(Request $request)
    {
        $request->validate(
            [
                // 'rating' => 'required',
                'feedback' => 'required',

            ]
        );


        $user = auth()->user();
        $ins_id = decrypt_str($request->to_id);
        $program_id = decrypt_str($request->program_id);
        $record = ReviewModel::where("from_id",  $user->id)->where("to_id",  $ins_id)->where("program_id", $program_id)->first();
        if ($record) {
            return response()->json(['status' => false, 'message' => "Review Already submitted", "resetForm" => true]);
        } else {
            $obj = new ReviewModel();

            $obj->from_id = $user->id;
            $obj->to_id =  $ins_id;
            $obj->rating = $request->rating ? $request->rating : 0;
            $obj->review = $request->feedback;
            $obj->program_id = $program_id;
            $obj->save();
            return response()->json(['status' => true, 'message' => "Review submitted successfully", "resetForm" => true, "reload" => true]);
        }
    }

    public function ratinglist(Request $request)
    {
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;

        $qry = ReviewModel::where('from_id', $user->id)->where('to_id', '!=', null);
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id', 'DESC')->get();
        $data = array();
        $i = 1;

        foreach ($result as $row) {

            $date = date('m-d-Y', strtotime($row->created_at));


            $name = getstaffname($row->to_id);

            $data[] = array(

                "to_id" => $name,
                "rating" => $row->rating,
                "review" => $row->review,
                "created_at" => $date,

            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function get_dynamic_school_data(Request $request)
    {


        //this will give the collection of ids
        // $search = $request->input('query');
        // $subjectIds = DB::table("tbl_subjects")
        //     ->where("subject_name", "like", "%{$search}%")
        //     ->pluck('id');

        // $userIds = DB::table("onboarding_instructor_subjects")
        //     ->whereIn("subject", $subjectIds)
        //     ->pluck('user_id'); // Get user IDs who are linked to these subjects

        // $activeInstructors = DB::table("new_onboarding_instructor")
        //     ->whereIn("id", $userIds)
        //     ->where("user_status", "active")
        //     ->get();
        $search = $request->input('query');

        // Step 1: Get subject IDs matching the search
        $subjectIds = DB::table("tbl_subjects")
            ->where("subject_name", "like", "{$search}%")
            ->pluck('id');
        
        // Step 2: Get user IDs linked to those subjects
        $userIdsFromSubjects = DB::table("onboarding_instructor_subjects")
            ->whereIn("subject", $subjectIds)
            ->pluck('user_id');
        
        // Step 3: Get active instructors whose name matches OR are linked to a matching subject
        $activeInstructors = DB::table("new_onboarding_instructor")
        ->where("user_status", "active")
        ->where(function ($query) use ($userIdsFromSubjects, $search) {
            $query->whereIn("id", $userIdsFromSubjects)
                  ->orWhere("first_name", "like", "{$search}%");
        })
        ->select('image', 'first_name', 'description','id') // Specify the fields you want to select
        ->get();

       
        $data=[];
        
        foreach ($activeInstructors as $instructor) {
            $link = url('/profile/' . encrypt_str($instructor->id));

        
        
            $image=generateSignedUrl($instructor->image);
            $data[] = (object)[
                'href' =>$link,
                'image' => $image,
                'title' => $instructor->first_name,
                'description' => $instructor->description,
                'tags' => $instructor->tags??"",
                "status" => $instructor->status ?? ""



            ];
        }

       return response()->json($data);
    }
}
