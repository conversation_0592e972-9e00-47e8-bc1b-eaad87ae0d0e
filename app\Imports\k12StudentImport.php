<?php

namespace App\Imports;

use App\Classes;
use App\Models\PlatformSchoolRoster;
use App\Models\k12ConnectionPrograms;
use App\Models\PlatformSchoolRequirements;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class k12StudentImport implements ToModel, WithHeadingRow
{
    protected $data;
    protected $filename;
    protected $programId;
    protected $lastInsertedId = null;
    protected $validationErrors = [];
    protected $hasValidData = false;

    public function __construct(?PlatformSchoolRequirements $data, $filename, $programId = null)
    {
        $this->data = $data;
        $this->filename = $filename;
        $this->programId = $programId;
    }
    
    public function model(array $row)
    {
        if (!empty($row['student_name']) && !empty($row['grade'])) {

            // Format the date and time before validation
           
          
            $validator = Validator::make($row, [
                'student_name' => 'required',
                'grade' => 'required'
            ]);

            if ($validator->fails()) {
                throw new \Exception("Import Excel error: <br>" . implode('<br>', $validator->errors()->all()));
            }

                $gradeExit = Classes::where("class_name", $row['grade'])->first();
                if($gradeExit){
                    $obj = PlatformSchoolRoster::firstOrNew(['student_name' => $row['student_name'], 'grade' => $row['grade']]);
                    $obj->requirement_id = $this->data ? $this->data->id : null;
                    $this->hasValidData = true;
                    return $this->fillModelData($obj, $row);
                }else{
                    return [];
                }
           
        } else {
            if (empty($row['student_name'])) {
                $this->validationErrors[] = "Student name is required.";
            }
            if (empty($row['grade'])) {
                $this->validationErrors[] = "Grade is required.";
            }
            return null;
        }
    }

    protected function fillModelData(PlatformSchoolRoster $obj, array $row): void
    {
        $obj->student_name = $row['student_name'];
        $obj->grade = $row['grade'];
        $obj->requirement_id = $this->data ? $this->data->id : null;
        $obj->program_id = $this->programId;
        $obj->csv_file = $this->filename;
        $obj->save();
        $this->lastInsertedId = $obj->id;
    }

    public function getLastInsertedId()
    {
        return $this->lastInsertedId;
    }

    public function getValidationErrors()
    {
        return $this->validationErrors;
    }

    public function hasValidData()
    {
        return $this->hasValidData;
    }
}
