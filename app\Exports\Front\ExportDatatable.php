<?php

namespace App\Exports\Front;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Collection;

class ExportDatatable implements FromCollection, WithHeadings
{
    protected $classes;

    public function __construct($classes)
    {
        $this->classes = $classes;
    }

    public function collection()
    {
        return new Collection($this->formatData());
    }

    public function headings(): array
    {
        return [
            'Class Start Date',
            'Start-End Time',
            'Class rating (Educator)',
            'Class summary (Educator)',
            'Class rating (Proctor)',
            'Class summary (Proctor)',
            'Class Status',
        ];
    }

    private function formatData()
    {
        $formattedData = $this->classes->map(function ($class) {
            $educatorLog = \App\Models\k12ConnectionClassLogs::where('class_id', $class->id)->first();
            $proctorFeedback = \App\Models\k12ConnectionProctorFeedback::where('class_id', $class->id)->first();

            $classDate = $class->class_date ? \Carbon\Carbon::parse($class->class_date) : null;
            $today = \Carbon\Carbon::today();

            if ($classDate && $classDate->lessThanOrEqualTo($today)) {
                if (empty($class->class_log_id)) {
                    $status = "Missing Summary";
                } elseif ($class->status === "under review") {
                    $status = "Pending Review";
                } else {
                    $status = ucfirst($class->status);
                }
            } else {
                $status = $class->status ? ($class->status == "scheduled" ? "Upcoming" : ($class->status == "under review" ? "Pending Review" : ucfirst($class->status))) : 'N/A';
            }

            return [
                'class_start_date' => $classDate ? $classDate->format('m/d/Y') : 'N/A',
                'time_range' => ($class->start_time && $class->end_time) 
                    ? \Carbon\Carbon::parse($class->start_time)->format('h:i A') . ' - ' . \Carbon\Carbon::parse($class->end_time)->format('h:i A') 
                    : 'N/A',
                'educator_rating' => $educatorLog->class_rating ?? 'No Rating',
                'educator_summary' => $educatorLog->note ?? 'No summary available',
                'proctor_rating' => $proctorFeedback->proctor_rating ?? 'No Rating',
                'proctor_summary' => $proctorFeedback->note ?? 'No summary available',
                'class_status' => $status, 
            ];
        });

        // Sort by class_start_date
        return $formattedData->sortBy(function ($item) {
            return strtotime($item['class_start_date']); 
        })->values(); 
    }

}