<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\District;
use App\school_contact_info;
use Hash;
use Mail;
use Crypt;
use App\CommomModel;
use App\Helpers\DataTableHelper;
use App\Models\k12ConnectionCategorizedData;
use App\Programs;
use App\NewSchoolPostRequirementModel;
use App\User;

DB::enableQueryLog();

class InstituteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $district = District::where("status", "1")->get();
        $cbo = DB::table("tbl_cbo")
            ->where("status", "1")
            ->get();
        $gradeLavel = DB::table("tbl_classes")
            ->where("status", "1")
            ->get();
        return view(
            "admin.institute.addinstitute",
            compact("district", "cbo", "gradeLavel")
        );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $email = $request->email;
        $full_name = $request->full_name;

        if ($email != "" && $full_name != "") {

            $userExits = Users::where("email",  $email)
                ->exists();


            if ($userExits) {

                return response()->json([
                    "success" => false,
                    "message" => "Email already exits",
                ]);
            }

            $logoName = "";
            //check logo if exists
            if ($request->hasfile("profile_upload")) {
                // move | upload file on server
                $image = $request->file("profile_upload");


                $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
                uploads3image($filename, $image);
                $data["image"] =  $filename;
            }

            $length = 6;
            $randpassword = substr(
                str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
                1,
                $length
            );
            $user_id = substr(str_shuffle("0123456789"), 1, $length);
            $data["full_name"] = $request->full_name;
            $data["first_name"] = $request->full_name;
            $data["email"] = $request->email;
            $data["user_id"] = $user_id;
            // $data['phone_number'] = $request->phone;
            $data["about"] = strip_tags($request->notes);
            $data["district"] = $request->district;
            $data["organizationtype"] = $request->organizationtype;
            $data["organizationname"] = $request->organizationname;
            $data["grade"] = implode(",", $request->grade);
            $data["cust_type"] = $request->cust_type;
            $data["website_url"] = $request->website;
            $data["password"] = Hash::make($randpassword);
            $data["password"] = Hash::make($randpassword);
            // $obj["passwordStr"] = $randpassword;
            $data["state"] = $request->state;
            $data["city"] = $request->city;

            $data["country"] = $request->country;
            $data["zipcode"] = $request->zipcode;
            $data["address"] = $request->address;
            $data["cbo"] = $request->cbo;
            $data["type"] = 6;
            $data["status"] = "1";
            $data["created_at"] = date("Y-m-d H:i:s");
            $data["updated_at"] = date("Y-m-d H:i:s");

            $save = Users::insertGetId($data);

            if ($save) {
                for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                    $data1["job_title"] = $_POST["job_title"][$i];
                    $data1["email"] = $_POST["cemail"][$i];
                    $data1["first_name"] = $_POST["first_name"][$i];
                    $data1["last_name"] = $_POST["last_name"][$i];
                    $data1["phone"] = $_POST["phone"][$i];
                    $data1["school_id"] = $save;
                    school_contact_info::insertGetId($data1);
                }


                $template = DB::table("tbl_email_templates")->where("email_template_id", "18")->first();
                $body =  $template->description;

                $full_name = $request->full_name;


                $body = str_replace('{{NAME}}', $full_name, $body);
                $body = str_replace('{{Password}}', $randpassword, $body);
                $body = str_replace('{{Username}}', $request->email, $body);
                $subject = $template->subject;

                $data = array('template' => $body);

                // Mail::send('template', $data, function (
                //     $message
                // ) use ($email,$subject) {
                //     $message->to($email)->subject($subject);
                // });



                return response()->json([
                    "success" => true,
                    "message" => "School successfully created",
                    "redirect" => url("/school-list"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went wrong",
                ]);
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }
    public function update_institute_picture(Request $request)
    {
        if ($request->hasFile("profile_upload")) {
            $image = $request->file("profile_upload");
            // $name = time() . "." . $image->getClientOriginalExtension();
            // $destinationPath = public_path("/uploads/institute");
            // $image->move($destinationPath, $name);
            // $updpic = url("/uploads/institute/" . $name);

            $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename, $image);
            $updpic = $filename;
            $name = generateSignedUrl($filename);
        }

        $user_id = $request->input("user_id");
        $obj = [];
        $obj["image"] = $updpic;

        $result = Users::where("id", $user_id)->update($obj);
        if ($result) {
            $data["success"] = true;
            $data["message"] = "Image Updated successfully";
            $data["img"] = $name;
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = decrypt_str($request->id);
        $email = $request->email;
        $full_name = $request->full_name;
        $first_name = $request->full_name;

        if ($email != "" && $full_name != "") {
            $userExits = Users::where("email",  $email)->where("id", "!=",  $id)
                ->exists();


            if ($userExits) {
                return response()->json([
                    "success" => false,
                    "message" => "Email already exits",
                ]);
            }

            // if (count($ExitsInstitute)) {
            //     return response()->json(['success' => false, 'message' => 'Institute already exits']);
            // } else {

            $logoName = "";
            //check logo if exists
            if ($request->hasfile("profile_upload")) {
                $image = $request->file("profile_upload");


                $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
                uploads3image($filename, $image);
                $data["image"] =  $filename;
            }

            $data['first_name'] = $request->full_name;
            // $data['last_name'] = $request->last_name;
            $data["full_name"] = $request->full_name;
            $data["email"] = $request->email;
            $data["organizationtype"] = $request->organizationtype;
            $data["organizationname"] = $request->organizationname;
            $data["district"] = $request->district;
            $data["state"] = $request->state;
            $data["city"] = $request->city;
            $data["about"] = strip_tags($request->notes);
            // $data['image'] = $logoName;
            $data["type"] = 6;
            $data["country"] = $request->country;
            $data["zipcode"] = $request->zipcode;
            $data["address"] = $request->address;
            $data["cbo"] = $request->cbo;
            if ($request->gradelevel) {
                $data["grade"] = implode(",", $request->gradelevel);
            }


            $data["cust_type"] = $request->cust_type;
            $data["website_url"] = $request->website;


            $data["status"] = "1";
            $data["updated_at"] = date("Y-m-d H:i:s");
            $save = Users::where("id", $id)->update($data);

            if ($save) {
                $res = school_contact_info::where(
                    "school_id",
                    "=",
                    $id
                )->delete();

                for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                    $data1["job_title"] = $_POST["job_title"][$i];
                    $data1["email"] = $_POST["cemail"][$i];
                    $data1["first_name"] = $_POST["first_name"][$i];
                    $data1["last_name"] = $_POST["last_name"][$i];
                    $data1["phone"] = $_POST["phone"][$i];
                    $data1["school_id"] = $id;
                    school_contact_info::insertGetId($data1);
                }

                return response()->json([
                    "success" => true,
                    "message" => "Details successfully updated",
                    "redirect" => url("/school-list"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went wrong",
                ]);
            }
            //}
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function listnewinstitute(Request $request)
    {

        if (get_childpermission(get_permission(session('Adminnewlogin')['type']), 'manageschool', 'view') != true) {
            return redirect("/no-permission");
        }
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }
            $qry = Users::where("type", "=", "6")
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');


            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });




            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);

                ///
                $chat = "";
                if (isset($res['manageschool'])) :
                    if (array_key_exists('manageschool', $res)) :
                        if (in_array('Manage Program', json_decode($res['manageschool'], true))) :
                            $chat = $this->generateChatLink($encryptedId);
                        endif;
                    endif;
                endif;
                ///

                $action = $this->generateActionButtons($encryptedStrId, $res);

                $data[] = [
                    "id" => $row->id,
                    "full_name" => $row->full_name,
                    "email" => $row->email,
                    "organizationtype" => $row->organizationtype,
                    "organizationname" => $row->organizationname,

                    "status" => $this->generateStatusButton($row->status, $row->id),
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        $where = ["type" => 6];
        // $user_list = Users::get_all_client_record($where);
        $user_list = array();
        return view("admin.institute.listinstitute", compact("user_list"));
    }

    private function generateChatLink($encryptedId)
    {
        $chatRoute = url('school-chat/' . $encryptedId);
        return "<a href='{$chatRoute}'><i class='fas fa-comment fa-lg'></i></a>";
    }

    private function generateActionButtons($encryptedStrId, $res)
    {
        $viewRoute = url('viewschooldetails/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = '';
        $programRoute = $programButton = '';
        if (isset($res['manageschool'])) :
            if (array_key_exists('manageschool', $res)) :
                if (in_array('Manage Program', json_decode($res['manageschool'], true))) :
                    $programRoute = url('viewschoolprogram/' . $encryptedStrId);

                    $programButton = "<a href='{$programRoute}'> <button class='btn btn-success btn-rounded'>Program</button></a>&nbsp;";
                endif;

                if (in_array('update', json_decode($res['manageschool'], true))) :
                    $editRoute = url('viewschool/' . $encryptedStrId);

                    $editButton = "<a href='{$editRoute}'><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>&nbsp;";
                endif;

                if (in_array('delete', json_decode($res['manageschool'], true))) :
                    $deleteButton = "<a class='delete_data_Institute' href='{$actionUrl}' data-id='{$encryptedStrId}'><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>&nbsp;";

                endif;

            endif;
        endif;


        $viewButton = "<a href='{$viewRoute}'><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>&nbsp;";


        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$programButton}{$editButton}{$viewButton}{$deleteButton}</div>";
    }



    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<button onclick="status_update(' . $id . ', 1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $id . '">' . __('messages.deactive') . '</button>';
            case 1:
                return '<button onclick="status_update(' . $id . ', 0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $id . '">' . __('messages.active') . '</button>';
            case 2:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deleted</button>';
            case 3:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deactivated</button>';
            default:
                return '';
        }
    }



    public function statuschange(Request $request)
    {
        $id = $request->id;
        $record = Users::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Users::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function deleteinstitute(Request $request)
    {
        $id = decrypt_str($request->id);
        $currentDate = date('Y-m-d');
        $exitsProgram = Programs::where("end_date", '>', $currentDate)->where("school_name", $id)->first();
        if ($exitsProgram) {
            return response()->json([
                "success" => false,
                "message" => "Unable to delete a school account because it's currently running a program. ",
            ]);
        }


        if (isset($id)) {
            $record = Users::where("id", $id)->first();
            if ($record) {
                $res = Users::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewinstitute($id)
    {
        $user_id1 = decrypt_str($id);
        $user_id = $id;
        $where = ["id" => $user_id1, "type" => 6];
        $user_list = Users::get_all_client_record($where);
        $district = District::where("status", "1")->get();
        $cbo = DB::table("tbl_cbo")
            ->where("status", "1")
            ->get();
        $school_contact_info = DB::table("tbl_school_contact_info")
            ->where("school_id", $user_id1)
            ->get();
        $gradeLavel = DB::table("tbl_classes")
            ->where("status", "1")
            ->get();
        return view(
            "admin.institute.editinstitute",
            compact(
                "user_list",
                "district",
                "user_id",
                "cbo",
                "gradeLavel",
                "school_contact_info"
            )
        );
    }

    public function viewinstitutedetails($id)
    {
        $user_id = decrypt_str($id);
        $school_contact_info = DB::table("tbl_school_contact_info")
            ->where("school_id", $user_id)
            ->get();
        $user_list = Users::where("users.id", $user_id)
            ->select("users.*", "cb.name as cname", "sp.name as dname")
            ->where("users.type", 6)
            ->leftjoin("tbl_districts as sp", "users.district", "=", "sp.id")
            ->leftjoin("tbl_cbo as cb", "users.cbo", "=", "cb.id")
            ->first();

        return view(
            "admin.institute.viewinstitute",
            compact("user_list", "user_id", 'school_contact_info')
        );
    }

    public function viewschoolprogram($id)
    {
        $user_id = decrypt_str($id);
        $user_list = DB::table("tbl_school_programs")
            ->where("tbl_school_programs.school_id", $user_id)
            ->join("tbl_programs as sp", "tbl_school_programs.program_id", "=", "sp.id")
            ->get();

        return view("admin.institute.programlist", compact("user_list"));
    }

    function getUserIpAddr()
    {
        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            //ip from share internet
            $ip = $_SERVER["HTTP_CLIENT_IP"];
        } elseif (!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            //ip pass from proxy
            $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } else {
            $ip = $_SERVER["REMOTE_ADDR"];
        }
        return $ip;
    }

    public function change_password($id)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        $user_id = decrypt_str($id);
        return view("admin.institute.changepassword", compact("user_id"));
    }

    public function updateChangePassword(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $new_password = $request->input("new_password");
        $confirm_password = $request->input("confirm_password");
        $id = $request->input("userid");

        $login = Users::where("id", $id)->first();

        if (!empty($login)) {
            if ($new_password == $confirm_password) {
                $insertpwd = bcrypt($new_password);
                Users::where("id", $id)->update([
                    "password" => $insertpwd,
                    // "passwordStr" => $new_password,
                ]);

                return response()->json([
                    "success" => true,
                    "message" => "Password successfully changed",
                    "redirect" => url("/viewschooldetails/" . encrypt_str($id)),
                ]);
            } else {
                return response()->json([
                    "success" => true,
                    "message" =>
                    "New password and Confirm password does not matchd",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Password does not exist",
            ]);
        }
    }

    public function sendcred(Request $request)
    {
        $id = $request->uid;
        $length = 6;
        $randpassword = substr(
            str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $login = Users::where("id", $id)->first();
        $email = $login["email"];
        $dataEmail = [
            "email" => $login["email"],
            "subject" => "Created credential",
            "mailbody" => "New credential",
            "first_name" => $login["first_name"],
            "last_name" => $login["last_name"],
            "randpassword" => $randpassword,
        ];

        Mail::send("admin.email-temp.signup", $dataEmail, function (
            $message
        ) use ($email) {
            $message->to($email)->subject("New credential");
            $message->from("<EMAIL>");
        });
        return response()->json([
            "success" => true,
            "message" => "Successfully credential sent",
        ]);
    }

    public function schoolManagementSetting()
    {

        $settings = DB::table('school_management_setting')->select('type')->distinct()->get();
        return view('admin.school-management-setting.content-setting.list-setting', compact('settings'));
    }

    public function addSchoolManagementSetting(Request $request)
    {
        $type = $request->type ?? 'all';
        if (in_array($type, ['requirement_for', 'position_type', 'certificate', 'language', 'per_hour_range', 'payment_frequency', 'program_type', 'credentialing_agency', 'profile_type', 'Learning_Management_Systems', 'Student_Management_Systems', 'Instructional_And_Engagement_Tools'])) {
            $data = DB::table('school_management_setting')->where('type', $type)->first();
        } else {
            $data = '';
        }

        return view('admin.school-management-setting.content-setting.add-setting', compact('type', 'data'));
    }

    public function storeSchoolManagementSetting(Request $request)
    {
        $settings = [
            "requirement_for" => ["value" => $request->requirement_for],
            "position_type" => ["value" => $request->position_type],
            "certificate" => ["value" => $request->certificate],
            "language" => ["value" => $request->language],
            "per_hour_range" => ["value" => $request->per_hour_range],
            "payment_frequency" => ["value" => $request->payment_frequency],
            "program_type" => ["value" => $request->program_type],
            "credentialing_agency" => ["value" => $request->credentialing_agency],
            "profile_type" => ["value" => $request->profile_type],
            "Learning_Management_Systems" => ["value" => $request->Learning_Management_Systems],
            "Student_Management_Systems" => ["value" => $request->Student_Management_Systems],
            "Instructional_And_Engagement_Tools" => ["value" => $request->Instructional_And_Engagement_Tools],
        ];

        $allEmpty = collect($settings)->every(function ($item) {
            $value = $item['value'];

            // Handle nested arrays like ['value' => [null]]
            if (is_array($value)) {
                return collect($value)->every(fn($v) => empty($v));
            }

            return empty($value);
        });

        if ($allEmpty) {
            session()->flash('settings_empty', 'No subcategory selected.');
            return redirect()->back();
        }







        foreach ($settings as $type => $value) {
            if (!empty($value['value'])) {
                $existingSettings = DB::table('school_management_setting')->where('type', $type)->where('id', $request->id)->get();

                foreach ($existingSettings as $key => $existingSetting) {
                    DB::table('school_management_setting')
                        ->where('id', $existingSetting->id)
                        ->update(['value' => json_encode($value['value'])]);
                }

                if ($type == "program_type") {
                    $programTypes = is_array($value['value']) ? $value['value'] : [$value['value']];
                    k12ConnectionCategorizedData::where('type', 'program_type')->delete();
                    foreach ($programTypes as $program) {
                        k12ConnectionCategorizedData::create([
                            'type' => 'program_type',
                            'description' => $program,
                            'name' => $program,
                        ]);
                    }
                }
            }
        }

        if ($existingSettings) {
            return redirect()->route('schoolManagementSetting')->with('success', 'Update Successfully!!');
        } else {
            return redirect()->route('schoolManagementSetting')->with('success', 'Add Successfully!!');
        }
    }

    public function deleteSchoolManagementSetting(Request $request)
    {

        $type = $request->type;


        if (in_array($type, ['requirement_for', 'position_type', 'certificate', 'language', 'per_hour_range', 'payment_frequency', 'program_type', 'credentialing_agency', 'profile_type', "Learning_Management_Systems", "Student_Management_Systems", "Instructional_And_Engagement_Tools"])) {
            $data = DB::table('school_management_setting')->where('id', $request->id)->first();

            if ($data) {
                $currentValues = json_decode($data->value);
                if (($key = array_search($request->value, $currentValues)) !== false) {
                    unset($currentValues[$key]);

                    $dataUpdate = DB::table('school_management_setting')->where('id', $request->id)->update([
                        'value' => json_encode(array_values($currentValues))
                    ]);
                }
            }

            if ($type == "program_type") {
                $programTypes = $currentValues; // No need for json_decode
                k12ConnectionCategorizedData::where('type', 'program_type')->delete();

                if (!empty($programTypes)) { // Check if array is not empty
                    foreach ($programTypes as $program) {
                        k12ConnectionCategorizedData::create([
                            'type' => 'program_type',
                            'description' => $program,
                            'name' => $program,
                        ]);
                    }
                }
            }
        }

        if ($dataUpdate) {
            return response()->json(["success" => true, "message" => "Successfully Deleted",]);
        } else {
            return response()->json(["success" => false, "message" => "Something went wrong",]);
        }
    }

    public function listPostRequirements(Request $request)
    {
        $schools = User::where('type', 6)->orderBy('full_name', 'ASC')->get();
        if ($request->ajax()) {
            $schoolId = $request->school;
            $query = NewSchoolPostRequirementModel::with(['school']);
            $query->whereHas('school', function ($q) use ($schoolId) {
                $q->where('id', $schoolId);
            });
            $requirements = $query->get();
            $view = view("admin.school-management-setting.requirements-components", compact('requirements', 'schools'))->render();
            return response()->json(['success' => true, 'view' => $view]);
        }
        $requirements = NewSchoolPostRequirementModel::with(['school'])->get();
        return view('admin.school-management-setting.requirements.requirements-list', compact('requirements', 'schools'));
    }
}
