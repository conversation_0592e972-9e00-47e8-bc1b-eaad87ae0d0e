<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\document_form;
use Session;
use Hash;
use Mail;
use Illuminate\Support\Facades\Crypt;

DB::enableQueryLog();
class FormController extends Controller
{
        /**
         * Display a listing of the Course.
         *
         * @return \Illuminate\Http\Response
         */
        public function index()
        {
            if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'manageform','view')!=true){
                return redirect("/no-permission");
            }  
            $form = document_form::all();
            return view("admin.form.index", compact("form"));
        }
    
        /**
         * Show the form for creating a new resource.
         *
         * @return \Illuminate\Http\Response
         */
        public function create()
        {
            return view("admin.form.add");
        }
    
        /**
         * Store a newly created resource in storage.
         *
         * @param  \Illuminate\Http\Request  $request
         * @return \Illuminate\Http\Response
         */
        public function store(Request $request)
        {
            $title = $request->title;
            if ($title != "") {
               
                    $data["title"] = $request->title;
                    $data["status"] = 1;
                    $data["created_at"] = date("Y-m-d H:i:s");
                    $data["updated_at"] = date("Y-m-d H:i:s");
                    if ($request->hasfile("file_data")) {
                        $image = $request->file("file_data");
                        // $extension = $file->getClientOriginalExtension(); // getting image extension
                        // $logopic = "image-" . time() . "." . $extension;
                        // $destinationPath = public_path("/uploads/form/");
                        // $file->move($destinationPath, $logopic);
                        $filename = 'uploads/form/'.uniqid() . '_' . $image->getClientOriginalName();
                        uploads3image($filename,$image);
                        $data["file"] = $filename;
                    }
                    $save = document_form::insert($data);
    
                    if ($save) {
                        return response()->json([
                            "success" => true,
                            "message" => "Form successfully added",
                        ]);
                    } else {
                        return response()->json([
                            "success" => false,
                            "message" => "Something went wrong",
                        ]);
                    }
                
            }else{
                return response()->json([
                    "success" => false,
                    "message" => "Enter required field",
                ]);
            }
        }
    
        /**
         * Display the specified resource.
         *
         * @param  int  $id
         * @return \Illuminate\Http\Response
         */
        public function show($id)
        {
            //
        }
    
        /**
         * Show the form for editing the specified resource.
         *
         * @param  int  $id
         * @return \Illuminate\Http\Response
         */
        public function edit(Request $request)
        {
            $id = Crypt::decryptString($request->id);
            $form = document_form::where("id", $id)->first();
        
            return view("admin.form.edit", [
                "form" => $form
            ]);
            return view("admin.form.edit");
        }
    
        /**
         * Update the specified resource in storage.
         *
         * @param  \Illuminate\Http\Request  $request
         * @param  int  $id
         * @return \Illuminate\Http\Response
         */
        public function update(Request $request)
        {
            $id = $request->id;
            $title = $request->title;
            if ($title != "") {
                    $data["title"] = $request->title;
                    if ($request->hasfile("file_data")) {
                        $image = $request->file("file_data");
                        $filename = 'uploads/form/'.uniqid() . '_' . $image->getClientOriginalName();
                        uploads3image($filename,$image);
                        $data["file"] = $filename;
                    }
                    $data["created_at"] = date("Y-m-d H:i:s");
                    $data["updated_at"] = date("Y-m-d H:i:s");
                    $save = document_form::where("id", $id)->update($data);
                    if ($save) {
                        return response()->json([
                            "success" => true,
                            "message" => "Details successfully updated",
                        ]);
                    } else {
                        return response()->json([
                            "success" => false,
                            "message" => "Something went wrong",
                        ]);
                    }
                
            }else{
                return response()->json([
                    "success" => false,
                    "message" => "Enter required field",
                ]);
            }
        }
    
        /**
         * Remove the specified resource from storage.
         *
         * @param  int  $id
         * @return \Illuminate\Http\Response
         */
      
         public function status_change(Request $request)
         {
             $id = $request->id;
             $record = document_form::where("id", $id)->first();
             if ($record->status == 1) {
                 $data["status"] = "0";
                 $res = document_form::where("id", $id)->update($data);
                 $message = "Status Deactivated Successfully.";
             } else {
                 $data["status"] = "1";
                 $res = document_form::where("id", $id)->update($data);
                 $message = "Status Activated Successfully.";
             }
     
             return response()->json(
                 ["status" => true, "message" => @$message],
                 200
             );
         }
    
        public function destroy(Request $request)
        {
            if (!Session::has("Adminnewlogin")) {
                return redirect("/admin");
            }
          
            $id = decrypt_str($request->id);
            if (isset($id)) {
                $record = document_form::where("id", $id)->first();
                if ($record) {
                    $res = document_form::where("id", "=", $id)->delete();
                    if ($res) {
                        return response()->json([
                            "success" => true,
                            "message" => "Successfully Deleted",
                        ]);
                    } else {
                        return response()->json([
                            "success" => false,
                            "message" => "Something went worng",
                        ]);
                    }
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Record not found",
                    ]);
                }
            }
        }
    
      
    }
    