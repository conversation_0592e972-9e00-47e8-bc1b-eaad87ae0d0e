<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProgramNoteStudent extends Model
{
    protected $table = 'program_note_students';

    protected $fillable = [
        'program_id', 'user_id', 'program_note_id', 'class_id', 'student'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function note()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
    public function class()
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }
}