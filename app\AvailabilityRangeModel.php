<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use DateTime;
use DateTimeZone;
class AvailabilityRangeModel extends Model
{
    protected $table = 'tbl_user_availability_ranges';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'availability_id', 'type', 'from_date', 'to_date', 'day_time_slots', 'step', 'position'
    ];
    
    public function timeSlots(){
        return $this->hasMany(AvailabilityDayTimeSlotsModel::class,'range_id');
    }
    
    public function availability()
    {
        return $this->belongsTo(AvailabilityModel::class,'availability_id');
    }
    
    public function getFromDateAttribute($value)
    {
        //from_date

        return $value ? date('m/d/Y',strtotime($value)) : null;
    }
    
    public function getToDateAttribute($value)
    {
        //to_date

        return $value ? date('m/d/Y',strtotime($value)) : null;

    }

    public function setFromDateAttribute($value)
    {
        //from_date

        return $value ? date('Y-m-d',strtotime($value)) : null;
    }
    
    public function setToDateAttribute($value)
    {
        //to_date

        return $value ? date('Y-m-d',strtotime($value)) : null;

    }

    public function getFromDateRawAttribute()
    {
        //from_date
        return $this->attributes['from_date']; //  raw attribute in the database

    }
    
    public function getToDateRawAttribute()
    {
        //to_date
        return $this->attributes['to_date']; //  raw attribute in the database

    }

}