<?php

namespace App\Http\Controllers;

use App\User;
use Illuminate\Http\Request;
use App\Models\HeatMap;

class HeatMapController extends Controller
{
    public function heatMap()
    {
        $schools = User::whereNotNull('full_name')->get();
        return view('admin.marketplace.heat-map.index', compact('schools'));
    }

    public function heatMapData(Request $request)
    {
        $heatMap = HeatMap::where('school_id', decrypt($request->schoolId))->get();
        return response()->json($heatMap);
    }
}
