<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChatAttachment extends Model
{
    // protected $table = 'chat_attachments';
    // protected $fillable = ['chatId', 'attachmentId'];
    protected $table = 'chat_attachments';
    protected $primaryKey = null; 
    public $incrementing = false;

    public function chat()
    {
        return $this->belongsTo(Chat::class, 'chatId');
    }

    public function attachment()
    {
        return $this->belongsTo(Attachment::class, 'attachmentId');
    }
}

