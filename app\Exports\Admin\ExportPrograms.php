<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportPrograms implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $filter = [];
        $filter['searchValue'] = $request['searchValue'];
        $filter['columnSortOrder'] = $request['columnSortOrder'];
        $filter['columnName'] = $request['columnName'];
        $this->requestFilters = $filter;
    }

    public function collection()
    {
        $programsQry = Programs::with(['school','subSubject','users']);
        $this->applyFilters($programsQry);

        return $programsQry->orderBy('id', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        if (!empty($this->requestFilters['searchValue'])) {
            $query->where(function ($q) {
                foreach ($this->requestFilters['columnName'] as $data) {
                    if ($data == 'id' || $data == 'name') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }

                    if ($data == 'school_name') {
                        $q->orWhereHas('school', function($subQuery) {
                            $subQuery->where('full_name', 'LIKE', "%{$this->requestFilters['searchValue']}%");
                        });
                    }

                    if ($data == 'course') {
                        $q->orWhereHas('subSubject', function($subQuery) {
                            $subQuery->where('name', 'LIKE', "%{$this->requestFilters['searchValue']}%");
                        });
                    }
                    
                    if ($data == 'instructore') {
                        $q->orWhereHas('nextCommingProgram', function($subQuery) {
                            $subQuery->whereHas('user', function($userQuery) {
                                $userQuery->where('full_name', 'LIKE', "%{$this->requestFilters['searchValue']}%");
                            });
                        });
                    }
                    if ($data == 'delivery_type') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }
                    
                    if ($data == 'start_date') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }
                    
                    if ($data == 'end_date') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }
                    
                    if ($data == 'program_type') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }
                    
                    if ($data == 'created_at') {
                        $q->orWhere($data, 'LIKE', "%{$this->requestFilters['searchValue']}%");
                    }
                    
                }
            });
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Program Name',
            'School Name',
            'Subject',
            'Main Instructor',
            'stand By Instructor',
            'Start Date',
            'End Date',
            'Status',
            'Program Type',
            'Created At',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {

        return [
            $row->id ? $row->id : 'NIL',
            $row->name ? $row->name : '',
            $row->school ? $row->school->full_name : '',
            $row->subSubject ? $row->subSubject->name : '',
            isset($row->nextCommingProgram->user_id) ? username($row->nextCommingProgram->user_id) : '',
            $row->id ? getstandbyusername($row->id) : '',
            $row->start_date ? date('m-d-Y', strtotime($row->start_date)) : '',
            $row->end_date ? date('m-d-Y', strtotime($row->end_date)) : '',
            $row->status ? 'Active' : 'Deactive',
            $row->program_type ? $row->program_type : '',
            $row->created_at ? getAdminTimestamp($row->created_at) : null,
        ];
    }
}
