<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'facebook' => [
        'client_id' => env('FACEBOOK_APP_ID'),
        'client_secret' =>env('FACEBOOK_SECRET'),
        'redirect' => env('FACEBOOK_REDIRECT')
    ],
    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT'),
    ],
    'zoom'=>[
        'api_key_1' => env('ZOOM_API_KEY'),
        'api_secret_1' => env('ZOOM_API_SECRET'),
        'account_key_1' => env('ZOOM_API_ACCOUNT'),
    ],
    'checkr'=>[
        'api_url' => env('CHECKR_API_URL'),
        'api_secret' => env('CHECKR_API_SECRET'),
    ],

    'location'=>[
        'location_key' => env('MAP_KEY')
    ],

    'node' => [
        'url' => env('NODE_URL'),
    ],

];
