<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddToolsColumnOnOnboardingInstructorExperiencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('onboarding_instructor_experiences', function (Blueprint $table) {
            $table->longText('tools')->nullable()->after('profile_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('onboarding_instructor_experiences', function (Blueprint $table) {
            $table->dropColumn('tools');
        });
    }
}
