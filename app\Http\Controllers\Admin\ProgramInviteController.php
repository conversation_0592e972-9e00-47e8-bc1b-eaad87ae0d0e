<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\invite_programs;
use App\Programs;
use App\InviteProgramNote;
use App\ProgramNote;
use Illuminate\Http\Request;
use Excel;
use App\Exports\Admin\ExportInvite;


class ProgramInviteController extends Controller
{

    public function index(Request $request)
    {
        $sidebarMenu = 'program-invite';

        $query = invite_programs::has('program')->with('program', 'user', 'program.school', 'program.schedules');
        $query->where('has_requested', '=', '0');
        $query->whereNotNull('deadline');

        $whereInIds = getAdminUserProgramIds();
        if (!empty($whereInIds)) {
            $query->whereIn('program_id', $whereInIds);
        }

        if ($request->has('expired')) {
            $query->where('deadline', '<', now()->toDateString());
            $query->where('is_approved', null);
            $query->where('tbl_invite_programs.status', null);
        } elseif ($request->has('status')) {
            $status = $request->filled('status') ? $request->status : null;

            $query->where('tbl_invite_programs.status', $status);
        }
        $query->where('is_auto_invite', 0);
        $query->where('user_id', '!=', null);

        $query->where('tbl_invite_programs.program_invite_type', null);
        $invites = $query->orderBy('tbl_invite_programs.id', 'DESC')->get();
        return view("admin.program.invites.list", compact("invites", "sidebarMenu"));
    }

    public function viewclasses($id, Request $request)
    {

        $currentDate = now()->toDateString();
        $program = new Programs;
        $invite = invite_programs::findOrFail($id);
        $timezone = $invite->timezone ?? 'America/Los_Angeles';
        $noteIds = InviteProgramNote::where('invite_program_id', $id)->pluck('program_note_id')->toArray();

        $user_id = $invite->user_id;
        if ($invite->admin_type == 1 || $invite->is_standby == 1) {
            if ($invite->status == 1) {
                if ($invite->replacement_start_date){
                    $classes = ProgramNote::where('program_id', $invite->program_id)
                    ->where('class_date', '>=', $invite->replacement_start_date)
                    ->where('user_id', $user_id)
                    ->when(!empty($noteIds), function ($qry) use ($noteIds) {
                        $qry->whereIn('id', $noteIds);
                    })
                    ->when(empty($noteIds), function ($qry) {
                        $qry->whereNull('is_makeup_class');
                    })
                    ->get();
                } else {
                    $classes = ProgramNote::where('program_id', $invite->program_id)
                        ->where('user_id', $user_id)
                        ->when(!empty($noteIds), function ($qry) use ($noteIds) {
                            $qry->whereIn('id', $noteIds);
                        })
                        ->when(empty($noteIds), function ($qry) {
                            $qry->whereNull('is_makeup_class');
                        })
                        ->get();
                }
            } else {
                if ($invite->replacement_start_date) {

                    $classes = ProgramNote::where('class_date', '>=', $invite->replacement_start_date)
                        ->where('program_id', $invite->program_id)
                        ->whereNull(['note', 'status', 'sub_user_id'])->get();
                } else {

                    $classes = ProgramNote::where('program_id', $invite->program_id)
                        ->when(!empty($noteIds), function ($qry) use ($noteIds) {
                            $qry->whereIn('id', $noteIds);
                        })->when(empty($noteIds), function ($qry) {
                            $qry->whereNull('is_makeup_class');
                        })
                        ->get();
                }
            }
        } elseif ($invite->admin_type == 0) {

            $classes = ProgramNote::whereIn('id', $noteIds)
                ->get();
        }


        $view = view("components.admin.modals.classes.viewclasses", compact('classes', 'timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function export(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Invite'.time().'.xlsx';
            return Excel::download(new ExportInvite($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}
