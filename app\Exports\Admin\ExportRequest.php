<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{AvailabilityModel, invite_programs, ProgramNote, Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportRequest implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->searchInput;
    }

    public function collection()
    {
        $query = invite_programs::with('program', 'requester', 'program.school', 'program.schedules', 'notes.programNote');
        $whereInIds = getAdminUserProgramIds();
        if (!empty($whereInIds)) {
            $query->whereIn('program_id', $whereInIds);
        }
        $query->whereNotNull('replacement_type');
        $query->whereNotNull('requested_by');
        $this->applyFilters($query);

        return $query->orderBy('created_at', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        if (!empty($this->requestFilters)) {
            $query->where(function ($q) {
                // Program ID filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('program_id', $this->requestFilters);;
                });

                // Name filter
                $q->orWhereHas('requester', function ($subQuery) {
                    $subQuery->where('first_name', 'LIKE', '%' . $this->requestFilters . '%')
                              ->orWhere('last_name', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Program type filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('program_type', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Delivery type filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('delivery_type', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Requested Instructor Type filter
                if (str_contains(strtolower($this->requestFilters), 'sub')) {
                    $searchText = 0;
                    $q->orWhere('replacement_type', $searchText)
                    ->whereHas('notes', function ($noteQuery) {
                        $noteQuery->whereNotNull('id');
                    });
                } elseif (str_contains(strtolower($this->requestFilters), 'main')) {
                    $searchText = 1;
                    $q->orWhere('replacement_type', $searchText);
                }
            });
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Requester Name',
            'Program Type',
            'Delivery Type',
            'Start date',
            'End date',
            'Class Date',
            'Requested date',
            'Replacement start date',
            'Requested Instructor Type',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        $formattedDateTime = '';
        if ($row->notes->isNotEmpty()) {
            $visibleNotes = 1;
            foreach ($row->notes as $index => $note) {
                if ($note->programNote) {
                    if ($index < $visibleNotes) {
                        if ($note->programNote->class_date) {
                            $classDate = explode(" ", $note->programNote->class_date)[0];
                            $startTime = $note->programNote->start_time;
                            $dateTimeString = $classDate . ' ' . $startTime;
                            $classDateTime = new DateTime($dateTimeString);
                            $formattedDateTime = $classDateTime->format('m-d-Y h:i A');
                        } else {
                            $formattedDateTime = '';
                        }
                    }
                }
            }
        }
        return [
            $row->program_id ? $row->program_id : 'NIL',
            $row->requester ? $row->requester->first_name . ' ' . $row->requester->last_name : '',
            $row->program ? $row->program->program_type : '',
            $row->program ? $row->program->delivery_type : '',
            $row->program ? date('m-d-Y', strtotime($row->program->start_date)) : '',
            $row->program ? date('m-d-Y', strtotime($row->program->end_date)) : '',
            $formattedDateTime,
            getAdminTimestamp($row->created_at),
            getAdminTimestamp($row->replacement_start_date),
        ];
    }
}
