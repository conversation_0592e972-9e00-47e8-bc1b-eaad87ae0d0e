<?php

namespace App\Imports;

use App\RosterModel;
use App\Programs;
use App\Classes;

use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;
use DB;
class StudentImport implements ToModel, WithHeadingRow
{
    protected $program;
    protected $validationErrors = [];

    public function __construct(Programs $program)
    {
        $this->program = $program;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        if (!empty($row['student']) && !empty($row['grade_level'])) {

            // Format the date and time before validation
           
          
            $validator = Validator::make($row, [
                'student' => 'required',
                'grade_level' => 'required'
            ]);

            if ($validator->fails()) {
                throw new \Exception("Import Excel error: <br>" . implode('<br>', $validator->errors()->all()));
            }

       $gradeExit = Classes::where("class_name", $row['grade_level'])->first();
       if($gradeExit){
       
        $programgradeExit = DB::table('program_class')->where("class_id", $gradeExit->id)->where("program_id", $this->program->id)->first();
        
        if($programgradeExit){

        $obj = RosterModel::firstOrNew(['student_name' => $row['student'],'class_id' => $row['grade_level']]);
        $obj->program_id = $this->program->id;
        return $this->fillModelData($obj, $row);
        }else{
            return [];
        }

       }else{
        return [];
       }
           

        } elseif (empty($row['student']) && empty($row['grade_level'])) {
            return [];
        } else {
            $validator = Validator::make($row, [
                
                'student' => 'required',
                'grade_level' => 'required',
            ]);

            if ($validator->fails()) {
                throw new \Exception("Import Excel error: <br>" . implode('<br>', $validator->errors()->all()));
            }
        }
    }

    protected function fillModelData(RosterModel $obj, array $row): void
    {

        $obj->student_name = $row['student'];
        $obj->class_id = $row['grade_level'];
        $obj->save();
    }
}
