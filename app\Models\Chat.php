<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Chat extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'sender', 'sender_role', 'recipient', 'recipient_role',
        'content', 'delivered_at', 'readed_at', 'message_type',
        'referance_id', 'parentChat'
    ];

    protected $table = 'chats';
    protected $primaryKey = 'id';
    public $incrementing = false; 
    protected $keyType = 'binary'; 
    // protected $fillable = ['id', 'parentChatId'];

    protected $dates = ['delivered_at', 'readed_at'];

    // Parent Chat Relationship (Self-Referencing)
    public function parentChat()
    {
        return $this->belongsTo(Chat::class, 'parentChatId', 'id');
    }
    public function attachments()
    {
        return $this->belongsToMany(Attachment::class, 'chat_attachments', 'chatId', 'attachmentId');
    }

    // Relationship with PlatformSchoolRequirements
    public function requirement()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'referance_id', 'id');
    }
}

