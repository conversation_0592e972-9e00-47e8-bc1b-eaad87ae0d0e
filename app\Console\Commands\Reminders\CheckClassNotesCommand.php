<?php

namespace App\Console\Commands\Reminders;

use App\notification;
use App\Notification_content;
use App\ProgramNote;
use Illuminate\Console\Command;

class CheckClassNotesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:check-class-notes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to All instructors with no class notes submission for classes completed on that day";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = $this->signature;
        $notes = ProgramNote::select('class_date', 'user_id', 'sub_user_id', 'program_id')
            ->whereNull(["status", "note"])
            ->where(function ($query) {
                $query->whereNotNull("user_id")
                    ->orWhereNotNull("sub_user_id");
            })
            ->where("class_date", "=", now()->toDateString())
            ->with('program:id,school_name', 'program.school:id,full_name')
            ->get();




        if (!empty($notes)) {
            foreach ($notes as $note) {

                $encryptedId = encrypt($note->program_id);
                $link = route('user.program-detail', ['encryptedId' => $encryptedId]);


                $template = Notification_content::where("signature", $signature)->first();
                $body =  @$template->content;

                $body = str_replace('{{link}}', $link, $body);
                $body = str_replace('{{class_date}}', $note->class_date->format('m/d/Y'), $body);
                if(@$note->program && @$note->program->school_name){
                    $school_name = schoolusername($note->program->school_name);
                    $body = str_replace('{{school_name}}','at '. $school_name, $body);
                }

                if ($note->user_id) {
                    $user_id = $note->user_id;
                } else {
                    $user_id = $note->sub_user_id;
                }
                notification::insert([
                    'title' => 'notification',
                    'user_id' => $user_id,
                    'program_id' => null,
                    'notification' => $body,
                    'type' => "user",
                    'user_type' =>  "user",
                ]);
            }
        }
    }
}
