<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateBudgetStatesAndSubjectBudgetV1 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Step 1: Clear subject_budget_v1 data
        // DB::table('subject_budget_v1')->truncate();
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            $table->dropForeign(['state_id']);
        });

        // Step 2: Drop old budget_states table
        Schema::dropIfExists('budget_states');

        // Step 3: Create new budget_states_v1 table
        Schema::create('budget_states_v1', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('in_person');
            $table->integer('case_management');
            $table->integer('bilingual_inc');
            $table->integer('sped_rec_comp');
            $table->timestamps();
        });

        // Step 4: Add foreign key from subject_budget_v1 → budget_states_v1
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            $table->foreign('state_id')->references('id')->on('budget_states_v1')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        // Step 1: Drop FK to budget_states_v1
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            $table->dropForeign(['state_id']);
        });

        // Step 2: Drop budget_states_v1
        Schema::dropIfExists('budget_states_v1');

        // Step 3: (Optional) Recreate old budget_states table
        Schema::create('budget_states', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        // Step 4: Recreate foreign key to budget_states
        Schema::table('subject_budget_v1', function (Blueprint $table) {
            $table->foreign('state_id')->references('id')->on('budget_states')->onDelete('cascade');
        });
    }
}
