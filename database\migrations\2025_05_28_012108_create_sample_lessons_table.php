<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSampleLessonsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sample_lessons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');

            // Foreign key constraint
            $table->foreign('user_id')
                ->references('id')
                ->on('new_onboarding_instructor')
                ->onDelete('cascade');

            $table->string("subject_id")->nullable();
            $table->string("file_url")->nullable();
            $table->string("additional_notes")->nullable();
            $table->string("file_name")->nullable(); // fixed typo and added nullable
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sample_lessons');
    }
}
