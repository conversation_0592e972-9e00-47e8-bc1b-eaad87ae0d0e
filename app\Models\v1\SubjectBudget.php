<?php

namespace App\Models\v1;


use Illuminate\Database\Eloquent\Model;

class SubjectBudget extends Model
{

    protected $table = 'subject_budget_v1';
    protected $fillable = ['subject_id', 'state_id', 'base_pay_0_3', 'pay_3_6', 'pay_6_10', 'pay_10_plus', 'masters_inc', 'doctorate_inc', 'non_tech_time', 'bilingual_inc', 'sped_rec_comp', 'curriculum_inc'];

    // Define the relationship with subjects_v1 table
    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
}
