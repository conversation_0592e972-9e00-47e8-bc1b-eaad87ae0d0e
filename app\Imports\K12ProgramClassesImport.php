<?php

namespace App\Imports;

use App\Models\k12ConnectionClasses;
use App\Models\k12ConnectionPrograms;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;

class k12ProgramClassesImport implements ToModel, WithHeadingRow
{
    protected $program;
    protected $timezone;
    protected $end_date;
    protected $validationErrors = [];

    public function __construct(k12ConnectionPrograms $program)
    {
        $this->program = $program;
        $this->timezone = $program->timezone;
        $this->end_date = $program->end_date;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {

        if (!empty($row['class_date']) && !empty($row['start_time']) && !empty($row['end_time'])) {

            // Format the date and time before validation
            // $formattedRow = [
            //     'class_date' => Carbon::parse(Date::excelToDateTimeObject(preg_replace('/\D/', '', $row['class_date']))->format('Y-m-d')),
            //     'start_time' => Carbon::parse(Date::excelToDateTimeObject($row['start_time'])->format('h:i A')),
            //     'end_time' => Carbon::parse(Date::excelToDateTimeObject($row['end_time'])->format('h:i A')),
            // ];

            $formattedRow = [
                'class_date' => Carbon::parse(Date::excelToDateTimeObject(preg_replace('/\D/', '', $row['class_date']))->format('Y-m-d'))->toDateString(),
                // 'class_date' => @$row['class_date'] ? Carbon::parse($row['class_date'])->format('Y-m-d') : null,
                // 'class_date' =>$row['class_date']->format('Y-m-d'),
                // 'start_time' => Carbon::parse(Date::excelToDateTimeObject($row['start_time'])->format('h:i A')),
                'start_time' => Carbon::parse(Date::excelToDateTimeObject($row['start_time']))->format('h:i A'),
                'end_time' => Carbon::parse(Date::excelToDateTimeObject($row['end_time']))->format('h:i A'),
                // 'end_time' => Carbon::parse(Date::excelToDateTimeObject($row['end_time'])->format('h:i A')),
            ];


            $currentDate = date('Y-m-d');



            $classDateWithoutTime = $formattedRow['class_date'];

            if ($classDateWithoutTime < $currentDate || $classDateWithoutTime > $this->end_date) {

                return [];
            }


            $validator = Validator::make($formattedRow, [
                'class_date' => 'required|date',
                'start_time' => 'required',
                'end_time' => 'required|after:start_time',
            ]);

            if ($validator->fails()) {
                throw new \Exception("Import Excel error: <br>" . implode('<br>', $validator->errors()->all()));
            }

            $obj = k12ConnectionClasses::firstOrNew(['class_date' => $formattedRow['class_date'],'program_id' => $this->program->id]);
            $obj->user_id = @$obj->user_id ?? null;
            $obj->program_id = $this->program->id;

            return $this->fillModelData($obj, $formattedRow);
        } elseif (empty($row['class_date']) && empty($row['start_time']) && empty($row['end_time'])) {

            return [];
        } else {
        }
    }

    protected function fillModelData(k12ConnectionClasses $obj, array $row): void
    {


        $obj->class_date = $row['class_date'];
        $obj->day = date('N', strtotime($row['class_date']));

        $obj->start_time = formatTimeForTimezone($row['start_time'],$this->timezone??'America/Los_Angeles');
        $obj->end_time = formatTimeForTimezone($row['end_time'],$this->timezone??'America/Los_Angeles');

        $obj->save();
    }
}
