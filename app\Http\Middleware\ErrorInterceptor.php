<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Validation\ValidationException;
use App\Models\ErrorLog;

class ErrorInterceptor
{
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
                $content = $response->getContent();
                $data = json_decode($content, true);
            // Check for response status codes 400 or higher (client or server errors)
            if ($response->getStatusCode() >= 400 || ($data && isset($data['success']) && $data['success'] === false) ) {
                $this->logError($request, new \Exception("HTTP Error: " . $response->getStatusCode() . " - " . $response->statusText()), $response->getStatusCode()); // Log as exception
            }

            return $response;

        } catch (\Exception $e) {
            $this->logError($request, $e); // Log the caught exception
            if ($e instanceof ValidationException) {
                $errors = $e->validator->errors();
                if ($request->expectsJson()) {
                    return response()->json(['errors' => $errors], 422);
                } else {
                    return redirect()->back()->withErrors($errors)->withInput();
                }
            } else {
                return $response;
            } 
        }
    }

    private function logError(Request $request, \Exception $e, $statusCode = null) {

        ErrorLog::create([
            'message' => $e->getMessage(),
            'exception_type' => get_class($e),
            'stack_trace' => $e->getTraceAsString(),
            'request_data' => json_encode($request->all()),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
            'status_code' => $statusCode, // Store the status code
        ]);

        \Log::error($e);  // Keep logging to files for detailed debugging
    }
}