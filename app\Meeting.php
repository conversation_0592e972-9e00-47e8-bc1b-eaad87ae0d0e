<?php

namespace App;

use App\Casts\TimeCast;
use Illuminate\Database\Eloquent\Model;

class Meeting extends Model
{
    protected $table = 'meetings';

    protected $fillable = [
        'date', 'start_time', 'end_time', 'link', 'zoom_link', 'created_by', 'program_id', 'status', 'meeting_type'
    ];
        protected $casts = [
        'start_time' => TimeCast::class,
        'end_time' => TimeCast::class,
    ]; 
    protected $dates = ['date'];

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function programNotes()
    {
        return $this->belongsToMany(ProgramNote::class, 'meeting_program_note', 'meeting_id', 'program_note_id');
    }
    public function classNotes()
    {
        return $this->hasMany(ProgramNote::class);
    }
}
