<style>
    #recordingConfirmationModal #recording-confirmation-text {
        color: #004CBD;
    }
    
    #recordingConfirmationModal #recording-confirmation {
        color: #004CBD;
        border: 1px solid #004CBD;
        border-radius: 50px;
        font-weight: 500;
        background: transparent;
    }
    
    #recordingConfirmationModal #recording-confirmation:hover {
        color: #ffffff;
        background-color: #004CBD;
    }
    
    #recordingConfirmationModal #recording-confirmation-cancel {
        color: #787777;
        border: 1px solid #787777;
        border-radius: 50px;
        font-weight: 500;
    }
    
    #recordingConfirmationModal #recording-confirmation-cancel:hover {
        color: #ffffff;
        border: 1px solid #787777;
        background-color: #787777;
    }
    .force-center {
    display: flex !important;
    justify-content: center !important;
}
</style>

<div class="modal fade" id="recordingConfirmationModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body pt-5">
                <button type="button" class="btn-close" style="float: right;" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="invite-svg text-center">
                </div>
                <h3 class="text-center fw-500 pt-4" id="recording-confirmation-title" style="color: #004CBD;">

                </h3>
                <p id="recording-confirmation-text" class="text-center pt-4 mb-0">
                    
                </p>
                <div class="addProctor-btn d-flex justify-content-between pt-5">
                    <a href="javascript:void(0);" class="invite-close-btn px-4" id="recording-confirmation-cancel">Cancel</a>
                    <button type="button" class="btn btn-primary px-4" id="recording-confirmation">Yes, Stop</button>
                </div>
            </div>
        </div>
    </div>
</div>