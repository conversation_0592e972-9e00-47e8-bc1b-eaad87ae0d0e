<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProgramAdminNotesRequest;
use App\Http\Requests\Admin\ProgramViewClassRequest;
use App\Meeting;
use App\ProgramAdminNote;
use App\ProgramNote;
use App\Programs;
use Illuminate\Http\Request;


class ProgramViewClassController extends Controller
{

    public function list($eId, Request $request)
    {
        $programId = decrypt($eId);
        $program = Programs::findOrFail($programId);
        if ($request->ajax()) {


            $params = DataTableHelper::getParams($request);

            $qry = $program->userNotes();
            $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $action = $this->generateActionButtons($row,$res);

                $data[] = [
                    "id" => $i,
                    "class_date" => date('m-d-Y', strtotime($row->class_date)),
                    "day" => getDayName($row->day),
                    "start_time" => date('h:i A', strtotime($row->start_time)),
                    "end_time" => date('h:i A', strtotime($row->end_time)),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view('admin.program.view-classes', compact('program'));
    }

    private function generateActionButtons($row,$res)
    {
        $editButton = $deleteButton = '';


        if (isset($res['manageprogram'])) {
            $actions = json_decode($res['manageprogram'], true);
            $status = $row->status;


            if (is_null($status) && $row->class_date>now()) {
                $editButton = in_array('update', $actions) ? $this->getEditButton($row) : '';
                $deleteButton = in_array('delete', $actions) ? $this->getDeleteButton($row) : '';
            } else {

                if($status == '1'){
                    $editButton = 'Completed';
                }elseif($status == '0'){
                    $editButton = 'Cancelled';
                }else{
                    $editButton = '';

                }

            }
        }

        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$deleteButton}</div>";
    }

    private function getEditButton($row)
    {
        $editRoute = route('admin.program.view-class.edit', ['programNote' => $row->id]);
        return "<a href='javascript:void(0);' onclick=openAdminModal('$editRoute') class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
    }

    private function getDeleteButton($row)
    {
        $deleteRoute = route('admin.program.view-class.delete', ['programNote' => $row->id]);
        return "<a href='javascript:void(0);' onclick=deleteRow('$deleteRoute') class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";
    }



    public function editViewClass(ProgramNote $programNote, Request $request)
    {

        $program = Programs::where('id',$programNote->program_id)->first();
        $view = view("components.admin.modals.edit-view-class", compact('programNote','program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateViewClass(ProgramNote $programNote, ProgramViewClassRequest $request)
    {
        $programNote->class_date = date('Y-m-d', strtotime($request->class_date));
        $programNote->start_time = date('H:i', strtotime($request->start_time));
        $programNote->end_time = date('H:i', strtotime($request->end_time));
        $programNote->day = date('N', strtotime($request->class_date));
        $programNote->save();

        return response()->json(['status' => true, 'message' => "Class saved successfully"]);
    }

    public function deleteViewClass(ProgramNote $programNote)
    {
        $res = $programNote->forceDelete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }
    public function createViewClass(Programs $program)
    {
        $view = view("components.admin.modals.create-view-class", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeViewClass(Programs $program, ProgramViewClassRequest $request)
    {
        $programNote = new ProgramNote();
        $programNote->user_id = null;
        $programNote->program_id = @$program->id ?? null;

        $programNote->class_date = date('Y-m-d', strtotime($request->class_date));

        $programNote->start_time = date('H:i:s', strtotime($request->start_time));

        $programNote->end_time = date('H:i:s', strtotime($request->end_time));
        $programNote->day = date('N', strtotime($request->class_date));

        $programNote->save();

        return response()->json(['status' => true, 'message' => "Class saved successfully"]);
    }
}
