<?php
namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use Session;
use DB;
use App\Http\Requests;
use App\Users;
use Validator;
use View;
use URL;
use DateTime;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
DB::enableQueryLog();

class AdminforgetpassController extends Controller
{
    public function forgot_password(Request $request)
    {
        return view("admin.forgetpassword.forgot-password");
    }
}
