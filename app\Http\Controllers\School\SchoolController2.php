<?php

namespace App\Http\Controllers\School;

use App\Classes;
use App\AgencyCertificatesModel;
use App\Models\PlatformSchoolProctor;
use App\CredentialingAgencyModel;
use App\EducationListModel;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use DB;
use App\View\Components\PublicProfile;
use App\StateModel;
use App\GradeLevelModel;
use App\Imports\k12StudentImport;
use App\InstructorThirdStepOnboardingModel;
use App\LastChatModel;
use App\Models\k12ConnectionCategorizedData;
use App\Models\k12ConnectionClasses;
use App\Models\k12ConnectionMeetingLinks;
use App\Models\k12ConnectionProgramNotes;
use App\Models\k12ConnectionPrograms;
use App\Models\PlatformSchoolInvites;
use App\Models\PlatformSchoolRequirements;
use App\Models\PlatformSchoolRoster;
use App\Models\SchoolReviewApplicants;
use App\Subject;
use App\NewSchoolPostRequirementModel;
use App\OnboardingInstructor;
use App\SchoolInstructorHiring;
use App\ShortlistInstructorModel;
use App\SubsubjectModel;
use App\User;
use App\Users;
use App\ZoomModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Excel;
use App\Models\k12ConnectionProgramsSchedule;
use App\Services\ZoomService;
use App\ZoomlinkModel;
use App\Exports\Front\ExportDatatable;
use App\Exports\Front\ExportSampleRoster;
use App\Helpers\DataTableHelper;
use App\Models\Chat;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\PlatformSchoolUserSave;
use App\Models\PlatformSchoolUserSaveList;
use App\Exports\DataExport;
use App\Helpers\SchoolCalanderHelper;
use App\Models\PlatformSchoolCalendersModel;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\SubjectArea;
use App\NotificationRequirement;
use App\Models\v1\SubjectBudget;
use App\PlatformInstructorCategory;
use App\PlatformSchoolCategoryList;
use Exception;
use Illuminate\Support\Facades\Http;

class SchoolController2 extends Controller
{
    protected $zoomService;
    public function __construct(ZoomService $zoomService)
    {
        $this->zoomService = $zoomService;
    }

    public function dashboardSchool()
    {
        if (auth()->user() && auth()->user()->type == 6 && auth()->user()->cust_type != 'Platform') {
            return redirect()->route('indexschool');
        }
        $authUserId = auth()->user()->id;
        $requirementsPostedCount  = PlatformSchoolRequirements::where('school_id', $authUserId)->where(['status' => 'open'])->count();
        $hiredEducatorsCount  = SchoolInstructorHiring::where('school_id', $authUserId)->where('status', 'accepted')->count();

        $postrequirements = PlatformSchoolRequirements::where(['status' => 'open'])->latest()->take(30)->get();

        $requirements = PlatformSchoolRequirements::with(['reviewApplicants.user'])->withCount('reviewApplicants')->where('school_id', auth()->user()->id)->latest()->limit(2)->get();

        $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6'])->where('user_status', 'Active')->latest()->get();

        $marketplaceInstructorIds = OnboardingInstructorMarketplaceContract::pluck('user_id')->toArray();
        $whizaraInstructorIds = OnboardingInstructorContract::pluck('user_id')->toArray();
        $subjectArea = SubjectArea::with('subjects')->get();

        // Combine both contract instructor IDs and make them unique
        $validInstructorIds = array_unique(array_merge($marketplaceInstructorIds, $whizaraInstructorIds));

        $users = $users->filter(function ($user) use ($validInstructorIds) {
            return in_array($user->id, $validInstructorIds);
        })->take(3);
        return view('school-marketplace.school-dashboard', compact('requirements', 'postrequirements', 'users', 'requirementsPostedCount', 'hiredEducatorsCount','subjectArea'));
    }

    public function postRequirements($id = null)
    {
        if (auth()->user()->cust_type == 'Platform') {
            $data = '';
            if (!empty($id)) 
            {
                $id = decrypt_str($id);
                $data = PlatformSchoolRequirements::find($id);
            }

            return view('school-marketplace.post-requirements', compact('data', 'id'));
        } else {
            return redirect()->route('indexschool');
        }
    }

    public function newSchoolGetTabData(Request $request)
    {
        $schoolData = '';
        if (!empty($request->id)) {
            $id = decrypt_str($request->id);
            $schoolData = PlatformSchoolRequirements::find($id);
        }

        $tab = $request->tab;
        $requirement = DB::table('school_management_setting')->where('type', 'requirement_for')->first();
        $position = DB::table('school_management_setting')->where('type', 'position_type')->first();
        $certificates = DB::table('school_management_setting')->where('type', 'certificate')->first();
        $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
        $languages = DB::table('school_management_setting')->where('type', 'language')->first();
        $profile_type = DB::table('school_management_setting')->where('type', 'profile_type')->first();
        $grades = Classes::all();
        // $subjectArea = Subject::with('subSubjects')->get();
        $subjectArea = SubjectArea::with('subjects')->get();
        $per_hour_range = DB::table('school_management_setting')->where('type', 'per_hour_range')->first();
        $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
        $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();

        $schools = User::where(['type' => 6, 'cbo' => auth()->user()->cbo, 'district' => auth()->user()->district])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', auth()->user()->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', auth()->user()->id)->where('type', 'district')->first();

        $view = view("school-marketplace.post-requirement-components.{$tab}", compact('requirement', 'position', 'states', 'certificates', 'languages', 'grades', 'subjectArea', 'per_hour_range', 'schools', 'program_type', 'timezone', 'schoolData', 'profile_type', 'school_calender', 'district_calender'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function subjectBudget($id){
        try{
            $subjectBudget = SubjectBudget::where('subject_id', $id)->first();
            if (!$subjectBudget) {
                return response()->json(['status' => false, 'message' => 'Subject budget not found']);
            }
            $totalHours = request()->get('total_hours');
            $provideCurriculum = request()->get('provide_curriculum');
            $qualification = request()->get('qualification');
            $experience = request()->get('experience');

            // Step 1: Curriculum Increment
            $curriculumInc = $provideCurriculum ? ($subjectBudget->curriculum_inc ?? 0) : 0;

            // Step 2: Qualification Increment
            $qualificationInc = 0;
            if ($qualification == "Master’s") {
                $qualificationInc = $subjectBudget->masters_inc ?? 0;
            } elseif ($qualification == "Doctorate") {
                $qualificationInc = $subjectBudget->doctorate_inc ?? 0;
            }

            // Step 3: Calculate for each experience range
            $basePay = $subjectBudget->base_pay_0_3 ?? 0;

            // Calculate for each experience range
            $budgets = [
                '3'  => ($curriculumInc + $qualificationInc + $basePay) * $totalHours,
                '6'  => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_3_6 ?? 0)) * $totalHours,
                '10' => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_6_10 ?? 0)) * $totalHours,
                '11' => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_10_plus ?? 0)) * $totalHours,
            ];

            return response()->json(['status' => true, 'subjectBudget' => $subjectBudget, 'budgets' => $budgets]);
        } catch (Exception $e) {
            log::info($e->getMessage());
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    // public function get_subsubjects(Request $request)
    // {
    //     $data['subsubject'] = subsubject($request->id);
    //     $data['schoolData'] = '';
    //     if (!empty($request->reqId)) {
    //         $data['schoolData'] = PlatformSchoolRequirements::find(decrypt_str($request->reqId));
    //     }
    //     return view('school-marketplace.post-requirement-components.subsubject')->with($data);
    // }

    public function get_subsubjects(Request $request)
    {
        $data['subsubject'] = v1SubSubject($request->id);
        $data['schoolData'] = '';
        if (!empty($request->reqId)) {
            $data['schoolData'] = PlatformSchoolRequirements::find(decrypt_str($request->reqId));
        }
        return view('school-marketplace.post-requirement-components.subsubject')->with($data);
    }

    public function storePostRequirement(Request $request)
    {
        $data = [];
        $calender1 = [];
        $calender2 = [];
        $requirements = '';

        if (!empty($request->requirementTitle)) {
            $data['requirement_type'] = $request->requirementType;
            $data['requirement_title'] = $request->requirementTitle;
            $data['requirement_name'] = $request->requirementName;
            $data['school_id'] = $request->schoolName;
            $data['class_type'] = $request->classType;
            $data['delivery_mode'] = !empty($request->deliveryMode) ? implode(',', $request->deliveryMode) : null;
            $data['subject_area_id'] = v1SubjectId($request->sub_subject);
            $data['subject_id'] = $request->sub_subject;
            $data['grade_levels_id'] = !empty($request->gradeLevels) ? implode(',', $request->gradeLevels) : null;
            $data['capacity'] = $request->numberOfStudents;
            $data['address'] = $request->address;
            $data['city'] = $request->city;
            $data['state'] = $request->state;
            $data['zip_code'] = $request->zipcode;
            $data['country'] = $request->country;
            $data['description'] = $request->requirementDescription;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }


        if (empty($request->id)) {
            $requirements = PlatformSchoolRequirements::insertGetId($data);
        }
        if (!empty($request->class_start_date)) {
            $data['start_date'] = Carbon::createFromFormat('m/d/Y', $request->class_start_date)->format('Y-m-d');
        }

        if (!empty($request->class_end_date)) {
            $data['end_date'] = Carbon::createFromFormat('m/d/Y', $request->class_end_date)->format('Y-m-d');
        }

        // if (!empty($request->timezone)) {
        //     $data['time_zone'] = $request->timezone;
        // }

        // if (!empty($request->no_class_dates)) {
        //     $data['no_class_dates'] = json_encode($request->no_class_dates);
        // }

        // if (!empty($request->schedule)) {
        //     $schedules = [];
        //     foreach ($request->schedule as $index => $day) {
        //         $schedules[] = [
        //             'day' => $day,
        //             'start_time' => $request->schedule_start_time[$index] ?? null,
        //             'end_time' => $request->schedule_end_time[$index] ?? null,
        //         ];
        //     }
        //     $data['schedules'] = json_encode($schedules);
        // }

        // if (!empty($request->totalHours)) {
        //     $data['totalHours'] = $request->totalHours;
        // }

        if (!empty($request->no_instrtructional_days)) {
            $data['no_instrtructional_days'] = $request->no_instrtructional_days;
        }

        if (!empty($request->class_duration)) {
            $data['class_duration'] = $request->class_duration;
        }

        if (!empty($request->no_non_instructional_hr)) {
            $data['no_non_instructional_hr'] = $request->no_non_instructional_hr;
        }

        if (!empty($request->scheduleType)) {
            $data['schedule_type'] = $request->scheduleType;
        }

        if (!empty($request->regular_days)) {
            $schedules = [];
            foreach (json_decode($request->regular_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_start_time ?? null,
                    'end_time' => $request->schedule_end_time ?? null,
                ];
            }
            $data['regular_days'] = json_encode($schedules);
            $data['schedule_1_days'] = null;
            $data['schedule_2_days'] = null;
        }

        if (!empty($request->schedule_1_days)) {
            $schedules = [];
            foreach (json_decode($request->schedule_1_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_1_start_time ?? null,
                    'end_time' => $request->schedule_1_end_time ?? null,
                ];
            }
            $data['schedule_1_days'] = json_encode($schedules);
            $data['regular_days'] = null;
        }

        if (!empty($request->schedule_2_days)) {
            $schedules = [];
            foreach (json_decode($request->schedule_2_days) as $index => $day) {
                $schedules[] = [
                    'day' => $day,
                    'start_time' => $request->schedule_2_start_time ?? null,
                    'end_time' => $request->schedule_2_end_time ?? null,
                ];
            }
            $data['schedule_2_days'] = json_encode($schedules);
        }

        if (!empty($request->sch_cal_screenshot)) {
            if ($request->hasFile('sch_cal_screenshot')) {
                $file = $request->file('sch_cal_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/school/calender' . $name;
                uploads3image($filename, $file);
            }
            $calender1['calender_url'] = $filename;
            $calender1['school_id'] = auth()->user()->id;
            $calender1['district_id'] = auth()->user()->district;
            $calender1['type'] = 'school';
            $calender1['lastUpdatedFrom'] = 'Requirement-'.$request->id;
            PlatformSchoolCalendersModel::create($calender1);
        }

        if (!empty($request->district_cal_screenshot)) {
            if ($request->hasFile('district_cal_screenshot')) {
                $file = $request->file('district_cal_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/' . $name;
                uploads3image($filename, $file);
            }
            $calender2['calender_url'] = $filename;
            $calender2['school_id'] = auth()->user()->id;
            $calender2['district_id'] = auth()->user()->district;
            $calender2['type'] = 'district';
            $calender2['lastUpdatedFrom'] = 'Requirement-'.$request->id;
            PlatformSchoolCalendersModel::create($calender2);
        }

        if ($request->has('teacher_schedule_screenshot')) {
            if ($request->hasFile('teacher_schedule_screenshot')) {
                $file = $request->file('teacher_schedule_screenshot');
                $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $name = time() . '-' . $originalName . '.' . $extension;
                $filename = 'uploads/' . $name;
                uploads3image($filename, $file);
            } else {
                $filename = $request->teacher_schedule_screenshot;
            }
            $data['teacher_schedule_screenshot'] = $filename;
        }

        if (!empty($request->other_sch_class_details)) {
            $data['class_details'] = $request->other_sch_class_details;
            $data['regular_days'] = null;
            $data['schedule_1_days'] = null;
            $data['schedule_2_days'] = null;
        }

        if (!empty($request->profileType_requirements)) {
            $data['profileType_requirements'] = $request->profileType_requirements;
        }

        if (!empty($request->language_requirements)) {
            $data['language_requirements'] = !empty($request->language_requirements) ? implode(',', $request->language_requirements) : null;
        }

        if (!empty($request->other_requirements)) {
            $data['other_requirements'] = $request->other_requirements;
        }

        if (!empty($request->states)) {
            $data['certifications_valid'] = !empty($request->states) ? implode(',', $request->states) : null;
        }

        if (!empty($request->min)) {
            if (!empty($request->min)) {
                $data['compensation_amount_min'] = floatval(preg_replace('/[^0-9.]/', '', $request->min));
            }
        }

        if (!empty($request->max)) {
            $data['compensation_amount_max'] = floatval(preg_replace('/[^0-9.]/', '', $request->max));
        }


        if (!empty($request->per_hour)) {
            $data['compensation_type'] = $request->per_hour;
        }

        if (!empty($request->benefits)) {
            $data['benefits'] = $request->benefits;
        }

        if (!empty($request->status)) {
            $data['status'] = $request->status;
        }

        if (!empty($request->will_choose_requiremnt)) {
            $data["will_choose_educator"] = $request->will_choose_requiremnt;
        }

        if (!empty($request->credential_type)) {
            $data["credential_check"] = $request->credential_type;
        }

        if (!empty($request->special_education)) {
            $data["special_education_certificate"] = $request->special_education;
        }

        if (!empty($request->provide_curriculum)) {
            $data["will_follow_provided_curriculum"] = $request->provide_curriculum;
        }

        if (!empty($request->access_to_schedule)) {
            $data["provide_schedule_access"] = $request->access_to_schedule;
        }
        if (!empty($request->qualification)) {
            $data['qualifications'] = $request->qualification;
        }
        if (!empty($request->experience)) {
            $data['experience'] = $request->experience;
        }


        if (!empty($request->total_budget)) {
            $data['total_budget'] = $request->total_budget;
        }

        if (!empty($request->min) && !empty($request->max) && !empty($request->per_hour) && !empty($request->benefits)) {
            $data['is_valid'] = '1';

            $subject_id = PlatformSchoolRequirements::where("id", decrypt_str($request->id))->value("subject_id");



            $user_ids_from_subject_table = DB::table("onboarding_instructor_subjects")
                ->where("sub_subject", $subject_id)
                ->pluck("user_id")
                ->toArray();


            $unique_user_id = [];
            $whizara_table_user_id = DB::table("onboarding_instructor_whizara_educator_contract")->whereIn("user_id", $user_ids_from_subject_table)->pluck("user_id")->toArray();

            $market_table_user_id = DB::table("onboarding_instructor_marketplace_educator_contract")->whereIn("user_id", $user_ids_from_subject_table)->pluck("user_id")->toArray();

            $unique_user_id = array_unique(array_merge($whizara_table_user_id, $market_table_user_id));

            $from_new_onboarding_instructor = DB::table("new_onboarding_instructor")->whereIn("id", $unique_user_id)->get();
            $count_result = $from_new_onboarding_instructor->count();
            $requirement_name = PlatformSchoolRequirements::where('id', decrypt_str($request->id))
                ->pluck('requirement_name')
                ->first();

            $content = "<span class='notification_requirement_count'>{$count_result}</span> educators matching your requirement <span class='notification_requirement_name'>{$requirement_name}</span> are available. Review educator profiles and invite them to apply.";
            $user_ids_json = json_encode($unique_user_id);
            $requirement_id = decrypt_str($request->id);


            if (!empty($unique_user_id)) {

                $requirementnotfication = new NotificationRequirement();
                $requirementnotfication->content = $content;
                $requirementnotfication->requirement_id = $requirement_id;
                $requirementnotfication->user_ids = $user_ids_json;
                $requirementnotfication->school_id = auth()->user()->id;
                $requirementnotfication->save();
            }
        } elseif (empty($request->min) && empty($request->max) && !empty($request->per_hour)) {
            $data['is_valid'] = '1';
        } else {
            $data['is_valid'] = '0';
        }


        if (!empty($request->id)) {
            $req = PlatformSchoolRequirements::find(decrypt_str($request->id));
            $req->update($data);
        }
        $id = empty($request->id) ? (!empty($requirements) ? encrypt_str($requirements) : null) : $request->id;

        if (empty($request->id) && empty($requirements)) {
            return response()->json(['success' => false, 'id' => $id]);
        }
        return response()->json(['success' => true, 'id' => $id]);
    }

    public function listRequirements(Request $request)
    {
        if (auth()->user()->cust_type == 'Platform') {
            $data = PlatformSchoolRequirements::with(['reviewApplicants'])->withCount('reviewApplicants')->where('school_id', auth()->user()->id)->OrderBy('id', 'DESC')->get();

            if ($request->has('search')) {
                $data = PlatformSchoolRequirements::with(['reviewApplicants'])->withCount('reviewApplicants')->where('school_id', auth()->user()->id)
                    // ->where('requirement_name', 'LIKE', '%' . $request->search . '%')
                    ->where(function ($q) use ($request) {
                        $q->where('requirement_type', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('requirement_name', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('class_type', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('delivery_mode', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('city', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('state', 'LIKE', '%' . $request->search . '%')
                            ->orWhere('country', 'LIKE', '%' . $request->search . '%');
                    })
                    ->OrderBy('id', 'DESC')->get();
                // dd($data);
                if ($data->isEmpty()) {
                    $data = PlatformSchoolRequirements::with(['reviewApplicants'])->withCount('reviewApplicants')->where('school_id', auth()->user()->id)->OrderBy('id', 'DESC')->get();
                }
                return response()->json(["success" => true, 'data' => $data]);
            }

            return view('school-marketplace.list-requirements', compact('data'));
        } else {
            return redirect()->route('indexschool');
        }
    }

    public function updatePostRequirement(Request $request)
    {
        $data = PlatformSchoolRequirements::find($request->id);
        $data->update([
            'status' => $request->status
        ]);

        return response()->json(["success" => true, "message" => "Update Successfully"]);
    }

    public function deletePostRequirement(Request $request)
    {
        $data = PlatformSchoolRequirements::find($request->id);
        if ($data->delete()) {
            return response()->json(["success" => true, "message" => "Successfully Deleted"]);
        } else {
            return response()->json(["success" => false, "message" => "Requirement not found"]);
        }
    }

    // LIST APPLICANTS
    public function listApplicants($encryptedId, Request $request)
    {
        $perPage = 10;
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);
        $currentUserId = auth()->user()->id;

        // SchoolReviewApplicants::where('requirement_id', $data->id)->where('school_id', auth()->user()->id)
        // ->where(function ($query) {
        //     $query->whereNull('last_seen_at')
        //           ->orWhere('last_seen_at', '<', Carbon::today());
        // })
        // ->where('seen_user', '<', 1)
        // ->update([
        //     'seen_user' => DB::raw('seen_user + 1'),
        //     'last_seen_at' => now(),
        // ]);

        $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $data->id)->where('school_id', auth()->user()->id)
            ->whereHas('user')
            ->where(function ($query) {
                $query->whereDoesntHave('user.shortList')
                    ->orWhereHas('user.shortList', function ($subQuery) {
                        $subQuery->where('status', '>', 0);
                    });
            });
        $totalApplicants = $applicants->count();
        $applicants = $applicants->orderBy('id', 'DESC')->get();
        $totalCost = $applicants->sum(function ($applicant) use ($data) {
            return $data->totalHours * $applicant->proposed_rate;
        });

        // **Fix maxTotalCost calculation**
        $maxTotalCost = $applicants->max(function ($applicant) use ($data) {
            return $data->totalHours * $applicant->proposed_rate;
        });
        $filters = $this->filterOptions($data->delivery_mode, $maxTotalCost);
        // dd($applicants);
        $LastChatModel = LastChatModel::with(['k12User', 'requirements', 'k12User.step1', 'k12User.step2.education', 'k12User.step2.teching', 'k12User.step3.subjects', 'k12User.step5', 'k12User.step6', 'k12User.shortList'])->where('from_id', auth()->user()->id)->where('requirement_id', $data->id)->where('to_instructor', 'marketplace')->orderBy('id', 'DESC')->groupBy('to_id')->get();
        $totalChatMessage = count($LastChatModel);
        $payment_frequency = DB::table('school_management_setting')->where('type', 'payment_frequency')->first();
        $likeShortlist = ShortlistInstructorModel::with(['requirements.school', 'user'])->where(['school_id' => auth()->user()->id, 'status' => 1, 'requirement_id' => $data->id])->orderBy('id', 'DESC')->get();
        $totalLikeShortlist = count($likeShortlist);
        $dislikeShortlist = ShortlistInstructorModel::with(['requirements.school', 'user'])->where(['school_id' => auth()->user()->id, 'status' => 0, 'requirement_id' => $data->id])->orderBy('id', 'DESC')->get();
        $totalDislikeShortlist = count($dislikeShortlist);
        $redirectRoute = route('new-school.sort', ['encryptedId' => $encryptedId]);

        // $chats = Chat::where('message_type', 'requirement')
        //     ->where('referance_id', $data->id)
        //     ->where(function ($query) use ($currentUserId) {
        //         $query->where('sender', $currentUserId);
        //     })
        //     ->selectRaw('*, CASE WHEN sender = ? THEN recipient ELSE sender END as user_id', [$currentUserId])
        //     ->get(); // Get full chat data

        // // Get unique users from OnboardingInstructor
        // $users = OnboardingInstructor::whereIn('id', $chats->pluck('user_id')->toArray())->get();

        // // Merge chat data with user data
        // $chatWithUsers = collect($users)->map(function ($user) use ($chats) {
        //     return (object) [
        //         'user' => $user,
        //         'recipient' => $user->id,
        //         'chats' => $chats->where('user_id', $user->id)->values(), // Ensure collection, not array
        //     ];
        // });

        $chats = Chat::where('message_type', 'requirement')
            ->where('referance_id', $data->id)
            ->where(function ($query) use ($currentUserId) {
                $query->where('sender', $currentUserId)->orWhere('recipient', $currentUserId);
            })
            ->selectRaw('*, CASE WHEN sender = ? THEN recipient ELSE sender END as user_id', [$currentUserId])
            ->orderBy('created_at', 'desc') // Order by latest message
            ->get();

        // Get unique users and preserve order of latest chat
        $users = OnboardingInstructor::whereIn('id', $chats->pluck('user_id')->unique()->toArray())->get();

        // Merge chat data with user data
        $chatWithUsers = collect($users)->map(function ($user) use ($chats) {
            return (object) [
                'user' => $user,
                'recipient' => $user->id,
                'chats' => $chats->where('user_id', $user->id)->sortByDesc('created_at')->values(), // Sort chats per user
            ];
        })->sortByDesc(function ($chat) {
            return $chat->chats->first()->created_at ?? null; // Sort users by latest chat
        });


        // dd($chats,$chatWithUsers);
        if ($request->ajax()) {
            switch ($request->type) {
                case 'applicants':
                    $view = view('school-marketplace.components.partial-list-applicants', compact('data', 'applicants', 'payment_frequency', 'redirectRoute', 'filters'))->render();
                    break;
                case 'shortlist':
                    $view = view('school-marketplace.components.partial-short-list-applicants', compact('data', 'applicants', 'payment_frequency', 'likeShortlist', 'redirectRoute', 'filters'))->render();
                    break;

                case 'messaged':
                    $view = view('school-marketplace.components.partial-message-list-applicants', compact('data', 'applicants', 'payment_frequency', 'redirectRoute', 'filters', 'encryptedId', 'chatWithUsers'))->render();
                    break;

                case 'archived':
                    $view = view('school-marketplace.components.partial-archived-list-applicants', compact('data', 'applicants', 'payment_frequency', 'dislikeShortlist', 'redirectRoute', 'filters'))->render();
                    break;

                default:
                    $view = view('school-marketplace.components.partial-list-applicants', compact('data', 'applicants', 'payment_frequency', 'redirectRoute', 'filters'))->render();
                    break;
            }
            return response()->json(['status' => true, 'view' => $view, 'totalApplicants' => $totalApplicants, 'totalDislikeShortlist' => $totalDislikeShortlist, 'totalLikeShortlist' => $totalLikeShortlist, 'totalChatMessage' => $totalChatMessage]);
        }
        return view('school-marketplace.applicants.list-applicants', compact('data', 'applicants', 'payment_frequency', 'filters', 'redirectRoute', 'totalApplicants', 'LastChatModel', 'dislikeShortlist', 'likeShortlist'));
    }

    // public function get_chatData_for_requirement($encryptedId, Request $request)
    // {
    //     $currentUserId = auth()->user()->id;
    //     $id = $encryptedId;

    //     $chats = Chat::where('message_type', 'requirement')
    //     ->where('referance_id', $id)
    //     ->where(function ($query) use ($currentUserId) {
    //         $query->where('sender', $currentUserId);
    //     })
    //     ->selectRaw('*, CASE WHEN sender = ? THEN recipient ELSE sender END as user_id', [$currentUserId])
    //     ->get(); // Get full chat data

    //     // Get unique users from OnboardingInstructor
    //     $users = OnboardingInstructor::whereIn('id', $chats->pluck('user_id')->toArray())->get();

    //     // Merge chat data with user data
    //     $chatWithUsers = collect($users)->map(function ($user) use ($chats) {
    //         return (object) [
    //             'user' => $user,
    //             'recipient' => $user->id,
    //             'chats' => $chats->where('user_id', $user->id)->values(), // Ensure collection, not array
    //         ];
    //     });

    //     //         $chatWithUsers = Chat::where('message_type', 'requirement') 
    //     //         ->where('referance_id', $id)
    //     //         ->where(function ($query) use ($currentUserId) {
    //     //             $query->where('sender', $currentUserId)
    //     //                 ->orWhere('recipient', $currentUserId);
    //     //         })
    //     //         ->selectRaw('CASE WHEN sender = ? THEN recipient ELSE sender END as user_id', [$currentUserId])
    //     //         ->distinct()
    //     //         ->get();
    //     // dd($chatWithUsers);
    //     $user = OnboardingInstructor::find($request->userId);
    //     $userProfile = !empty($user->image) ? generateSignedUrl($user->image) : url('website/img/pre.png');

    //     return response()->json(['success' => true, 'data' => $chatWithUsers, 'profile' => $userProfile]);

    // }

    public function get_chat_by_user($encryptedId, Request $request)
    {
        $user = auth()->user()->id;
        $recipient =  $request->userId;
        $id = $encryptedId;

        $chats = Chat::where('message_type', 'requirement')
            ->where('referance_id', $id)
            ->where(function ($query) use ($user, $recipient) {
                $query->where(function ($q) use ($user, $recipient) {
                    $q->where('sender', $user)->where('recipient', $recipient);
                })->orWhere(function ($q) use ($user, $recipient) {
                    $q->where('sender', $recipient)->where('recipient', $user);
                });
            })
            ->orderBy('created_at', 'asc')
            ->get();

        // Update readed_at for messages where the recipient is auth user

        Chat::where('message_type', 'requirement')
            ->where('referance_id', $id)
            ->where('sender', $recipient)
            ->whereNull('readed_at')
            ->update(['readed_at' => now()]);

        $user = OnboardingInstructor::find($request->userId);
        $userProfile = !empty($user->image) ? generateSignedUrl($user->image) : url('website/img/pre.png');
        // return $chats;
        return response()->json(['success' => true, 'data' => $chats, 'profile' => $userProfile]);
    }


    public function inviteTalent($encryptedId, Request $request)
    {
        $perPage = 10;
        $id = decrypt_str($encryptedId);

        $data = PlatformSchoolRequirements::find($id);
        $filters = $this->filterOptions($data->delivery_mode);
        $redirectRoute = route('new-school.sort', ['encryptedId' => $encryptedId]);
        $type = $request->type ?? 'talent';
        $today = Carbon::today()->format('Y-m-d'); // Ensure Y-m-d format
        $fiveDaysAgo = Carbon::today()->subDays(5)->format('Y-m-d');
        $instructors = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])->whereIn('user_status', ['Active'])->orderBy('id', 'DESC')->get();
        $marketplaceContracts = OnboardingInstructorMarketplaceContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
            ->get()
            ->map(function ($contract) {
                $contract->is_new = true;
                return $contract;
            });

        $whizaraContracts = OnboardingInstructorContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
            ->get()
            ->map(function ($contract) {
                $contract->is_new = true; // 5 din ke andar hai toh "New" tag set karein
                return $contract;
            });

        $marketplaceInstructorIds = OnboardingInstructorMarketplaceContract::pluck('user_id')->toArray();
        $whizaraInstructorIds = OnboardingInstructorContract::pluck('user_id')->toArray();

        // Combine both contract instructor IDs and make them unique
        $validInstructorIds = array_unique(array_merge($marketplaceInstructorIds, $whizaraInstructorIds));
        $filteredInstructors = collect();
        // Filter instructors based on valid instructor IDs
        if (!$request->ajax()) {
            // Filter instructors based on valid instructor IDs
            $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                return in_array($instructor->id, $validInstructorIds);
            });
            $totalApplicants = $filteredInstructors->count();
        }

        $filteredInstructors = $filteredInstructors->sortByDesc(function ($instructor) use ($marketplaceContracts, $whizaraContracts) {
            $instructorId = $instructor->id;
            $isNew = false;
            $currentDate = \Carbon\Carbon::now();

            $marketplaceContract = $marketplaceContracts->where('user_id', $instructorId)->first();
            if ($marketplaceContract && \Carbon\Carbon::parse($marketplaceContract->contract_sign)->diffInDays($currentDate) <= 5) {
                $isNew = true;
            }

            $whizaraContract = $whizaraContracts->where('user_id', $instructorId)->first();
            if ($whizaraContract && \Carbon\Carbon::parse($whizaraContract->contract_sign)->diffInDays($currentDate) <= 5) {
                $isNew = true;
            }

            return $isNew ? 1 : 0; // New instructors get priority in sorting
        });

        // dd($filteredInstructors,$filteredInstructors->count());
        $counts = PlatformSchoolInvites::with('user')->where('school_id', auth()->user()->id)
            ->where('requirement_id', $data->id)
            ->whereHas('user')
            ->selectRaw("
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted_count,
            SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined_count
        ")
            ->first();
        $invitedInstructorIds = PlatformSchoolInvites::where('school_id', auth()->user()->id)
            ->where('requirement_id', $data->id)
            ->whereIn('status', ['pending', 'accepted', 'declined'])
            ->pluck('user_id')
            ->toArray();
        if ($request->ajax()) {
            switch ($type) {
                case 'talent':
                    $instructors = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])->whereIn('user_status', ['Active'])->orderBy('id', 'DESC')->get();
                    $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                        return in_array($instructor->id, $validInstructorIds);
                    });
                    $totalApplicants = $filteredInstructors->count();
                    break;

                case 'invited':
                    $instructors = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'pending')->orderBy('id', 'DESC')->get();
                    $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                        return $instructor->user && in_array($instructor->user->id, $validInstructorIds);
                    });
                    $totalApplicants = $filteredInstructors->count();
                    break;

                case 'accepted':
                    $instructors = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'accepted')->orderBy('id', 'DESC')->get();
                    $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                        return $instructor->user && in_array($instructor->user->id, $validInstructorIds);
                    });
                    $totalApplicants = $filteredInstructors->count();
                    break;

                case 'declined':
                    $instructors = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'declined')->orderBy('id', 'DESC')->get();
                    $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                        return $instructor->user && in_array($instructor->user->id, $validInstructorIds);
                    });
                    $totalApplicants = $filteredInstructors->count();
                    break;

                default:
                    $instructors = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])->whereIn('user_status', ['Active'])->orderBy('id', 'DESC')->get();
                    $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
                        return in_array($instructor->id, $validInstructorIds);
                    });
                    $totalApplicants = $filteredInstructors->count();
                    break;
            }
            // dd($type,$instructors);
            $view = view('school-marketplace.components.partial-invite-applicants', compact('data', 'instructors', 'redirectRoute', 'type', 'totalApplicants', 'invitedInstructorIds', 'whizaraContracts', 'marketplaceContracts', 'filteredInstructors'))->render();
            return response()->json(['status' => true, 'view' => $view, 'counts' => $counts, 'totalApplicants' => $totalApplicants]);
        }
        return view('school-marketplace.invite-applicants', compact('data', 'instructors', 'filters', 'redirectRoute', 'type', 'counts', 'totalApplicants', 'invitedInstructorIds', 'whizaraContracts', 'marketplaceContracts', 'filteredInstructors'));
    }

    public function viewInstructor($encryptedUserId, $encryptedDataId, Request $request)
    {
        // dd($request->all());
        $userId = decrypt_str($encryptedUserId);
        $dataId = decrypt_str($encryptedDataId);
        $data = NewSchoolPostRequirementModel::find($dataId);
        $instructor = User::with(['requirements', 'subjects', 'teachingPreference', 'certificates', 'education', 'availability.ranges.timeSlots', 'availableLocations'])->find($userId);
        $payment_frequency = DB::table('school_management_setting')->where('type', 'payment_frequency')->first();
        // dd($instructor);

        return view('school-marketplace.view-instructor', compact('data', 'instructor', 'payment_frequency'));
    }

    public function inviteInstructor(Request $request)
    {
        $invite = DB::table('new_school_invite_instructor')->insert([
            'requirement_id' => $request->reqId,
            'user_id' => $request->userId,
        ]);

        if ($invite) {
            return response()->json(['success' => true, 'message' => 'Invited Successfully!!']);
        } else {
            return response()->json(['success' => false, 'message' => 'Something went wrong!!']);
        }
    }

    public function saveInstructor(Request $request)
    {
        $invite = DB::table('new_school_save_instructor')->insert([
            'requirement_id' => $request->reqId,
            'user_id' => $request->userId,
        ]);

        if ($invite) {
            return response()->json(['success' => true, 'message' => 'Saved Successfully!!']);
        } else {
            return response()->json(['success' => false, 'message' => 'Something went wrong!!']);
        }
    }



    public function viewHires($encryptedId, $screen = '')
    {
        $id = decrypt_str($encryptedId);

        $data = PlatformSchoolRequirements::find($id);
        $filters = $this->filterOptions($data->delivery_mode);
        if (!empty($screen) && $screen == 'offer') {
            $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'pending'])->get();
        } else {
            $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'accepted'])->get();
        }
        // $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'accepted'])->get();
        $schools = User::where('type', 6)->orderBy('full_name', 'ASC')->get();
        $redirectRoute = route('new-school.sort', ['encryptedId' => $encryptedId]);
        $acceptedCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'accepted'
        ])->count();

        $pendingCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'pending'
        ])->count();
        $programs = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
        return view('school-marketplace.view-hires', compact('data', 'hires', 'schools', 'filters', 'redirectRoute', 'encryptedId', 'acceptedCount', 'pendingCount', 'programs', 'screen'));
    }

    public function sendMsgForSchool(Request $request)
    {
        // $already=LastChatModel::where(['from_id'=>$request->from_id,'to_id'=>$request->to_id,'requirement_id' => $request->reqId])->first();
        // if(!$already){
        LastChatModel::insert(['from_id' => $request->from_id, 'to_id' => $request->to_id, 'requirement_id' => $request->reqId, 'to_instructor' => $request->to_instructor, 'from_instructor' => $request->from_instructor, 'message' => $request->message]);
        // }
    }

    public function shortlistInstructor(Request $request)
    {
        $already = ShortlistInstructorModel::where(['requirement_id' => $request->reqId, 'school_id' => $request->schoolId, 'user_id' => $request->userId])->first();

        if (!$already && $request->status != null) {
            $shortlist = new ShortlistInstructorModel;
            $shortlist->requirement_id = $request->reqId;
            $shortlist->school_id = $request->schoolId;
            $shortlist->user_id = $request->userId;
            $shortlist->status = $request->status;
            $shortlist->save();
        } else if ($already && $request->status != null) {
            $shortlist = $already->update(['status' => $request->status]);
        } else if ($request->status == null) {
            $shortlist = $already->delete();
        }

        if ($shortlist) {
            // return redirect()->back()->with(['success' => true, 'message' => 'Instructor Shortlisted']);
            return response()->json(['success' => true, 'message' => 'Instructor Shortlisted']);
        } else {
            return redirect()->back()->with(['success' => false, 'message' => 'Something went wrong!!']);
        }
    }

    public function showChats($encryptedId)
    {
        $id = decrypt_str($encryptedId);
        $user = User::with(['requirements'])->find($id);
        return view('school-marketplace.view-chats', compact('user'));
    }

    public function allHires()
    {
        $hires = SchoolInstructorHiring::with(['user', 'requirements'])->where(['school_id' => auth()->user()->id])->get();
        return view('school-marketplace.all-hires', compact('hires'));
    }

    public function getNameByRefranceId($refranceId)
    {
        $user = NewSchoolPostRequirementModel::find($refranceId);
        if ($user) {
            // Return the name as a JSON response
            return response()->json(['name' => $user->position_title]);
        } else {
            // Return a 404 if no user is found
            return response()->json(['message' => 'Name not found'], 404);
        }
    }

    public function filterOptions($delivery_mode, $totalCost = null)
    {
        $grades = Classes::all()->pluck('class_name')->toArray();
        $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->pluck('name')->toArray();
        // $subjectArea = Subject::all()->pluck('subject_name')->toArray();
        $subjectArea = SubjectArea::all()->pluck('subject_area')->toArray();
        $agency = CredentialingAgencyModel::all()->pluck('agency')->toArray();
        // $programType = k12ConnectionCategorizedData::where('type', 'program_type')->pluck('description')->toArray();
        $programType = DB::table('school_management_setting')->where('type', 'program_type')->first();
        $language = DB::table('school_management_setting')->where('type', 'language')->select('value')->first();
        $profile_type = DB::table('school_management_setting')->where('type', 'profile_type')->first();
        $education = EducationListModel::pluck('education')->toArray();
        $filterConfig = [];
        $delivery_modes = explode(',', $delivery_mode);

        $filters = [
            'Budget' => [],
            'Educator preference' => [
                'Program Type' => json_decode($programType->value, true),
                'Subject Area' => $subjectArea,
                'Subject' => [
                    'relation' => [
                        'basedOn' => 'Subject Area',
                        'fetchFrom' => '/get_subject/:id',
                        'key' => 'id',
                        'responseKey' => 'subjects',
                        'labelKey' => 'title',
                    ]
                ],
                'Grade Levels' => $grades,
                'Delivery mode' => [],
            ],
            'Educator profile' => [
                // 'Profile Type' => array_merge(
                //     ['Licensed/Credentialed Educator'],
                //     json_decode($profile_type->value, true) ?? []
                // ),
                'Profile Type' => json_decode($profile_type->value, true) ?? [],

                'Minimum education level' => $education,
                'Years of relevant experience' => [
                    'range' => [
                        'prefix' => '',
                        'suffix' => ' yrs',
                        'min' => 0,
                        'max' => 26,
                    ],
                ],
                'Language spoken' => json_decode($language->value, true),
                // 'Credentialing Agency' => $agency,
                // 'Certification/License' => [
                //     'relation' => [
                //         'basedOn' => 'Credentialing Agency',
                //         'fetchFrom' => '/get_certificates/:id',
                //         'key' => 'id',
                //         'responseKey' => 'certificates'
                //         'labelKey' => 'certificate',
                //     ]
                // ],
            ],
            'Certification/License' => [
                'Valid in state(s)' => $states,
            ]
        ];

        if (request()->segment(1) == 'list-applicants') {
            $totalCost = (int) $totalCost;
            if ($totalCost > 0) {
                $step = ceil($totalCost / 10); // Divide into 10 parts

                $budgetRanges = [];
                for ($i = 0; $i < 10; $i++) {
                    $min = round($i * $step, 2);
                    $max = round(($i + 1) * $step, 2);

                    // $budgetRanges[] = "$min - $max";
                    $budgetRanges[] = "$" . number_format($min) . " - $" . number_format($max);
                }

                // Add calculated budget to filters
                $filters['Budget']['Total cost'] = $budgetRanges;
            }
        }

        if (in_array('hybrid', $delivery_modes)) {
            $filters['Budget']['Educator online rate'] = [
                'range' => [
                    'prefix' => '$',
                    'suffix' => '',
                    'min' => 0,
                    'max' => 101,
                ],
            ];
            $filters['Budget']['Educator in-person rate'] = [
                'range' => [
                    'prefix' => '$',
                    'suffix' => '',
                    'min' => 0,
                    'max' => 101,
                ],
            ];
            $filters['Educator preference']['Delivery mode'] = ['Online', 'In-person'];

            $filters['Educator preference']['Zipcode'] = [
                'type' => 'input',
                'placeholder' => 'Enter zipcode',
            ];
        } else {
            if (in_array('online', $delivery_modes)) {
                $filters['Budget']['Educator online rate'] = [
                    'range' => [
                        'prefix' => '$',
                        'suffix' => '',
                        'min' => 0,
                        'max' => 101,
                    ],
                ];
                $filters['Educator preference']['Delivery mode'] = ['Online'];
            }
            if (in_array('in-person', $delivery_modes)) {
                $filters['Budget']['Educator in-person rate'] = [
                    'range' => [
                        'prefix' => '$',
                        'suffix' => '',
                        'min' => 0,
                        'max' => 101,
                    ],
                ];
                $filters['Educator preference']['Delivery mode'] = ['In-person'];

                $filters['Educator preference']['Zipcode'] = [
                    'type' => 'input',
                    'placeholder' => 'Enter zipcode',
                ];
            }
        }

        $filterConfig['filters'] = $filters;
        $filterConfig['elementIds'] = [
            'Subject Area' => 'subjectarea',
            'Grade Levels' => 'grades',
            // 'Taught By' => 'instructor_type',
            'Years of Experience.' => 'experience',
            // 'Credentialing Agency' => 'agency',
            // 'Certification/License' => 'certificates',
            'State' => 'states',
            // 'Hourly Rate' => 'hourly_rate'
        ];
        return $filterConfig;
    }


    public function get_certificates(Request $request)
    {
        $agency = CredentialingAgencyModel::where('agency', $request->name);
        $certificates = $agency->with('certificates')->first();
        return response()->json(['status' => true, 'data' => $certificates]);
    }

    public function get_subject($name, Request $request)
    {
        $subjectArea = SubjectArea::where('subject_area', $name)->first();
        $subSubject['subjects'] = V1Subject::where('subject_area_id', $subjectArea->id)->get();
        return response()->json(['status' => true, 'data' => $subSubject]);
    }

    public function inviteTalent2($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $instructors = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])->whereIn('user_status', ['Active'])->orderBy('id', 'DESC')->get();
        $postRequirement = NewSchoolPostRequirementModel::find($id);
        return view('school-marketplace.invite-talents', compact('instructors', 'postRequirement'));
    }

    public function requirementDetails($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);

        return view('school-marketplace.requirements-details', compact('data'));
    }

    public function sortAndFilterApplicants($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);
        $totalApplicants = '';
        if ($request->tab == 'list-applicants') {
            $step3_name = 'user.step3';
            $step2_name = 'user.step2';
            $instructorId_name = '';
            $user_name = 'user';
            $step5_name = 'user.step5';

            switch ($request->reviewTabId) {
                case 'applicants':
                    $instructorId_name = 'platform_school_review_applicants.instructor_id';
                    $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $data->id)->where('school_id', auth()->user()->id)
                        ->where(function ($query) {
                            $query->whereDoesntHave('user.shortList') // Applicants with no shortlist
                                ->orWhereHas('user.shortList', function ($subQuery) {
                                    $subQuery->where('status', '!=', 0); // Include applicants with shortlist status != 0
                                });
                        });
                    break;

                case 'shortlist':
                    $instructorId_name = 'school_shortlist_instructor.user_id';
                    $applicants = ShortlistInstructorModel::with(['requirements.school', 'user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where(['school_id' => auth()->user()->id, 'status' => 1, 'requirement_id' => $data->id]);
                    break;

                case 'messaged':
                    $instructorId_name = 'tbl_last_chat_message.to_id';
                    $applicants = LastChatModel::with(['k12User', 'requirements', 'k12User.step1', 'k12User.step2.education', 'k12User.step2.teching', 'k12User.step3.subjects', 'k12User.step5', 'k12User.step6', 'k12User.shortList'])->where('from_id', auth()->user()->id)->where('requirement_id', $data->id)->where('to_instructor', 'marketplace');
                    $step3_name = 'k12User.step3';
                    $step2_name = 'k12User.step2';
                    break;

                case 'archived':
                    $instructorId_name = 'school_shortlist_instructor.user_id';
                    $applicants = ShortlistInstructorModel::with(['requirements.school', 'user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where(['school_shortlist_instructor.school_id' => auth()->user()->id, 'school_shortlist_instructor.status' => 0, 'school_shortlist_instructor.requirement_id' => $data->id]);
                    break;

                default:
                    # code...
                    break;
            }
            switch ($request->sort) {
                case 'years':
                    $applicants->leftJoin('onboarding_instructor_experiences', 'onboarding_instructor_experiences.user_id', '=', $instructorId_name)
                        ->orderBy('onboarding_instructor_experiences.total_experience', 'ASC')
                        ->with(['user.step2']);
                    break;

                case 'certified':
                    $applicants->leftJoin('onboarding_instructor_experiences', 'onboarding_instructor_experiences.user_id', '=', $instructorId_name)
                        ->orderBy('onboarding_instructor_experiences.certification', 'ASC')
                        ->with(['user.step2']);
                    break;

                case 'atoz':
                    $applicants->leftJoin('new_onboarding_instructor', 'new_onboarding_instructor.id', '=', $instructorId_name)
                        ->orderBy('new_onboarding_instructor.first_name', 'ASC')
                        ->with(['user.step2']);
                    break;

                case 'ztoa':
                    $applicants->leftJoin('new_onboarding_instructor', 'new_onboarding_instructor.id', '=', $instructorId_name)
                        ->orderBy('new_onboarding_instructor.first_name', 'DESC')
                        ->with(['user.step2']);
                    break;


                case 'oldest-to-newest-applicants':
                    $applicants->orderBy('created_at', 'ASC');
                    break;


                case 'newest-to-oldest-applicants':
                    $applicants->orderBy('created_at', 'DESC');
                    break;

                case 'onlineRate_asc':
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $applicants->where('format', 'like', '%online%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'online' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%online%' THEN CAST(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 1) AS UNSIGNED)
                            END ASC
                        ");
                    break;

                case 'onlineRate_desc':
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $applicants->where('format', 'like', '%online%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'online' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%online%' THEN CAST(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 1) AS UNSIGNED)
                            END DESC
                        ");
                    break;

                case 'in-personRate_asc':
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $applicants->where('format', 'like', '%in-person%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'in-person' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%in-person%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 2), ',', -1) AS UNSIGNED)
                            END ASC
                        ");
                    break;

                case 'in-personRate_desc':
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $applicants->where('format', 'like', '%in-person%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'in-person' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%in-person%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 2), ',', -1) AS UNSIGNED)
                            END DESC
                        ");
                    break;

                case 'proposedRate':
                    $applicants->addSelect([
                        'requirement_data' => PlatformSchoolRequirements::selectRaw('platform_school_requirements.delivery_mode')
                            ->whereColumn('platform_school_review_applicants.requirement_id', 'platform_school_requirements.id')
                            ->limit(1)
                    ]);

                    $applicants->addSelect([
                        'format_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.format')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'platform_school_review_applicants.instructor_id')
                            ->limit(1)
                    ]);

                    $applicants->addSelect([
                        'compensation_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.compensation')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'platform_school_review_applicants.instructor_id')
                            ->limit(1)
                    ]);
                    // $applicants->whereHas('requirement', function ($query) {
                    //     // $query->whereNotNull('delivery_mode'); // Ensure delivery_mode exists
                    //     $query->where(function ($subQuery) {
                    //         $subQuery->where('delivery_mode', 'online')
                    //             ->whereIn('user.step3.format', ['online']);
                    //     })->orWhere(function ($subQuery) {
                    //         $subQuery->where('delivery_mode', 'in-person')
                    //             ->whereIn('user.step3.format', ['in-person']);
                    //     });
                    // })
                    // ->whereHas('user.step3');
                    // ->where(function ($query) {
                    //     // Match delivery_mode with format
                    // });
                    break;

                default:
                    break;
            }

            if (!empty($request->total_cost)) {
                $costRanges = request()->input('total_cost'); // Array of ranges
                $applicants->where(function ($query) use ($costRanges, $data) {
                    foreach ($costRanges as $costRange) {
                        $costRange = str_replace('$', '', $costRange);
                        $costRange = trim($costRange);
                        list($minCost, $maxCost) = explode(' _ ', $costRange);
                        $minCost = (int) $minCost;
                        $maxCost = (int) $maxCost;

                        // Apply OR condition for multiple ranges
                        $query->orWhereRaw('(proposed_rate * ?) BETWEEN ? AND ?', [$data->totalHours, $minCost, $maxCost]);
                    }
                });
            }
        } else {
            $invitedInstructorIds = PlatformSchoolInvites::where('school_id', auth()->user()->id)
                ->where('requirement_id', $data->id)
                ->whereIn('status', ['pending', 'accepted', 'declined'])
                ->pluck('user_id')
                ->toArray();
            $today = Carbon::today()->format('Y-m-d');
            $fiveDaysAgo = Carbon::today()->subDays(5)->format('Y-m-d');
            $marketplaceContracts = OnboardingInstructorMarketplaceContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
                ->get()
                ->map(function ($contract) {
                    $contract->is_new = true;
                    return $contract;
                });

            $whizaraContracts = OnboardingInstructorContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
                ->get()
                ->map(function ($contract) {
                    $contract->is_new = true; // 5 din ke andar hai toh "New" tag set karein
                    return $contract;
                });

            $marketplaceInstructorIds = OnboardingInstructorMarketplaceContract::pluck('user_id')->toArray();
            $whizaraInstructorIds = OnboardingInstructorContract::pluck('user_id')->toArray();

            $validInstructorIds = array_unique(array_merge($marketplaceInstructorIds, $whizaraInstructorIds));


            switch ($request->inviteTabId) {
                case 'talent':
                    $step3_name = 'step3';
                    $step2_name = 'step2';
                    $step5_name = 'step5';
                    $user_name = '';
                    $instructorId_name = 'new_onboarding_instructor.id';
                    $applicants = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])
                        ->where('user_status', 'Active');
                    break;

                case 'invited':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'pending');
                    break;

                case 'accepted':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'accepted');
                    break;

                case 'declined':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'declined');
                    break;

                default:
                    # code...
                    break;
            }
        }

        if (!empty($request->searchText)) {
            $searchText = $request->searchText;
            $applicants->where(function ($query) use ($searchText, $user_name, $step2_name, $step3_name, $step5_name, $request) {
                if ($request->tab == 'list-applicants') {
                    $query->whereHas($user_name, function ($query) use ($searchText) {
                        $query->where('first_name', 'LIKE', "%$searchText%")
                            ->orWhere('last_name', 'LIKE', "%$searchText%")
                            ->orWhere('email', 'LIKE', "%$searchText%")
                            ->orWhere('state', 'LIKE', "%$searchText%")
                            ->orWhere('city', 'LIKE', "%$searchText%")
                            ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                    });
                } else {
                    if ($request->inviteTabId == 'talent') {
                        $query->where(function ($query) use ($searchText) {
                            $query->where('first_name', 'LIKE', "%$searchText%")
                                ->orWhere('last_name', 'LIKE', "%$searchText%")
                                ->orWhere('email', 'LIKE', "%$searchText%")
                                ->orWhere('state', 'LIKE', "%$searchText%")
                                ->orWhere('city', 'LIKE', "%$searchText%")
                                ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                        });
                    } else {
                        $query->whereHas($user_name, function ($query) use ($searchText) {
                            $query->where('first_name', 'LIKE', "%$searchText%")
                                ->orWhere('last_name', 'LIKE', "%$searchText%")
                                ->orWhere('email', 'LIKE', "%$searchText%")
                                ->orWhere('state', 'LIKE', "%$searchText%")
                                ->orWhere('city', 'LIKE', "%$searchText%")
                                ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                        });
                    }
                }

                // Step 2
                $query->orWhereHas($step2_name, function ($query) use ($searchText) {
                    $query->where('profile_type', 'LIKE', "%$searchText%")
                        ->orWhere('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%")
                                ->orWhereRaw('JSON_CONTAINS(states, ?)', [json_encode($searchText)]);
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 3
                $query->orWhereHas($step3_name, function ($query) use ($searchText) {
                    $query->join('tbl_classes', 'tbl_classes.id', '=', 'onboarding_instructor_teaching_preferences.i_prefer_to_teach')
                        ->where("tbl_classes.class_name", 'LIKE', "%$searchText%")
                        ->orWhere('format', 'LIKE', "%$searchText%")
                        ->orWhere('language_teach_that_i_teach', 'LIKE', "%$searchText%")
                        ->orWhere('program_type', 'LIKE', "%$searchText%")
                        ->orWhere('other_language', 'LIKE', "%$searchText%")
                        ->orWhere('other_program_type', 'LIKE', "%$searchText%")
                        ->orWhereHas('subjects', function ($q) use ($searchText) {
                            $q->join('subject_area_v1', 'subject_area_v1.id', '=', 'onboarding_instructor_subjects.subject')
                                ->join('subjects_v1', 'subjects_v1.id', '=', 'onboarding_instructor_subjects.sub_subject')
                                ->where('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                                ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%");
                        });
                });

                // Step 5
                $query->orWhereHas($step5_name, function ($query) use ($searchText) {
                    $query->where('profile_title', 'LIKE', "%$searchText%")
                        ->orWhere('description', 'LIKE', "%$searchText%")
                        ->orWhere('profile_tags', 'LIKE', "%$searchText%");
                });
            });
        }


        if (!empty($request->valid_in_states)) {
            $states = StateModel::whereIn('name', $request->valid_in_states)->pluck('name')->toArray();
            $applicants->whereHas($step2_name . '.education', function ($query) use ($states) {
                foreach ($states as $index => $state) {
                    if ($index == 0) {
                        $query->whereJsonContains('states', $state);
                    } else {
                        $query->orWhereJsonContains('states', $state);
                    }
                }
            });
        }

        $applicants->addSelect([
            'format_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.format')
                ->whereColumn('onboarding_instructor_teaching_preferences.user_id', $instructorId_name)
                ->limit(1),
            'compensation_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.compensation')
                ->whereColumn('onboarding_instructor_teaching_preferences.user_id', $instructorId_name)
                ->limit(1),
        ]);

        $deliveryMode = $data->delivery_mode;
        if ($request->tab != 'list-applicants') {
            // $applicants->whereRaw("FIND_IN_SET($deliveryMode, format_data)");
        }

        if (!empty($request->subject_area)) {
            $subjects = SubjectArea::whereIn('subject_area', $request->subject_area)->pluck('id')->toArray();
            $applicants->whereHas($step3_name . '.subjects', function ($query) use ($subjects) {
                $query->whereIn('subject', $subjects);
            });
        }

        if (!empty($request->subject)) {
            $subSubjects = V1Subject::whereIn('title', $request->subject)->pluck('id')->toArray();
            $applicants->whereHas($step3_name . '.subjects', function ($query) use ($subSubjects) {
                $query->whereIn('sub_subject', $subSubjects);
            });
        }

        if (!empty($request->grade_levels)) {
            $grades = Classes::whereIn('class_name', $request->grade_levels)->pluck('id')->toArray();
            $applicants->whereHas($step3_name, function ($query) use ($grades) {
                $query->where(function ($q) use ($grades) {
                    foreach ($grades as $grade) {
                        $q->orWhereRaw("FIND_IN_SET(?, i_prefer_to_teach)", [$grade]);
                    }
                });
            });
        }

        if (!empty($request->years_of_relevant_experience)) {
            $YearsOfExp = array_map('intval', $request->years_of_relevant_experience);
            $minYears = min($YearsOfExp);
            $maxYears = max($YearsOfExp);
            if ($minYears != 0 || $maxYears != 26) {
                $applicants->whereHas($step2_name, function ($query) use ($minYears, $maxYears) {
                    $query->whereRaw('CAST(total_experience AS UNSIGNED) BETWEEN ? AND ?', [$minYears, $maxYears]);
                });
            }
        }
        // dd($applicants->get());
        if (!empty($request->credentialing_agency)) {
            $agency = CredentialingAgencyModel::whereIn('agency', $request->credentialing_agency)->pluck('agency')->toArray();
            $applicants->whereHas($step2_name . '.education', function ($query) use ($agency) {
                $query->whereIn('credentialing_agency', $agency);
            });
        }

        if (!empty($request->certificationlicense)) {
            $certificate = AgencyCertificatesModel::whereIn('certificate', $request->certificationlicense)->pluck('certificate')->toArray();
            $applicants->whereHas($step2_name . '.education', function ($query) use ($certificate) {
                $query->whereIn('education', $certificate);
            });
        }

        if (!empty($request->program_type)) {
            $applicants->whereHas($step3_name, function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->program_type as $program) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, program_type)", [$program]);
                    }
                });
            });
        }

        if (!empty($request->profile_type)) {
            $profileTypes = $request->profile_type;
            $updatedProfileTypes = array_map(function ($item) {
                return $item === "Licensed/Credentialed Educator" ? "Certified Teacher" : $item;
            }, $profileTypes);
            $applicants->whereHas($step2_name, function ($query) use ($updatedProfileTypes) {
                $query->where('profile_type', $updatedProfileTypes);
            });
        }

        if (!empty($request->minimum_education_level)) {
            $applicants->whereHas($step2_name, function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->minimum_education_level as $education) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, highest_level_of_education)", [$education]);
                    }
                });
            });
        }

        if (!empty($request->language_spoken)) {
            $applicants->whereHas($step3_name, function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->language_spoken as $language) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, language_teach_that_i_teach)", [$language]);
                    }
                });
            });
        }

        if (!empty($request->delivery_mode)) {
            $normalizedModes = array_map(function ($mode) {
                return strtolower(str_replace('_', '-', $mode));
            }, $request->delivery_mode);

            $applicants->whereHas($step3_name, function ($query) use ($normalizedModes) {
                $query->where(function ($subQuery) use ($normalizedModes) {
                    foreach ($normalizedModes as $mode) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, format)", [$mode]);
                    }
                });
            });
        }

        if (!empty($request->zipcode)) {
            if ($request->inviteTabId == 'talent') {
                $applicants->where(function ($query) use ($request) {
                    $query->where('zipcode', $request->zipcode);
                });
            } else {
                $applicants->whereHas($user_name, function ($query) use ($request) {
                    $query->where('zipcode', $request->zipcode);
                });
            }
        }

        if (!empty($request->educator_online_rate)) {
            $onlineRates = array_map('intval', $request->educator_online_rate);
            $minRate = min($onlineRates);
            $maxRate = max($onlineRates);

            if ($minRate != 0 || $maxRate != 101) {
                if ($request->tab == 'list-applicants') {
                    $applicants->whereHas('user.step3', function ($query) use ($minRate, $maxRate) {
                        $query->where(function ($q1) use ($minRate, $maxRate) {
                            // If format is exactly 'online' or 'in-person', check the compensation directly
                            $q1->where('format', 'online')
                                ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$minRate, $maxRate]);
                        })
                            ->orWhere(function ($q2) use ($minRate, $maxRate) {
                                // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                                $q2->whereRaw("SUBSTRING_INDEX(compensation, ',', 1) BETWEEN ? AND ?", [$minRate, $maxRate]);
                            });
                    });
                } else {
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences as onlinePreference', 'onlinePreference.user_id', '=', $instructorId_name);

                    $applicants->whereHas($step3_name, function ($query) use ($minRate, $maxRate) {
                        $query->where(function ($q1) use ($minRate, $maxRate) {
                            // If format is exactly 'online' or 'in-person', check the compensation directly
                            $q1->where('format', 'online')
                                ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$minRate, $maxRate]);
                        })
                            ->orWhere(function ($q2) use ($minRate, $maxRate) {
                                // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                                $q2->whereRaw("SUBSTRING_INDEX(compensation, ',', 1) BETWEEN ? AND ?", [$minRate, $maxRate]);
                            });
                    });
                }
            }
        }

        if (!empty($request->educator_in_person_rate)) {
            $inPersonRates = array_map('intval', $request->educator_in_person_rate);
            $mininPersonRate = min($inPersonRates);
            $maxinPersonRate = max($inPersonRates);

            if ($mininPersonRate != 0 || $maxinPersonRate != 101) {
                if ($request->tab == 'list-applicants') {
                    $applicants->whereHas('user.step3', function ($query) use ($mininPersonRate, $maxinPersonRate) {
                        $query->where(function ($q1) use ($mininPersonRate, $maxinPersonRate) {
                            // If format is exactly 'online' or 'in-person', check the compensation directly
                            $q1->where('format', 'in-person')
                                ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                        })
                            ->orWhere(function ($q2) use ($mininPersonRate, $maxinPersonRate) {
                                // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                                $q2->whereRaw("SUBSTRING_INDEX(SUBSTRING_INDEX(compensation, ',', 2), ',', -1) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                            });
                    });
                } else {
                    $applicants->leftJoin('onboarding_instructor_teaching_preferences', 'onboarding_instructor_teaching_preferences.user_id', '=', $instructorId_name);

                    $applicants->whereHas($step3_name, function ($query) use ($mininPersonRate, $maxinPersonRate) {
                        $query->where(function ($q1) use ($mininPersonRate, $maxinPersonRate) {
                            // If format is exactly 'online' or 'in-person', check the compensation directly
                            $q1->where('format', 'in-person')
                                ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                        })
                            ->orWhere(function ($q2) use ($mininPersonRate, $maxinPersonRate) {
                                // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                                $q2->whereRaw("SUBSTRING_INDEX(SUBSTRING_INDEX(compensation, ',', 2), ',', -1) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                            });
                    });
                    // dd($applicants->get());

                }
            }
        }

        if (empty($request->sort)) {
            $applicants->orderBy('id', 'DESC');
        }

        $applicants = $applicants->get();

        if ($request->tab == 'list-applicants') {

            switch ($request->reviewTabId) {
                case 'applicants':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-list-applicants', ['applicants' => $applicants, 'data' => $data])->render();
                    break;

                case 'shortlist':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-short-list-applicants', ['likeShortlist' => $applicants, 'data' => $data])->render();
                    break;

                case 'messaged':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-message-list-applicants', ['LastChatModel' => $applicants, 'data' => $data])->render();
                    break;

                case 'archived':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-archived-list-applicants', ['dislikeShortlist' => $applicants, 'data' => $data])->render();
                    break;

                default:
                    # code...
                    break;
            }
        } else {
            switch ($request->inviteTabId) {
                case 'talent':
                    $applicants = $applicants->filter(function ($applicant) use ($validInstructorIds) {
                        return in_array($applicant->id, $validInstructorIds);
                    });
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-invite-applicants', ['filteredInstructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds, 'whizaraContracts' => $whizaraContracts, 'marketplaceContracts' => $marketplaceContracts])->render();
                    break;

                case 'invited':
                    $applicants = $applicants->filter(function ($applicant) use ($validInstructorIds) {
                        return $applicant->user && in_array($applicant->user->id, $validInstructorIds);
                    });
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-invite-applicants', ['filteredInstructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds, 'whizaraContracts' => $whizaraContracts, 'marketplaceContracts' => $marketplaceContracts])->render();
                    break;

                case 'accepted':
                    $applicants = $applicants->filter(function ($applicant) use ($validInstructorIds) {
                        return $applicant->user && in_array($applicant->user->id, $validInstructorIds);
                    });
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-invite-applicants', ['filteredInstructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds, 'whizaraContracts' => $whizaraContracts, 'marketplaceContracts' => $marketplaceContracts])->render();
                    break;

                case 'declined':
                    $applicants = $applicants->filter(function ($applicant) use ($validInstructorIds) {
                        return $applicant->user && in_array($applicant->user->id, $validInstructorIds);
                    });
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-invite-applicants', ['filteredInstructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds, 'whizaraContracts' => $whizaraContracts, 'marketplaceContracts' => $marketplaceContracts])->render();
                    break;

                default:
                    # code...
                    break;
            }
            // $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data])->render();
        }
        // dd($totalApplicants);
        return response()->json(['success' => true, 'view' => $view, 'request' => $request->all(), 'totalApplicants' => $totalApplicants]);
    }

    public function duplicateRequirement($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);

        if ($data) {
            $newData = $data->replicate();
            $newData->parent_id = $data->id;
            $newData->status = 'draft';
            $newData->is_valid = '0';
            $newData->save();

            return response()->json(['success' => true]);
        }

        return response()->json(['error' => 'Data not found']);
    }

    public function closeRequirement(Request $request)
    {
        $id = decrypt_str($request->id);
        $data = PlatformSchoolRequirements::find($id);

        if ($data) {
            $data->update(['status' => 'closed']);

            return response()->json(['success' => true]);
        }

        return response()->json(['error' => 'Data not found']);
    }

    public function publicProfile($encryptedId, Request $request)
    {

        $requirement_id = decrypt_str($request->reqId);
        $id = decrypt_str($encryptedId);
        $proposal = !empty($request->proposalId) ? SchoolReviewApplicants::where('id', decrypt_str($request->proposalId))->first() : null;
        $instructor = OnboardingInstructor::where('id', $id)->with('step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3', 'step3.subjects', 'step5', 'step6', 'shortlist')->first();
        $data = PlatformSchoolRequirements::find($requirement_id);
        $invite = PlatformSchoolInvites::where(['school_id' => $data->school_id, 'requirement_id' => $data->id, 'user_id' => $instructor->id])->first();
        $requirement = PlatformSchoolRequirements::all();
        $component = new PublicProfile($instructor, 'school', $data, $proposal, $invite, $requirement);

        return response()->json([
            'html' => $component->render()->render()
        ]);
    }

    public function hiresTab($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $tab = 'partial-hire-list-applicants';
        $data = PlatformSchoolRequirements::find($id);
        $schools = User::where('type', 6)->orderBy('full_name', 'ASC')->get();
        $redirectRoute = route('new-school.sort', ['encryptedId' => $encryptedId]);
        $acceptedCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'accepted'
        ])->count();

        $pendingCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'pending'
        ])->count();
        $programs = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
        switch ($request->type) {
            case 'hire-hired':
                $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'accepted'])->get();
                $view = view("school-marketplace.components.partial-hire-list-applicants", compact('data', 'hires', 'schools', 'redirectRoute', 'encryptedId', 'programs'))->render();
                break;

            case 'hire-offers':
                $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'pending'])->get();
                $view = view("school-marketplace.components.partial-hire-offer-list-applicants", compact('data', 'hires', 'schools', 'redirectRoute', 'encryptedId', 'programs'))->render();
                break;

            default:
                $hires = SchoolInstructorHiring::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'requirements'])->where(['requirment_id' => $data->id, 'school_id' => auth()->user()->id, 'status' => 'accepted'])->get();
                break;
        }

        return response()->json(['status' => true, 'view' => $view, 'acceptedCount' => $acceptedCount, 'pendingCount' => $pendingCount]);
    }

    public function searchFilter($encryptedId, Request $request)
    {
        // dd($request->all());
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);
        $totalApplicants = '';

        if ($request->tab == 'list-applicants') {
            $step3_name = 'user.step3';
            $step2_name = 'user.step2';
            $step5_name = 'user.step5';
            $instructorId_name = '';
            $user_name = 'user';

            switch ($request->reviewTabId) {
                case 'applicants':
                    $instructorId_name = 'platform_school_review_applicants.instructor_id';
                    $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $data->id)->where('school_id', auth()->user()->id)
                        ->where(function ($query) {
                            $query->whereDoesntHave('user.shortList') // Applicants with no shortlist
                                ->orWhereHas('user.shortList', function ($subQuery) {
                                    $subQuery->where('status', '>', 0); // Include applicants with shortlist status != 0
                                });
                        });
                    break;
                case 'shortlist':
                    $instructorId_name = 'school_shortlist_instructor.user_id';
                    $applicants = ShortlistInstructorModel::with(['requirements.school', 'user'])->where(['school_id' => auth()->user()->id, 'status' => 1, 'requirement_id' => $data->id]);
                    break;

                case 'messaged':
                    $instructorId_name = 'tbl_last_chat_message.to_id';
                    $applicants = LastChatModel::with(['k12User', 'requirements', 'k12User.step1', 'k12User.step2.education', 'k12User.step2.teching', 'k12User.step3.subjects', 'k12User.step5', 'k12User.step6', 'k12User.shortList'])->where('from_id', auth()->user()->id)->where('requirement_id', $data->id)->where('to_instructor', 'marketplace');
                    $step3_name = 'k12User.step3';
                    $step2_name = 'k12User.step2';
                    $step5_name = 'k12User.step5';
                    $user_name = 'k12User';
                    break;

                case 'archived':
                    $instructorId_name = 'school_shortlist_instructor.user_id';
                    $applicants = ShortlistInstructorModel::with(['requirements.school', 'user'])->where(['school_shortlist_instructor.school_id' => auth()->user()->id, 'school_shortlist_instructor.status' => 0, 'school_shortlist_instructor.requirement_id' => $data->id]);
                    break;

                default:
                    # code...
                    break;
            }
            // dd($applicants->orderBy('id', 'DESC')->toSql(), $applicants->getBindings());

        } else {
            $invitedInstructorIds = PlatformSchoolInvites::where('school_id', auth()->user()->id)
                ->where('requirement_id', $data->id)
                ->whereIn('status', ['pending', 'accepted', 'declined'])
                ->pluck('user_id')
                ->toArray();
            switch ($request->inviteTabId) {
                case 'talent':
                    $step3_name = 'step3';
                    $step2_name = 'step2';
                    $step5_name = 'step5';
                    $user_name = 'new_onboarding_instructor';
                    $instructorId_name = 'new_onboarding_instructor.id';
                    $applicants = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step3.subjects', 'step5', 'step6'])
                        ->where('user_status', 'Active');
                    break;

                case 'invited':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'pending');
                    break;

                case 'accepted':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'accepted');
                    break;

                case 'declined':
                    $step3_name = 'user.step3';
                    $step2_name = 'user.step2';
                    $step5_name = 'user.step5';
                    $user_name = 'user';
                    $instructorId_name = 'platform_school_invites.id';
                    $applicants = PlatformSchoolInvites::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6'])->where('school_id', auth()->user()->id)->where('requirement_id', $data->id)->where('status', 'declined');
                    break;

                default:
                    # code...
                    break;
            }
        }

        if (!empty($request->searchText)) {
            $searchText = $request->searchText;

            $applicants->where(function ($query) use ($searchText, $user_name, $step2_name, $step3_name, $step5_name, $request) {
                if ($request->tab == 'list-applicants') {
                    $query->whereHas($user_name, function ($query) use ($searchText) {
                        $query->where('first_name', 'LIKE', "%$searchText%")
                            ->orWhere('last_name', 'LIKE', "%$searchText%")
                            ->orWhere('email', 'LIKE', "%$searchText%")
                            ->orWhere('state', 'LIKE', "%$searchText%")
                            ->orWhere('city', 'LIKE', "%$searchText%");
                    });
                } else {
                    if ($request->inviteTabId == 'talent') {
                        $query->where(function ($query) use ($searchText) {
                            $query->where('first_name', 'LIKE', "%$searchText%")
                                ->orWhere('last_name', 'LIKE', "%$searchText%")
                                ->orWhere('email', 'LIKE', "%$searchText%")
                                ->orWhere('state', 'LIKE', "%$searchText%")
                                ->orWhere('city', 'LIKE', "%$searchText%");
                        });
                    } else {
                        $query->whereHas($user_name, function ($query) use ($searchText) {
                            $query->where('first_name', 'LIKE', "%$searchText%")
                                ->orWhere('last_name', 'LIKE', "%$searchText%")
                                ->orWhere('email', 'LIKE', "%$searchText%")
                                ->orWhere('state', 'LIKE', "%$searchText%")
                                ->orWhere('city', 'LIKE', "%$searchText%");
                        });
                    }
                }

                // Step 2
                $query->orWhereHas($step2_name, function ($query) use ($searchText) {
                    $query->where('profile_type', 'LIKE', "%$searchText%")
                        ->orWhere('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%")
                                ->orWhereRaw('JSON_CONTAINS(states, ?)', [json_encode($searchText)]);
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 3
                $query->orWhereHas($step3_name, function ($query) use ($searchText) {
                    $query->join('tbl_classes', 'tbl_classes.id', '=', 'onboarding_instructor_teaching_preferences.i_prefer_to_teach')
                        ->where("tbl_classes.class_name", 'LIKE', "%$searchText%")
                        ->orWhere('format', 'LIKE', "%$searchText%")
                        ->orWhere('language_teach_that_i_teach', 'LIKE', "%$searchText%")
                        ->orWhere('program_type', 'LIKE', "%$searchText%")
                        ->orWhere('other_language', 'LIKE', "%$searchText%")
                        ->orWhere('other_program_type', 'LIKE', "%$searchText%")
                        ->orWhereHas('subjects', function ($q) use ($searchText) {
                            $q->join('subject_area_v1', 'subject_area_v1.id', '=', 'onboarding_instructor_subjects.subject')
                                ->join('subjects_v1', 'subjects_v1.id', '=', 'onboarding_instructor_subjects.sub_subject')
                                ->where('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                                ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%");
                        });
                });

                // Step 5
                $query->orWhereHas($step5_name, function ($query) use ($searchText) {
                    $query->where('profile_title', 'LIKE', "%$searchText%")
                        ->orWhere('description', 'LIKE', "%$searchText%")
                        ->orWhere('profile_tags', 'LIKE', "%$searchText%");
                });
            });
        }

        $applicants = $applicants->orderBy('id', 'DESC')->get();

        if ($request->tab == 'list-applicants') {

            switch ($request->reviewTabId) {
                case 'applicants':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-list-applicants', ['applicants' => $applicants, 'data' => $data])->render();
                    break;

                case 'shortlist':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-short-list-applicants', ['likeShortlist' => $applicants, 'data' => $data])->render();
                    break;

                case 'messaged':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-message-list-applicants', ['LastChatModel' => $applicants, 'data' => $data])->render();
                    break;

                case 'archived':
                    $totalApplicants = count($applicants);
                    $view = view('school-marketplace.components.partial-archived-list-applicants', ['dislikeShortlist' => $applicants, 'data' => $data])->render();
                    break;

                default:
                    # code...
                    break;
            }
        } else {
            switch ($request->inviteTabId) {
                case 'talent':
                    $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds])->render();
                    break;

                case 'invited':
                    $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds])->render();
                    break;

                case 'accepted':
                    $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds])->render();
                    break;

                case 'declined':
                    $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data, 'type' => $request->inviteTabId, 'invitedInstructorIds' => $invitedInstructorIds])->render();
                    break;

                default:
                    # code...
                    break;
            }
            // $view = view('school-marketplace.components.partial-invite-applicants', ['instructors' => $applicants, 'data' => $data])->render();
        }

        return response()->json(['success' => true, 'view' => $view, 'request' => $request->all(), 'totalApplicants' => $totalApplicants]);
    }

    public function get_chatMsg($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);

        $data = PlatformSchoolRequirements::find($id);
        $LastChatModel = LastChatModel::with(['k12User', 'requirements', 'k12User.step1', 'k12User.step2.education', 'k12User.step2.teching', 'k12User.step3.subjects', 'k12User.step5', 'k12User.step6', 'k12User.shortList'])->where('from_id', auth()->user()->id)->where('requirement_id', $data->id)->where('to_instructor', 'marketplace')->where('to_id', $request->userId)->orderBy('id', 'ASC')->get();
        $user = OnboardingInstructor::find($request->userId);
        $userProfile = !empty($user->image) ? generateSignedUrl($user->image) : url('website/img/pre.png');

        return response()->json(['success' => true, 'data' => $LastChatModel, 'profile' => $userProfile]);
    }

    public function inviteUser(Request $request)
    {
        $data = $request->except('token');
        $invitation = PlatformSchoolInvites::create($data);

        if ($invitation) {
            return response()->json(['success' => true]);
        }
    }

    public function inviteBulkUser(Request $request)
    {
        $userIds = $request->user_ids;
        $data = collect($userIds)->map(function ($userId) use ($request) {
            return [
                'user_id' => $userId,
                'requirement_id' => $request->requirement_id,
                'school_id' => $request->school_id,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        })->toArray();

        PlatformSchoolInvites::insert($data);

        return response()->json(['success' => true]);
    }

    public function withdrawInvitation(Request $request)
    {
        $invitation = PlatformSchoolInvites::where(['school_id' => $request->school_id, 'requirement_id' => $request->requirement_id, 'user_id' => $request->user_id, 'status' => 'pending'])->first();

        if ($invitation) {
            $invitation->delete();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false, 'message' => 'No pending invitation found or the invitation has already been accepted/rejected.']);
        }
    }

    public function viewContractDetails($encryptedId, $userId, Request $request)
    {
        $classSetup = '';
        $manageClass = '';
        $viewContract = '';
        if ($request->has('class')) {
            $classSetup = $request->class;
        }

        if ($request->has('manageClass')) {
            $manageClass = $request->manageClass;
        }

        if ($request->has('viewContract')) {
            $viewContract = $request->viewContract;
        }

        $id = decrypt_str($encryptedId);
        $user_id = decrypt_str($userId);
        $school = User::find(auth()->user()->id);
        PlatformSchoolProctor::updateOrCreate(
            ['email' => $school->email], // Condition to check existing record
            [
                'school_id' => $school->id,
                'proctor_name' => $school->full_name,
                'phone' => $school->phone_number,
                'updated_at' => now(),
            ]
        );
        $user = OnboardingInstructor::where('id', $user_id)->with('step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3', 'step3.subjects', 'step5', 'step6', 'shortlist')->first();
        $data = PlatformSchoolRequirements::find($id);
        $hire = SchoolInstructorHiring::with('requirements')->where('requirment_id', $id)->where('school_id', auth()->user()->id)->where('instructor_id', $user_id);
        if (!empty($classSetup) || !empty($manageClass) || !empty($viewContract)) {
            $hire = $hire->where('status', '!=', 'declined');
        } else {
            $hire = $hire->where('status', '!=', 'withdraw');
        }
        $hire = $hire->first();
        $applicant = SchoolReviewApplicants::where(['school_id' => $hire->school_id, 'requirement_id' => $hire->requirment_id, 'instructor_id' => $hire->instructor_id])->first();
        $zone = k12ConnectionCategorizedData::find($data->time_zone);
        $proctors = PlatformSchoolProctor::where('school_id', $data->school_id)->get();
        $zoom = ZoomModel::select('id', 'account_name')->where("status", 1)->get();
        $programs = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
        $programs_note = !empty($data->id) ? k12ConnectionProgramNotes::where('requirement_id', $data->id)->first() : '';
        $class_link = !empty($data->id) ? k12ConnectionMeetingLinks::where('requirement_id', $data->id)->first() : '';
        $roster = !empty($data->id) ? PlatformSchoolRoster::where('requirement_id', $data->id)->first() : '';


        // **Total Classes & Total Hours Calculation**
        $totalClasses = 0;
        $totalHours = 0;
        if (!empty($programs)) {
            $classes = k12ConnectionClasses::where('program_id', $programs->id)->get();

            // Yahan classes ko fetch karna hoga jo current requirement se related hon

            foreach ($classes as $class) {
                if (in_array($class->status, ['completed', 'under review'])) {
                    $totalClasses++; // Counting only required statuses

                    if ($class->start_time && $class->end_time) {
                        $start = Carbon::parse($class->start_time);
                        $end = Carbon::parse($class->end_time);
                        if ($end < $start) {
                            $end->addDay(); // Next day adjust
                        }

                        $totalHours += $end->diffInMinutes($start) / 60; // Convert minutes to hours
                    }
                }
            }
        }

        // $view = view('school-marketplace.contract.contract-details', compact('user', 'data', 'encryptedId', 'hire'))->render();
        // return response()->json([
        //     'html' => $view]);
        return view('school-marketplace.contract.contract-details', compact('user', 'data', 'encryptedId', 'hire', 'zone', 'proctors', 'zoom', 'programs', 'userId', 'programs_note', 'class_link', 'roster', 'school', 'totalClasses', 'totalHours', 'applicant', 'classSetup', 'manageClass', 'viewContract'));
    }

    public function getDatatable($encryptedId, $userId, Request $request)
    {
        $currentDate = Carbon::now()->toDateString();
        if ($request->ajax()) {
            $id = decrypt_str($encryptedId);
            $userId = decrypt_str($userId);
            // $params = [
            //     'draw' => $request->input('draw'),
            //     'row' => $request->input('start', 0),
            //     'rowperpage' => $request->input('length', 10),
            // ];
            $params = DataTableHelper::getParams($request);
            $data = PlatformSchoolRequirements::find($id);
            $program = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
            $classes = [];
            if (!empty($program)) {
                // $classes = k12ConnectionClasses::where('program_id', $program->id)->get();
                $query = k12ConnectionClasses::where('program_id', $program->id)->with(['classLog', 'proctorLog']);

                // Class Status Filter
                // if (
                //     $request->has('class_status') &&
                //     !empty($request->class_status) &&
                //     $request->class_status != 'all'
                //     ) {
                //     $query->where('status', $request->class_status);
                // }

                if (!empty($request->viewClass) && $request->viewClass == 'true') {
                    $query->whereDate('class_date', '>=', $currentDate);
                }
                // dd($query->get());
                if (
                    $request->has('class_status') && !empty($request->class_status)
                ) {
                    $query->where('status', $request->class_status);
                }

                // Class Rating (Educator) Filter
                if ($request->has('class_rating_educator') && !empty($request->class_rating_educator)) {
                    $query->whereHas('classLog', function ($q) use ($request) {
                        $q->where('class_rating', $request->class_rating_educator);
                    });
                }

                // Class Rating (Proctor) Filter
                if ($request->has('class_rating_proctor') && !empty($request->class_rating_proctor)) {
                    $query->whereHas('proctorLog', function ($q) use ($request) {
                        $q->where('proctor_rating', $request->class_rating_proctor);
                    });
                }

                // Date Range Filter
                if ($request->has('class_start_date') && !empty($request->class_start_date)) {
                    $dateRange = explode(' - ', $request->class_start_date);
                    if (count($dateRange) == 2) {
                        $startDate = Carbon::createFromFormat('m/d/Y', trim($dateRange[0]))->startOfDay();
                        $endDate = Carbon::createFromFormat('m/d/Y', trim($dateRange[1]))->endOfDay();
                        $query->whereBetween('class_date', [$startDate, $endDate]);
                    }
                }


                // if (
                //     $request->has('class_start_date') && !empty($request->class_start_date)) {
                //     $query->where('class_date', $request->class_start_date);
                //     dd($request->class_start_date);
                // }

                [$classCount, $classes] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);
            }
            $class_rating_educator = '<svg xmlns="http://www.w3.org/2000/svg" width="104" height="18" viewBox="0 0 104 18"
                                    fill="none">
                                    <path
                                        d="M9.22231 1.73899L11.0491 6.1312L11.1664 6.41318L11.4708 6.43759L16.2126 6.81773L12.5999 9.91241L12.3679 10.1111L12.4388 10.4082L13.5425 15.0353L9.48294 12.5557L9.22231 12.3965L8.96169 12.5557L4.90208 15.0353L6.00582 10.4082L6.07668 10.1111L5.84475 9.91241L2.23203 6.81773L6.97379 6.43759L7.27821 6.41318L7.39549 6.1312L9.22231 1.73899Z"
                                        fill="#F1C644" stroke="#F1C644" />
                                    <path
                                        d="M30.6442 1.73899L32.471 6.1312L32.5883 6.41318L32.8927 6.43759L37.6345 6.81773L34.0218 9.91241L33.7898 10.1111L33.8607 10.4082L34.9644 15.0353L30.9048 12.5557L30.6442 12.3965L30.3836 12.5557L26.324 15.0353L27.4277 10.4082L27.4986 10.1111L27.2666 9.91241L23.6539 6.81773L28.3957 6.43759L28.7001 6.41318L28.8174 6.1312L30.6442 1.73899Z"
                                        fill="#F1C644" stroke="#F1C644" />
                                    <path
                                        d="M52.0739 1.73899L53.9007 6.1312L54.018 6.41318L54.3224 6.43759L59.0642 6.81773L55.4514 9.91241L55.2195 10.1111L55.2904 10.4082L56.3941 15.0353L52.3345 12.5557L52.0739 12.3965L51.8132 12.5557L47.7536 15.0353L48.8574 10.4082L48.9282 10.1111L48.6963 9.91241L45.0836 6.81773L49.8253 6.43759L50.1298 6.41318L50.2471 6.1312L52.0739 1.73899Z"
                                        fill="#F1C644" stroke="#F1C644" />
                                    <path
                                        d="M73.4958 1.73899L75.3226 6.1312L75.4399 6.41318L75.7443 6.43759L80.486 6.81773L76.8733 9.91241L76.6414 10.1111L76.7122 10.4082L77.816 15.0353L73.7564 12.5557L73.4958 12.3965L73.2351 12.5557L69.1755 15.0353L70.2793 10.4082L70.3501 10.1111L70.1182 9.91241L66.5055 6.81773L71.2472 6.43759L71.5516 6.41318L71.6689 6.1312L73.4958 1.73899Z"
                                        fill="#F1C644" stroke="#F1C644" />
                                    <path
                                        d="M94.9176 1.73899L96.7444 6.1312L96.8617 6.41318L97.1662 6.43759L101.908 6.81773L98.2952 9.91241L98.0633 10.1111L98.1341 10.4082L99.2379 15.0353L95.1783 12.5557L94.9176 12.3965L94.657 12.5557L90.5974 15.0353L91.7011 10.4082L91.772 10.1111L91.5401 9.91241L87.9273 6.81773L92.6691 6.43759L92.9735 6.41318L93.0908 6.1312L94.9176 1.73899Z"
                                        stroke="#F1C644" />
                                </svg>';
            $class_rating_educator = '';
            $today = Carbon::today();
            $classData = [];
            foreach ($classes as $class) {
                // Fetch Data from Related Tables
                $educatorLog = \App\Models\k12ConnectionClassLogs::where('class_id', $class->id)->first();
                $proctorFeedback = \App\Models\k12ConnectionProctorFeedback::where('class_id', $class->id)->first();

                $formattedDate = $class->class_date ? Carbon::parse($class->class_date)->format('m/d/Y') : 'N/A';

                $classDate = $class->class_date ? Carbon::parse($class->class_date) : null;

                if ($classDate && $classDate->lessThanOrEqualTo($today)) {
                    // if (empty($educatorLog) || empty($proctorFeedback)) {
                    if (empty($class->class_log_id)) {
                        $status = "Missing Summary"; // *Past/Todays Date + No Educator Feedback*
                    } elseif ($class->status === "under review") {
                        $status = "Pending Review"; // *Has Educator Feedback + Under Review*
                    } else {
                        $status = ucfirst($class->status);
                        // $status = ($class->status == "under review" ? "Pending Review" : ucfirst($class->status)) ?? 'N/A';
                    }
                } else {
                    $status = $class->status ? ($class->status == "scheduled" ? "Upcoming" : ($class->status == "under review" ? "Pending Review" : ucfirst($class->status))) : 'N/A';
                }

                $classData[] = [
                    'class_date' => $formattedDate,
                    'start_end_time' => ($class->start_time && $class->end_time)
                        ? Carbon::parse($class->start_time)->format('h:i A') . ' - ' . Carbon::parse($class->end_time)->format('h:i A')
                        : 'N/A',
                    'class_rating_educator' => $this->getStarRating($educatorLog->class_rating ?? 0),
                    'class_summary_educator' => $educatorLog->note ?? '-',
                    'class_rating_proctor' => $this->getStarRating($proctorFeedback->proctor_rating ?? 0),
                    'class_summary_proctor' => $proctorFeedback->note ?? '-',
                    // 'class_status' => ($class->status == "scheduled"? "Upcoming": ucfirst($class->status)) ?? 'N/A',
                    'class_status' => $status,
                ];
            }
            if (!empty($classData)) {
                // Sort classData by class_start_date in ascending order
                usort($classData, function ($a, $b) {
                    return strtotime($a['class_date']) - strtotime($b['class_date']);
                });
            }

            return DataTableHelper::generateResponse($params['draw'], $classCount, $classData);
        }

        return view('school-marketplace.components.contract-details-session-summary');
    }

    public function exportDataTable($encryptedId, $userId, $exportType)
    {
        $id = decrypt_str($encryptedId);
        $userId = decrypt_str($userId);
        $data = PlatformSchoolRequirements::find($id);

        if (!$data) {
            return redirect()->back()->with('error', 'Data not found.');
        }

        $program = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
        $classes = k12ConnectionClasses::where('program_id', $program->id)->get();
        // $classes = $data->classes()->get(); 

        if ($classes->isEmpty()) {
            return redirect()->back()->with('error', 'No data available for export.');
        }


        $fileName = 'export_' . time() . '.csv';

        if ($exportType == 'csv') {
            return Excel::download(new ExportDatatable($classes), $fileName, \Maatwebsite\Excel\Excel::CSV);
            // return Excel::download(new FrontExportDatatable(collect($data->classes)), $fileName, \Maatwebsite\Excel\Excel::CSV);

        }

        return redirect()->back()->with('error', 'Invalid export type.');
    }

    public function viewHireOffer($encryptedId, Request $request)
    {
        $requirement_id = decrypt_str($request->reqId);
        $id = decrypt_str($encryptedId);
        $instructor = OnboardingInstructor::where('id', $id)->with('step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3', 'step3.subjects', 'step5', 'step6', 'shortlist')->first();
        $data = PlatformSchoolRequirements::find($requirement_id);
        $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $data->id)->where('school_id', auth()->user()->id)->where('instructor_id', $instructor->id)->first();
        // dd($instructor, $data,$applicants);

        return response()->json([
            'html' => view('school-marketplace.components.partial-offer-applicants', compact('instructor', 'data', 'applicants'))->render()
        ]);
        // return view('school-marketplace.components.partial-offer-applicants');
    }

    public function searchHire(Request $request)
    {
        $encryptedId = $request->reqId;
        $data = PlatformSchoolRequirements::find(decrypt_str($request->reqId));
        $hires = SchoolInstructorHiring::with([
            'user.step1',
            'user.step2.education',
            'user.step2.teching',
            'user.step3.subjects',
            'user.step5',
            'user.step6',
            'user.shortList',
            'requirements'
        ])->where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id
        ]);
        switch ($request->hireTabId) {
            case 'hire-hired':
                $hires->where('status', 'accepted');
                break;
            case 'hire-offers':
                $hires->where('status', 'pending');
                break;
            default:
                $hires->where('status', 'accepted');
                break;
        }

        if (!empty($request->searchText)) {
            $searchText = $request->searchText;

            $hires->where(function ($query) use ($searchText) {
                $query->whereHas('user', function ($query) use ($searchText) {
                    $query->where('first_name', 'LIKE', "%$searchText%")
                        ->orWhere('last_name', 'LIKE', "%$searchText%")
                        ->orWhere('email', 'LIKE', "%$searchText%")
                        ->orWhere('state', 'LIKE', "%$searchText%")
                        ->orWhere('city', 'LIKE', "%$searchText%");
                });

                // Step 2
                $query->orWhereHas('user.step2', function ($query) use ($searchText) {
                    $query->where('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%")
                                ->orWhereRaw('JSON_CONTAINS(states, ?)', [json_encode($searchText)]);
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 3
                $query->orWhereHas('user.step3', function ($q) use ($searchText) {
                    $q->join('tbl_classes', 'tbl_classes.id', '=', 'onboarding_instructor_teaching_preferences.i_prefer_to_teach')
                        ->where("tbl_classes.class_name", 'LIKE', "%$searchText%")
                        ->orWhere('format', 'LIKE', "%$searchText%")
                        ->orWhere('language_teach_that_i_teach', 'LIKE', "%$searchText%")
                        ->orWhere('program_type', 'LIKE', "%$searchText%")
                        ->orWhere('other_language', 'LIKE', "%$searchText%")
                        ->orWhere('other_program_type', 'LIKE', "%$searchText%")
                        ->orWhereHas('subjects', function ($q) use ($searchText) {
                            $q->join('subject_area_v1', 'subject_area_v1.id', '=', 'onboarding_instructor_subjects.subject')
                                ->join('subjects_v1', 'subjects_v1.id', '=', 'onboarding_instructor_subjects.sub_subject')
                                ->where('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                                ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%");
                        });
                });
                // $query->orWhereHas('user.step3.subjects', function ($q) use ($searchText) {
                //     $q->whereHas('subject', function ($q) use ($searchText) {
                //         $q->where('subject_name', 'LIKE', "%$searchText%");
                //     })->orWhereHas('sub_subject', function ($q) use ($searchText) {
                //         $q->where('name', 'LIKE', "%$searchText%");
                //     });
                // });

                // Step 5
                $query->orWhereHas('user.step5', function ($query) use ($searchText) {
                    $query->where('profile_title', 'LIKE', "%$searchText%")
                        ->orWhere('description', 'LIKE', "%$searchText%")
                        ->orWhere('profile_tags', 'LIKE', "%$searchText%");
                });
            });
        }
        $hires = $hires->orderBy('id', 'DESC')->get();
        $acceptedCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'accepted'
        ])->count();

        $pendingCount = SchoolInstructorHiring::where([
            'requirment_id' => $data->id,
            'school_id' => auth()->user()->id,
            'status' => 'pending'
        ])->count();

        $view = view("school-marketplace.components.partial-hire-list-applicants", compact('data', 'hires', 'encryptedId'))->render();
        switch ($request->hireTabId) {
            case 'hire-hired':
                $view = view("school-marketplace.components.partial-hire-list-applicants", compact('data', 'hires', 'encryptedId'))->render();
                break;

            case 'hire-offers':
                $view = view("school-marketplace.components.partial-hire-offer-list-applicants", compact('data', 'hires', 'encryptedId'))->render();
                break;

            default:
                $view = view("school-marketplace.components.partial-hire-list-applicants", compact('data', 'hires', 'encryptedId'))->render();
                break;
        }

        return response()->json(['success' => true, 'view' => $view, 'acceptedCount' => $acceptedCount, 'pendingCount' => $pendingCount]);
    }

    public function storeHireOffer(Request $request)
    {
        try {
            $schoolId = auth()->user()->id;

            $validatedData = $request->validate([
                'data_id' => 'required|integer',
                'instructor_id' => 'required|integer',
                'payment_option' => 'required|string',
                'pay_per_hour' => 'required|numeric',
                'total_cost' => 'required|string',
                'contract_terms' => 'nullable|string',
                'file' => 'nullable|max:5120',
            ]);
            $hireExist = SchoolInstructorHiring::where('school_id', $schoolId)->where('requirment_id', $validatedData['data_id'])->whereIn('status', ['accepted', 'pending'])->first();
            if (!empty($hireExist)) {
                return response()->json(['success' => false, 'message' => 'Offer already sent!']);
            } else {
                $filename = null;
                if ($request->hasFile('file')) {
                    $file = $request->file('file');
                    $name = time() . "." . $file->getClientOriginalExtension();
                    $filename = 'uploads/marketplace/offers/' . $name;
                    uploads3image($filename, $file);
                    // dd(generateSignedUrl($filename));
                }

                $insertData = [
                    'requirment_id' => $validatedData['data_id'],
                    'school_id' => $schoolId,
                    'instructor_id' => $validatedData['instructor_id'],
                    'invited_by_school' => $schoolId,
                    'payment_option' => $validatedData['payment_option'],
                    'pay_hour' => $validatedData['pay_per_hour'],
                    'total_contract_cost' => $validatedData['total_cost'],
                    'additional_contract_terms' => $validatedData['contract_terms'],
                    'contract_file' => $filename,
                ];

                SchoolInstructorHiring::create($insertData);

                return response()->json(['success' => true, 'message' => 'Offer sent successfully!']);
            }
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('Error in storeHireOffer: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An unexpected error occurred. Please try again later.'], 500);
        }
    }

    public function updateHireOffer($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $hireExist = SchoolInstructorHiring::find($id);
        if (empty($hireExist)) {
            $success = false;
        } else {
            $success = true;
            SchoolInstructorHiring::where('id', $id)->update(['status' => $request->status]);
            PlatformSchoolRequirements::where(['id' => $hireExist->requirment_id])->update(['status' => 'open']);
        }
        return response()->json(['success' => $success]);
    }

    public function storeClassSetup($encryptedId, Request $request)
    {
        // dd($request->all());
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);
        $program = k12ConnectionPrograms::where('requirement_id', $id)->where('school_id', $data->school_id)->first();
        $lastProctorId = !empty($request->proctor_id) ? $request->proctor_id : null;
        $lastRosterId = !empty($request->roster_id) ? $request->roster_id : null;
        $linkId = !empty($request->classLink_id) ? $request->classLink_id : null;
        $noteId = !empty($request->onboarding_id) ? $request->onboarding_id : null;
        $hasCompleteSetup = true;
        if (!empty($request->select_proctor_name)) {
            $proctorExist = PlatformSchoolProctor::where('id', $request->select_proctor_name)->first();
            if (!empty($proctorExist)) {
                $lastProctorId = $proctorExist->id;
                $data->update(['proctor_id' => $lastProctorId]);
            }
        }

        $csv_file = null;
        if ($request->has('csv')) {
            PlatformSchoolRoster::where('requirement_id', $data->id)->delete();
            $file = $request->file("csv");
            $name = time() . "." . $file->getClientOriginalExtension();
            $filename = 'uploads/marketplace/roster/' . $name;
            $result = $this->importStore($request->file('csv'), $data, $filename, $program);
            // if (!empty($lastRosterId)) {
            //     // uploads3image($filename, $file);
            //     // $csv_file = $lastRosterId;
            // }
            if (!empty($result['errors'])) {
                return response()->json(['success' => false, 'errors' => $result['errors']]);
            }

            $lastRosterId = $result['lastInsertedId'];
        } else {
            if (!empty($request->rosterFile)) {
                $rosters = PlatformSchoolRoster::where('requirement_id', $data->id)->get();
                foreach ($rosters as $roster) {
                    $roster->csv_file = $request->rosterFile;
                    $roster->save();
                }
            } else {
                $hasCompleteSetup = false;
            }
        }

        // if (!empty($request->class_link)) {
        $linksData = [
            'program_id' => $program->id,
            'requirement_id' => $data->id,
        ];

        if (!empty($request->class_link)) {
            $linksData['link'] = $request->class_link;
        } else {
            $linksData['link'] = null;
            $hasCompleteSetup = false;
        }

        $links = k12ConnectionMeetingLinks::updateOrCreate(
            ['requirement_id' => $data->id],  // Check if link exists for requirement
            $linksData
        );
        $linkId = $links->id;
        // }

        $onboarding_file = null;
        $document = null;
        // if (!empty($request->onboarding_instructions) || $request->hasFile("document")) {
        $noteData = [
            'program_id' => $program->id,
            'requirement_id' => $data->id,
            'created_by' => auth()->user()->id,
        ];
        if ($request->hasFile("document")) {
            $file = $request->file("document");
            $name = time() . "." . $file->getClientOriginalExtension();
            $filename = 'uploads/marketplace/onboarding-document/' . $name;
            uploads3image($filename, $file);
            $document = $filename;
            $onboarding_file = generateSignedUrl($filename);
            $noteData['document'] = $document;
            $noteData['document_name'] = $file->getClientOriginalName();
        } else {
            $noteData['document'] = null;
            $noteData['document_name'] = null;
        }
        if (!empty($request->onboarding_instructions) && $request->onboarding_instructions != null) {
            $noteData['note'] = $request->onboarding_instructions;
        } else {
            $noteData['note'] = null;
            $hasCompleteSetup = false;
        }
        $note = k12ConnectionProgramNotes::updateOrCreate(
            ['requirement_id' => $data->id],  // Check if note exists for requirement
            $noteData
        );
        $noteId = $note->id;
        // }

        if ($hasCompleteSetup) {
            $data->update(['finalize_setup' => 'true']);
        } else {
            $data->update(['finalize_setup' => 'false']);
        }
        // dd($lastProctorId, $lastRosterId, $linkId, $noteId);
        return response()->json(['success' => true, 'lastProctorId' => $lastProctorId, 'lastRosterId' => $lastRosterId, 'linkId' => $linkId, 'noteId' => $noteId, 'csv_file' => $csv_file, 'onboarding_file' => $onboarding_file, 'programId' => $program->id]);
    }

    public function removeNoteDocument(Request $request)
    {
        if (!empty($request->noteId)) {
            $notes = k12ConnectionProgramNotes::find(decrypt_str($request->noteId));
            $notes->update(['document' => null]);
        }
        return response()->json(['success' => true]);
    }

    public function saveClassSetup($encryptedId, Request $request)
    {
        $id = decrypt_str($encryptedId);
        $data = PlatformSchoolRequirements::find($id);
        $program = k12ConnectionPrograms::where('requirement_id', $id)->where('school_id', $data->school_id)->first();
        $lastProctorId = !empty($request->proctor_id) ? $request->proctor_id : null;
        $lastRosterId = !empty($request->roster_id) ? $request->roster_id : null;
        $linkId = !empty($request->classLink_id) ? $request->classLink_id : null;
        $noteId = !empty($request->onboarding_id) ? $request->onboarding_id : null;
        // dd($request->all());

        // if ($request->type == 'proctor') {
        if (empty($request->first_name)) {
            $proctorExist = PlatformSchoolProctor::find($request->select_proctor);
            if (!empty($proctorExist)) {
                $lastProctorId = $proctorExist->id;
                $data->update(['proctor_id' => $lastProctorId]);
            }
        } else {
            $proctorData = [
                'school_id' => $data->school_id,
                'proctor_name' => ucfirst($request->first_name) . ' ' . ucfirst($request->last_name),
                'email' => $request->email,
                'phone' => $request->phone,
            ];

            $lastProctor = PlatformSchoolProctor::updateOrCreate(
                ['email' => $request->email],  // Check if proctor exists by email
                $proctorData
            );
            $lastProctorId = $lastProctor->id;
            $data->update(['proctor_id' => $lastProctor->id]);
        }
        // }

        // $csv_file = null;
        // if ($request->type == 'roster') {
        //     if ($request->has('csv')) {
        //         PlatformSchoolRoster::where('requirement_id', $data->id)->delete();
        //         $file = $request->file("csv");
        //         $name = time() . "." . $file->getClientOriginalExtension();
        //         $filename = 'uploads/marketplace/roster/' . $name;
        //         $lastRosterId = $this->importStore($request->file('csv'), $data, $filename);
        //         if (!empty($lastRosterId)) {
        //             uploads3image($filename, $file);
        //             $csv_file = generateSignedUrl($filename);
        //         }
        //     }
        // }

        // if ($request->type == 'links') {
        //     $linksData = [
        //         'program_id' => $program->id,
        //         'requirement_id' => $data->id,
        //     ];

        //     if (!empty($request->class_link)) {
        //         $linksData['link'] = $request->class_link;
        //     }

        //     $links = k12ConnectionMeetingLinks::updateOrCreate(
        //         ['requirement_id' => $data->id],  // Check if link exists for requirement
        //         $linksData
        //     );
        //     $linkId = $links->id;
        // }

        // $onboarding_file = null;
        // if ($request->type == 'onboarding') {
        //     if (!empty($request->onboarding_instructions)) {
        //         $document = null;
        //         if ($request->hasFile("document")) {
        //             $file = $request->file("document");
        //             $name = time() . "." . $file->getClientOriginalExtension();
        //             $filename = 'uploads/marketplace/onboarding-document/' . $name;
        //             uploads3image($filename, $file);
        //             $document = $filename;
        //             $onboarding_file = generateSignedUrl($filename);
        //         }

        //         $noteData = [
        //             'program_id' => $program->id,
        //             'requirement_id' => $data->id,
        //             'note' => $request->onboarding_instructions,
        //             'document' => $document ?? null,
        //             'created_by' => auth()->user()->id,
        //         ];

        //         $note = k12ConnectionProgramNotes::updateOrCreate(
        //             ['requirement_id' => $data->id],  // Check if note exists for requirement
        //             $noteData
        //         );
        //         $noteId = $note->id;
        //     }
        // }

        if (!empty($lastProctorId) && !empty($lastRosterId) && !empty($linkId) && !empty($noteId)) {
            $data->update(['finalize_setup' => 'true']);
        }
        return response()->json(['success' => true, 'lastProctorId' => $lastProctorId, 'lastRosterId' => $lastRosterId, 'linkId' => $linkId, 'noteId' => $noteId]);
    }

    public function importStore($file, $data, $filename, $program)
    {
        if ($file) {
            DB::beginTransaction();
            $import = new k12StudentImport($data, $filename, $program ? $program->id : null);
            Excel::import($import, $file);
            $lastInsertedId = $import->getLastInsertedId();
            $errors = $import->getValidationErrors();
            DB::commit();
            if (!empty($errors)) {
                return ['errors' => $errors]; // Return validation errors
            }

            if (!$import->hasValidData()) {
                DB::rollBack(); // Rollback if no data is found
                return ['errors' => ['CSV file has no data or incorrect data.']];
            }

            return ['lastInsertedId' => $lastInsertedId];
            // return $lastInsertedId;
        }
    }

    public function getClasses()
    {
        $classes = Classes::get();
        return response()->json(['success' => true, 'classes' => $classes]);
    }

    public function exportRosterSample()
    {
        return Excel::download(new ExportSampleRoster, 'sample.xlsx');
    }

    public function viewRoster(Request $request)
    {
        $rosters = PlatformSchoolRoster::where('program_id', $request->programId)->get();
        $view = view('school-marketplace.components.roster-list', compact('rosters'))->render();
        return response()->json(['success' => true, 'view' => $view]);
    }

    private function getStarRating($rating)
    {
        if (!$rating || intval($rating) === 0) {
            return "-"; // No feedback case
        }

        $fullStar = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                        <path d="M9 1.5L11.09 6.26L16.18 6.91L12.545 10.64L13.635 15.5L9 12.75L4.365 15.5L5.455 10.64L1.82 6.91L6.91 6.26L9 1.5Z" 
                              fill="#F1C644" stroke="#F1C644"/>
                    </svg>';

        $emptyStar = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                        <path d="M9 1.5L11.09 6.26L16.18 6.91L12.545 10.64L13.635 15.5L9 12.75L4.365 15.5L5.455 10.64L1.82 6.91L6.91 6.26L9 1.5Z" 
                              fill="none" stroke="#F1C644"/>
                    </svg>';

        $stars = '';
        $rating = intval($rating); // Ensure integer value
        $rating = max(0, min(5, $rating)); // Ensure rating is between 0 to 5

        // Pehle full stars add karna
        for ($i = 1; $i <= $rating; $i++) {
            $stars .= $fullStar;
        }

        // Jo bache huye stars hai wo empty stars rahenge
        for ($i = $rating + 1; $i <= 5; $i++) {
            $stars .= $emptyStar;
        }

        return $stars;
    }

    public function getLists(Request $request)
    {
        $lists = PlatformSchoolUserSaveList::where('school_id', auth()->user()->id)->where('requirement_id', decrypt_str($request->reqId))->where('user_id', decrypt_str($request->userId))->pluck('list_name')->toArray();
        return response()->json(['success' => true, 'lists' => $lists]);
    }

    public function bookmarkListSave(Request $request)
    {
        PlatformSchoolUserSaveList::create([
            'list_name' => $request->list_name,
            'user_id' => decrypt_str($request->userId),
            'requirement_id' => decrypt_str($request->reqId),
            'school_id' => auth()->user()->id,
        ]);

        // Return updated lists
        return response()->json([
            'success' => true,
            'lists' => PlatformSchoolUserSaveList::where('school_id', auth()->user()->id)->where('requirement_id', decrypt_str($request->reqId))->where('user_id', decrypt_str($request->userId))->pluck('list_name')->toArray()
        ]);
    }

    public function allRequirements(Request $request)
    {
        $query  = PlatformSchoolRequirements::with(['reviewApplicants.user'])->withCount('reviewApplicants')->where('school_id', auth()->user()->id);
        $redirectRoute = route('allRequirements');
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('platform_school_requirements.status', $request->status);
        }

        if ($request->has('searchText')) {
            $formattedDate = '';
            $searchText = $request->searchText;
            $query->join('subject_area_v1', 'subject_area_v1.id', '=', 'platform_school_requirements.subject_area_id')
                ->join('subjects_v1', 'subjects_v1.id', '=', 'platform_school_requirements.subject_id');
            $query->where(function ($q) use ($searchText) {
                // if (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $searchText)) {
                //     $date = \Carbon\Carbon::createFromFormat('m/d/Y', $searchText)->format('Y-m-d');
                //     $q->orWhereDate('start_date', $date)
                //         ->orWhereDate('end_date', $date);
                // } elseif (preg_match('/^\d{4}$/', $searchText)) { // Check if input is a 4-digit year (e.g., "2025")
                //     $q->orWhereYear('start_date', $searchText)
                //     ->orWhereYear('end_date', $searchText);
                // } elseif (preg_match('/^\d{2}$/', $searchText)) { // Check if input is a 2-digit number
                //     $q->orWhereMonth('start_date', $searchText)
                //       ->orWhereDay('start_date', $searchText)
                //         ->orWhereMonth('end_date', $searchText)
                //         ->orWhereDay('end_date', $searchText);
                // }
                $q->where('platform_school_requirements.status', 'LIKE', strtolower($searchText) != 'archived' ? "%$searchText%" : "%closed%")
                    ->orWhere('platform_school_requirements.requirement_name', 'LIKE', "%$searchText%")
                    ->orWhere('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                    ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%")
                    ->orWhereHas('reviewApplicants.user', function ($sub) use ($searchText) {
                        $sub->where('first_name', 'LIKE', "%$searchText%")
                            ->orWhere('last_name', 'LIKE', "%$searchText%")
                            ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                    });
            });
        }

        $requirements = $query->orderBy('id', 'DESC')->get();
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'html' => view('school-marketplace.components.all-requirements-content', compact('requirements'))->render()
            ]);
        }
        return view('school-marketplace.all-requirements', compact('requirements', 'redirectRoute'));
    }

    public function discover(Request $request)
    {
        $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6']);
        $requirements = PlatformSchoolRequirements::where(['status' => 'open'])->orderBy('id', 'DESC')->get();
        $redirectRoute = route('new-school.discover');
        $delivery_mode = 'online,in-person,hybrid';
        $filters = $this->filterOptions($delivery_mode);

        if (!empty($request->searchText)) {
            $searchText = $request->searchText;
            $users->where(function ($query) use ($searchText, $request) {
                $query->where(function ($q) use ($searchText) {
                    $q->where('first_name', 'LIKE', "%$searchText%")
                        ->orWhere('last_name', 'LIKE', "%$searchText%")
                        ->orWhere('email', 'LIKE', "%$searchText%")
                        ->orWhere('state', 'LIKE', "%$searchText%")
                        ->orWhere('city', 'LIKE', "%$searchText%")
                        ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                });

                $query->orWhereHas('step1', function ($q) use ($searchText) {
                    $q->where('state', 'LIKE', "%$searchText%")
                        ->orWhere('city', 'LIKE', "%$searchText%")
                        ->orWhere('zip_code', 'LIKE', "%$searchText%");
                });

                // Step 2
                $query->orWhereHas('step2', function ($query) use ($searchText) {
                    $query->where('profile_type', 'LIKE', "%$searchText%")
                        ->orWhere('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%")
                                ->orWhereRaw('JSON_CONTAINS(states, ?)', [json_encode($searchText)]);
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 3
                $query->orWhereHas('step3', function ($query) use ($searchText) {
                    $query->join('tbl_classes', 'tbl_classes.id', '=', 'onboarding_instructor_teaching_preferences.i_prefer_to_teach')
                        ->where("tbl_classes.class_name", 'LIKE', "%$searchText%")
                        ->orWhere('format', 'LIKE', "%$searchText%")
                        ->orWhere('language_teach_that_i_teach', 'LIKE', "%$searchText%")
                        ->orWhere('program_type', 'LIKE', "%$searchText%")
                        ->orWhere('other_language', 'LIKE', "%$searchText%")
                        ->orWhere('other_program_type', 'LIKE', "%$searchText%")
                        ->orWhereHas('subjects', function ($q) use ($searchText) {
                            $q->join('subject_area_v1', 'subject_area_v1.id', '=', 'onboarding_instructor_subjects.subject')
                                ->join('subjects_v1', 'subjects_v1.id', '=', 'onboarding_instructor_subjects.sub_subject')
                                ->where('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                                ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%");
                        });
                });

                // Step 5
                $query->orWhereHas('step5', function ($query) use ($searchText) {
                    $query->where('profile_title', 'LIKE', "%$searchText%")
                        ->orWhere('description', 'LIKE', "%$searchText%")
                        ->orWhere('profile_tags', 'LIKE', "%$searchText%");
                });
            });
        }

        if (!empty($request->subject_area)) {
            $subjects = Subject::whereIn('subject_name', $request->subject_area)->pluck('id')->toArray();
            $users->whereHas('step3.subjects', function ($query) use ($subjects) {
                $query->whereIn('subject', $subjects);
            });
        }

        if (!empty($request->subject)) {
            $subSubjects = SubsubjectModel::whereIn('name', $request->subject)->pluck('id')->toArray();
            $users->whereHas('step3.subjects', function ($query) use ($subSubjects) {
                $query->whereIn('sub_subject', $subSubjects);
            });
        }

        if (!empty($request->grade_levels)) {
            $grades = Classes::whereIn('class_name', $request->grade_levels)->pluck('id')->toArray();
            $users->whereHas('step3', function ($query) use ($grades) {
                $query->where(function ($q) use ($grades) {
                    foreach ($grades as $grade) {
                        $q->orWhereRaw("FIND_IN_SET(?, i_prefer_to_teach)", [$grade]);
                    }
                });
            });
        }

        if (!empty($request->years_of_relevant_experience)) {
            $YearsOfExp = array_map('intval', $request->years_of_relevant_experience);
            $minYears = min($YearsOfExp);
            $maxYears = max($YearsOfExp);
            if ($minYears != 0 || $maxYears != 26) {
                $users->whereHas('step2', function ($query) use ($minYears, $maxYears) {
                    $query->whereRaw('CAST(total_experience AS UNSIGNED) BETWEEN ? AND ?', [$minYears, $maxYears]);
                });
            }
        }
        // dd($applicants->get());
        if (!empty($request->credentialing_agency)) {
            $agency = CredentialingAgencyModel::whereIn('agency', $request->credentialing_agency)->pluck('agency')->toArray();
            $users->whereHas('step2.education', function ($query) use ($agency) {
                $query->whereIn('credentialing_agency', $agency);
            });
        }

        if (!empty($request->certificationlicense)) {
            $certificate = AgencyCertificatesModel::whereIn('certificate', $request->certificationlicense)->pluck('certificate')->toArray();
            $users->whereHas('step2.education', function ($query) use ($certificate) {
                $query->whereIn('education', $certificate);
            });
        }

        if (!empty($request->program_type)) {
            $users->whereHas('step3', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->program_type as $program) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, program_type)", [$program]);
                    }
                });
            });
        }

        if (!empty($request->profile_type)) {
            $profileTypes = $request->profile_type;
            $updatedProfileTypes = array_map(function ($item) {
                return $item === "Licensed/Credentialed Educator" ? "Certified Teacher" : $item;
            }, $profileTypes);

            $users->whereHas('step2', function ($query) use ($updatedProfileTypes) {
                $query->where('profile_type', $updatedProfileTypes);
            });
        }

        if (!empty($request->minimum_education_level)) {
            $users->whereHas('step2', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->minimum_education_level as $education) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, highest_level_of_education)", [$education]);
                    }
                });
            });
        }

        if (!empty($request->language_spoken)) {
            $users->whereHas('step3', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->language_spoken as $language) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, language_teach_that_i_teach)", [$language]);
                    }
                });
            });
        }

        if (!empty($request->delivery_mode)) {
            $normalizedModes = array_map(function ($mode) {
                return strtolower(str_replace('_', '-', $mode));
            }, $request->delivery_mode);

            $users->whereHas('step3', function ($query) use ($normalizedModes) {
                $query->where(function ($subQuery) use ($normalizedModes) {
                    foreach ($normalizedModes as $mode) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, format)", [$mode]);
                    }
                });
            });
        }

        // if (!empty($request->zipcode)) {
        //     if ($request->inviteTabId == 'talent') {
        //         $users->where(function ($query) use ($request) {
        //             $query->where('zipcode', $request->zipcode);
        //         });
        //     } else {
        //         $users->whereHas($user_name, function ($query) use ($request) {
        //             $query->where('zipcode', $request->zipcode);
        //         });
        //     }
        // }
        $users = $users->orderBy('id', 'DESC')->get();
        // dd($users);
        $marketplaceInstructorIds = OnboardingInstructorMarketplaceContract::pluck('user_id')->toArray();
        $whizaraInstructorIds = OnboardingInstructorContract::pluck('user_id')->toArray();

        // Combine both contract instructor IDs and make them unique
        $validInstructorIds = array_unique(array_merge($marketplaceInstructorIds, $whizaraInstructorIds));

        $users = $users->filter(function ($user) use ($validInstructorIds) {
            return in_array($user->id, $validInstructorIds);
        });

        if ($request->ajax()) {
            return response([
                'success' => true,
                'view' => view('school-marketplace.components.discover-content', compact('users', 'requirements'))->render(),
                'request' => $request->all(),
                'count' => count($users)
            ]);
        }
        return view('school-marketplace.discover', compact('users', 'filters', 'redirectRoute', 'requirements'));
    }

    // public function reports(Request $request)
    // {
    //     if ($request->ajax()) {
    //         $hires = SchoolInstructorHiring::with(['user','requirements'])->where('school_id', auth()->user()->id)->whereIn('status', ['accepted', 'declined', 'withdraw'])
    //         ->has('user');


    //         $params = DataTableHelper::getParams($request);
    //         if ($params['columnName'] == 'contractId') {
    //             $params['columnName']   = 'school_instructor_hiring.id';
    //         }

    //         if ($params['columnName'] == 'contractDetails') {
    //             $hires->addSelect([
    //                 'contractDetails' => PlatformSchoolRequirements::selectRaw("platform_school_requirements.requirement_name")
    //                                 ->whereColumn('school_instructor_hiring.requirment_id', 'platform_school_requirements.id')
    //                                 ->limit(1)
    //             ]);
    //         }

    //         if ($params['columnName'] == 'educator') {
    //             $hires->addSelect([
    //                 'educator' => OnboardingInstructor::selectRaw("CONCAT(new_onboarding_instructor.first_name, ' ', new_onboarding_instructor.last_name)")
    //                                 ->whereColumn('school_instructor_hiring.instructor_id', 'new_onboarding_instructor.id')
    //                                 ->limit(1)
    //             ]);
    //         }

    //         // if ($params['columnName'] == 'totalBudget') {
    //         //     $hires->addSelect([
    //         //         'totalBudget' => DB::raw("total_contract_cost * pay_hour AS totalBudget")
    //         //     ]);
    //         // }

    //         $hires->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

    //         [$count, $result] = DataTableHelper::applyPagination($hires, $params['row'], $params['rowperpage']);
    //         $data = [];
    //         $chartData = [];
    //         $i = 1;


    //         foreach ($result as $row) {

    //          $requirement_id=$row->requirment_id;
    //          $program_id = k12ConnectionPrograms::where("requirement_id", $requirement_id) // you should pass the value of requirement_id here
    //          ->pluck('id')
    //          ->first();


    //          $completed_classes=k12ConnectionClasses::where(["status"=>"completed","program_id"=>$program_id])->get();
    //          $total_hour = 0;

    //          foreach ($completed_classes as $class) {
    //              // Convert start_time and end_time to Carbon instances
    //              $start_time = Carbon::createFromFormat('H:i:s', $class->start_time);
    //              $end_time = Carbon::createFromFormat('H:i:s', $class->end_time);

    //              // Calculate the difference in minutes
    //              $class_duration_minutes = $end_time->diffInMinutes($start_time);

    //              // Convert minutes to decimal hours (e.g., 90 minutes = 1.5 hours)
    //              $class_hour = $class_duration_minutes / 60; 

    //              // Add the decimal hours to total_hour
    //              $total_hour += $class_hour;
    //          }

    //          $budget_used = round($total_hour * $row->pay_hour, 2);

    //           $status="";
    //           $current_date = Carbon::now(); // Get the current date and time

    //           // Parse the start and end dates
    //           $start_date = Carbon::parse($row->requirements->start_date);
    //           $end_date = Carbon::parse($row->requirements->end_date);
    //           if ($current_date->between($start_date, $end_date)) {
    //             // If the current date is within the range, set status to "on-going"
    //             $status = "<button class='btn m-3' style='background-color:#EBF9F1;border-radius:15px;color:#1F9254'>On-going</button>";
    //         } elseif ($current_date->gt($end_date)) {
    //             // If the current date is after the end date, set status to "halted"
    //             $status = "<button class='btn m-3' style='background-color:#FBE7E8;border-radius:15px;color:#A30D11'>halted</button>";

    //         } elseif($row->status=="withdraw"||$row->status=="declined"){
    //             $status = "<button class='btn m-3' style='background-color:#FEF2E5;border-radius:15px;color:#CD6200'>On-Hold</button>";


    //         }
    //         else{

    //             $status="upcoming";

    //         }



    //             $data[] = [
    //                 "contractId" => $row->id,
    //                 "contractDetails" => !empty($row->requirements) ? $row->requirements->requirement_name : '',
    //                 "contractDates" => !empty($row->requirements) ? Carbon::parse($row->requirements->start_date)->format('M jS') .' - '. Carbon::parse($row->requirements->end_date)->format('M jS, Y') : '',
    //                 "educator" => k12username($row->instructor_id),
    //                 "totalBudget" => '$' . $row->total_contract_cost,
    //                 // "budgetUsed" => '$' . $row->total_contract_cost - 100,
    //                 "budgetUsed" => $budget_used,
    //                 "status" =>$status ,
    //             ];

    //             $chartData[] = [
    //                 'contract' => $row->id,
    //                 'totalBudget' => $row->total_contract_cost,
    //                 'budgetUsed' => $row->total_contract_cost - 100, // Replace with actual used amount
    //             ];

    //             $i++;
    //         }

    //         return DataTableHelper::generateResponse($params['draw'], $count, $data, [], $chartData);
    //     }
    //     return view('school-marketplace.reports.report');
    // }

    public function reports(Request $request)
    {
        if ($request->ajax()) {





            if ($request->reportType == "spend-by-contracts") {

                $hires = SchoolInstructorHiring::with(['user', 'requirements'])->where('school_id', auth()->user()->id)->whereIn('status', ['accepted', 'declined', 'withdraw'])
                    ->has('user');

                // if (!empty($request->search_name_contract)) {
                //     // Apply the condition to the 'requirements' relationship where 'requirement_name' matches search_name

                //     $hires = SchoolInstructorHiring::with(['user', 'requirements'])
                //         ->where('school_id', auth()->user()->id)
                //         ->whereIn('status', ['accepted', 'declined', 'withdraw'])
                //         ->has('user')
                //         ->whereHas('requirements', function ($query) use ($request) {
                //             // Add condition on the 'requirements' relationship where 'requirement_name' is like the search_name
                //             $query->where('requirement_name', 'like', "{$request->search_name_contract}%");
                //         });
                // }
                if (!empty($request->search_name_contract)) {
                    // Apply the condition to 'user' first name or 'requirements' requirement name
                    $hires = SchoolInstructorHiring::with(['user', 'requirements'])
                        ->where('school_id', auth()->user()->id)
                        ->whereIn('status', ['accepted', 'declined', 'withdraw'])
                        ->has('user')
                        ->where(function ($query) use ($request) {
                            // Either filter by user's first_name or requirement_name
                            $query->whereHas('user', function ($q) use ($request) {
                                // Filter by first name in user model
                                $q->where('first_name', 'like', "{$request->search_name_contract}%");
                            })
                                ->orWhereHas('requirements', function ($q) use ($request) {
                                    // Filter by requirement name in requirements model
                                    $q->where('requirement_name', 'like', "{$request->search_name_contract}%");
                                });
                        });
                }

                $params = DataTableHelper::getParams($request);
                if ($params['columnName'] == 'contractId') {
                    $params['columnName']   = 'school_instructor_hiring.id';
                }

                if ($params['columnName'] == 'contractDetails') {
                    $hires->addSelect([
                        'contractDetails' => PlatformSchoolRequirements::selectRaw("platform_school_requirements.requirement_name")
                            ->whereColumn('school_instructor_hiring.requirment_id', 'platform_school_requirements.id')
                            ->limit(1)
                    ]);
                }

                if ($params['columnName'] == 'educator') {
                    $hires->addSelect([
                        'educator' => OnboardingInstructor::selectRaw("CONCAT(new_onboarding_instructor.first_name, ' ', new_onboarding_instructor.last_name)")
                            ->whereColumn('school_instructor_hiring.instructor_id', 'new_onboarding_instructor.id')
                            ->limit(1)
                    ]);
                }

                // if ($params['columnName'] == 'totalBudget') {
                //     $hires->addSelect([
                //         'totalBudget' => DB::raw("total_contract_cost * pay_hour AS totalBudget")
                //     ]);
                // }

                $hires->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

                [$count, $result] = DataTableHelper::applyPagination($hires, $params['row'], $params['rowperpage']);
                $data = [];
                $chartData = [];
                $i = 1;


                foreach ($result as $row) {


                    $requirement_id = $row->requirment_id;
                    $program_id = k12ConnectionPrograms::where("requirement_id", $requirement_id) // you should pass the value of requirement_id here
                        ->pluck('id')
                        ->first();




                    $completed_classes = k12ConnectionClasses::where(["status" => "completed", "program_id" => $program_id])->get();
                    $total_hour = 0;
                    foreach ($completed_classes as $class) {
                        // Convert start_time and end_time to Carbon instances
                        $start_time = Carbon::createFromFormat('H:i:s', $class->start_time);
                        $end_time = Carbon::createFromFormat('H:i:s', $class->end_time);

                        // Calculate the difference in minutes
                        $class_duration_minutes = $end_time->diffInMinutes($start_time);

                        // Convert minutes to decimal hours (e.g., 90 minutes = 1.5 hours)
                        $class_hour = $class_duration_minutes / 60;

                        // Add the decimal hours to total_hour
                        $total_hour += $class_hour;
                    }

                    $budget_used = round($total_hour * $row->pay_hour, 2);

                    $status = "";
                    $current_date = Carbon::now(); // Get the current date and time

                    // Parse the start and end dates
                    $start_date = Carbon::parse($row->requirements->start_date);
                    $end_date = Carbon::parse($row->requirements->end_date);
                    if ($current_date->between($start_date, $end_date)) {
                        // If the current date is within the range, set status to "on-going"
                        $status = "<button class='btn m-3' style='background-color:#EBF9F1;border-radius:15px;color:#1F9254'>On-going</button>";
                    } elseif ($current_date->gt($end_date)) {
                        // If the current date is after the end date, set status to "halted"
                        $status = "<button class='btn m-3' style='background-color:#FBE7E8;border-radius:15px;color:#A30D11'>halted</button>";
                    } elseif ($row->status == "withdraw" || $row->status == "declined") {
                        $status = "<button class='btn m-3' style='background-color:#FEF2E5;border-radius:15px;color:#CD6200'>On-Hold</button>";
                    } else {

                        $status = "upcoming";
                    }



                    $data[] = [
                        "contractId" => $row->id,
                        "contractDetails" => !empty($row->requirements) ? $row->requirements->requirement_name : '',
                        "contractDates" => !empty($row->requirements) ? Carbon::parse($row->requirements->start_date)->format('M jS') . ' - ' . Carbon::parse($row->requirements->end_date)->format('M jS, Y') : '',
                        "educator" => k12username($row->instructor_id),
                        "totalBudget" => '$' . $row->total_contract_cost,
                        // "budgetUsed" => '$' . $row->total_contract_cost - 100,
                        "budgetUsed" => '$' . $budget_used,
                        "status" => $status,
                    ];

                    $chartData[] = [
                        'contract' => $row->id,
                        'totalBudget' => $row->total_contract_cost,
                        'budgetUsed' => $row->total_contract_cost - 100, // Replace with actual used amount
                    ];

                    $i++;
                }

                return DataTableHelper::generateResponse($params['draw'], $count, $data, [], $chartData);
            } else {

                // start aur draw ke liye
                $start = $request->start ?? 0;
                $draw = $request->draw ?? 1;

                // base query
                $query = SchoolInstructorHiring::with(['user', 'requirements'])
                    ->where('school_instructor_hiring.school_id', auth()->id())
                    ->where('school_instructor_hiring.status', 'accepted')
                    ->select([
                        'instructor_id',
                        DB::raw('CONCAT(first_name, " ", last_name) as educator_name'),
                        DB::raw('COUNT(*) as contract_count'),
                        DB::raw('SUM(total_contract_cost) as total_budget'),
                    ])
                    ->join('new_onboarding_instructor', 'instructor_id', '=', 'new_onboarding_instructor.id')
                    ->groupBy('instructor_id', 'first_name', 'last_name');

                if (!empty($request->search_name_educator)) {

                    $query->whereHas('user', function ($query) use ($request) {
                        $query->where('first_name', 'like', "%{$request->search_name_educator}%");
                    });
                }






                // Date Filter Apply   
                if ($request->filled('startDate') && $request->filled('endDate')) {
                    $start = Carbon::createFromFormat('m/d/Y', $request->startDate)->startOfDay();
                    $end = Carbon::createFromFormat('m/d/Y', $request->endDate)->endOfDay();

                    $query->whereBetween('updated_at', [$start, $end]);
                }

                $totalRecords = $query->count();

                $limit = $request->get('length') ?? 10;
                $offset = $request->get('start') ?? 0;

                $dataResult = (clone $query)->offset($offset)->limit($limit)->get();

                $filteredRecords = (clone $query)->count();
                $chartData = [];
                // Map data
                $data = $dataResult->map(function ($item, $index) use ($start) {
                    return [
                        'srNo' => $start + $index + 1,
                        'educatorName' => $item->educator_name,
                        // 'activeContracts' => $item->contract_count,
                        'activeContracts' => $item->contract_count == 1 ? '<strong>One contract</strong>' : '<strong>Multiple contracts</strong>',
                        'totalBudget' => '$' . number_format($item->total_budget, 2),
                        // 'amountUsed' => '$'.number_format($item->total_budget * 0.7, 2),
                        // 'remainingBudget' => '$'.number_format($item->total_budget * 0.3, 2),
                        'amountUsed' => '$' . number_format($item->total_budget, 2),
                        'remainingBudget' => '$' . number_format($item->total_budget, 2),
                        'instructor_id' => $item->instructor_id,
                    ];

                    // Pie Chart Data
                    $chartData[] = [
                        'educator' => $item->educator_name,
                        'amountUsed' => '$' . number_format($item->total_budget * 0.7, 2),
                        'remainingBudget' => $remainingBudget,
                    ];
                });
                return DataTableHelper::generateResponse($draw, $totalRecords, $data, [], $chartData);
            }
        }
        return view('school-marketplace.reports.report');
    }

    public function getContracts($instructorId)
    {
        try {
            $contracts = SchoolInstructorHiring::with(['requirements'])
                ->where('instructor_id', $instructorId)
                ->get()
                ->map(function ($contract) {
                    return [
                        // 'srNo' => $contract->id,
                        'srNo' => '-',
                        'educatorName' => '-',
                        'activeContracts' => $contract->requirements->requirement_name ?? 'N/A',
                        'totalBudget' => '$' . number_format($contract->total_contract_cost, 2),
                        'amountUsed' => '$' . number_format($contract->total_contract_cost, 2),
                        'remainingBudget' => '$' . number_format($contract->total_contract_cost, 2),
                        'dates' => $contract->requirements ?
                            Carbon::parse($contract->requirements->start_date)->format('M j') . ' - ' .
                            Carbon::parse($contract->requirements->end_date)->format('M j, Y') : 'N/A'
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $contracts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load contracts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function spendByContractReports()
    {
        return view('school-marketplace.reports.spend-by-contracts');
    }

    public function spendByEducatorReports()
    {
        return view('school-marketplace.reports.spend-by-educators');
    }


    public function transactionHistoryReports(Request $request)
    {
        if ($request->ajax()) {

            $schoolId = auth()->user()->id;
            $params = DataTableHelper::getParams($request);

            // Column name mapping for sorting
            if ($params['columnName'] == 'transaction_id' || empty($params['columnName'])) {
                $params['columnName'] = 'school_instructor_hiring.id';
            }
            if ($params['columnName'] == 'date') {
                $params['columnName'] = 'school_instructor_hiring.updated_at';
            }
            if ($params['columnName'] == 'type') {
                $params['columnName'] = 'school_instructor_hiring.payment_option';
            }
            if ($params['columnName'] == 'amount_paid') {
                $params['columnName'] = 'school_instructor_hiring.total_contract_cost';
            }
            if ($params['columnName'] == 'contract_details') {
                $params['columnName'] = 'platform_school_requirements.requirement_name';
            }

            $query = SchoolInstructorHiring::with(['user', 'requirements'])
                ->where('school_id', $schoolId);

            // Sorting
            $query->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
            // dd();
            // Searching logic
            if (!empty($params['searchValue'])) {
                $searchValue = strtolower($params['searchValue']);
                $query->where(function ($q) use ($searchValue) {
                    $q->where('id', 'LIKE', "%{$searchValue}%")
                        ->orWhere('payment_option', 'LIKE', "%{$searchValue}%")
                        ->orWhere('total_contract_cost', 'LIKE', "%{$searchValue}%")
                        ->orWhere('updated_at', 'LIKE', "%{$searchValue}%")
                        ->orWhereHas('user', function ($subQuery) use ($searchValue) {
                            $subQuery->where('first_name', 'LIKE', "%{$searchValue}%")
                                ->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                        })
                        ->orWhereHas('requirements', function ($subQuery) use ($searchValue) {
                            $subQuery->where('requirement_name', 'LIKE', "%{$searchValue}%")
                                ->orWhere('grade_levels_id', 'LIKE', "%{$searchValue}%")
                                ->orWhere('updated_at', 'LIKE', "%{$searchValue}%");
                        });
                });
            }

            // Apply pagination
            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            // Formatting data for DataTable response
            $data = [];
            foreach ($result as $row) {
                $contractDetails = $row->requirements ? '<ul>
                                    <li><strong>' . $row->requirements->requirement_name . '</strong></li>
                                    <li>Grade: ' . $row->requirements->grade_levels_id . '</li>
                                    <li>Updated: ' . $row->requirements->updated_at->format('F j, Y') . '</li>
                                </ul>' : 'N/A';

                $educator = ($row->user)
                    ? ucfirst($row->user->first_name) . ' ' . ucfirst($row->user->last_name)
                    : 'N/A';
                $data[] = [
                    "transaction_id"   => 'TXN-' . str_pad($row->id, 6, '0', STR_PAD_LEFT),
                    "date"             => $row->updated_at ? $row->updated_at->format('Y-m-d') : 'N/A',
                    "type"             => $row->payment_option ? ucfirst($row->payment_option) : 'N/A',
                    "contract_details" => $contractDetails,
                    "educator"         => $educator,
                    "amount_paid"         => $row->pay_hour ? '$' . number_format($row->total_contract_cost, 2) : 'N/A',
                    "remaining"       => '$' . number_format($row->total_contract_cost, 2),
                ];
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        return view('school-marketplace.reports.transaction-history');
    }

    public function allContracts(Request $request)
    {
        $authId = auth()->user()->id;
        $status = $request->status;
        $searchText = $request->searchText; // Search parameter
        $requirementIds = PlatformSchoolRequirements::where('school_id', $authId)->pluck('id');

        $hires = SchoolInstructorHiring::with([
            'user.step1',
            'user.step2.education',
            'user.step2.teching',
            'user.step3.subjects',
            'user.step5',
            'user.step6',
            'user.shortList',
            'requirements'
        ])
            ->where('school_id', $authId)
            ->whereHas('user');
        // Filter by Status
        if (!empty($status) && $status !== 'all') {
            $hires->where('status', $status);
        }

        // Apply Search Filter
        if (!empty($searchText)) {
            $hires->where(function ($query) use ($searchText, $request) {

                $query->whereHas('user', function ($query) use ($searchText) {
                    $query->where('first_name', 'LIKE', "%$searchText%")
                        ->orWhere('last_name', 'LIKE', "%$searchText%")
                        ->orWhere('email', 'LIKE', "%$searchText%")
                        ->orWhere('state', 'LIKE', "%$searchText%")
                        ->orWhere('city', 'LIKE', "%$searchText%")
                        ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%$searchText%"]);
                });

                // Step 2 Search
                $query->orWhereHas('user.step2', function ($query) use ($searchText) {
                    $query->where('profile_type', 'LIKE', "%$searchText%")
                        ->orWhere('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 2
                $query->orWhereHas('user.step2', function ($query) use ($searchText) {
                    $query->where('profile_type', 'LIKE', "%$searchText%")
                        ->orWhere('highest_level_of_education', 'LIKE', "%$searchText%")
                        ->orWhereHas('education', function ($q) use ($searchText) {
                            $q->where('education', 'LIKE', "%$searchText%")
                                ->orWhere('credentialing_agency', 'LIKE', "%$searchText%")
                                ->orWhereRaw('JSON_CONTAINS(states, ?)', [json_encode($searchText)]);
                        })
                        ->orWhereHas('teching', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        })
                        ->orWhereHas('otherExper', function ($q) use ($searchText) {
                            $q->where('employer_name', 'LIKE', "%$searchText%")
                                ->orWhere('position', 'LIKE', "%$searchText%");
                        });
                });

                // Step 3
                $query->orWhereHas('user.step3', function ($query) use ($searchText) {
                    $query->join('tbl_classes', 'tbl_classes.id', '=', 'onboarding_instructor_teaching_preferences.i_prefer_to_teach')
                        ->where("tbl_classes.class_name", 'LIKE', "%$searchText%")
                        ->orWhere('format', 'LIKE', "%$searchText%")
                        ->orWhere('language_teach_that_i_teach', 'LIKE', "%$searchText%")
                        ->orWhere('program_type', 'LIKE', "%$searchText%")
                        ->orWhere('other_language', 'LIKE', "%$searchText%")
                        ->orWhere('other_program_type', 'LIKE', "%$searchText%")
                        ->orWhereHas('subjects', function ($q) use ($searchText) {
                            $q->join('subject_area_v1', 'subject_area_v1.id', '=', 'onboarding_instructor_subjects.subject')
                                ->join('subjects_v1', 'subjects_v1.id', '=', 'onboarding_instructor_subjects.sub_subject')
                                ->where('subject_area_v1.subject_area', 'LIKE', "%$searchText%")
                                ->orWhere('subjects_v1.title', 'LIKE', "%$searchText%");
                        });
                });

                // Step 5
                $query->orWhereHas('user.step5', function ($query) use ($searchText) {
                    $query->where('profile_title', 'LIKE', "%$searchText%")
                        ->orWhere('description', 'LIKE', "%$searchText%")
                        ->orWhere('profile_tags', 'LIKE', "%$searchText%");
                });
            });
        }

        $hires = $hires->get();

        return view('school-marketplace.contracts', compact('hires', 'authId'));
    }

    public function allClasses(Request $request)
    {
        if ($request->ajax()) {
            $currentDate = Carbon::now()->toDateString();
            if (!empty($request->type)) {
                list($requirements, $programs) = getRequirementsAndPrograms($currentDate);
                switch ($request->type) {
                    case 'today':
                    case 'inprogress':
                        $classes = k12ConnectionClasses::with(['programs.requirements.subject', 'programs.requirements.proctor'])->whereIn('program_id', $programs)->where('class_date', $currentDate);
                        break;

                    case 'upcoming':
                        $classes = k12ConnectionClasses::with(['programs.requirements.subject', 'programs.requirements.proctor'])->whereIn('program_id', $programs)->whereDate('class_date', '>', $currentDate);
                        break;

                    case 'completed':
                        $classes = k12ConnectionClasses::with(['programs.requirements.subject', 'programs.requirements.proctor'])->whereIn('program_id', $programs)->where('status', 'completed');
                        break;

                    default:
                        # code...
                        break;
                }
            }
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'subject') {
                $classes->join('k12_connection_programs', 'k12_connection_programs.id', '=', 'k12_connection_classes.program_id')
                    ->join('platform_school_requirements', 'platform_school_requirements.id', '=', 'k12_connection_programs.requirement_id')
                    ->join('subject_area_v1', 'platform_school_requirements.subject_area_id', '=', 'subject_area_v1.id');
                $classes->addSelect([
                    'subject' => SubjectArea::select('subject_area')
                        ->whereColumn('subject_area_v1.id', 'platform_school_requirements.subject_area_id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'grade_level') {
                $classes->join('k12_connection_programs', 'k12_connection_programs.id', '=', 'k12_connection_classes.program_id')
                    ->join('platform_school_requirements', 'platform_school_requirements.id', '=', 'k12_connection_programs.requirement_id')
                    ->join('tbl_classes', 'platform_school_requirements.grade_levels_id', '=', 'tbl_classes.id');
                $classes->addSelect([
                    'grade_level' => Classes::select('class_name')
                        ->whereColumn('tbl_classes.id', 'platform_school_requirements.grade_levels_id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'delivery_mode') {
                $classes->join('k12_connection_programs', 'k12_connection_programs.id', '=', 'k12_connection_classes.program_id')
                    ->join('platform_school_requirements', 'platform_school_requirements.id', '=', 'k12_connection_programs.requirement_id');
                $classes->addSelect([
                    'delivery_mode' => PlatformSchoolRequirements::select('delivery_mode')
                        ->whereColumn('platform_school_requirements.id', 'k12_connection_programs.requirement_id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'teacher') {
                $classes->addSelect([
                    'teacher' => OnboardingInstructor::selectRaw("CONCAT(new_onboarding_instructor.first_name, ' ', new_onboarding_instructor.last_name)")
                        ->whereColumn('k12_connection_classes.main_instructor_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'proctor') {
                $classes->join('k12_connection_programs', 'k12_connection_programs.id', '=', 'k12_connection_classes.program_id')
                    ->join('platform_school_requirements', 'platform_school_requirements.id', '=', 'k12_connection_programs.requirement_id')
                    ->join('platform_school_proctors', 'platform_school_requirements.proctor_id', '=', 'platform_school_proctors.id');
                $classes->addSelect([
                    'proctor' => PlatformSchoolProctor::select("platform_school_proctors.proctor_name")
                        ->whereColumn('platform_school_proctors.id', 'platform_school_requirements.proctor_id')
                        ->limit(1)
                ]);
            }

            if ($params['searchValue']) {
                $searchValue = strtolower($params['searchValue']);

                $classes->where(function ($qry) use ($searchValue) {
                    $qry->whereHas('programs.requirements.subject', function ($query) use ($searchValue) {
                        $query->whereRaw('LOWER(subject_name) LIKE ?', ["%{$searchValue}%"]);
                    })->orwhereHas('programs.requirements', function ($query) use ($searchValue) {
                        $query->whereRaw('LOWER(delivery_mode) LIKE ?', ["%{$searchValue}%"]);
                    })
                        ->orWhereHas('mainUser', function ($query) use ($searchValue) {
                            $query->whereRaw("LOWER(CONCAT(first_name, ' ', last_name)) LIKE ?", ["%{$searchValue}%"]);
                        })
                        ->orWhereHas('programs.requirements.proctor', function ($query) use ($searchValue) {
                            $query->whereRaw('LOWER(proctor_name) LIKE ?', ["%{$searchValue}%"]);
                        });
                });
            }

            $classes->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
            [$count, $result] = DataTableHelper::applyPagination($classes, $params['row'], $params['rowperpage']);
            $data = [];
            $i = 1;

            foreach ($result as $row) {
                $schedule = json_decode($row->programs->requirements->schedules, true);
                $start_time = isset($schedule[0]['start_time']) ? $schedule[0]['start_time'] : null;
                $end_time = isset($schedule[0]['end_time']) ? $schedule[0]['end_time'] : null;
                $actionRoute = route("new-school.viewClasses", ['classId' => $row->id]);
                $action = '<div class="dropdown">
                            <button class="dropdown_menu_btn" type="button" id="optionsMenu" data-bs-toggle="dropdown" aria-expanded="false" style="border: none;" fdprocessedid="xx7spk">
                                <svg width="10" height="13" viewBox="0 0 3 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1.5 6.5H1.51V6.51H1.5V6.5ZM1.5 2H1.51V2.01H1.5V2ZM1.5 11H1.51V11.01H1.5V11Z" stroke="black" stroke-width="3" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="optionsMenu">
                                <li>
                                    <a class="dropdown-item text-secondary" href="' . $actionRoute . '">
                                        View Class
                                    </a>
                                </li>
                                
                            </ul>
                        </div>';
                $data[] = [
                    'id' => $row->id,
                    'school_name' => auth()->user()->full_name,
                    'subject' => subjectName($row->programs->requirements->subject_area_id),
                    // 'subject' => 'coding',
                    'grade_level' => trim(gradeLevel($row->programs->requirements->grade_levels_id), ','),
                    'delivery_mode' => ucfirst($row->programs->requirements->delivery_mode),
                    'start_time' => date('h:i A', strtotime($row->start_time)),
                    'end_time' => date('h:i A', strtotime($row->end_time)),
                    'start_date' => date('d-F-Y', strtotime($row->programs->start_date)),
                    'end_date' => date('d-F-Y', strtotime($row->programs->end_date)),
                    'teacher' => k12username($row->main_instructor_id),
                    'proctor' => proctorName($row->programs->requirements->proctor_id),
                    'action' => $action,
                    // 'action' => '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    //                 <path d="M8 3.25C8.41421 3.25 8.75 2.91421 8.75 2.5C8.75 2.08579 8.41421 1.75 8 1.75C7.58579 1.75 7.25 2.08579 7.25 2.5C7.25 2.91421 7.58579 3.25 8 3.25Z" stroke="#656565" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    //                 <path d="M8 8.75C8.41421 8.75 8.75 8.41421 8.75 8C8.75 7.58579 8.41421 7.25 8 7.25C7.58579 7.25 7.25 7.58579 7.25 8C7.25 8.41421 7.58579 8.75 8 8.75Z" stroke="#656565" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    //                 <path d="M8 14.25C8.41421 14.25 8.75 13.9142 8.75 13.5C8.75 13.0858 8.41421 12.75 8 12.75C7.58579 12.75 7.25 13.0858 7.25 13.5C7.25 13.9142 7.58579 14.25 8 14.25Z" stroke="#656565" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    //             </svg>',
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        return view('school-marketplace.all-classes');
    }

    public function viewClasses(Request $request)
    {
        $classes = k12ConnectionClasses::with('programs.requirements')->find($request->classId);
        $encryptedId = encrypt_str($classes->programs->requirements->id);
        // dd($classes->programs->requirements->id);
        $userId = encrypt_str($classes->main_instructor_id);
        $id = decrypt_str($encryptedId);
        $user_id = decrypt_str($userId);
        $school = User::find(auth()->user()->id);
        PlatformSchoolProctor::updateOrCreate(
            ['email' => $school->email], // Condition to check existing record
            [
                'school_id' => $school->id,
                'proctor_name' => $school->full_name,
                'phone' => $school->phone_number,
                'updated_at' => now(),
            ]
        );
        $user = OnboardingInstructor::where('id', $user_id)->with('step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3', 'step3.subjects', 'step5', 'step6', 'shortlist')->first();
        $data = PlatformSchoolRequirements::find($id);
        $hire = SchoolInstructorHiring::where('requirment_id', $id)->where('school_id', auth()->user()->id)->where('instructor_id', $user_id);
        if (!empty($classSetup) || !empty($manageClass) || !empty($viewContract)) {
            $hire = $hire->where('status', '!=', 'declined');
        } else {
            $hire = $hire->where('status', '!=', 'withdraw');
        }
        $hire = $hire->first();
        $applicant = SchoolReviewApplicants::where(['school_id' => $hire->school_id, 'requirement_id' => $hire->requirment_id, 'instructor_id' => $hire->instructor_id])->first();
        $zone = k12ConnectionCategorizedData::find($data->time_zone);
        $proctors = PlatformSchoolProctor::where('school_id', $data->school_id)->get();
        $zoom = ZoomModel::select('id', 'account_name')->where("status", 1)->get();
        $programs = k12ConnectionPrograms::where('requirement_id', $data->id)->first();
        $programs_note = !empty($data->id) ? k12ConnectionProgramNotes::where('requirement_id', $data->id)->first() : '';
        $class_link = !empty($data->id) ? k12ConnectionMeetingLinks::where('requirement_id', $data->id)->first() : '';
        $roster = !empty($data->id) ? PlatformSchoolRoster::where('requirement_id', $data->id)->first() : '';


        // **Total Classes & Total Hours Calculation**
        $totalClasses = 0;
        $totalHours = 0;
        if (!empty($programs)) {
            $classes = k12ConnectionClasses::where('program_id', $programs->id)->get();

            // Yahan classes ko fetch karna hoga jo current requirement se related hon

            foreach ($classes as $class) {
                if (in_array($class->status, ['completed', 'under review'])) {
                    $totalClasses++; // Counting only required statuses

                    if ($class->start_time && $class->end_time) {
                        $start = Carbon::parse($class->start_time);
                        $end = Carbon::parse($class->end_time);
                        if ($end < $start) {
                            $end->addDay(); // Next day adjust
                        }

                        $totalHours += $end->diffInMinutes($start) / 60; // Convert minutes to hours
                    }
                }
            }
        }
        return view('school-marketplace.view-classes', compact('user', 'data', 'encryptedId', 'hire', 'zone', 'proctors', 'zoom', 'programs', 'userId', 'programs_note', 'class_link', 'roster', 'school', 'totalClasses', 'totalHours', 'applicant'));
    }

    public function classCalendar(Request $request)
    {
        $currentDate = Carbon::now()->toDateString();
        $requirements = PlatformSchoolRequirements::where('school_id', auth()->user()->id)
            ->where('status', 'filled')
            ->pluck('id');

        $programs = k12ConnectionPrograms::with(['userNotes'])->whereIn('requirement_id', $requirements)->where('school_id', auth()->user()->id)->get();

        // $classes = k12ConnectionClasses::with(['programs.requirements', 'userNotes'])->whereIn('program_id', $programs)->get();

        // $events = $classes->map(function ($class) {
        //     return [
        //         'id'    => $class->id,
        //         'title' => $class->programs->name ?? 'No Title', // Modify as needed
        //         'start' => $class->programs->start_date, // Ensure date format is YYYY-MM-DD
        //         'end'   => $class->programs->end_date,   // Optional
        //     ];
        // });
        $eventData = SchoolCalanderHelper::getClassCalendarEvents($programs);
        // dd($eventData);
        if ($request->ajax()) {
            return response()->json(['eventData' => $eventData]);
        }
        return view('school-marketplace.class-calendar');
    }


    //this is for the export of cv while clickoing on  the download excel file
    public function export_report_by_contract(Request $request)
    {
        $searchValue = $request->search; // The 'search' field name matches the dynamically created hidden input name

        $headings = ['Full contractId', 'contractDetails', 'contractDates', 'educator', 'totalBudget', 'budgetUsed', 'status'];  // Example headings

        // Fetch data from the database

        $data = [];
        if (!empty($searchValue)) {


            SchoolInstructorHiring::with(['user', 'requirements'])
                ->where('school_id', auth()->user()->id)
                ->whereIn('status', ['accepted', 'declined', 'withdraw'])
                ->has('user')
                ->where(function ($query) use ($request) {
                    // Either filter by user's first_name or requirement_name
                    $query->whereHas('user', function ($q) use ($request) {
                        // Filter by first name in user model
                        $q->where('first_name', 'like', "{$request->search_name_contract}%");
                    })
                        ->orWhereHas('requirements', function ($q) use ($request) {
                            // Filter by requirement name in requirements model
                            $q->where('requirement_name', 'like', "{$request->search_name_contract}%");
                        });
                })->chunk(100, function ($chunk) use (&$data) {
                    foreach ($chunk as $row) {
                        $data[] = [
                            "contractId" => $row->id,
                            "contractDetails" => $row->requirements ? $row->requirements->requirement_name : 'N/A',
                            "contractDates" => $row->requirements ?
                                Carbon::parse($row->requirements->start_date)->format('M jS') . ' - ' .
                                Carbon::parse($row->requirements->end_date)->format('M jS, Y') : 'N/A',
                            "educator" => $row->user ? k12username($row->instructor_id) : 'N/A',
                            "totalBudget" => '$' . $row->total_contract_cost,
                            "budgetUsed" => '$' . classCompletedCost($row->instructor_id, $row->requirment_id, $row->pay_hour),
                            "status" => $row->status,
                        ];
                    }
                });
        }



        // SchoolInstructorHiring::with(['user', 'requirements'])
        //     ->where('school_id', auth()->user()->id)
        //     ->whereIn('status', ['accepted', 'declined', 'withdraw'])
        //     ->has('user')
        //     ->chunk(100, function ($chunk) use (&$data) {
        //         foreach ($chunk as $row) {
        //             $data[] = [
        //                 "contractId" => $row->id,
        //                 "contractDetails" => $row->requirements ? $row->requirements->requirement_name : 'N/A',
        //                 "contractDates" => $row->requirements ?
        //                     Carbon::parse($row->requirements->start_date)->format('M jS') . ' - ' .
        //                     Carbon::parse($row->requirements->end_date)->format('M jS, Y') : 'N/A',
        //                 "educator" => $row->user ? k12username($row->instructor_id) : 'N/A',
        //                 "totalBudget" => '$' . $row->total_contract_cost,
        //                 "budgetUsed" => '$' . classCompletedCost($row->instructor_id, $row->requirment_id, $row->pay_hour),
        //                 "status" => $row->status,
        //             ];
        //         }
        //     });

        $dataCollection = collect($data);
        return Excel::download(new DataExport($dataCollection, $headings), 'contracts_report.xlsx');

        // Pass the data and headings to the export class
        return Excel::download(new DataExport($data, $headings), 'users_export.xlsx');
    }
    public function export_spend_by_educator(Request $request)
    {

        $headings = ['srNo', 'educatorName', 'activeContracts', 'totalBudget', 'amountUsed', 'remainingBudget', 'instructor_id'];  // Example headings

        $data = [];
        $index = 0; // Initialize the index for row numbering
        $status_accepted_instructor_ids = SchoolInstructorHiring::where('school_instructor_hiring.school_id', auth()->id())
            ->where('school_instructor_hiring.status', 'accepted')
            ->pluck('instructor_id');

        $query = SchoolInstructorHiring::with(['user', 'requirements'])
            ->where('school_instructor_hiring.school_id', auth()->id())
            ->where('school_instructor_hiring.status', 'accepted')
            ->select([
                'instructor_id',
                DB::raw('CONCAT(first_name, " ", last_name) as educator_name'),
                DB::raw('COUNT(*) as contract_count'),
                DB::raw('SUM(total_contract_cost) as total_budget'),
            ])
            ->join('new_onboarding_instructor', 'instructor_id', '=', 'new_onboarding_instructor.id')
            ->groupBy('instructor_id', 'first_name', 'last_name');

        if (!empty($request->search)) {


            $query->whereHas('user', function ($query) use ($request) {
                $query->where('first_name', 'like', "%{$request->search}%");
            });
        }

        $query->chunk(100, function ($chunk) use (&$data, &$index, $status_accepted_instructor_ids) {

            foreach ($chunk as $row) {

                $index++; // Increment the index for each row

                $data[] = [
                    'srNo' => $index, // Serial number based on the index
                    'educatorName' => $row->educator_name,
                    'activeContracts' => $this->getthecontracts_of_school($row->instructor_id),
                    'totalBudget' => '$' . number_format($row->total_budget, 2), // Format as currency
                    'amountUsed' => '$' . number_format($row->total_budget, 2), // Example where total_budget is fully used
                    'remainingBudget' => '$' . number_format($row->total_budget * 0.3, 2), // Example where 30% is remaining
                    'instructor_id' => $row->instructor_id,
                ];
            }
        });

        // Now $data contains the structured data, you can use it in the response
        $dataCollection = collect($data);


        return Excel::download(new DataExport($dataCollection, $headings), 'instructor_reports.xlsx');
    }

    public function getthecontracts_of_school($requirement_instructor_ids)
    {
        $contracts = ""; // Initialize as an empty string
        $contracts_instructor = SchoolInstructorHiring::with(['requirements'])
            ->where('instructor_id', $requirement_instructor_ids)->where("school_id", auth()->user()->id)
            ->get();

        foreach ($contracts_instructor as $instructor_id) {

            $contracts .= $instructor_id->requirements->requirement_name . ", ";
        }

        // Remove the trailing comma and space at the end
        $contracts = rtrim($contracts, ", "); // Remove the last comma and space
        return $contracts;
    }


    public function profile_details(Request $request)
    {


        $instructor = OnboardingInstructor::with('category')->find(10);

        // dump(count($instructor->category));



        if ($request->search_from_dashobard) {
            session(['search_from_dashboard' => true]);
        }
        $redirectRoute = route('profile_details');


        $search = "";
        $count = "";
        $school_requirement = PlatformSchoolRequirements::where("school_id", auth()->user()->id)
            ->where("status", "open")
            ->get();
        //this is for the outside searcbar
        $existing_input = "";
        if ($request->input("query") || $request->input("query") === "0") {

            if ($request->search_from_dashobard) {

                $existing_input = $request->input("query");
            }

            $search = $request->input('query');

            // Step 1: Get subject IDs matching the search
            $subjectIds = DB::table("subject_area_v1")
                ->where("subject_area", "like", "{$search}%")
                ->pluck('id');

            // Step 2: Get user IDs linked to those subjects
            $userIdsFromSubjects = DB::table("onboarding_instructor_subjects")
                ->whereIn("subject", $subjectIds)
                ->pluck('user_id');

            $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6'])
                ->where('user_status', 'Active')
                ->where(function ($query) use ($userIdsFromSubjects, $search) {
                    $query->whereIn('id', $userIdsFromSubjects)
                        ->orWhere('first_name', 'like', "{$search}%");
                });
        }

        //this is searchtext is there
        else  if ($request->input("searchText")) {


            $search = $request->input('searchText');

            $subjectIds = DB::table("subject_area_v1")
                ->where("subject_area", "like", "{$search}%")
                ->pluck('id');

            // Step 2: Get user IDs linked to those subjects
            $userIdsFromSubjects = DB::table("onboarding_instructor_subjects")
                ->whereIn("subject", $subjectIds)
                ->pluck('user_id');

            $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6'])
                ->where('user_status', 'Active')
                ->where(function ($query) use ($userIdsFromSubjects, $search) {
                    $query->whereIn('id', $userIdsFromSubjects)
                        ->orWhere('first_name', 'like', "{$search}%");
                });
        }
        // means only filter  
        else if ($request->input("query") == "" && $request->input("searchText") == "") {

            $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6'])->where('user_status', 'Active');
        }

        if (!empty($request->subject_area)) {
            $subjects = Subject::whereIn('subject_name', $request->subject_area)->pluck('id')->toArray();
            $users->whereHas('step3.subjects', function ($query) use ($subjects) {
                $query->whereIn('subject', $subjects);
            });
        }

        if (!empty($request->subject)) {
            $subSubjects = SubsubjectModel::whereIn('name', $request->subject)->pluck('id')->toArray();
            $users->whereHas('step3.subjects', function ($query) use ($subSubjects) {
                $query->whereIn('sub_subject', $subSubjects);
            });
        }

        if (!empty($request->grade_levels)) {
            $grades = Classes::whereIn('class_name', $request->grade_levels)->pluck('id')->toArray();
            $users->whereHas('step3', function ($query) use ($grades) {
                $query->where(function ($q) use ($grades) {
                    foreach ($grades as $grade) {
                        $q->orWhereRaw("FIND_IN_SET(?, i_prefer_to_teach)", [$grade]);
                    }
                });
            });
        }

        if (!empty($request->years_of_relevant_experience)) {
            $YearsOfExp = array_map('intval', $request->years_of_relevant_experience);
            $minYears = min($YearsOfExp);
            $maxYears = max($YearsOfExp);
            if ($minYears != 0 || $maxYears != 26) {
                $users->whereHas('step2', function ($query) use ($minYears, $maxYears) {
                    $query->whereRaw('CAST(total_experience AS UNSIGNED) BETWEEN ? AND ?', [$minYears, $maxYears]);
                });
            }
        }
        // dd($applicants->get());
        if (!empty($request->credentialing_agency)) {
            $agency = CredentialingAgencyModel::whereIn('agency', $request->credentialing_agency)->pluck('agency')->toArray();
            $users->whereHas('step2.education', function ($query) use ($agency) {
                $query->whereIn('credentialing_agency', $agency);
            });
        }

        if (!empty($request->certificationlicense)) {
            $certificate = AgencyCertificatesModel::whereIn('certificate', $request->certificationlicense)->pluck('certificate')->toArray();
            $users->whereHas('step2.education', function ($query) use ($certificate) {
                $query->whereIn('education', $certificate);
            });
        }

        if (!empty($request->program_type)) {
            $users->whereHas('step3', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->program_type as $program) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, program_type)", [$program]);
                    }
                });
            });
        }

        if (!empty($request->profile_type)) {
            $profileTypes = $request->profile_type;
            $updatedProfileTypes = array_map(function ($item) {
                return $item === "Licensed/Credentialed Educator" ? "Certified Teacher" : $item;
            }, $profileTypes);

            $users->whereHas('step2', function ($query) use ($updatedProfileTypes) {
                $query->where('profile_type', $updatedProfileTypes);
            });
        }

        if (!empty($request->minimum_education_level)) {
            $users->whereHas('step2', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->minimum_education_level as $education) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, highest_level_of_education)", [$education]);
                    }
                });
            });
        }

        if (!empty($request->language_spoken)) {
            $users->whereHas('step3', function ($query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    foreach ($request->language_spoken as $language) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, language_teach_that_i_teach)", [$language]);
                    }
                });
            });
        }

        if (!empty($request->delivery_mode)) {
            $normalizedModes = array_map(function ($mode) {
                return strtolower(str_replace('_', '-', $mode));
            }, $request->delivery_mode);

            $users->whereHas('step3', function ($query) use ($normalizedModes) {
                $query->where(function ($subQuery) use ($normalizedModes) {
                    foreach ($normalizedModes as $mode) {
                        $subQuery->orWhereRaw("FIND_IN_SET(?, format)", [$mode]);
                    }
                });
            });
        }



        if (!empty($request->educator_online_rate)) {
            $onlineRates = array_map('intval', $request->educator_online_rate);
            $minRate = min($onlineRates);
            $maxRate = max($onlineRates);

            if ($minRate != 0 || $maxRate != 101) {
                $users->whereHas('step3', function ($query) use ($minRate, $maxRate) {
                    $query->where(function ($q1) use ($minRate, $maxRate) {
                        // If format is exactly 'online' or 'in-person', check the compensation directly
                        $q1->where('format', 'online')
                            ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$minRate, $maxRate]);
                    })
                        ->orWhere(function ($q2) use ($minRate, $maxRate) {
                            // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                            $q2->whereRaw("SUBSTRING_INDEX(compensation, ',', 1) BETWEEN ? AND ?", [$minRate, $maxRate]);
                        });
                });
            }
        }

        if (!empty($request->educator_in_person_rate)) {
            $inPersonRates = array_map('intval', $request->educator_in_person_rate);
            $mininPersonRate = min($inPersonRates);
            $maxinPersonRate = max($inPersonRates);

            if ($mininPersonRate != 0 || $maxinPersonRate != 101) {
                $users->whereHas('step3', function ($query) use ($mininPersonRate, $maxinPersonRate) {
                    $query->where(function ($q1) use ($mininPersonRate, $maxinPersonRate) {
                        // If format is exactly 'online' or 'in-person', check the compensation directly
                        $q1->where('format', 'in-person')
                            ->whereRaw("CAST(compensation AS SIGNED) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                    })
                        ->orWhere(function ($q2) use ($mininPersonRate, $maxinPersonRate) {
                            // If format is a combination (e.g., "online,in-person,hybrid"), split the compensation
                            $q2->whereRaw("SUBSTRING_INDEX(SUBSTRING_INDEX(compensation, ',', 2), ',', -1) BETWEEN ? AND ?", [$mininPersonRate, $maxinPersonRate]);
                        });
                });
            }
        }

        if ($request->sort) {

            $instructorId_name = "new_onboarding_instructor.id";
            switch ($request->sort) {
                case 'years':
                    $users->orderByRaw("COALESCE((SELECT total_experience FROM onboarding_instructor_experiences WHERE onboarding_instructor_experiences.user_id = new_onboarding_instructor.id LIMIT 1), 0) ASC")
                        ->with(['step2']);
                    break;

                case 'certified':
                    $users->orderByRaw("COALESCE((SELECT certification FROM onboarding_instructor_experiences WHERE onboarding_instructor_experiences.user_id = new_onboarding_instructor.id LIMIT 1), 'zz') DESC")
                        ->with(['step2']);
                    break;

                case 'atoz':
                    $users->orderBy('first_name', 'ASC')
                        ->with(['step2']);
                    break;

                case 'ztoa':
                    $users->orderBy('first_name', 'DESC')
                        ->with(['step2']);
                    break;


                case 'oldest':
                    $users->orderBy('created_at', 'ASC');
                    break;


                case 'newest':
                    $users->orderBy('created_at', 'DESC');
                    break;

                case 'onlineRate':
                    $users->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $users->where('format', 'like', '%online%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'online' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%online%' THEN CAST(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 1) AS UNSIGNED)
                            END ASC
                        ");
                    break;

                case 'in-personRate':
                    $users->leftJoin('onboarding_instructor_teaching_preferences as teaching_prefs', function ($join) use ($instructorId_name) {
                        $join->on('teaching_prefs.user_id', '=', $instructorId_name);
                    });
                    $users->where('format', 'like', '%in-person%')
                        ->orderByRaw("
                            CASE
                                WHEN teaching_prefs.format = 'in-person' THEN CAST(teaching_prefs.compensation AS UNSIGNED)
                                WHEN teaching_prefs.format LIKE '%in-person%' THEN CAST(SUBSTRING_INDEX(teaching_prefs.compensation, ',', 2) AS UNSIGNED)
                            END ASC
                        ");
                    break;

                case 'proposedRate':
                    $users->addSelect([
                        'requirement_data' => PlatformSchoolRequirements::selectRaw('platform_school_requirements.delivery_mode')
                            ->whereColumn('platform_school_review_applicants.requirement_id', 'platform_school_requirements.id')
                            ->limit(1)
                    ]);

                    $users->addSelect([
                        'format_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.format')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'platform_school_review_applicants.instructor_id')
                            ->limit(1)
                    ]);

                    $users->addSelect([
                        'compensation_data' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.compensation')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'platform_school_review_applicants.instructor_id')
                            ->limit(1)
                    ]);
                    // $applicants->whereHas('requirement', function ($query) {
                    //     // $query->whereNotNull('delivery_mode'); // Ensure delivery_mode exists
                    //     $query->where(function ($subQuery) {
                    //         $subQuery->where('delivery_mode', 'online')
                    //             ->whereIn('user.step3.format', ['online']);
                    //     })->orWhere(function ($subQuery) {
                    //         $subQuery->where('delivery_mode', 'in-person')
                    //             ->whereIn('user.step3.format', ['in-person']);
                    //     });
                    // })
                    // ->whereHas('user.step3');
                    // ->where(function ($query) {
                    //     // Match delivery_mode with format
                    // });
                    break;

                default:
                    break;
            }
        }


        if (empty($request->sort)) {
            $users = $users->orderBy('id', 'DESC');
        }
        $instructors = $users->get();


        //this is for the filters
        $delivery_mode = 'online,in-person,hybrid';
        $filters = $this->filterOptions($delivery_mode);
        //this is for the requirements
        //this is for the requirements as wen are inviting educators right
        $requirements = PlatformSchoolRequirements::where(['status' => 'open'])->orderBy('id', 'DESC')->get();

        $today = Carbon::today()->format('Y-m-d'); // Ensure Y-m-d format

        $fiveDaysAgo = Carbon::today()->subDays(5)->format('Y-m-d');


        //this is basically for the market place
        $marketplaceContracts = OnboardingInstructorMarketplaceContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
            ->get()
            ->map(function ($contract) {
                $contract->is_new = true;
                return $contract;
            });


        //this is for the another except marketplace
        $whizaraContracts = OnboardingInstructorContract::whereBetween('contract_sign', [$fiveDaysAgo, $today])
            ->get()
            ->map(function ($contract) {
                $contract->is_new = true; // 5 din ke andar hai toh "New" tag set karein
                return $contract;
            });

        $marketplaceInstructorIds = OnboardingInstructorMarketplaceContract::pluck('user_id')->toArray();
        $whizaraInstructorIds = OnboardingInstructorContract::pluck('user_id')->toArray();

        // Combine both contract instructor IDs and make them unique
        $validInstructorIds = array_unique(array_merge($marketplaceInstructorIds, $whizaraInstructorIds));

        // You can then use methods like .filter(), .map(), .pluck(), .sortBy(), and more in collect

        // Filter instructors based on valid instructor IDs
        $filteredInstructors = $instructors->filter(function ($instructor) use ($validInstructorIds) {
            return in_array($instructor->id, $validInstructorIds);
        });
        $count = count($filteredInstructors);

        if ($request->ajax()) {
            $view = view('school-marketplace.components.partial-search-results', compact('filteredInstructors', 'requirements', 'marketplaceContracts', 'whizaraContracts', 'school_requirement'))->render();
            return response([
                'success' => true,
                'view' => $view,
                'request' => $request->all(),
                'count' => $count,
                'search' => $search,
                "school_requirement" => $school_requirement
            ]);
        }

        return view("school-marketplace.instructor_profile", compact('filters', 'redirectRoute', 'requirements', 'filteredInstructors', 'marketplaceContracts', 'whizaraContracts', 'search', 'count', "school_requirement", "existing_input"));
    }

    public function messages(Request $request)
    {
        if ($request->ajax()) {
            $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5'])
                ->where('user_status', 'Active');
            if ($request->has('search')) {
                $search = $request->input('search');
                $users->where(function ($query) use ($search) {
                    $query->where('first_name', 'LIKE', "%{$search}%")
                        ->orWhere('last_name', 'LIKE', "%{$search}%");
                });
            }
            $users = $users->get();
            $html = view('school-marketplace.components.message-sidebar-content', compact('users'))->render();
            return response()->json(['success' => true, 'html' => $html]);
        }

        $users = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5'])->where('user_status', 'Active')->get();
        $requirements = PlatformSchoolRequirements::where('school_id', auth()->user()->id)
            ->whereIn('status', ['open', 'filled'])
            ->orderBy('id', 'DESC')
            ->get();

        return view('school-marketplace.messages', compact('users', 'requirements'));
    }

    public function getSchoolTimezone(Request $request)
    {
        $schoolName = $request->input('school');

        if (!$schoolName) {
            return response()->json(['error' => 'School name is required.'], 400);
        }

        // Step 1: Get coordinates from NCES API
        $ncesUrl = 'https://services1.arcgis.com/Ua5sjt3LWTPigjyD/arcgis/rest/services/Postsecondary_School_Locations_Current/FeatureServer/0/query';
        // dd($schoolName);
        // For exact match (without LIKE or wildcards)
        // $searchTerm = strtoupper($schoolName);

        // Escape special characters (e.g., &)
        // $searchTerm = str_replace('&', 'AND', $searchTerm);  // Replace & with AND to avoid issues

        $whereClause = "NAME = '$schoolName'";  // Use exact match

        $ncesResponse = Http::get($ncesUrl, [
            'where' => $whereClause,
            'outFields' => 'NAME,LAT,LON',
            'f' => 'json'
        ]);

        $ncesData = $ncesResponse->json();
        // dd($ncesResponse, $ncesData);
        if (empty($ncesData['features'])) {
            return response()->json(['error' => 'School not found.'], 404);
        }

        // Pick the first matching school
        // $location = $ncesData['features'][0]['attributes'];
        // $lat = $location['LAT'];
        // $lon = $location['LON'];

        // Step 2: Get timezone using Google Time Zone API
        // $googleApiKey = env('GOOGLE_MAPS_API_KEY'); // Store this in your .env file
        // $timestamp = time();
        // $googleUrl = "https://maps.googleapis.com/maps/api/timezone/json";

        // $googleResponse = Http::get($googleUrl, [
        //     'location' => "$lat,$lon",
        //     'timestamp' => $timestamp,
        //     'key' => 'AIzaSyCjbhs5R7IoIq8x7SE5AfQ6bx1gylGrcLI'
        // ]);

        // $googleData = $googleResponse->json();
        // dd($googleData, $googleResponse);
        // if ($googleData['status'] !== 'OK') {
        //     return response()->json(['error' => 'Timezone lookup failed.', 'details' => $googleData], 500);
        // }

        return response()->json([
            // 'school' => $location['NAME'],
            // 'latitude' => $lat,
            // 'longitude' => $lon,
            'data' => $ncesData
            // 'timezone_id' => $googleData['timeZoneId'],
            // 'timezone_name' => $googleData['timeZoneName']
        ]);
    }

    public function create_category(Request $request)
    {
        try {

            $existingCategory = PlatformSchoolCategoryList::where('school_id', auth()->user()->id)
                ->where('name', $request->category)
                ->first();
            if (!$existingCategory) {
                $category_instance = new PlatformSchoolCategoryList();

                $category_instance->school_id = auth()->user()->id;
                $category_instance->name = $request->category;
                $category_instance->save();
                $category_id_new = $category_instance->id; // Get the ID after saving

                $all_categories = PlatformSchoolCategoryList::where("school_id", auth()->user()->id)->get();


                $assigned_to_instructor_categories = PlatformInstructorCategory::where("instructor_id", $request->instructor_id)
                    ->pluck("category_id")
                    ->toArray();

                return response()->json([
                    'status' => "success",
                    "all_categories" => $all_categories,
                    "assigned_to_instructor_category" => $assigned_to_instructor_categories,
                    "category_id_new" => $category_id_new,
                    "instructor_id" => $request->instructor_id,

                ], 201);
            } else {
                return response()->json([
                    'status' => "success",
                    "message" => "already_exist"



                ], 201);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => "error",
                'message' => 'Failed to create category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getallcateogry(Request $request)
    {


        $instructor_id = $request->instructor_id;


        $all_categories = PlatformSchoolCategoryList::where("school_id", auth()->user()->id)->get();


        $assigned_to_instructor_categories = PlatformInstructorCategory::where("instructor_id", $request->instructor_id)
            ->pluck("category_id")
            ->toArray();

        return response()->json([

            "all_categories" => $all_categories,
            "assigned_to_instructor_category" => $assigned_to_instructor_categories,
            "instructor_id" => $instructor_id,



        ]);
    }
    public function instructor_category_save(Request $request)
    {

        $categories = $request->categories;
        if (empty($categories)) {

            PlatformInstructorCategory::where('instructor_id', $request->instructorId)->delete();

            return response()->json([
                "status" => "success",
                "instructor_id" => $request->instructorId,
                "message" => "category_is_empty",
            ]);
        } else {
            PlatformInstructorCategory::where('instructor_id', $request->instructorId)->delete();
            $instructorId = $request->instructorId;
            try {

                foreach ($categories as $category) {
                    $existing = PlatformInstructorCategory::where('school_id', auth()->user()->id)
                        ->where('instructor_id', $instructorId)
                        ->where('category_id', $category)
                        ->first();

                    if ($existing) {
                        // If it already exists, you can update something if needed
                        // Example: $existing->some_field = 'new value';
                        $existing->save(); // or just save without changes if needed
                    } else {
                        // If not exist, then create new
                        $newinstrucator_category = new PlatformInstructorCategory();
                        $newinstrucator_category->school_id = auth()->user()->id;
                        $newinstrucator_category->category_id = $category;
                        $newinstrucator_category->instructor_id = $instructorId;
                        $newinstrucator_category->save();
                    }
                }
                return response()->json([
                    "status" => "success",
                    "instructor_id" => $request->instructorId,
                    "message" => "category_added_successfully",

                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'status' => "error",
                    'message' => 'Failed to create category',
                    'error' => $e->getMessage()
                ], 500);
            }
        }
    }
    public function notfication_read(Request $request)
    {



        try {
            $mark_notification_unread = NotificationRequirement::where('school_id', auth()->user()->id)
                ->update(['is_read' => true]);


            $notifications_data = NotificationRequirement::orderBy('created_at', 'desc')
                ->take(10)
                ->get();

            return response()->json([
                "status" => "success",
                "notifications_data" => $notifications_data,
            ]);
        } catch (\Exception $e) {
            return response()->json([

                "status" => "failure",
                "msg" => $e->getMessage()
            ]);
        }
    }
    public function calculate_budget(){

        
        return view("calculate_budget/calculate_budget");





    
}
    
}
