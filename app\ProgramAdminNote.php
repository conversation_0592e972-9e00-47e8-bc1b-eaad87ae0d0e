<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProgramAdminNote extends Model
{
    protected $table = 'program_admin_notes';

    protected $fillable = [
        'program_id', 'user_id', 'additional_notes', 'instructions', 'internal_notes', 'created_by'
    ];

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

}