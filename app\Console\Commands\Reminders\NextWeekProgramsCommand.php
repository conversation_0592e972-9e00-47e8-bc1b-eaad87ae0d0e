<?php

namespace App\Console\Commands\Reminders;

use App\invite_programs;
use App\notification;
use App\Notification_content;
use App\Programs;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class NextWeekProgramsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:next-week-programs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to All instructors  having at least one program with a class next week";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = $this->signature;
        $users = User::active()
            ->where("type", "=", "5")
            ->where("app_notification", "=", "1")
            ->where("profile_status", "=", "12")
            ->withCount(['nextWeekClasses', 'nextWeekSubClasses'])
            ->where(function ($query) {
                $query->whereHas('nextWeekClasses')
                    ->orWhereHas('nextWeekSubClasses');
            })
            ->get();



        // Merge counts into a single array
        $result = $users->mapWithKeys(function ($user) {
            return [
                $user->id => ($user->next_week_classes_count + $user->next_week_sub_classes_count),
            ];
        })->toArray();

        $link = url("/my-program/");
        if (!empty($result)) {
            foreach ($result as $id => $class_count) {
                $template = Notification_content::where("signature", $signature)->first();
                $body =  @$template->content;

                $body = str_replace('{{link}}', $link, $body);
                $body = str_replace('{{class_count}}', $class_count, $body);

                notification::insert([
                    'title' => 'notification',
                    'user_id' => $id,
                    'program_id' => null,
                    'notification' => $body,
                    'type' => "user",
                    'user_type' =>  "user",
                ]);
            }
        }
    }
}
