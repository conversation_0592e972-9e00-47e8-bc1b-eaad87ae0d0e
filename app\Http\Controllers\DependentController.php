<?php

namespace App\Http\Controllers;

use App\Subject;
use Illuminate\Http\Request;

class DependentController extends Controller
{
    public function getSubSubjects($id)
    {
        $subject = Subject::with('subSubjects')->findOrFail($id);
        $subjects = $subject->subSubjects;

        $view = view("components.admin.sub-subjects", compact('subjects'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
}
