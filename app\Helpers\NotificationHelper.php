<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Mail;

class NotificationHelper
{
    /**
     * Sends a program notification via email and app notification.
     *
     * @param \App\Models\User $recipient The recipient of the notification.
     * @param \App\Models\EmailTemplate $template The email template to use.
     * @param int $programId The ID of the program.
     * @param string $schoolName The name of the school.
     * @param string $notificationTemplate The notification message template.
     * @param string $title The title of the notification.
     * @param string $type The type of the notification.
     * @return void
     */
    public static function sendProgramNotification($recipient, $template, $programId, $schoolName, $notificationTemplate, $title, $type)
    {
        // logger()->info($recipient->toArray());

        $fullName = $recipient->first_name . ' ' . $recipient->last_name;
        $email = $recipient->email;
        $url = static::generateUrl($recipient, $programId);
        $link = "<a target='_blank' href='{$url}'>{$schoolName}</a>";
        $notification = str_replace(['{{link}}', '{{link_url}}'], [$link, $url], $notificationTemplate);
        $notification = str_replace(['{link}', '{link_url}'], [$link, $url], $notification);

        if ($recipient->app_notification || $recipient->type != 5) {
            CustomHelper::sendNotification($recipient->id, $title, $notification, $type, $programId);
        }

        $body = str_replace(['{{ NAME }}', '{{ notification }}'], [$fullName, $notification], $template->description);
        $subject = $title;

        if ($recipient->email_notification|| $recipient->type != 5) {
            // logger()->info($recipient->email);
            static::sendEmail($email, $subject, $body);
        }
    }

    /**
     * Generates the URL for the recipient based on their type.
     *
     * @param \App\Models\User $recipient The recipient of the notification.
     * @param int $programId The ID of the program.
     * @return string The generated URL.
     */
    public static function generateUrl($recipient, $programId)
    {
        switch ($recipient->type) {
            case 4:
                // Recruiter
                return url('view-program/step1/' . encrypt_str($programId));
            case 6:
                // School
                return url('school-program-detail/' . encrypt($programId));
            default:
                return route('user.program-detail', ['encryptedId' => encrypt($programId)]);
        }
    }

    /**
     * Sends an email with the specified subject and body to the given email address.
     *
     * @param string $email The recipient's email address.
     * @param string $subject The subject of the email.
     * @param string $body The body of the email.
     * @return void
     */
    public static function sendEmail($email, $subject, $body)
    {
        try {
            if(is_array($body)){
                $data = ['template' => $body['template'], 'sendFooter' => $body['sendFooter']];
            }
            else{
                $data = ['template' => $body, 'sendFooter' => false];
            }

            Mail::send('template', $data, function ($message) use ($email, $subject) {
                $message->to($email)->subject($subject);
            });
        } catch (\Throwable $th) {
            logger()->info([
                '$email'=>$email,
                '$th'=>$th->getMessage(),
            ]);
            //throw $th;
        }
    }
}
