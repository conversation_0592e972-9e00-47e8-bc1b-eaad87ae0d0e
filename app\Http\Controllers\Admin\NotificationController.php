<?php
namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\notification;
use Hash;
use Mail;
use App\Helpers\DataTableHelper;
DB::enableQueryLog();

// addactivitycategory.blade.php
class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

        public function index(Request $request)
    {

        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managenotification','view')!=true){
            return redirect("/no-permission");
        }
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'id';
            }

            $adminSession = session()->get('Adminnewlogin');
            $adminType = $adminSession['type'];

            if($adminType==1){
                $qry = notification::where('user_type','Admin')
                    ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

             notification::where('is_read',0)->where('user_type','Admin')->update(['is_read'=>1]);

            }elseif($adminType==3){
                $qry = notification::where('type','Reviewer')
                    ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
                notification::where('is_read',0)->where('user_type','Reviewer')->update(['is_read'=>1]);
            }else{
                $qry = notification::where('user_id',$adminSession['id'])
                    ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
                notification::where('is_read',0)->where('user_id',$adminSession['id'])->update(['is_read'=>1]);
            }
            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });



            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);
                $action = $this->generateActionButtons($encryptedStrId,$res);

                $data[] = [
                    "id" =>  $i,
                    "notification" => $row->notification,
                    "program_id" => $row->program_id,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.notification.index");
    }

    private function generateActionButtons($encryptedStrId,$res)
    {

        $actionUrl = "javascript:void(0);";
         $deleteButton ='';

        if (isset($res['managenotification'])) :
            if (array_key_exists('managenotification', $res)) :


                if (in_array('delete', json_decode($res['managenotification'], true))) :
                    $deleteButton = "<a class='notification_delete' href='{$actionUrl}' data-id='{$encryptedStrId}'><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;


            endif;
        endif;

        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$deleteButton}</div>";
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */

    public function destroy(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = notification::where("id", $id)->first();
            if ($record) {
                $res = notification::where("id", "=", $id)->delete();

                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);

        }
    }


}
