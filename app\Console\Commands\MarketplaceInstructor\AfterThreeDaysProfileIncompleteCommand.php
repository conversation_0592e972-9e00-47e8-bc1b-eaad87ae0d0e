<?php

namespace App\Console\Commands\MarketplaceInstructor;

use App\EmailTemplate;
use App\notification;
use App\Notification_content;
use App\OnboardingInstructor;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class AfterThreeDaysProfileIncompleteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'MarketplaceInstructor:after-three-days';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mail sent for profile complete after 3 days';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $template = EmailTemplate::find(38);
        $body =  $template->description;

        $threeDaysAgo = Carbon::now()->subDays(3)->startOfDay();
        
        $instructors = OnboardingInstructor::whereDate('created_at', '=', $threeDaysAgo)->whereIn('user_status', ['InProgress', 'ChangeRequested'])->get();

        foreach ($instructors as $instructor) {
            if($instructor->email_notification==1){
                $full_name = $instructor->first_name . ' ' . $instructor->last_name;
                $email = $instructor->email;
                $url = url('k12connections/application');
                $body = str_replace('{{NAME}}', $full_name, $body);
                $body = str_replace('{{redirect}}', $url, $body);

                $subject = $template->subject;
                $data = array('template' => $body);
                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
            }

            $template = Notification_content::where("signature", 'marketplace-instructor-profile-after-3days')->first();
            $body =  @$template->content;

            notification::insert([
                'title' => 'notification',
                'user_id' => $instructor->id,
                'notification' => $body,
                'type' => "Marketplace Instructor Profile after 3 days",
                'user_type' =>  "marketplace user",
            ]);
        }
    }
}
