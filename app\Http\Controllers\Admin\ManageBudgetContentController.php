<?php

namespace App\Http\Controllers\Admin;

// use App\Budget_States;
use App\BudgetCaseManagementModel;
use Illuminate\Http\Request;
use App\Exports\Admin\ExportSubjectBudget;
use App\Http\Controllers\Controller;
use App\Models\v1\Subject;
use App\Models\v1\BudgetState;
use App\Models\v1\SubjectArea;
use App\Models\v1\SubjectBudget;
use Excel;
use DB;

class ManageBudgetContentController extends Controller
{
    public function BudgetContentManagement()
    {
        return redirect()->route('admin.BudgetContentSubject');;
    }

    public function BudgetContentSubject(Request $request)
    {
        $caseManagement = BudgetCaseManagementModel::firstOrCreate([]);

        $budget_states = BudgetState::all();

        // Check if the fo rm is submitted (POST request)
        if ($request->isMethod('post')) {
            $validatedData = $request->validate([
                'case_management' => 'integer|min:0',
            ]);

            // Update the value in the database
            $caseManagement->update(['case_management' => $validatedData['case_management']]);

            return redirect()->back()->with('success', 'Case Management updated successfully!');
        }

        // Fetch all subject areas for display
        $subjectAreas = SubjectArea::all();

        return view("admin.budget-content-management.subject.index", compact('caseManagement', 'subjectAreas', 'budget_states'));
    }

    public function budget_states_update(Request $request)
    {
        $budget_state_id = $request->budget_state_id;
        $budget_state = BudgetState::find($budget_state_id);
        if ($budget_state) {
            $budget_state->case_management = $request->case_management;
            $budget_state->in_person = $request->in_person;
            $budget_state->bilingual_inc = $request->bilingual_inc;
            $budget_state->sped_rec_comp = $request->sped_rec_comp;

            $budget_state->save();

            return response()->json([
                "status" => "success",
            ]);
        }
    }


    public function getSubjectsBySubjectAreaId(Request $request)
    {
        $subjectAreaId = $request->subject_area_id;
        $stateId = $request->state_id;
        $subjects = Subject::with('subjectBudget')
        ->where('subject_area_id', $subjectAreaId)
        ->whereHas('subjectBudget', function ($query) use ($stateId) {
            $query->where('state_id', $stateId);
        })
        ->get();
        return response()->json($subjects);
    }

    public function importBudgetContent(Request $request)
    {
        $request->validate([
            'csv'   => 'required',
            'state' => 'required',
        ]);

        $filePath = $request->file('csv')->getRealPath();
        $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        if (!$lines || count($lines) < 2) {
            return response()->json(['error' => 'CSV file must have a header and at least one data row.'], 422);
        }

        $rows = array_map(fn($line) => str_getcsv(mb_convert_encoding($line, 'UTF-8', 'auto')), $lines);

        $headers = array_map('trim', array_shift($rows));
        $requiredHeaders = ['Code', 'Subject Area', 'SCED Course Code', 'Course Title', 'Course Description'];

        foreach ($requiredHeaders as $required) {
            if (!in_array($required, $headers)) {
                return response()->json(['error' => "Missing required header: $required"], 422);
            }
        }

        DB::beginTransaction();

        try {
            foreach ($rows as $lineNumber => $row) {
                $row = array_pad($row, count($headers), '');
                $row = array_combine($headers, $row);

                $normalized = self::normalizeRow($row);

                if (empty($normalized['Code']) || strtolower($normalized['Code']) === 'no change') {
                    continue;
                }

                $subjectArea = SubjectArea::updateOrCreate(
                    ['code' => $normalized['Code']],
                    ['subject_area' => $normalized['Subject Area']]
                );

                $subject = Subject::updateOrCreate(
                    [
                        'subject_area_id' => $subjectArea->id,
                        'subject_code'    => $normalized['SCED Course Code']
                    ],
                    [
                        'title'       => $normalized['Course Title'],
                        'description' => $normalized['Course Description']
                    ]
                );

                SubjectBudget::updateOrCreate(
                    [
                        'subject_id' => $subject->id,
                        'state_id'   => $request->state,
                    ],
                    [
                        'base_pay_0_3'    => self::floatOrZero($normalized['Base Pay (0-3 yrs)'] ?? null),
                        'pay_3_6'         => self::floatOrZero($normalized['Incremental Pay (3-6 yrs)'] ?? null),
                        'pay_6_10'        => self::floatOrZero($normalized['Incremental Pay (6-10 yrs)'] ?? null),
                        'pay_10_plus'     => self::floatOrZero($normalized['Incremental Pay (10+ yrs)'] ?? null),
                        'masters_inc'     => self::floatOrZero($normalized["Master's Degree Increment"] ?? null),
                        'doctorate_inc'   => self::floatOrZero($normalized['Doctorate Increment'] ?? null),
                        'non_tech_time'   => self::floatOrZero($normalized['Non-Teaching Time Compensation'] ?? null),
                        'curriculum_inc'  => self::floatOrZero($normalized['Curriculum increment'] ?? null),
                    ]
                );
            }

            DB::commit();
            return response()->json(['message' => 'Import successful']);
        } catch (\Throwable $e) {
            DB::rollBack();
            \Log::error('Budget CSV import failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json(['error' => 'Failed to import data. Check logs for details.'], 500);
        }
    }

    /**
     * Normalize and sanitize a row: fix hyphens, remove commas, trim whitespace.
     */
    protected static function normalizeRow(array $row): array
    {
        return array_map(function ($value) {
            $value = str_replace(['–', '—'], '-', $value); // EN/EM dash to hyphen
            $value = str_replace(',', '', $value);         // Remove commas in numbers
            return trim($value);
        }, $row);
    }

    /**
     * Convert to float safely or return 0.
     */
    protected static function floatOrZero(?string $value): float
    {
        return is_numeric($value) ? (float) $value : 0.0;
    }

    public function exportBudgetContent(Request $request)
    {
        $filename = "subject_export.csv";

        return Excel::download(new ExportSubjectBudget(), $filename, \Maatwebsite\Excel\Excel::CSV, [
            'Content-Type' => 'text/csv',
        ]);
    }

    public function budget_single_state_data(Request $request)
    {
        $state_id = $request->id;

        $budget_state_data = BudgetState::where("id", $state_id)->first();

        if ($budget_state_data) {
            
            return response()->json([
                "status" => "success",

                "budget_state_data" => $budget_state_data
            ]);

        } else {
            
                return response()->json([
                "status" => "failure",
               ]);
        }
    }

    public function syncToAllStates(Request $request)
    {
        $stateId = $request->id;

        $sourceState = BudgetState::find($stateId);
        if (!$sourceState) {
            return response()->json(['error' => 'State not found'], 404);
        }

        $subjectBudgets = SubjectBudget::where('state_id', $stateId)->get();

        if ($subjectBudgets->isEmpty()) {
            return response()->json(['error' => 'No subject budgets found for this state'], 404);
        }

        try {


            DB::beginTransaction();

            // Fetch all states excluding the source
            $targetStates = BudgetState::where('id', '!=', $stateId)->get();
            $now = now()->toDateTimeString();
            $columns = [
                'state_id',
                'subject_id',
                'base_pay_0_3',
                'pay_3_6',
                'pay_6_10',
                'pay_10_plus',
                'masters_inc',
                'doctorate_inc',
                'non_tech_time',
                'curriculum_inc',
                'created_at',
                'updated_at'
            ];

            $rows = [];

            foreach ($targetStates as $state) {

                foreach ($subjectBudgets as $budget) {
                    $rows[] = sprintf(
                        '(%d, %d, %f, %f, %f, %f, %f, %f, %f, %f, "%s", "%s")',
                        $state->id,
                        $budget->subject_id,
                        $budget->base_pay_0_3,
                        $budget->pay_3_6,
                        $budget->pay_6_10,
                        $budget->pay_10_plus,
                        $budget->masters_inc,
                        $budget->doctorate_inc,
                        $budget->non_tech_time,
                        $budget->curriculum_inc,
                        $now,
                        $now
                    );
                }
            }

            // Chunk and insert in batches
            $chunkSize = 500;
            foreach (array_chunk($rows, $chunkSize) as $chunk) {
                $sql = sprintf(
                    'INSERT INTO subject_budget_v1 (%s) VALUES %s 
                    ON DUPLICATE KEY UPDATE 
                        base_pay_0_3 = VALUES(base_pay_0_3),
                        pay_3_6 = VALUES(pay_3_6),
                        pay_6_10 = VALUES(pay_6_10),
                        pay_10_plus = VALUES(pay_10_plus),
                        masters_inc = VALUES(masters_inc),
                        doctorate_inc = VALUES(doctorate_inc),
                        non_tech_time = VALUES(non_tech_time),
                        curriculum_inc = VALUES(curriculum_inc),
                        updated_at = VALUES(updated_at)',
                    implode(',', $columns),
                    implode(',', $chunk)
                );

                DB::statement($sql);
            }


            DB::commit();
            return response()->json(['message' => 'Subject budgets synced to all states successfully.'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to sync subject budgets',
                'details' => $e->getMessage()
            ], 500);
        }
    }
}
