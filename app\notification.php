<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class notification extends Model
{
    protected $table = 'tbl_notifications';
    const UPDATED_AT = null;

    public function scopeUnread($query): void
    {
        $query->where('is_read',0);
    }

    public function scopeRead($query): void
    {
        $query->where('is_read',1);
    }

    public function getDateFormat()
    {
        return 'Y-m-d H:i:s';  // The format used by your database
    }
}
