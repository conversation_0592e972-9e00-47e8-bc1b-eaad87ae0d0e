<?php

namespace App\Exports\Admin;

use App\{Programs,User};
use App\Reimbursement;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportReimbursements implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }

    public function collection()
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];
        $programsQry = Programs::query();
        $programsQry->whereHas('reimbursements');
        if ($adminType != '1') {
            $whereInIds = getAdminUserProgramIds();
            $programsQry->whereIn('id', $whereInIds);
        }
        $programs = $programsQry->pluck('name', 'id');
        $programIds = array_keys($programs->toArray());
        $query = Reimbursement::with('user', 'program');
        if ($adminType != '1') {
            $query->whereHas('program', function ($qry) use ($programIds) {
                $qry->whereIn('program_id', $programIds);
            });
        }

        $this->applyFilters($query);

        return $query->orderBy('id','DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        switch($filters['status']) {
            case 'paid':
                $query->where('status', 3);
                break;
            case 'unpaid':
                $query->where(function ($qry) {
                    $qry->where('status', '!=', 3);
                    $qry->orWhereNull('status');
                });
                break;
            case 'accepted':
                $query->where('status', 1);
                break;
            case 'declined':
                $query->where('status', 0);
                break;
            case 'pending':
                $query->whereNull('status');
                break;
            default: break;
        }
        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }
        $separator = ' TO ';

        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }

    protected function applyDateRangeFilter($query, $daterange, $separator)
    {

        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();
        $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings =  [
            'Program ID',
            'Requested By',
            'Email',
            'Requested Date',
            'Amount',
            'Type',
            'Reimbursement Status',
        ];
        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;
        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        $filtersInfo = '';

        if (!empty($filters['program_id'])) {
            $filtersInfo .= 'Program: ' . Programs::find($filters['program_id'])->name . PHP_EOL;
        }

        if (!empty($filters['user_id'])) {
            $user = User::find($filters['user_id']);
            if ($user) {
                $filtersInfo .= 'Instructor: ' . $user->first_name . ' ' . $user->last_name . PHP_EOL;
            }
        }

        if (!empty($filters['daterange'])) {
            $filtersInfo .= 'Date Range: ' . $filters['daterange'] . PHP_EOL;
        }

        if (!empty($filters['status'])) {
            $filtersInfo .= 'Payment Status: ' . $filters['status']  . PHP_EOL;
        }

        return $filtersInfo;
    }

    public function map($row): array
    {
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');

        return [
            $row->program ? $row->program->id : 'NIL',
            $row->user ? ($row->user->first_name . ' ' . $row->user->last_name) : '',
            $row->user ? ($row->user->email) : '',
            $row->created_at ? getAdminTimestamp($row->created_at) : null,
            $formattedAmount,
            $row->type,
            getReimbursementStatus($row->status),
        ];
    }
}
