<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{Programs, User, SubsubjectModel};
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportPayments implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }

    public function collection()
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];
        $programsQry = Programs::query();
        $programsQry->whereHas('programNoteAmounts');
        if ($adminType != '1') {
            $whereInIds = getAdminUserProgramIds();
            $programsQry->whereIn('id', $whereInIds);
        }
        $programs = $programsQry->pluck('name', 'id');
        $programIds = array_keys($programs->toArray());
        $query = ProgramNoteAmount::with('note', 'user', 'program');
        if ($adminType != '1') {
            $query->whereHas('program', function ($qry) use ($programIds) {
                $qry->whereIn('program_id', $programIds);
            });
        }

        $this->applyFilters($query);

        return $query->orderBy('id','DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        switch($filters['status']) {
            case 'paid': $query->whereNotNull('payment_date_updated'); break;
            case 'unpaid': $query->whereNull('payment_date_updated'); break;
        }
        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }
        $separator = ' TO ';

        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }

    protected function applyDateRangeFilter($query, $daterange, $separator)
    {

        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

        // $query->whereBetween('created_at', [$startDate, $endDate]);
        $query->whereHas('note', function ($q) use ($startDate, $endDate) {
            $q->whereBetween('class_date', [$startDate, $endDate]);
        });
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Instructor Name',
            'Instructor Email',
            'Instructor Type',
            'Class Date',
            'School',
            'Sub Subject',
            'Hours',
            'Minutes',
            'Hourly Pay Rate',
            'Amount',
            'Created Date',
            'Payment Status',
            'Paid At',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        $filtersInfo = '';

        if (!empty($filters['program_id'])) {
            $filtersInfo .= 'Program: ' . Programs::find($filters['program_id'])->name . PHP_EOL;
        }

        if (!empty($filters['user_id'])) {
            $user = User::find($filters['user_id']);
            if ($user) {
                $filtersInfo .= 'Instructor: ' . $user->first_name . ' ' . $user->last_name . PHP_EOL;
            }
        }

        if (!empty($filters['daterange'])) {
            $filtersInfo .= 'Date Range: ' . $filters['daterange'] . PHP_EOL;
        }
        
        if (!empty($filters['status'])) {
            $filtersInfo .= 'Payment Status: ' . $filters['status']  . PHP_EOL;
        }

        return $filtersInfo;
    }

    public function map($row): array
    {
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
        $formattedRate = '$' . number_format($row->rate, 2, '.', ',');
        return [
            $row->program ? $row->program->id : 'NIL',
            $row->user ? ($row->user->first_name . ' ' . $row->user->last_name) : '',
            $row->user ? ($row->user->email) : '',
            getInstructorType($row->type),
            optional($row->note)->class_date ? optional($row->note->class_date)->format('m-d-Y') : '',
            $row->program->school ? $row->program->school->full_name: '',
            $row->program->subSubject ?$row->program->subSubject->name : '',
            $row->hours,
            $row->minutes,
            $formattedRate,
            $formattedAmount,
            $row->created_at ? getAdminTimestamp($row->created_at) : null,
            empty($row->payment_date_updated) ? 'Pending': "Paid",
            $row->payment_date_updated? getAdminTimestamp($row->payment_date_updated): ''
        ];
    }
}
