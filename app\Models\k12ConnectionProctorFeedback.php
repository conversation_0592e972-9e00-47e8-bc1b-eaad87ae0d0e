<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class k12ConnectionProctorFeedback extends Model
{
    use SoftDeletes;

    protected $table = 'k12_connection_proctor_feedback'; // Table name
    protected $fillable = [
        'program_id', 'class_id', 'entered_by', 'note', 'proctor_rating'
    ];

    public function proctor()
    {
        return $this->belongsTo(PlatformSchoolProctor::class, 'entered_by'); // Relation added
    }
}