<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Users;
use Validator;
use Socialite;
use Exception;
use Auth;
class FacebookController extends Controller
{
    public function facebookRedirect()
    {
        return Socialite::driver('facebook')->redirect();
    }
    public function loginWithFacebook()
    {
        try {
    
            $user = Socialite::driver('facebook')->user();
            $isUser = Users::where('social_id', $user->id)->first();
     
            if($isUser){
                Auth::login($isUser);
                return redirect("/onboarding-step/" . encrypt($isUser["id"]));
            }else{
                $createUser = Users::create([
                    'name' => $user->name,
                    'email' => $user->email,
                    'social_id' => $user->id,
                    'email_verify_status'=>'1',
                    'type'=>'5',
                    'email_verify_time'=>date("Y-m-d H:i:s"),
                    'social_type' => 'Facebook'
                ]);
    
                Auth::login($createUser);
                return redirect("/onboarding-step/" . encrypt($createUser["id"]));
            }
    
        } catch (Exception $exception) {
            dd($exception->getMessage());
        }
    }
}