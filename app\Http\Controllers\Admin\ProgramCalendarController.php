<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\CustomHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\{Programs,User,sub_subjects};


class ProgramCalendarController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    // public function index(Request $request)
    // {
    //     $programFilter = $request->input('programFilter');
    //     $programDeliveryType = $request->input('deliveryType');

    //     $events = CustomHelper::getAllEventsData();

    //     if (is_object($events) && method_exists($events, 'toArray')) {
    //         $eventsArray = $events->toArray();
    //     } else {
    //         $eventsArray = $events;
    //     }

    //     if ($programFilter) {
    //         $eventsArray = array_filter($eventsArray, function ($event) use ($programFilter) {
    //             dd($event);
    //             foreach ($event as $e) {
    //                 if (isset($e['program']) && str_contains(strtolower($e['program']), strtolower($programFilter))) {
    //                     return true;
    //                 }
    //             }
    //             return false;
    //         });
    //     }

    //     $flattenedEvents = array_merge(...$eventsArray);

    //     if ($request->ajax()) {
    //         return response()->json(['flattenedEvents' => $flattenedEvents]);
    //     }

    //     return view("admin.program.program-calendar", compact('flattenedEvents', 'programFilter'));
    // }

    public function index(Request $request)
    {
        $programFilter = $request->input('programFilter'); // Filter by program name
        $programDeliveryType = $request->input('programDeliveryType') ?? $request->input('deliveryType'); // Filter by delivery type ('Online', 'In-Person')
        $schoolFilter = $request->input('schoolFilter'); // Filter by multiple school type
        $instructorFilter = $request->input('instructorFilter'); // Filter by multiple instructors
        $subsubjectFilter = $request->input('subsubjectFilter'); // Filter by sub-subject
        $addressFilter = $request->input('addressFilter'); // Filter by sub-subject

        $sd = $request->input('start'); // Filter by sub-subject
        $ed = $request->input('end'); // Filter by sub-subject

        if (!$request->ajax()){
            $schools = User::where("type", 6)->select('full_name')->orderBy('full_name', 'ASC')->get();
            $user = User::where("type", 5)->select('first_name', 'last_name')->orderBy('first_name', 'ASC')->get();
            $subsubjects = sub_subjects::orderBy('name', 'ASC')->select('name')->get();
            return view("admin.program.program-calendar", compact('programFilter', 'programDeliveryType', 'schools', 'user', 'subsubjects'));
        }
        // Fetch all events
        $events = CustomHelper::getAllEventsData($sd, $ed);
        // Convert the events to an array
        if (is_object($events) && method_exists($events, 'toArray')) {
            $eventsArray = $events->toArray();
        } else {
            $eventsArray = $events;
        }

        // Apply program filter
        if ($programFilter) {
            $eventsArray = array_filter($eventsArray, function ($event) use ($programFilter) {
                foreach ($event as $e) {
                    if (isset($e['program']) && str_contains(strtolower($e['program']), strtolower($programFilter))) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Apply delivery type filter
        if ($programDeliveryType) {
            $eventsArray = array_filter($eventsArray, function ($event) use ($programDeliveryType) {
                foreach ($event as $e) {
                    if (isset($e['delivery_type']) && strtolower($e['delivery_type']) === strtolower($programDeliveryType)) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Apply school filter (for multiple schools)
        if ($schoolFilter && is_array($schoolFilter)) {
            $eventsArray = array_filter($eventsArray, function ($event) use ($schoolFilter) {
                foreach ($event as $e) {
                    if (isset($e['schoolname']) && in_array(strtolower($e['schoolname']), array_map('strtolower', $schoolFilter))) {
                        return true;
                    }
                }
                return false;
            });
        }

        // Apply instructor filter (for multiple instructors)
        if ($instructorFilter && is_array($instructorFilter)) {
            $eventsArray = array_filter($eventsArray, function ($event) use ($instructorFilter) {
                foreach ($event as $e) {
                    if (isset($e['instructor']) && in_array(strtolower($e['instructor']), array_map('strtolower', $instructorFilter))) {
                        return true;
                    }
                }
                return false;
            });
        }
        // dd($schoolFilter,$subsubjectFilter);
        // Apply subsubject filter if both schoolFilter and subsubjectFilter are not null
        if ($schoolFilter && is_array($schoolFilter) && $subsubjectFilter) {

            $eventsArray = array_filter($eventsArray, function ($event) use ($schoolFilter, $subsubjectFilter) {
                foreach ($event as $e) {
                    $schoolMatch = isset($e['schoolname']) && in_array(strtolower($e['schoolname']), array_map('strtolower', $schoolFilter));
                    $subsubjectMatch = isset($e['subsubject']) && strtolower($e['subsubject']) === strtolower($subsubjectFilter);
                    if ($schoolMatch && $subsubjectMatch) {
                        return true;
                    }
                }
                return false;
            });
        }

         // Apply address filter
            if ($addressFilter) {
                $eventsArray = array_filter($eventsArray, function ($event) use ($addressFilter) {
                    foreach ($event as $e) {
                        if (isset($e['address']) && str_contains(strtolower($e['address']), strtolower($addressFilter))) {
                            return true;
                        }
                    }
                    return false;
                });
            }

        // Flatten the array
        $flattenedEvents = array_merge(...$eventsArray);

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            return response()->json(['flattenedEvents' => $flattenedEvents]);
        }

        // Render the view with filtered events
        return view("admin.program.program-calendar", compact('flattenedEvents', 'programFilter', 'programDeliveryType', 'schools', 'user', 'subsubjects'));
    }


}
