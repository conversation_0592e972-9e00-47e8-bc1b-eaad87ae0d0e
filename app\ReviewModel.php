<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ReviewModel extends Model
{
    protected $table = 'tbl_user_reviews';
    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'to_id');
    }
    public function fromuser()
    {
        return $this->belongsTo(User::class, 'from_id');
    }
    
}
