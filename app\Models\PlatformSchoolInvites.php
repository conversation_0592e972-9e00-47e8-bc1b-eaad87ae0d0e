<?php

namespace App\Models;

use App\OnboardingInstructor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlatformSchoolInvites extends Model
{
    use SoftDeletes;
    protected $table = 'platform_school_invites';
    protected $fillable = ['school_id', 'requirement_id', 'user_id', 'status'];

    public function user()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
}
