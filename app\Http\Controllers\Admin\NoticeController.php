<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\User;

use App\Programs;
use App\ProgramNote;

use App\MailModel;
use App\Helpers\DataTableHelper;
use Mail;
class NoticeController extends Controller
{

    public function index(Request $request)
    {

        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managenotice','view')!=true){
            return redirect("/no-permission");
        }
        $currentDate = now()->toDateString();
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }


                $qry = MailModel::select("users.*","tbl_followup.created_at as applieddate","tbl_followup.program_id","tbl_followup.user_id")->where("tbl_followup.f_type",  'Notice')
                ->join("users", "users.id", "=", "tbl_followup.user_id")
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilterMail($que, $params['searchValue'], $params['columns']);
            });


            $this->applyFilters($qry, $request);


            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);

                ///
                $chat = "";
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $chat = $this->generateChatLink($encryptedId);
                        endif;
                    endif;
                endif;
                ///

                $action = $this->generateActionButtons($encryptedStrId,$res);

                if($row->type==5){
                    $type='Instructor';
                   $name=$row->first_name.' '.$row->last_name;
                }else{
                    $name=$row->full_name;
                    $type='School';

                }
                $programsButton='';

                    $pids = ProgramNote::where(["user_id" =>$row->id])->orWhere('sub_user_id',$row->id)->where('class_date', '>=', $currentDate)->groupBy('program_id')->pluck('program_id');

                    if(!empty($pids)){
                    $programs =  Programs::whereIn('id', $pids)->pluck('name', 'id');
                    foreach ($programs as $key => $value) {
                        $viewProgramRoute =  url('view-program/step1/' . encrypt_str($key));
                        $programsButton .= " <a target='_blank' href='{$viewProgramRoute}'>{$value}</a>";
                    }
                    }


                $data[] = [
                    "id" => $row->id,
                    "first_name" => $name,
                    "program_id" => $programsButton,
                    "email" => $row->email,
                    "state" => $row->state,
                    "city" => $row->city,
                    "type" => $type,
                    "applieddate" => date('d-F-Y',strtotime($row->applieddate)),
                    "enddate" => date(
                        "d-F-Y",
                        strtotime($row->applieddate . " + 14 days")
                    ),
                    "status" => $this->generateStatusButton($row->status, $row->id),

                    "chat" => $row->type==5?$chat:'',
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.notice.index");
    }

    private function applyFilters($q, Request $request)
    {

        if ($request->input('is_sub')) {
            $q->where('users.is_sub', 1);
        }

        if ($request->input('background_check')) {
            $q->whereHas('backgroundVerifications', function ($query)  {
                $query->where(["type" => "background_check","status" => "1"]);
            });
        }
        if ($request->input('medical_requirements')) {
            $q->whereHas('backgroundVerifications', function ($query)  {
                $query->where(["type" => "medical_requirements","status" => "1"]);
            });
        }


        if ($request->filled(['location', 'lat', 'lng'])) {
            $lat = $request->lat;
            $lng = $request->lng;

            $q->whereHas('availableLocations', function ($query) use ($lat, $lng) {
                $query->selectRaw(
                    '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                    [$lat, $lng, $lat]
                )
                ->orderByRaw('distance');
            });
        }
        //tbl_user_doc_requests

        if ($request->input('state')) {
            $q->where('state', 'LIKE', "%{$request->input('state')}%");
        }

        if ($request->input('city')) {
            $q->where('city', 'LIKE', "%{$request->input('city')}%");
        }

        if ($request->filled('subject')) {
            $q->join('tbl_user_teaching_preferences', 'tbl_user_teaching_preferences.user_id', '=', 'users.id')
                ->join('tbl_user_subjects', 'tbl_user_subjects.step_id', '=', 'tbl_user_teaching_preferences.id')
                ->where('tbl_user_subjects.subject', trim($request->subject));

            if ($request->filled('sub_subject')) {
                $q->where('tbl_user_subjects.sub_subject', trim($request->sub_subject));
            }
        }


        if ($request->input('certified_teacher')) {
            $q->join('tbl_user_experiences', 'tbl_user_experiences.user_id', '=', 'users.id')
                ->where('tbl_user_experiences.profile_type', 'LIKE', "%{$request->input('certified_teacher')}%");
        }

        if ($request->input('profile_status')) {
            $q->where('profile_status', 'LIKE', "%{$request->input('profile_status')}%");
        }

        if ($request->input('format')) {
            $q->join('tbl_user_teaching_preferences as third', 'third.user_id', '=', 'users.id');

            $str = '';
            $i = 1;

            foreach ($request->input('format') as $key => $value) {
                $str .= 'FIND_IN_SET("' . $value . '" ,third.format)';
                if ($i < count($request->input('format'))) {
                    $str .= ' OR ';
                }
                $i++;
            }

            $q->whereRaw($str);
        }
    }

    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<button onclick="status_update(' . $id . ', 1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $id . '">' . __('messages.deactive') . '</button>';
            case 1:
                return '<button onclick="status_update(' . $id . ', 0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $id . '">' . __('messages.active') . '</button>';
            case 2:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deleted</button>';
            case 3:
                return '<button data-data="0" class="btn btn-danger btn-rounded">Account Deactivated</button>';
            default:
                return '';
        }
    }

    private function generateActionButtons($encryptedStrId,$res)
    {
        $viewRoute = url('viewinstructordetails/step1/' . $encryptedStrId);
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $mailButton = '';

        if (isset($res['manageinstructor'])) :
            if (array_key_exists('manageinstructor', $res)) :


                if (in_array('delete', json_decode($res['manageinstructor'], true))) :
                    $deleteButton = "<a class='admin_delete' href='{$actionUrl}' data-id='{$encryptedStrId}'><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;

                if (in_array('mail', json_decode($res['manageinstructor'], true))) :
                    $mailButton = "<a  href='{$viewMailRoute}' ><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-envelope' aria-hidden='true'></i></button></a>";

                endif;

            endif;
        endif;




        $viewButton = "<a href='{$viewRoute}'><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";


        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$viewButton}{$deleteButton}{$mailButton}</div>";
    }

    private function generateChatLink($encryptedId)
    {
        $chatRoute = url('instructor-chat/' . $encryptedId);
        return "<a href='{$chatRoute}'><i class='fas fa-comment fa-lg'></i></a>";
    }
}
