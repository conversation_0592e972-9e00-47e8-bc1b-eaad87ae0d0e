<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;  // Add WithHeadings concern

class DataExport implements FromCollection, WithHeadings  // Implement WithHeadings
{
    protected $data;
    protected $headings;

    public function __construct($data, $headings)
    {
        $this->data = $data;
        $this->headings = $headings;
    }

    // Return the data (rows) to be exported
    public function collection()
    {
        return $this->data;
    }

    // Return the headings (columns) for the export
    public function headings(): array
    {
        return $this->headings;  // This will set the column headings in the exported file
    }
}
