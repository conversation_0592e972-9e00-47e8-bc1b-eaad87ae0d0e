<?php

namespace App;

use App\Casts\TimeCast;
use Illuminate\Database\Eloquent\Model;
use DateTime;
use DateTimeZone;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class program_slots extends Model
{
    protected $table = 'tbl_program_slots';
    protected $fillable = [
        'slot_day', 'start_time', 'end_time', 'program_id'
    ];

    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
    protected $casts = [
        'start_time' => TimeCast::class,
        'end_time' => TimeCast::class,
    ];


    private function getUserTimezone()
    {
        $timezone = config('app.timezone', 'America/Los_Angeles');
        $user = request()->user();
        $isAdmin = in_array('CheckSession', request()->route()->middleware());
        $isSchool = in_array('CheckSchoolSession', request()->route()->middleware());

        if ($user  && !$isAdmin && !$isSchool) {
            $timezone = optional(@$user->availability)->teach_in_person_timezone ?? $timezone;
        } elseif ($isAdmin || $isSchool) {
            $timezone = $this->program->timezone ?? $timezone;
        }


        return $timezone;
    }

    public function getStartTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }

    public function getEndTimeAttribute($value)
    {
        $timezone = $this->getUserTimezone();

        return $value ? (new DateTime($value, new DateTimeZone('America/Los_Angeles')))->setTimezone(new DateTimeZone($timezone))->format('H:i:s') : null;
    }


    public function getStartTimeUtcAttribute()
    {
        return $this->attributes['start_time']; //  raw attribute in the database
    }

    public function getEndTimeUtcAttribute()
    {
        return $this->attributes['end_time']; // raw attribute in the database
    }
}
