<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON><PERSON><PERSON><PERSON>;
use Exception;
use <PERSON><PERSON>\JWTAuth\Http\Middleware\BaseMiddleware;

class TrainerJwtMiddleware extends BaseMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();
        } catch (Exception $e) {
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {

                return response()->json(['status' => '401', 'message' => 'This token is invalid. Please Login']);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {

                return response()->json(['status' => '103', 'message' => 'Token is Expired.Please Login']);
            } else {
                return response()->json(['status' => '404', 'message' => 'Authorization Token not found']);
            }
        }
        return $next($request);
    }
}
