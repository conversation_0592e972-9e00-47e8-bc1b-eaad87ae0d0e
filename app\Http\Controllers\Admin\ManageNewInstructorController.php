<?php

namespace App\Http\Controllers\Admin;

use App\AdditionalCertificateSubcategory;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\QuestionsModel;
use App\StateModel;
use App\LastChatModel;
use App\Helpers\NotificationHelper;
use App\EmailTemplate;
use App\RejectOnboardingInstructorModel;
use App\RequestChangeOnboardingInstructorModel;
use App\Helpers\DataTableHelper;
use App\OnboardingInstructor;
use App\InstructorFirstStepOnboardingModel;
use App\InstructorSecondStepOnboardingModel;
use App\InstructorThirdStepOnboardingModel;
use App\InstructorForthStepOnboardingModel;
use App\InstructorFifthStepOnboardingModel;
use App\InstructorSixthStepOnboardingModel;
use App\InstructorSeventhStepOnboardingModel;
use App\OnboardingIinstructorGoogleMapAddress;
use App\InstructorEducationSecondStepOnboardingModel;
use App\Exports\Admin\ExportApplications;
use DB;
use Validator;
use Session;
use App\Http\Requests;
use App\Users;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\scheduledInterview;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\ViewClassroomModel;
use App\UserUponCompletionNoteModel;
use App\ProfileStatusHistoryModel;
use App\rubric;
use App\user_references;
use App\UserEducationModel;
use App\user_interview_slots;
use App\user_contract;
use App\document_form;
use App\Subject;
use Hash;
use Mail;
use Auth;
use App\AvailabilityModel;
use App\AdministrativeInfoModel;
use App\AvailablityLocationModel;
use App\District;
use App\Exports\Admin\ExportMarketplaceApplicants;
use App\FreeResponseQuestionsModel;
use App\Http\Controllers\AdditionalCertificate;
use App\InstructorOnboardingAssessmentModel;
use App\InstructorSubjectsThirdStepOnboardingModel;
use App\invite_application_recruiter;
use App\Models\InstructorBudgetApprovedModel;
use App\Models\InstructorBudgetLine;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\BudgetState;
use App\SampleLesson;
use App\school_contact_info;
use App\User;
use App\StatesTerritoriesModel;
use Carbon\Carbon;
use Excel;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Hash as FacadesHash;

class ManageNewInstructorController extends Controller
{
    public function generateChatLink($encryptedId)
    {
        $chatRoute = url('admin/onboarding-instructor-chat/' . $encryptedId);
        return "<a href='{$chatRoute}'><i class='fas fa-comment fa-lg'></i></a>";
    }

    private function generateActionButtons($encryptedStrId, $res)
    {
        // $viewRoute = url('admin/viewonboardinginstructordetails/step1/' . $encryptedStrId);
        $viewRoute = url('admin/k12connections/application/step1/');
        // dd($viewRoute);
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $mailButton = '';

        if (isset($res['manageinstructor'])) :
            if (array_key_exists('manageinstructor', $res)) :
                // if (in_array('update', json_decode($res['manageinstructor'], true))) :
                //     $editRoute = url('edit-onboarding-instructor/' . $encryptedStrId);

                //     $editButton = "<a href='{$editRoute}'><button type='button' class='btn btn-rounded btn-block btn-xs btn-outline-secondary' title='Edit'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>";
                // endif;

                if (in_array('delete', json_decode($res['manageinstructor'], true))) :
                    $deleteButton = "<a class='instructor_delete'  href='{$actionUrl}' data-id='{$encryptedStrId}'><button type='button' title='Delete' class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;

                if (in_array('mail', json_decode($res['manageinstructor'], true))) :
                    $mailButton = "<a  href='{$viewMailRoute}' ><button type='button' title='Mail' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-envelope' aria-hidden='true'></i></button></a>";

                endif;

            endif;
        endif;



        //     $viewButton = "
        //     <form action='{$viewRoute}' method='POST' style='display:inline;'>
        //         <input type='hidden' name='_token' value='{$csrfToken}'>
        //         <input type='hidden' name='encryptedStrId' value='{$encryptedStrId}'>
        //         <button type='submit' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'>
        //             <i class='fa fa-eye' aria-hidden='true'></i>
        //         </button>
        //     </form>
        // ";

        $viewButton = "
            <form action='" . url('admin/k12connections/set-instructor-id') . "' method='POST' style='display:inline;'>
                " . csrf_field() . "
                <input type='hidden' name='instructor_id' value='{$encryptedStrId}'>
                <button type='submit' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'>
                    <i class='fa fa-eye' aria-hidden='true'></i>
                </button>
            </form>
        ";

        // $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";

        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$viewButton}{$deleteButton}{$mailButton}</div>";
    }

    private function getEmailSubject($message_type, $company_name = 'Whizara')
    {
        $subject = '';
        switch ($message_type) {
            case 'action_required':
                $subject = "Action Required: Important Message from $company_name";
                break;
            case 'important_notification':
                $subject = "Important Notification: Message from $company_name";
                break;
            case 'reminder':
                $subject = "Reminder: Please Review Your Latest Message";
                break;
            case 'system_alert':
                $subject = "System Alert: Important Update from $company_name";
                break;
            case 'event_update':
                $subject = "Event Update: Important Details Inside";
                break;
            case 'new_feature_announcement':
                $subject = "New Feature: Check Out What's New at $company_name";
                break;
            case 'security_alert':
                $subject = "Security Alert: Important Message Regarding Your Account";
                break;
            case 'general_information':
                $subject = "Information Update: New Message from $company_name";
                break;
            default:
                $subject = "New Message from $company_name";
                break;
        }
        return $subject;
    }

    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 'InProgress':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus" disabled data-toggle="select2" name="userStatus">
                    <option value="InProgress" selected> Pending </option>
                </select>';
                break;

            case 'UnderReview':
            case 'ChangeRequested':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                        <option value="UnderReview" hidden ' . ($status == 'UnderReview' ? 'selected' : '') . '>Under Review</option>
                        <option value="ChangeRequested" ' . ($status == 'ChangeRequested' ? 'selected hidden' : '') . ' >Request Change</option>
                        <option value="Approved">Approve</option>
                        <option value="Declined">Reject</option>

                </select>';
                break;
            case 'Active':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="Active" selected>Activate</option>
                    <option value="Declined">Reject</option>
                </select>';
                break;

            case 'Declined':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="ChangeRequested">Request Change</option>
                    <option value="Approved">Approve</option>
                    <option value="Declined" selected>Reject</option>
                </select>';
                break;
            case  'Approved':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="Approved" selected>Approved</option>
                    <option value="UnderReview">Withdraw</option>
                    <option value="Declined">Reject</option>
                </select>';

            default:
                # code...
                break;
        }
    }

    private function generateVerifiedButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<span class="btn btn-danger">Email Not Verified</span>';
            case 1:
                // return '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                //         <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                //         <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                //         <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                //         </svg>';
                return '<span class="btn btn-success">Email Verified</span>';
            default:
                return '';
        }
    }

    private function isStepPending($pendings, $step)
    {
        foreach ($pendings as $pendingStep) {
            if (strpos($pendingStep, $step) === 0) {
                return true; // Found a match
            }
        }
        return false; // No match found
    }

    public function manageInstructor(Request $request)
    {
        if (get_childpermission(get_permission(session('Adminnewlogin')['type']), 'manageinstructor', 'view') != true) {
            return redirect("/no-permission");
        }

        if ($request->ajax()) {
            $qry = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6', 'step7', 'googleMap', 'rejectInstructor'])->select('new_onboarding_instructor.*')
                ->where("new_onboarding_instructor.type", "=", "5");
            // ->whereIn("user_status", ['InProgress','UnderReview','ChangeRequested','Active','Declined']);

            $id = $request->id;
            switch ($id) {

                case "ALL":
                    $qry->whereIn("user_status", ['InProgress', 'UnderReview', 'ChangeRequested', 'Active', 'Declined', 'Withdraw', 'Approved']);
                    break;

                case "InProgress":
                    $qry->where("user_status", "InProgress");
                    break;

                case "UnderReview":
                    $qry->where("user_status", "UnderReview");
                    break;

                case "ChangeRequested":
                    $qry->where("user_status", "ChangeRequested");
                    break;

                case "Active":
                    $qry->where("user_status", "Active")
                        ->with(['whizaraContract', 'marketplaceContract'])
                        ->where(function ($query) {
                            $query->whereHas('whizaraContract')
                                ->orWhereHas('marketplaceContract');
                        });
                    break;

                case "Declined":
                    $qry->where("user_status", "Declined");
                    break;

                case "Approved":
                    $qry->where("user_status", "Approved");
                    break;


                default:
                    $qry->whereIn("user_status", ['InProgress', 'UnderReview', 'ChangeRequested', 'Active', 'Declined', 'Approved']);
                    break;
            }
            $params = DataTableHelper::getParams($request);
            if (empty($params['columnName'])) {
                $params['columnName']   = 'new_onboarding_instructor.submission_date';
            }
            if ($params['columnName'] == 'applicationSubmittedDate') {
                $params['columnName']   = 'new_onboarding_instructor.submission_date';
            }
            if ($params['columnName'] == 'id') {
                $params['columnName']   = 'new_onboarding_instructor.id';
            }
            if ($params['columnName'] == 'applicationStartDate') {
                $params['columnName']   = 'new_onboarding_instructor.created_at';
            }
            if ($params['columnName'] == 'approvedDeclinedDate') {
                $params['columnName']   = 'new_onboarding_instructor.status_updated_at';
            }
            if ($params['columnName'] == 'inpersonRate') {
                $qry->addSelect([
                    'inpersonRate' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN
                                                SUBSTRING_INDEX(compensation, ',', 1)
                                            WHEN FIND_IN_SET('in-person', format) THEN
                                                compensation
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'onlineRate') {
                $qry->addSelect([
                    'onlineRate' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN
                                                SUBSTRING_INDEX(compensation, ',', 1)
                                            WHEN FIND_IN_SET('online', format) THEN
                                                compensation
                                            ELSE NULL
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'format') {
                $qry->addSelect([
                    'format' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN 'Both (Online & In-person)'
                                            WHEN FIND_IN_SET('online', format) THEN 'Online'
                                            WHEN FIND_IN_SET('in-person', format) THEN 'In-person'
                                            ELSE NULL
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'subjects') {
                $qry->addSelect([
                    'subjects' => InstructorSubjectsThirdStepOnboardingModel::select('subjects_v1.subject_code')
                        ->join('subjects_v1', 'onboarding_instructor_subjects.sub_subject', '=', 'subjects_v1.id')
                        ->whereColumn('onboarding_instructor_subjects.user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }
            $qry->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            if ($params['searchValue']) {
                $searchValue = strtolower($params['searchValue']);
                // dd($params['columns'], $searchValue);

                $qry->where(function ($qry) use ($searchValue, $params) {
                    $qry->where(function ($query) use ($searchValue, $params) {
                        // Check if $searchValue is a valid date
                        if (strtotime($searchValue)) {
                            $formattedDate = Carbon::parse($searchValue)->format('Y-m-d');
                            $userIds = OnboardingInstructor::whereDate('created_at', $formattedDate)->pluck('id');
                            $query->whereIn('id', $userIds);
                        }
                    })->orWhere(function ($subQuery) use ($searchValue, $params) {
                        $filteredColumns = array_filter($params['columns'], function ($column) {
                            return isset($column['data']) && $column['data'] === 'subjects';
                        });
                        // dd($filteredColumns);
                        if (!empty($filteredColumns)) {
                            $subjectIds = V1Subject::where('subject_code', 'LIKE', "%{$searchValue}%")
                                ->orWhere('title', 'LIKE', "%{$searchValue}%")
                                ->pluck('id');
                            // dd($subjectIds);
                            if ($subjectIds->isNotEmpty()) {
                                $userIds = InstructorSubjectsThirdStepOnboardingModel::whereIn('sub_subject', $subjectIds)->pluck('user_id');
                                $subQuery->whereIn('id', $userIds);
                            }
                        }
                        // dd($subQuery->get());
                    })->orWhere(function ($subQuery) use ($params) {
                        $filteredColumns = array_filter($params['columns'], function ($column) {
                            return $column['data'] !== 'applicationStartDate' && $column['data'] !== 'applicationSubmittedDate' && $column['data'] !== 'subjects';
                        });

                        if (!empty($filteredColumns)) {
                            $params['columns'] = $filteredColumns; // Update columns
                            DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], $params['columns']);
                        }
                    });
                });

                // $qry->where(function ($que) use ($params) {
                //     DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                // });
            }

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);

            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);

                ///
                $chat = "";
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $chat = $this->generateChatLink($encryptedId);
                        endif;
                    endif;
                endif;

                $checboxButton  = '';
                $action = $this->generateActionButtons($encryptedStrId, $res);

                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $checboxButton  = "<input type='checkbox'  value='{$row->id}' form='chatprogram' name='instructor[]'><input type='hidden'  value='{$row->email}' form='chatprogram' id='email{$row->id}'><input type='hidden'  value='{$row->first_name} {$row->last_name}' form='chatprogram' id='to_name{$row->id}'><input type='hidden'  value='{$row->image}' form='chatprogram' id='img{$row->id}'> ";
                        endif;
                    endif;
                endif;

                if ($row->is_sub == 1) {
                    $aval = 'Sub';
                } else {
                    $aval = 'Main/Sub';
                }

                $step3 = InstructorThirdStepOnboardingModel::where("user_id", $row->id)->first();
                $prefrence = !empty($step3) && !empty($step3->i_prefer_to_teach) ? '<a target="_blank" href="' . url('admin/viewonboardinginstructordetails/step3/' . encrypt_str($row->id)) . '">Teaching preferences</a>' : 'null';
                $format = (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false) ? "Both (Online & In-person)" : (!empty($step3->format) ? ucfirst($step3->format) : null);
                if (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false && !empty($step3->compensation)) {
                    $compensation = explode(',', $step3->compensation);
                    $online = $compensation[0];
                    $inperson = $compensation[1];
                } else {
                    $compensation = (!empty($step3) && !empty($step3->format) && !empty($step3->compensation)) ? $step3->compensation : null;
                    $online = '';
                    $inperson = '';
                    if (!empty($step3->format) && !empty($step3->compensation)) {
                        if (strpos($step3->format, 'online') !== false) {
                            $online = $compensation;
                        } elseif (strpos($step3->format, 'in-person') !== false) {
                            $inperson = $compensation;
                        }
                    }
                }

                $statusButton = '';
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('update', json_decode($res['manageinstructor'], true))) :
                            $statusButton = $this->generateStatusButton($row->user_status, $row->id);
                        endif;
                    endif;
                endif;

                $verifiedButton = '';
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('update', json_decode($res['manageinstructor'], true))) :
                            $verifiedButton = $this->generateVerifiedButton($row->email_verify_status, $row->id);
                        endif;
                    endif;
                endif;
                $approvedDeclinedDate = $row->user_status == 'Active' ? Carbon::parse($row->status_updated_at)->format('d-F-Y H:i:s') : (($row->user_status == 'Declined' && !empty($row->rejectInstructor)) ? Carbon::parse($row->rejectInstructor->date)->format('d-F-Y H:i:s') : '');
                $applicationSubmittedDate = !empty($row->submission_date) ? Carbon::parse($row->submission_date)->format('d-F-Y') : '';
                // dd($applicationSubmittedDate);
                $applicationStartDate = Carbon::parse($row->created_at)->format('d-F-Y H:i:s');
                $pendingSteps = [
                    'step-1' => 'Us Work Authorization',
                    'step-3' => 'Education & Experience',
                    'step-4' => 'Your Preferences',
                    'step-5' => 'Profile',
                    'step-6' => 'Assessment: Quiz',
                ];

                $pending = json_decode($row->pending_onboarding_steps);
                $displayedSteps = [];

                foreach ($pendingSteps as $stepKey => $stepValue) {
                    $isStepPending = false;

                    if (!empty($pending)) {
                        foreach ($pending as $pendingStep) {
                            if (strpos($pendingStep, $stepKey) !== false) {
                                $isStepPending = true;
                                break;
                            }
                        }
                    }

                    if ($isStepPending && isset($pendingSteps[$stepKey])) {
                        $displayedSteps[] = $pendingSteps[$stepKey];
                    }
                }
                $commaSeparatedSteps = implode(', ', $displayedSteps);
                // dd($pending,in_array('step-3', $pending ?? []));

                $subjectTitles = [];
                if (!empty($row->step3->subjects)) {
                    foreach ($row->step3->subjects as $subject) {
                        $subSubjectId = $subject->sub_subject;
                        $subjectModel = V1Subject::find($subSubjectId);
                        if ($subjectModel) {
                            $subjectTitles[] = $subjectModel->subject_code . ': ' . $subjectModel->title;
                        }
                    }
                }
                $subjectString = !empty($subjectTitles) ? implode(', ', $subjectTitles) : '';
                $data[] = [
                    "id" => $row->id,
                    "email_verify_status" => $verifiedButton,
                    // "id" =>$row->id,
                    "first_name" => $row->first_name . ' ' . $row->last_name,
                    "email" => $row->email,
                    "onlineRate" => $online,
                    "inpersonRate" => $inperson,
                    "availibility" => '',
                    "approvals" =>  $aval ?? '',
                    "format" => $format ?? '',
                    "state" => $row->state,
                    "city" => $row->city,
                    'subjects' => $subjectString,
                    'inpersonrate' => $row->inpersonrate,
                    'onlinerate' => $row->onlinerate,
                    "status" => $statusButton,
                    "chat" => $chat,
                    "applicationSubmittedDate" => $applicationSubmittedDate,
                    "approvedDeclinedDate" => $approvedDeclinedDate,
                    "applicationStartDate" => $applicationStartDate,
                    'notes' => requestChangeReson($row->id),
                    "action" => $action,
                    "pending_onboarding_steps" => $commaSeparatedSteps,
                    "step_1" => $this->isStepPending($pending, 'step-1')
                        ?  ''
                        : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_3" => $this->isStepPending($pending, 'step-3')
                        ?   ''
                        : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_4" => $this->isStepPending($pending, 'step-4') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_5" => $this->isStepPending($pending, 'step-5') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_6" => $this->isStepPending($pending, 'step-6') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.new-instructor.instructor_list");
    }

    public function setInstructorId(Request $request, $id = null)
    {
        $instructorId = $request->input('instructor_id', $id);
        session(['selected_instructor_id' => $instructorId]);
        return redirect()->route('admin.marketplaceviewapplication', ['id' => 'step1']);
    }


    public function sendmsg(Request $request)
    {
        if (!$request->filled('instructor')) {
            return $this->jsonErrorResponse(["message" => "Please select at least one instructor"], false);
        }

        $view = view("admin.new-instructor.modals.sendmsgmodel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendOnboardingMsgForAdmin(Request $request)
    {
        $from_id = session("Adminnewlogin")["id"];
        $toid = $_POST['id'];
        $message = $_POST['message'];
        $message_type = $_POST['message_type'];
        $count = 0;
        foreach ($toid as $id) {
            $already = LastChatModel::where(['from_id' => $from_id, 'to_id' => $id, 'message' => $message])->first();
            if (!$already) {
                $count++;
                $user = OnboardingInstructor::find($id);
                $email = $user['email'];
                $emailTemplate = EmailTemplate::where('title', 'Message copy')->limit(1)->get();
                $subject = $this->getEmailSubject($message_type);
                $fullName = $user['first_name'] . ' ' . $user['last_name'];
                $emailBody = str_replace(['{{ NAME }}', '{{ message }}'], [$fullName, $message], $emailTemplate[0]->description);
                NotificationHelper::sendEmail($email, $subject, $emailBody);
                LastChatModel::insert(['from_id' => $from_id, 'to_id' => $id, 'message' => $message]);
            }
        }
        if ($count > 0) {
            return response()->json([
                "success" => true,
                "message" => "Message sent successfully",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Message already sent",
            ]);
        }
    }

    public function sendRejectOnboardingMsg(Request $request)
    {
        $view = view("admin.new-instructor.modals.rejectmsgModel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendChangeRequestOnboardingMsg(Request $request)
    {
        $view = view("admin.new-instructor.modals.changeRequestmsgModel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendActiveOnboardingMsg(Request $request)
    {

        $user = OnboardingInstructor::with(['step1', 'step2', 'step3'])->where('id', $request->userid)->first();
        $subjectIds = $user->step3->subjects->pluck('sub_subject');
        $subjects = V1Subject::with('subjectBudget')->whereIn('id', $subjectIds)->get();
        $case_management = 0;
        $bilingual = 0;
        $sped=0;
        $matchedEducations = empty($user->step2->education)
            ? []
            : collect($user->step2->education)
            ->filter(function ($edu) {
                return stripos($edu->education ?? '', 'special education') !== false;
            })
            ->mapWithKeys(function ($edu) {
                $stateKeys = json_decode($edu->states, true) ?: [];

                $statesWithPrices = BudgetState::whereIn('name', $stateKeys)
                    ->get()
                    ->map(function ($state) {
                        $price = number_format($state->sped_rec_comp, 2); // Format price if needed
                        return [$state->name, $price];
                    })
                    ->values()
                    ->toArray();
                return [$edu->education => $statesWithPrices];
            })
            ->toArray();
        $languages = array_map('trim', explode(',', $user->step3->language_teach_that_i_teach));

        $hasOtherLanguages = collect($languages)->filter(function ($lang) {
            return strtolower($lang) !== 'english';
        })->isNotEmpty();

        $stateData = BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            if($hasOtherLanguages) {
                $bilingual = $stateData->bilingual_inc ?? 0;
            }
            $sped = $stateData->sped_rec_comp;
        }

        $view = view("admin.new-instructor.modals.activemsgModel", ['user' => $user, 'subjects' => $subjects, "case_management" => $case_management, "bilingual" => $bilingual, "specialEducation" => $matchedEducations, 'sped' => $sped])->render();

        return response()->json(['status' => true, 'view' => $view]);
    }

    public function update_user_status(Request $request)
    {

        $user = OnboardingInstructor::find($request->userid);
        $dataUpdate = [
            'user_status' => $request->status
        ];
        $userEmail = $user->email;
        $adminUser = User::find(3);
        if ($request->status == 'Approved') {
            $request->validate([
                'userid' => 'required|exists:new_onboarding_instructor,id',
                'in_person' => 'required|numeric',
                'bilingual_inc' => 'required|numeric',
                'case_management' => 'required|numeric',
                'budget_lines' => 'required|array',
                'budget_lines.*.code_title' => 'required|string',
                'budget_lines.*.base_pay' => 'required|numeric',
                'budget_lines.*.experience_pay' => 'required|numeric',
                'budget_lines.*.education_pay' => 'required|numeric',
                'budget_lines.*.non_teaching' => 'nullable|numeric',
                'budget_lines.*.special_education' => 'nullable|numeric',
                'budget_lines.*.total' => 'required|numeric'
            ]);
            $template = EmailTemplate::find(35);
            $body = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->description);
            $data = array('template' => $body);
            $subject = $template->subject;


            try {
                DB::beginTransaction();
                // Update or create the main budget approval record
                $approved = InstructorBudgetApprovedModel::updateOrCreate(
                    ['user_id' => $request->userid], // Unique by user
                    [
                        'in_person' => $request->in_person,
                        'bilingual_inc' => $request->bilingual_inc,
                        'case_management' => $request->case_management,
                        'status_updated_at' => now(),
                    ]
                );

                // Then handle each budget line uniquely by subject
                foreach ($request->budget_lines as $line) {
                    $parts = explode(':', $line['code_title']);
                    $subject_code = array_shift($parts);
                    $subject_name = implode(':', $parts);

                    if ($line['selected'] == 'true') {
                        InstructorBudgetLine::updateOrCreate(
                            [
                                'approved_id' => $approved->id,
                                'subject_code' => $subject_code,
                            ],
                            [
                                'subject_title' => $subject_name,
                                'base_pay' => $line['base_pay'],
                                'experience_pay' => $line['experience_pay'],
                                'education_pay' => $line['education_pay'],
                                'non_teaching' => $line['non_teaching'] ?? 0,
                                'special_education' => $line['special_education'] ?? 0,
                                'total' => $line['total'],
                            ]
                        );
                    }
                }

                Mail::send('template', $data, function ($message) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
                DB::commit();
            } catch (\Exception  $e) {
                DB::rollback();
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to save instructor budget.',
                    'error' => $e->getMessage()
                ], 500);
            }
        } elseif ($request->status == 'ChangeRequested') {

            $reject = new RequestChangeOnboardingInstructorModel;
            $reject->onboarding_instructor_id = $user->id;
            $reject->reason = $request->reason;
            $reject->date = date('Y-m-d h:i:s');
            $reject->save();

            $template = EmailTemplate::find(37);
            $url = url('k12connections/application');
            $body = str_replace(['{{NAME}}', '{{custom_feedback}}', '{{redirect}}'], [$user->first_name . ' ' . $user->last_name, $request->reason, $url], $template->description);
            // NotificationHelper::sendEmail($user->email, $template->subject, $body);
            $data = array('template' => $body);
            $subject = $template->subject;
            // SendNotificationForInstructorProfileResubmit($user, $adminUser);
            try {

                Mail::send('template', $data, function (
                    $message
                ) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }
        } elseif ($request->status == 'Declined' || $request->status == 'Active') {
            $admin = User::find(3);
            $template = EmailTemplate::find(36);
            $url = url('admin/k12connections/set-instructor-id/' . $user->id);
            $body = str_replace(['{{NAME}}', '{{redirect}}'], [$user->first_name . ' ' . $user->last_name, $url], $template->description);
            $subject = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->subject);
            NotificationHelper::sendEmail($admin->email, $subject, $body);
            if ($request->status == 'Declined') {
                $reject = new RejectOnboardingInstructorModel;
                $reject->onboarding_instructor_id = $user->id;
                $reject->reason = $request->reason;
                $reject->date = date('Y-m-d h:i:s');
                $reject->save();
                // no email trigger
            } elseif ($request->status == 'Active') {
                // $template = EmailTemplate::find(35);
                // $body = str_replace(['{{NAME}}'], [$user->first_name.' '. $user->last_name], $template->description);
                // // NotificationHelper::sendEmail($user->email, $template->subject, $body);
                // $data=array('template'=>$body);
                // $userEmail = $user->email;
                // $subject=$template->subject;
                // // SendNotificationForInstructorProfileApproval($user, $adminUser);

                // try {

                //     Mail::send('template', $data, function (
                //         $message
                //     ) use ($userEmail,$subject) {
                //         $message->to($userEmail)->subject($subject);
                //     });
                // } catch (\Throwable $th) {
                //     //throw $th;
                // }
            }
        } else if ($request->status == 'UnderReview') {
            $template = EmailTemplate::find(45);
            $url = url('k12connections/application');
            $body = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->description);
            // NotificationHelper::sendEmail($user->email, $template->subject, $body);
            $data = array('template' => $body);
            $subject = $template->subject;
            try {

                Mail::send('template', $data, function (
                    $message
                ) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }

            OnboardingInstructor::where('id', $user->id)->update(['educator' => null]);
            OnboardingInstructorMarketplaceContract::where('user_id', $user->id)->delete();
            OnboardingInstructorContract::where('user_id', $user->id)->delete();
        }
        $user->update($dataUpdate);
        return response()->json(['status' => true]);
    }

    public function chat($id)
    {
        $id = decrypt($id);

        $rows = OnboardingInstructor::where("id", $id)->first();

        return view("admin.instructor.chat", compact("rows"));
    }

    public function delete_admin(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = OnboardingInstructor::where("id", $id)->first();
            if ($record) {

                $user = $record;

                $res = OnboardingInstructor::where("id", "=", $id)->delete();


                // if($res) {
                //     OnboardingIinstructorGoogleMapAddress::where('instructor_onboarding_id', $id)->delete();

                return response()->json([
                    "success" => true,
                    "message" => "Successfully Deleted",
                ]);
                // } else {
                //     return response()->json([
                //         "success" => false,
                //         "message" => "Something went worng",
                //     ]);
                // }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewnewinstructordetails($id, $id2)
    {
        $application_id = decrypt_str($id2);

        $users = OnboardingInstructor::where("id", $application_id)
            ->firstOrFail();

        $user = OnboardingInstructor::with(['step1', 'step2', 'step2.education', 'step2.teching', 'step2.otherExper', 'step3.subjects', 'step5', 'step6', 'step7', 'googleMap'])->find($application_id);

        $user_first_step = InstructorFirstStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_second_step = InstructorSecondStepOnboardingModel::with(['teching', 'education', 'otherExper'])
            ->where("user_id", $application_id)
            ->first();
        $user_third_step = InstructorThirdStepOnboardingModel::with(['subjects'])->where("user_id", $application_id)
            ->first();

        $user_fifth_step = InstructorFifthStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_sixth_step = InstructorSixthStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_seventh_step = InstructorSeventhStepOnboardingModel::where("user_id", $application_id)
            ->first();

        $question = QuestionsModel::orderBy("question_id", "asc")->get();
        $state = StateModel::where(["country_id" => "239"])->get();
        // dd($user,$application_id,$user_first_step,$user_second_step,$user_third_step,$user_fourth_step,$user_fifth_step,$user_sixth_step,$user_seventh_step);

        return view(
            "admin.new-instructor.viewinstructor",
            compact(
                "question",
                "state",
                "user",
                "application_id",
                "user_first_step",
                "user_second_step",
                "user_third_step",
                "user_fifth_step",
                "user_sixth_step",
                "user_seventh_step",
            )
        );
    }

    public function marketplaceviewapplication($id)
    {
        $instructorId = session('selected_instructor_id');
        $application_id = decrypt_str($instructorId);
        $user = OnboardingInstructor::with(['step1', 'step2', 'step3'])->where("id", $application_id)->first();

        $educatorData = OnboardingInstructor::where("id", $application_id)->where('user_status', 'Active')->whereNotNull('educator')->whereNull('isDeclineContract')->first();
        if ($educatorData) {
            $marketplaceExists = false;
            $whizaraExists = false;

            $educatorTypes = explode(',', $educatorData->educator);
            if (in_array('marketplace educator', $educatorTypes)) {
                $marketplaceExists = OnboardingInstructorMarketplaceContract::where('user_id', $educatorData->id)->exists();
            }
            if (in_array('whizara educator', $educatorTypes)) {
                $whizaraExists = OnboardingInstructorContract::where('user_id', $educatorData->id)->exists();
            }

            if ($marketplaceExists && $whizaraExists) {
                $educator = 'Marketplace Educator and Whizara Educator';
            } elseif ($marketplaceExists) {
                $educator = 'Marketplace Educator';
            } elseif ($whizaraExists) {
                $educator = 'Whizara Educator';
            } else {
                $educator = 'Educator Not Defined';
            }
        } else {
            $educator = 'Educator Not Defined';
        }

        if (!empty($user)) {
            $data["user_first_step"] = InstructorFirstStepOnboardingModel::where('user_id', $application_id)->first();
            $data["user_third_step"] = InstructorSecondStepOnboardingModel::with(['education', 'teching', 'otherExper', 'references'])
                ->where('user_id', $application_id)
                ->get();
            $data["user_fourth_step"] = InstructorThirdStepOnboardingModel::with(['subjects'])->where("user_id", $application_id)
                ->get();
            $data["profile"] = InstructorFifthStepOnboardingModel::where("user_id", $application_id)->first();
            $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
            $data["assessment"] = FreeResponseQuestionsModel::orderBy("id", "asc")->get();
            $data["quiz"] = InstructorSixthStepOnboardingModel::where(["user_id" => $application_id])->first();
            $data["assessments_ans"] = InstructorOnboardingAssessmentModel::where(["user_id" => $application_id])->first();
            $data["whizaraEducator"] = OnboardingInstructorContract::where(["user_id" => $application_id])->first();
            $data["marketplaceEducator"] = OnboardingInstructorMarketplaceContract::where(["user_id" => $application_id])->first();
            $data["additional_certificates"] = DB::table("additional_certificates")->where("instructor_id", $application_id)->get();
            $data["sample_lesson"]=SampleLesson::where("user_id",$application_id)->get();

        //this is for the modal data of checkboxes
        $subjectIds = $user->step3->subjects->pluck('sub_subject');
        $subjects = V1Subject::with('subjectBudget')->whereIn('id', $subjectIds)->get();
        $case_management = 0;
        $bilingual = 0;
        $sped=0;
        $matchedEducations = empty($user->step2->education)
            ? []
            : collect($user->step2->education)
            ->filter(function ($edu) {
                return stripos($edu->education ?? '', 'special education') !== false;
            })
            ->mapWithKeys(function ($edu) {
                $stateKeys = json_decode($edu->states, true) ?: [];

                $statesWithPrices = BudgetState::whereIn('name', $stateKeys)
                    ->get()
                    ->map(function ($state) {
                        $price = number_format($state->sped_rec_comp, 2); // Format price if needed
                        return [$state->name, $price];
                    })
                    ->values()
                    ->toArray();
                return [$edu->education => $statesWithPrices];
            })
            ->toArray();

        $stateData = BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            $bilingual = $stateData->bilingual_inc;
            $sped = $stateData->sped_rec_comp;
        }

                // $view = view("admin.new-instructor.modals.activemsgModel", ['user' => $user, 'subjects' => $subjects, "case_management" => $case_management, "bilingual" => $bilingual, "specialEducation" => $matchedEducations, 'sped' => $sped])->render();

            return view("admin.marketplace.viewdetails", compact("data", "application_id", "user", "educator","user","subjects","case_management","bilingual","matchedEducations","sped"));
        } else {
            return redirect('/admin-dashboard');
        }
    }

    public function exportApplicants(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Applicants' . time() . '.xlsx';
            return Excel::download(new ExportMarketplaceApplicants($request), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

    public function managePlatformSchools(Request $request)
    {
        try{
            if (get_childpermission(get_permission(session('Adminnewlogin')['type']), 'manageschool', 'view') != true) {
                return redirect("/no-permission");
            }
            if ($request->ajax()) {
                $params = DataTableHelper::getParams($request);

                if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                    $params['columnName']   = 'users.id';
                }
                $qry = Users::where("type", "=", "6")->where('cust_type', 'Platform')->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
                $qry->where(function ($que) use ($params) {
                    DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                });
                [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

                $data = [];
                $i = 1;
                $res = get_permission(session('Adminnewlogin')['type']);
                foreach ($result as $row) {
                    $encryptedId = encrypt($row->id);
                    $encryptedStrId = encrypt_str($row->id);

                    ///
                    $chat = "";
                    if (isset($res['manageschool'])) :
                        if (array_key_exists('manageschool', $res)) :
                            if (in_array('Manage Program', json_decode($res['manageschool'], true))) :
                                $chat = $this->generateChatLink($encryptedId);
                            endif;
                        endif;
                    endif;
                    ///

                    $action = $this->generateActionButtons($encryptedStrId, $res);

                    $data[] = [
                        "id" => $row->id,
                        "full_name" => $row->full_name,
                        "email" => $row->email,
                        "organizationtype" => $row->organizationtype,
                        "organizationname" => $row->organizationname,
                        "status" => $this->generateStatusButton($row->status, $row->id),
                        "created_at" => getAdminTimestamp($row->created_at),
                        "action" => $action,
                    ];
                    $i++;
                }
                return DataTableHelper::generateResponse($params['draw'], $count, $data);
            }
            $where = ["type" => 6];
            $user_list = array();
            return view("admin.marketplace.schools.manageplatformschools", compact("user_list"));
        } catch (\Exception $e) {
            return redirect()->back();
        }
    }

    // ********Add-Platform-Schools********
    public function addPlatformSchools()
    {
        try{
            $district = District::where("status", "1")->get();
            $cbo = FacadesDB::table("tbl_cbo")->where("status", "1")->get();
            $gradeLavel = FacadesDB::table("tbl_classes")->where("status", "1")->get();
            return view("admin.marketplace.schools.addplatformschools", compact("district", "cbo", "gradeLavel"));
        } catch (\Exception $e) {
            return redirect()->back();
        }
    }
    // ********Add-Platform-Schools********

    // ********Save-Platform-Schools********
    public function savePlatformSchools(Request $request)
    {
        $email = $request->email;
        $full_name = $request->full_name;

        if ($email != "" && $full_name != "") {
            $userExits = Users::where("email",  $email)->exists();
            if ($userExits) {
                return response()->json(["success" => false, "message" => "Email already exits"]);
            }

            $logoName = "";
            if ($request->hasfile("profile_upload")) {
                $image = $request->file("profile_upload");
                $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
                uploads3image($filename, $image);
                $data["image"] =  $filename;
            }

            $length = 6;
            $randpassword = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 1, $length);
            $user_id = substr(str_shuffle("0123456789"), 1, $length);
            $data["full_name"] = $request->full_name;
            $data["first_name"] = $request->full_name;
            $data["email"] = $request->email;
            $data["user_id"] = $user_id;
            // $data['phone_number'] = $request->phone;
            $data["about"] = strip_tags($request->notes);
            $data["district"] = $request->district;
            $data["organizationtype"] = $request->organizationtype;
            $data["nces_cource_code"] = $request->nces_cource_code;
            $data["organizationname"] = $request->organizationname;
            $data["grade"] = implode(",", $request->grade);
            $data["cust_type"] = $request->cust_type;
            $data["website_url"] = $request->website;
            $data["password"] = FacadesHash::make($randpassword);
            $data["password"] = FacadesHash::make($randpassword);
            // $obj["passwordStr"] = $randpassword;
            $data["state"] = $request->state;
            $data["city"] = $request->city;
            $data["country"] = $request->country;
            $data["zipcode"] = $request->zipcode;
            $data["address"] = $request->address;
            $data["cbo"] = $request->cbo;
            $data["type"] = 6;
            $data["status"] = "1";
            $data["created_at"] = date("Y-m-d H:i:s");
            $data["updated_at"] = date("Y-m-d H:i:s");
            $save = Users::insertGetId($data);

            if ($save) {
                for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                    $data1["job_title"] = $_POST["job_title"][$i];
                    $data1["email"] = $_POST["cemail"][$i];
                    $data1["first_name"] = $_POST["first_name"][$i];
                    $data1["last_name"] = $_POST["last_name"][$i];
                    $data1["phone"] = $_POST["phone"][$i];
                    $data1["school_id"] = $save;
                    school_contact_info::insertGetId($data1);
                }

                $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", "18")->first();
                $body =  $template->description;
                $full_name = $request->full_name;
                $body = str_replace('{{NAME}}', $full_name, $body);
                $body = str_replace('{{Password}}', $randpassword, $body);
                $body = str_replace('{{Username}}', $request->email, $body);
                $subject = $template->subject;
                $data = array('template' => $body);
                // Mail::send('template', $data, function (
                //     $message
                // ) use ($email,$subject) {
                //     $message->to($email)->subject($subject);
                // });
                return response()->json(["success" => true, "message" => "School successfully created", "redirect" => url("/school-list")]);
            } else {
                return response()->json(["success" => false, "message" => "Something went wrong"]);
            }
        }
    }
    // ********Save-Platform-Schools********
}
