<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Attachment extends Model
{
    // protected $fillable = ['file_url', 'file_name', 'file_type'];
    protected $table = 'attachments';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'binary';
    protected $fillable = ['id'];

    public function chats()
    {
        return $this->belongsToMany(Chat::class, 'chat_attachments', 'attachmentId', 'chatId');
    }
}


