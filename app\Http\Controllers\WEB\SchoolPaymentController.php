<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{User, Programs, invite_programs, ProgramNote, ProgramNoteAmount, ProgramNoteStudent, MailModel,RosterModel,UserThirdStepModel,UserSecondStepModel,Reimbursement,Logistics};
use App\Helpers\DataTableHelper;
use App\Http\Requests\Front\ProgramClassRequest;
use Illuminate\Http\Request;
use App\Exports\Front\SchoolExportPayments;
use Excel;
use stdClass;
use Carbon\Carbon;

class SchoolPaymentController extends Controller
{

    public function index(Request $request)
    {

        $user = auth()->user();
        if (checkAuth($user->status)) {
            return redirect("/logout");
        }
        $totalAmount = ProgramNoteAmount::whereHas('program',function ($q)  {
            $q->where('school_name', auth()->user()->id);
        })->sum('amount');

        $paymentProgramData = ProgramNoteAmount::whereHas('program',function ($q)  {
            $q->where('school_name', auth()->user()->id);
        })->whereHas('user')->pluck('program_id','user_id')->toArray();

        $userIds =array_keys($paymentProgramData);
        $paymentProgramIds =array_values($paymentProgramData);

        $paymentPrograms = Programs::whereIn('id',$paymentProgramIds)->pluck('name','id');
        $paymentUsers = User::whereIn('id',$userIds)->get(['first_name','last_name','id']);
        
            return view('school.payment.summary',compact('totalAmount','paymentPrograms','paymentUsers'));
     
    }

    public function logistics(Request $request)
    {

        $user = auth()->user();
        if (checkAuth($user->status)) {
            return redirect("/logout");
        }
        $totalAmount = Reimbursement::whereHas('program',function ($q)  {
            $q->where('school_name', auth()->user()->id);
        })->sum('amount');
            return view('school.payment.logistics',compact('totalAmount'));
     
    }


    

    public function list(Request $request)
    {

      
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
      
        $qry = ProgramNoteAmount::whereHas('program',function ($q)  {
            $q->where('school_name', auth()->user()->id);
        });

        if ($request->program_id) {
          
            $qry->where('program_id', $request->program_id);
        }
        if ($request->user_id) {
          
            $qry->where('user_id', $request->user_id);
        }
        if ($request->daterange && strpos($request->daterange, ' TO ') !== false && strpos($request->daterange, ' date ') == false) {
            $separator = ' TO ';
            $dateRange = explode($separator, $request->daterange);
            $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
            $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

            $qry->whereHas('note', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('class_date', [$startDate, $endDate]);
            });
        }
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id','DESC')->get();
        $data = array();
        $i = 1;
        
        foreach ($result as $row) {
        
        
            $date = date('m-d-Y', strtotime(@$row->note->class_date));
            $start_time = date('h:i A', strtotime(@$row->note->start_time));
            $end_time = date('h:i A', strtotime(@$row->note->end_time));
            
            $encryptedId = encrypt($row->program_id);
            $detailUrl = route('school.program-detail', ['encryptedId' => $encryptedId]);
            $view = $row->program ?"<a class='idtextcolor' href='{$detailUrl}'>{$row->program->id}</a>":'NIl';

            $data[] = array(
        
                "program_id" => $view,
                "date" =>  $date?$date:'',
                "meeting_type" => @$row->format??'',

                "meeting_time" => $start_time.'-'.$end_time,                
                "hours" => $row->hours,
                "minutes" => $row->minutes,
                "rate" => '$'.$row->rate,
                "amount" => '$'.$row->amount,
    
            );
            
            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }
    public function logisticslist(Request $request)
    {
        $user = auth()->user();
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $qry = Logistics::with('program','school','subject','subsubject');
        $qry->where('school_id', auth()->user()->id);
           
    
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy('id','DESC')->get();
        $data = array();
        $i = 1;
        
        foreach ($result as $row) {
        
            $encryptedStrId = encrypt_str($row->id);
            $viewButton = "";
           
            $viewRoute =  url('school-program-detail/' . $encryptedStrId);

            $viewButton .= " <a href='{$viewRoute}'>{$row->name}</a>";
          
            $action = '';
            $getPath=$row->document?generateSignedUrl($row->document):'';
            $viewdocument = $row->document?"<a href='{$getPath}' target='_blank'>document</a>":'';
            if(strlen($row->description)>50)
            {
                $des=htmlentities($row->description);
                $description= "<p  data-toggle='tooltip' style='cursor: pointer;' title='{$des}'>".substr($row->description,0,50)."...</p>";
                
            }else{
                $description=$row->description;
            }
            $data[] = array(
                "id" => '',
                "program_id" => $viewButton,
                "program_type" => $row->program->program_type,
                "request_type" => $row->request_type,
                "title" => $row->title,
                "document"=>$viewdocument,
                "description"=>$description,
                "status" => $this->generateStatus($row->status),
                "created_at" => date('m-d-Y h:i A', strtotime($row->created_at)),
                "action" => $action,
    
            );
            
            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

 
    private function generateStatus($status)
    {
     
        switch ($status) {
            case 2:
                return 'Decline';
            case 1:
                return 'Accepted';
            default:
                return 'Pending';
        }
    }

    public function exportPayments(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Payments-' . time() . '.xlsx';
            // info($request->all());
            return Excel::download(new SchoolExportPayments($request), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
    
}
