<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{CancelledSub, CancelSubRequest, EmailTemplate, Programs, invite_programs, InviteProgramNote, Notification_content, ProgramNote, User};
use App\Helpers\CustomHelper;
use App\Helpers\DataTableHelper;
use App\Helpers\NotificationHelper;
use App\Http\Requests\Front\ProgramAssignRequest;
use Illuminate\Http\Request;
use stdClass;
use Carbon\Carbon;
use DateTime;
use Auth;

class ProgramInviteController extends Controller
{

    public function index(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $user = auth()->user();


        $qry = Programs::getListInvite($user,null, $request);
        $qry->active();
        $qry->with('userNotes');
        #$qry->wherePivot('deadline', '>=', now()->toDateTimeString());
        $qry->wherePivot('requested_by', '=', null);
        $qry->wherePivot('deadline', '!=', null);


        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);

        $data = array();
        $i = 1;

        if ($user->status == 1)
            foreach ($result as $row) {
                $programTimezone = $row->timezone ?? 'America/Los_Angeles';
                $deadlineTime = Carbon::createFromFormat('Y-m-d H:i:s', $row->pivot->deadline, $programTimezone);
                $now = Carbon::now()->timezone($row->pivot->timezone);
                $currentTime = Carbon::now($row->pivot->timezone);
                $javascriptVoid = "javascript:void(0);";
                $action = '<div class="new-action-btn">';
                if ($row->pivot->deadline && $currentTime->greaterThan($deadlineTime)) {
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Invite Expired'>Invite Expired</a>";
                } else {
                    $actionRoute = route('user.program-invites.change-status', ['id' => $row->pivot->id]);
                    if ($row->pivot->is_standby == '1') {
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Accept' onclick=\"showStandbyModal(1, '{$actionRoute}')\">Accept</a>";
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Decline' onclick=\"changeStatus(0, '{$actionRoute}')\">Decline</a>";
                    } elseif ($row->pivot->admin_type == '1') {
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Accept' onclick=\"showMainAcceptPopUp(1, '{$actionRoute}')\">Accept</a>";
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Decline' onclick=\"changeStatus(0, '{$actionRoute}')\">Decline</a>";
                    } elseif ($row->pivot->is_makeup == '1' && $row->pivot->is_standby == '1' && $row->pivot->admin_type == '0') {
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Accept' onclick=\"showStandbyModal(1, '{$actionRoute}')\">Accept</a>";
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Decline' onclick=\"changeStatus(0, '{$actionRoute}')\">Decline</a>";
                    } else {
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Accept' onclick=\"changeStatus(1, '{$actionRoute}')\">Accept</a>";
                        $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Decline' onclick=\"changeStatus(0, '{$actionRoute}')\">Decline</a>";

                    }
                }



                $action .= '</div>';
                $encryptedId = encrypt($row->id);



                $classsList = route('user.program-invites.view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);
                $classsMAINList = route('user.program-invites.view-main-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

                $classsListMakeup = route('user.program-invites.view-invite-main-user-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

                if ($row->pivot->is_makeup == 1 || $row->pivot->is_sub_only == 1) {
                    if ($row->pivot->is_standby == 1) {
                        $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
                        // $viewClass=getClassScheduleHtml($row->userNotes);
                    } else {

                        $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsListMakeup}')\">View Schedule</a>";
                    }
                } else {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
                    // $viewClass=getClassScheduleHtml($row->userNotes);
                }

                // #W334 - 2. Instructors end should have program timezone displayed for the invite

                $deadlinetime = route('user.program-invites.view-deadline', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

                 if($row->pivot->deadline){
                    $vieweye = "<a href='{$javascriptVoid}' class='ms-2' onclick=\"openEyeModal('{$deadlinetime}')\"><i class='fa fa-eye m-0' aria-hidden='true'></i></a>";
                }
                else{
                    $vieweye = "<a href='{$javascriptVoid}' class='ms-2'><i class='fa fa-eye m-0' aria-hidden='true'></i></a>";
                }


                //end



                $detailUrl = route('user.inviteprogram-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);

                if ($row->pivot->is_standby == '1') {
                    $admintype = 'StandBy';
                } else {
                    $admintype = getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type ?? $row->pivot->is_standby);
                }

                $data[] = array(
                    "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                    "school_name" => @$row->school->full_name,
                    "deadline" => !is_null($row->pivot->deadline) . $vieweye ? date('m-d-Y h:i A', strtotime(@$row->pivot->deadline)) . $vieweye : null,
                    "delivery_type" => $row->delivery_type,
                    "state" => $row->state,
                    "city" => $row->city,
                    "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                    "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                    // "schedule" => getClassScheduleHtml($row->userNotes), //
                    "schedule" => $viewClass, //
                    "name" => @$row->subSubject->name ?? '',
                    "grade" => $row->formatted_classes,
                    "program_type" => $row->program_type,
                    "admin_type" => $admintype,
                    "action" => $action,
                );

                $i++;
            }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function changeStatus($id, Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $invite = invite_programs::findOrFail($id);
        $programs = Programs::findOrFail($invite->program_id);
        $status = $request->status;

        if ($invite->admin_type == 1 && $status == 1 && $invite->is_standby == 0 && $invite->is_makeup == 0) {

            $errors = CustomHelper::checkInstructorBackground($programs, $invite->user_id, $invite->id);
            if (!empty($errors)) {
                return response()->json(['errors' => $errors], 400);
            }
        }
        if ($status != 0 && $invite->is_makeup != 1) {

            $errors = CustomHelper::checkInstructorSubClass($programs, $user_id, $id);

            if (!empty($errors)) {
                return response()->json(['errors' => $errors], 400);
            }
        }
        if ($status != 0 && $invite->is_makeup == 1) {

            $errors = CustomHelper::checkInstructorMakeupClass($invite->makeupNote, $user_id);

            if (!empty($errors)) {
                return response()->json(['errors' => $errors], 400);
            }
        }
        $message = '';
        $invite->status = $status;
        $invite->save();

        switch ($status) {
            case 0:
                $message = 'Declined';
                $invite->processDeclinedInvite();
                $this->declineNotification($invite);
                break;
            case 1:
                $message = 'Accepted';
                $this->processAcceptedStatus($invite);
                break;
            case 2:
                $message = 'Archived';
                $invite->processDeclinedInvite();
                break;
        }
        return response()->json(['status' => true, 'message' => $message . ' successfully']);
    }

    private function declineNotification($program)
    {


        $programs = Programs::find($program->program_id);
        if ($programs->school_name) {
            $school_name = institudeName($programs->school_name);
        } else {
            $school_name = '';
        }
        if ($program->admin_type == 1 && $program->is_standby == 0) {

            createAdminProgramCommanNotification($program->user_id, '', $programs, '11', 'Operations', 'Admin', $school_name);
        }

        if ($program->admin_type == 0 && $program->is_standby == 0) {

            createAdminProgramCommanNotification($program->user_id, '', $programs, '23', 'Operations', 'Admin', $school_name);
        }

        if ($program->is_standby == 1) {

            createAdminProgramCommanNotification($program->user_id, '', $programs, '17', 'Operations', 'Admin', $school_name);
        }
    }

    public function archivedList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $user = auth()->user();

        $programs = CustomHelper::getArchivedPrograms($user, $request);
        [$count, $result] = DataTableHelper::applyCustomPagination($programs, $row, $rowperpage);
        foreach ($result as $row) {
            $encryptedId = encrypt($row->id);

            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);

            $classsMAINList = route('user.program-invites.view-main-classes', ['program' => $row->id]);
            $javascriptVoid = "javascript:void(0);";
            $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";

            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                // "schedule" => getClassScheduleHtml($row->userNotes), //
                "schedule" => $viewClass, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
            );
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function getTabData(Request $request)
    {
        $tab = $request->tab;
        $events = [];
        $view = view("components.tabs.{$tab}", compact('events'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function getcalendarTabData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $tab = $request->tab;
        $userId = $user_id;
        $currentDate = now()->toDateString();


        switch ($tab) {

            case 'invites':


                $events = Programs::getEvents(['status' => null], $user)
                    ->active()
                    ->groupBy('tbl_invite_programs.program_id')
                    ->get();

                break;
            case 'stand-by':

                $events = Programs::getEvents(['status' => 1], $user)
                    ->active()
                    ->groupBy('tbl_invite_programs.program_id')
                    ->wherePivot('is_standby', 1)
                    ->where('end_date', '>=', $currentDate)
                    ->get();

                break;
            case 'new-program':
                $user = auth()->user();
                $events = CustomHelper::getUserAvailPrograms($user);

                break;

            case 'archived':
                $events = Programs::getEvents(['status' => [0, 2]], $user)
                    ->active()
                    ->with('userNotes')
                    // ->groupBy('tbl_invite_programs.program_id')
                    // ->orWhere('is_approved', 0)
                    ->get();

                break;

            case 'today-classes':

                $events = Programs::getEvents(['status' => 1], $user)
                    ->active()
                    ->wherePivot('is_approved', 1)
                    ->whereHas('userNotes', function ($query) use ($currentDate, $user_id) {
                        $query->where('class_date', $currentDate)
                            ->where(function ($q) use ($user_id) {
                                $q->where('user_id', $user_id)
                                    ->orWhere('sub_user_id', $user_id)
                                    ->where('tbl_invite_programs.has_requested', '!=', 2);
                            });
                    })
                    ->get();

                break;

            case 'in-progress':
                $events = Programs::getEvents(['status' => 1], $user)
                    ->active()
                    ->where('start_date', '<=', $currentDate)
                    ->where('end_date', '>=', $currentDate)
                    ->wherePivot('is_approved', 1)
                    ->with(['userNotes' => function ($q) use ($userId) {
                        $q->where('user_id', $userId)
                            ->orWhere('sub_user_id', $userId);
                    }])
                    ->get();



                break;


            case 'upcoming':
                $events = Programs::getEvents(['status' => 1], $user)
                    ->active()
                    ->where('start_date', '>', $currentDate)
                    ->wherePivot('is_approved', 1)
                    ->wherePivot('is_standby', '!=', 1)
                    ->with('userNotes')
                    ->where(function ($query) {
                        // $query->where('tbl_invite_programs.has_requested', '!=', 2);
                    })
                    // ->groupBy('tbl_invite_programs.program_id')
                    ->get();

                break;

            case 'completed':



                $events = Programs::getEvents(['status' => 1], $user)
                    ->where(function ($query) use ($currentDate) {
                        $query->where('program_status', 'Completed')
                            ->orWhere('end_date', '<', $currentDate);
                        // ->orWhere('tbl_invite_programs.has_requested', '=', 2);
                    })
                    ->wherePivot('is_standby', '!=', 1)
                    ->wherePivot('is_approved', 1)
                    ->with('userNotes')
                    // ->groupBy('tbl_invite_programs.program_id')

                    ->get();

                break;

            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }


        if ($user->status == 1 && $user->is_approved) {

            $events = CustomHelper::getCalendarEvents($events, $tab);
        } else {
            $events = [];
        }
        return response()->json($events);
    }

    public function getListData(Request $request)
    {
        $tab = $request->tab;

        switch ($tab) {

            case 'invites':
                $list = $this->index($request);
                break;
            case 'stand-by':
                $list = $this->standByList($request);
                break;
            case 'new-program':
                $list = $this->acceptedList($request);
                break;

            case 'archived':
                $list = $this->archivedList($request);
                break;

            case 'today-classes':
                $list = $this->todayList($request);
                break;

            case 'in-progress':
                $list = $this->inProgressList($request);
                break;

            case 'upcoming':
                $list = $this->upcomingList($request);
                break;

            case 'completed':
                $list = $this->completedList($request);
                break;



            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }


        return $list;
    }

    public function todayList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();
        $user = auth()->user();
        $user_id = $user->id;
        $qry = Programs::getList(auth()->user(),1, $request);
        $qry->active();
        $qry->with('userNotes');
        $qry->wherePivot('is_standby', '!=', 1);
        $qry->whereHas('userNotes', function ($query) use ($currentDate, $user_id) {
            $query->where('class_date', $currentDate)
                ->where(function ($q) use ($user_id) {
                    $q->where('user_id', $user_id)
                        ->orWhere('sub_user_id', $user_id);
                })->where(function ($q) {
                    // $q->where('tbl_invite_programs.has_requested', '!=', 2);
                });
        });

        $qry->wherePivot('is_approved', 1);

        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);
        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';


            $encryptedId = encrypt($row->id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";

            $classId = @$row->dateClassSchedule($user_id, $currentDate)->first()->id;
            if ($row->pivot->is_standby == '1') {
                $admintype = 'Sub';
            } else {
                $admintype = getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type ?? $row->pivot->is_standby);
            }
            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$classId}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "schedule" => getClassScheduleHtml($row->dateClassSchedule($user_id, $currentDate)->get()), //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "admintype" => $admintype,
                "action" => $action,
            );

            $i++;
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function inProgressList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();



        $maxDate = now()->toDateString();
        $user = auth()->user();
        $userId = $user->id;
        $qry = Programs::getList($user,1, $request)
            ->active()
            ->where('start_date', '<=', $currentDate)
            ->where('end_date', '>=', $currentDate)
            ->where(function ($query) {
                // $query->where('tbl_invite_programs.has_requested', '!=', 2);
            });
        $qry->wherePivot('is_standby', '!=', 1);
        $qry->wherePivot('is_approved', 1);


        $qry->whereHas('userNotes', function ($query) use ($maxDate, $userId) {
            $query->where(function ($q) use ($userId) {
                $q->where('user_id', $userId)
                    ->orWhere('sub_user_id', $userId);
            });


        });


        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);
        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';
            $javascriptVoid = "javascript:void(0);";
            $encryptedId = encrypt($row->id);


            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);
            if ($row->pivot->has_requested == '0') {

                $classes = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status', 'sub_user_id'])
                    ->exists();
                if ($classes) {
                    $subReplacement = route('user.program-invites.request-sub', ['program' => $row->id, 'replacement_type' => '0']); //sub
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Request Sub' onclick=\"openCommanModal('{$subReplacement}')\">Request Sub</a>";
                }
                $MainClasses = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status'])
                    ->exists();
                if ($MainClasses) {
                    $mainReplacement = route('user.program-invites.request-main', ['id' => $row->pivot->id, 'replacement_type' => $row->pivot->type]); //main

                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Request Replacement' onclick=\"openCommanModal('{$mainReplacement}')\">Request Replacement</a>";
                }
            } elseif ($row->pivot->has_requested == 1) {
                $classes = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status', 'sub_user_id'])
                    ->exists();
                if ($classes) {
                    $subReplacement = route('user.program-invites.request-sub', ['program' => $row->id, 'replacement_type' => '0']); //sub
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action mb-2' title='Request Sub' onclick=\"openCommanModal('{$subReplacement}')\">Request Sub</a>";
                }
                $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Requested Replacement'>Replacement Requested</a>";
            } elseif ($row->pivot->has_requested == 2) {
                $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Replacement Completed'>Replaced</a>";
            } elseif (!is_null($row->pivot->parent_id)) {
                $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            }
            if ($action == "") {
                $encryptedId = encrypt($row->id);
                $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);


                $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            }

            $classsMAINList = route('user.program-invites.view-main-user-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            $classsList = route('user.program-invites.view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            $MakuporSubclasssList = route('user.program-invites.makupsub-view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            if ($row->pivot->is_makeup == 1 || $row->pivot->is_sub_only == 1) {
                if ($row->pivot->is_standby == 1) {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";

                } else {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$MakuporSubclasssList}')\">View Schedule</a>";
                }
            } else {
                $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
            }



            if ($row->pivot->is_standby == '1') {
                $admintype = 'Sub';
            } else {
                $admintype = getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type ?? $row->pivot->is_standby);
            }

            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),

                "schedule" => $viewClass, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "admintype" => $admintype,
                "action" => $action,
            );

            $i++;
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function upcomingList(Request $request)
    {
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();
        $user = auth()->user();
        $userId = $user->id;
        $qry = Programs::getList($user,1, $request)
            ->active()
            ->where('start_date', '>', $currentDate)
            ->where(function ($query) {
                // $query->where('tbl_invite_programs.has_requested', '!=', 2);
            });
        $qry->wherePivot('is_standby', '!=', 1);
        $qry->wherePivot('is_approved', 1);
        $qry->with(['userNotes' => function ($q) use ($userId) {
            $q->where('user_id', $userId)
                ->orWhere('sub_user_id', $userId);
        }]);

        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);


        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';
            $javascriptVoid = "javascript:void(0);";

            $encryptedId = encrypt($row->id);


            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);
            if ($row->pivot->has_requested == '0') {

                $classes = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status', 'sub_user_id'])
                    ->exists();
                if ($classes) {
                    $subReplacement = route('user.program-invites.request-sub', ['program' => $row->id, 'replacement_type' => '0']); //sub
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Request Sub' onclick=\"openCommanModal('{$subReplacement}')\">Request Sub</a>";
                }
                $MainClasses = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status'])
                    ->exists();


                if ($MainClasses) {
                    $mainReplacement = route('user.program-invites.request-main', ['id' => $row->pivot->id, 'replacement_type' => $row->pivot->type]); //main

                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Request Replacement' onclick=\"openCommanModal('{$mainReplacement}')\">Request Replacement</a>";
                }
            } elseif ($row->pivot->has_requested == 1) {

                $classes = $row->userNotes()->where('class_date', '>=', $currentDate)
                    ->where('user_id', $user->id)
                    ->whereNull(['note', 'status', 'sub_user_id'])
                    ->exists();
                if ($classes) {
                    $subReplacement = route('user.program-invites.request-sub', ['program' => $row->id, 'replacement_type' => '0']); //sub
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action mb-2' title='Request Sub' onclick=\"openCommanModal('{$subReplacement}')\">Request Sub</a>";
                }
                $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Requested Replacement'>Replacement Requested</a>";
            } elseif ($row->pivot->has_requested == 2) {
                $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Replacement Completed'>Replaced</a>";
            } elseif (!is_null($row->pivot->parent_id)) {
                $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            }
            if ($action == "") {
                $encryptedId = encrypt($row->id);
                $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);


                $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            }

            $classsMAINList = route('user.program-invites.view-main-user-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            $classsList = route('user.program-invites.view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);
            $MakuporSubclasssList = route('user.program-invites.makupsub-view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);
            if ($row->pivot->is_makeup == 1 || $row->pivot->is_sub_only == 1) {
                if ($row->pivot->is_standby == 1) {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
                    // $viewClass=getClassScheduleHtml($row->userNotes);
                } else {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$MakuporSubclasssList}')\">View Schedule</a>";
                }
            } else {
                $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
            }

            if ($row->pivot->is_standby == '1') {
                $admintype = 'Sub';
            } else {
                $admintype = getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type ?? $row->pivot->is_standby);
            }



            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                "schedule" => getClassScheduleHtml($row->dateClassUniqueSchedule($user->id)->get()), //
                "schedule" =>  $viewClass, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "admintype" => $admintype,
                "action" => $action,
            );

            $i++;
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function completedList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();
        $user = auth()->user();
        $userId = $user->id;
        $qry = Programs::getList($user, 1, $request)
            ->where(function ($query) use ($currentDate) {
                $query->where('program_status', 'Completed')
                    ->orWhere('end_date', '<', $currentDate);
                // ->orWhere('tbl_invite_programs.has_requested', '=', 2);
            });
        $qry->wherePivot('is_standby', '!=', 1);
        $qry->wherePivot('is_approved', 1);
        // $qry->with('userNotes');
        // $qry->with(['userNotes'=>function($q)use($userId){
        //     $q->where('user_id',$userId)
        //     ->orWhere('sub_user_id',$userId);
        // }]);

        $qry->whereHas('userNotes', function ($query) use ($currentDate, $userId) {
            $query->where(function ($q) use ($userId) {
                $q->where('user_id', $userId)
                    ->orWhere('sub_user_id', $userId);
            });
            // ->whereRaw(
            //     '(SELECT max(class_date) FROM program_notes WHERE user_id = ?) < ?',
            //     [ $userId,$currentDate]
            // );

        });

        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);
        $data = array();
        $i = 1;

        $javascriptVoid = "javascript:void(0);";

        foreach ($result as $row) {

            $action = '';

            $encryptedId = encrypt($row->id);


            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            $classsMAINList = route('user.program-invites.view-main-user-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            $classsList = route('user.program-invites.view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            $MakuporSubclasssList = route('user.program-invites.makupsub-view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

            if ($row->pivot->is_makeup == 1 || $row->pivot->is_sub_only == 1) {
                if ($row->pivot->is_standby == 1) {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
                    // $viewClass=getClassScheduleHtml($row->userNotes);
                } else {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$MakuporSubclasssList}')\">View Schedule</a>";
                }
            } else {
                $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
            }

            if ($row->pivot->is_standby == '1') {
                $admintype = 'Sub';
            } else {
                $admintype = getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type ?? $row->pivot->is_standby);
            }
            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                // "schedule" => getClassScheduleHtml($row->dateClassUniqueSchedule($user->id)->get()), //
                "schedule" => $viewClass, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "admintype" => $admintype,
                "action" => $action,
            );

            $i++;
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function storeReplacement($id, Request $request)
    {

        $invite_program = invite_programs::findOrFail($id);
        $requested_by = auth()->user()->id;
        $replacement_type = $request->replacement_type;
        $program_id = $invite_program->program_id;
        $obj = new  invite_programs();
        $program_note_ids = $request->program_note_id;

        if ($replacement_type == 0) {
            $currentDate = now()->toDateString();

            $program_note_ids = ProgramNote::where(['program_id' => $program_id, 'sub_user_id' => $requested_by])
                ->where('class_date', '>=', $currentDate)
                ->whereNull(['note', 'status'])
                ->pluck('id')->toArray();
            if (empty($program_note_ids)) {
                return response()->json(['status' => false, 'message' => 'Replacement Request failed']);
            }
            $obj->type = 0;
            $obj->admin_type = 0;
            $obj->is_sub_only = 1;
        } else {
            $obj->type = $request->instructor_type;
        }

        $obj->requested_by = $requested_by;
        $obj->replacement_type = $replacement_type;
        $obj->program_id = $program_id;
        $obj->parent_id = $id;


        $obj->save();

        if ($obj) {
            $invite_program->has_requested = 1;
            $invite_program->save();
        }

        if ($request->filled('all_classes')) {
            return response()->json(['status' => true, 'message' => "Requested successfully"]);
        }

        if ($program_note_ids) {

            foreach ($program_note_ids as $program_note_id) {
                $inviteProgramNote = new InviteProgramNote();
                $inviteProgramNote->invite_program_id = $obj->id;
                $inviteProgramNote->program_note_id = $program_note_id;
                $inviteProgramNote->save();
            }
        }

        return response()->json(['status' => true, 'message' => "Requested successfully"]);
    }

    public function storeSubReplacement(Programs $program, Request $request)
    {

        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;

        if ($request->filled('all_classes')) {
            $program_note_ids = $program->userNotes()->where('class_date', '>=', $currentDate)->where('user_id', $user_id)->whereNull(['note', 'status'])->pluck('id')->toArray();
        } else {
            $program_note_ids = $request->program_note_id;
        }
        $program_notes = ProgramNote::whereIn('id', $program_note_ids)->get();
        // $program_note_ids =  ProgramNote::whereIn('id', $program_note_ids)->whereNull('sub_user_id')->whereIn('user_sub_requested', [0, 4])->pluck('id')->toArray();
        // if (empty($program_note_ids)) {
        //     return response()->json(['status' => false, 'message' => 'Already Requested this class']);
        // }
        $obj = new  invite_programs();
        $obj->requested_by = $user_id;
        $obj->replacement_type = 0;
        $obj->program_id = $program->id;
        $obj->admin_type = 0;
        $obj->is_sub_only = 1;
        $obj->type = 0;

        $obj->save();


        ProgramNote::whereIn('id', $program_note_ids)->update(['user_sub_requested' => 1]);

        foreach ($program_note_ids as $program_note_id) {

            $inviteProgramNote = new InviteProgramNote();
            $inviteProgramNote->invite_program_id = $obj->id;
            $inviteProgramNote->program_note_id = $program_note_id;
            $inviteProgramNote->save();
        }






        $sub_subject = $program->subSubject->name;
        $subject = "Request Sub";
        $programName = $program->name;
        $school = $program->school;
        $schoolName = @$school->full_name ?? '';
        $notificationContent = Notification_content::where("signature", "request-sub")->first();
        $notificationTemplate = str_replace(['{{schoolName}}', '{{programName}}'], [$schoolName, $programName], @$notificationContent->content ?? '');

        if($user_id == $program_notes[0]['sub_user_id']){
            $user =  User::where('id',$program_notes[0]['sub_user_id'])->first();
            sendNotificationmainorSubWantSubstitue($program,$user,$schoolName,$program_notes,$sub_subject);
        }
        else{
            $user =  User::where('id',$program_notes[0]['user_id'])->first();
            sendNotificationmainorSubWantSubstitue($program,$user,$schoolName,$program_notes,$sub_subject);
        }

        $type = 'request-sub';

        $template = EmailTemplate::find(26);
        $recruiter = getOwnerDeatils($program->id, 'Recruiter');
        if ($recruiter) {

            NotificationHelper::sendProgramNotification($recruiter, $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);
        }

        NotificationHelper::sendProgramNotification(User::firstWhere('type',1), $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);

        return response()->json(['status' => true, 'message' => "Requested successfully"]);
    }

    public function getSubRequestForm(Programs $program, Request $request)
    {
        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;

        $classes = $program->userNotes()->where('class_date', '>=', $currentDate)->where('user_id', $user_id)->whereNull(['note', 'status', 'sub_user_id'])->get();
        $view = view("components.modals.sub-replacement", compact('program', 'classes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function acceptedList(Request $request)
    {
        $user = auth()->user();
        $programs = CustomHelper::getUserAvailPrograms($user, $request);


        return $this->getAutoApplyList($request, $programs, $user);
        /*             if (!empty($programs)) {
        } else {
            return $this->getAutoAppliedList($request,$user);
        } */
    }

    private function getAutoApplyList(Request $request, $programs, $user)
    {
        $data = array();

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        if ($user->status == 1 && $user->is_approved) {
            [$count, $result] = DataTableHelper::applyCustomPagination($programs, $row, $rowperpage);
        } else {
            return DataTableHelper::generateResponse($draw, 0, $data);
        }


        $i = 1;


        foreach ($result as $row) {
            $program = Programs::findOrFail($row->id);
            $errors = CustomHelper::checkInstructorBackground($program, $user->id);

            if (!empty($errors)) {
            } else {
                $action = '<div class="new-action-btn">';

                $actionRoute = route('user.new-program-alert.decline', ['program' => $row->id]);
                $applyRoute = route('user.program.get-apply-form', ['id' => $row->id]);
                $javascriptVoid = "javascript:void(0);";
                $alreadyInvited = invite_programs::where([
                    'program_id' => $row->id,
                    'user_id' => $user->id,
                ])->exists();
                if (!$alreadyInvited) {
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Apply' onclick=\"openCommanModal('{$applyRoute}')\">Apply</a>";
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action Request' title='Decline' onclick=\"changeStatus(2, '{$actionRoute}')\">Decline</a>";
                } else {
                    $action .= "<a href='{$javascriptVoid}' class='default__button text-right action' title='Applied'>Pending</a>";
                }



                $action .= '</div>';
                $encryptedId = encrypt($row->id);

                $classsMAINList = route('user.program-invites.view-automain-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id]);

                $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";


                $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId]);
                $data[] = array(
                    "id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                    "school_name" => @$row->school->full_name,
                    "delivery_type" => $row->delivery_type,
                    "state" => $row->state,
                    "city" => $row->city,
                    "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                    "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                    // "schedule" => getClassScheduleHtml($row->userNotes), //
                    "schedule" => $viewClass, //
                    "name" => @$row->subSubject->name ?? '',
                    "grade" => $row->formatted_classes,
                    "program_type" => $row->program_type,
                    "action" => $action,
                );

                $i++;
            }
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }


    public function getApplyForm($id, Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $program = Programs::with('userNotes')->findOrFail($id);
        $userNotes = $program->userNotes;

        $errors = CustomHelper::checkInstructorBackground($program, $user_id);

        if (!empty($errors)) {
            return response()->json(['errors' => $errors], 400);
        }

        $view = view("components.modals.apply-for-program", compact('id', 'userNotes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeApplyForm($id,  ProgramAssignRequest $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $program = Programs::findOrFail($id);


        $alreadyInvited = invite_programs::where([
            'program_id' => $id,
            'user_id' => $user->id,
        ])->exists();

        if ($alreadyInvited) {

            return response()->json(['errors' => ['Already applied']], 400);
        }

        $errors = CustomHelper::checkInstructorBackground($program, $user_id);

        if (!empty($errors)) {
            return response()->json(['errors' => $errors], 400);
        }

        $obj = invite_programs::create([
            'program_id' => $id,
            'user_id' => $user->id,
            'deadline' =>  null,
            'admin_type' => null,
            'status' => 1,
            'is_auto_invite' => 1,
            'type' => 1,
            // 'type' => $request->instructor_type,
        ]);

        $subject = "Applied Request";
        $programName = $program->name;
        $school = $program->school;
        // $schoolName = @$school->full_name ?? '';
        $schoolName = schoolusername($program->school_name) ?? '';
        $subsubject = subsubjectname($program->sub_subject_id) ?? '';
        $instructorname = $user->first_name . ' ' . $user->last_name ?? '';
        $notificationContent = Notification_content::where("signature", "applied-request")->first();
        $notificationTemplate = str_replace(['{{schoolName}}', '{{programName}}','{{subsubject}}','{{instructorname}}'], [$schoolName, $programName,$subsubject,$instructorname], @$notificationContent->content ?? '');

        $type = 'applied-request';

        $template = EmailTemplate::find(26);
        $recruiter = getOwnerDeatils($program->id, 'Recruiter');
        if ($recruiter) {

            NotificationHelper::sendProgramNotification($recruiter, $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);
        }
        NotificationHelper::sendProgramNotification(User::firstWhere('type',1), $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);

        /*if ($request->filled('all_classes')) {
            return response()->json(['status' => true, 'message' => "Applied successfully"]);
        }

        $program_note_ids = $request->program_note_id;
        if (empty($program_note_ids)) {
            return response()->json(['status' => true, 'message' => "Applied successfully"]);
        }
        foreach ($program_note_ids as $program_note_id) {
            $inviteProgramNote = new InviteProgramNote();
            $inviteProgramNote->invite_program_id = $obj->id;
            $inviteProgramNote->program_note_id = $program_note_id;
            $inviteProgramNote->save();
        }*/
        return response()->json(['status' => true, 'message' => "Applied successfully"]);
    }

    public function declineNewAlert(Programs $program, Request $request)
    {
        $program_id = $program->id;
        $user = auth()->user();
        $user_id = $user->id;
        $obj = invite_programs::create([
            'program_id' => $program_id,
            'user_id' => $user_id,
            'deadline' =>  null,
            'admin_type' => null,
            'status' => 0,
            'is_auto_invite' => 1,
            'type' => null,
        ]);

        $message = 'Declined';

        return response()->json(['status' => true, 'message' => $message . ' successfully']);
    }


    public function cancelSub(Programs $program, Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $currentDate = now()->toDateString();

        $programNoteIds = $request->program_note_id;

        if (count($programNoteIds) ==2) {
            return response()->json(['status' => false, 'message' => 'The system cannot allow multiple selections for cancel sub request']);
        }

        $program_note_ids =  ProgramNote::
             whereIn('user_sub_requested', [1])
            ->where('class_date', '>=', $currentDate)
            ->whereIn('id', $programNoteIds)
            ->whereNull(['note', 'status'])
            ->pluck('id')->toArray();

            $program_note_sub_user_ids =  ProgramNote::
            whereIn('user_sub_requested', [0,1])
           ->where('class_date', '>=', $currentDate)
           ->whereIn('id', $programNoteIds)
           ->whereNull(['note', 'status'])
           ->whereNotNull('sub_user_id')
           ->pluck('sub_user_id')->toArray();


        if (empty($program_note_ids) && empty($program_note_sub_user_ids)) {
            return response()->json(['status' => false, 'message' => 'Substitute not requested for this class.']);
        }


        $cancelSubRequest = CancelSubRequest::create([
            'user_id' => $user_id,
            'program_id' => $program->id,
        ]);

        foreach ($programNoteIds as $programNoteId) {
            $programNote = ProgramNote::find($programNoteId);

            if ($programNote) {
                $programNote->update(['user_sub_requested' => 2]);

                CancelledSub::create([
                    'request_id' => $cancelSubRequest->id,
                    'program_note_id' => $programNoteId,
                    'user_id' => $programNote->sub_user_id,
                ]);
            }
        }

        return response()->json(['status' => true, 'message' => 'Requested successfully']);
    }

    public function standByList(Request $request)
    {
        $currentDate = now()->toDateString();

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $user = auth()->user();

        $user_id = $user->id;
        $qry = Programs::getList($user, 1, $request);
        $qry->with('userNotes');
        $qry->wherePivot('is_standby', '=', 1)
            ->where('end_date', '>=', $currentDate);


        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy("tbl_programs.id", "desc"), $row, $rowperpage);

        $data = array();
        $i = 1;

        foreach ($result as $row) {

            $javascriptVoid = "javascript:void(0);";


            $encryptedId = encrypt($row->id);
            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);


            $action = "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";


            $encryptedId = encrypt($row->id);


            $detailUrl = route('user.program-detail', ['encryptedId' => $encryptedId, 'pivot_id' => @$row->pivot->id]);

            $classsMAINList = route('user.program-invites.view-main-user-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id, 'admin_type' => 'StandBy']);

            $classsList = route('user.program-invites.view-classes', ['program' => $row->id, 'pivot_id' => @$row->pivot->id, 'admin_type' => 'StandBy']);

            if ($row->pivot->is_makeup == 1 || $row->pivot->is_sub_only == 1) {
                if ($row->pivot->is_standby == 1) {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
                    // $viewClass=getClassScheduleHtml($row->userNotes);
                } else {
                    $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsList}')\">View Schedule</a>";
                }
            } else {
                $viewClass = "<a href='{$javascriptVoid}' class='default__button text-right action' title='View Schedule' onclick=\"openCommanModal('{$classsMAINList}')\">View Schedule</a>";
            }


            $data[] = array(
                "program_id" => "<a href='{$detailUrl}'>{$row->id}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                // "schedule" => getClassScheduleHtml($row->userNotes), //
                "schedule" =>  $viewClass, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                // "admin_type" => getInstructorType($row->pivot->type ?? $row->pivot->admin_type ?? $row->pivot->replacement_type),
                "admin_type" => 'StandBy',
                "action" => $action,
            );

            $i++;
        }


        return DataTableHelper::generateResponse($draw, $count, $data);
    }

    public function viewclasses(Programs $program, Request $request)
    {

        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;

        // pivot_id
        $invite = invite_programs::with('notes.programNote')->findOrFail($request->pivot_id);
        $notes = $invite->notes;
        $subClasses = new stdClass;
        if ($notes->isEmpty()) {

            $subClasses = $program
                ->userNotes()
                ->where('class_date', '>=', $currentDate)
                ->where('sub_user_id', $user_id)
                ->whereNull(['note', 'status'])
                ->get();
        }

        $classes = $program
            ->userNotes()
            ->where('class_date', '>=', $currentDate)
            ->where('user_id', $user_id)
            ->whereNull(['note', 'status'])
            ->get();

        $view = view("components.modals.viewclasses", compact('program', 'classes', 'notes', 'invite', 'subClasses'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function makupsubviewclasses(Programs $program, Request $request)
    {

        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;

        $classes = $program
            ->userNotes()
            // ->where('class_date', '>=', $currentDate)
            ->where('user_id', $user_id)
            ->orWhere('sub_user_id', $user_id)
            ->where('program_id', $program->id)
            ->whereNull(['note', 'status'])
            ->get();

        $view = view("components.modals.makeupsubviewclasses", compact('program', 'classes', 'user_id'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function viewinviteMainUserclasses(Programs $program, Request $request)
    {

        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;

        // pivot_id
        $invite = invite_programs::with('notes.programNote')->findOrFail($request->pivot_id);
        $notes = $invite->notes;
        $subClasses = new stdClass;
        if ($notes->isEmpty()) {

            $subClasses = $program
                ->userNotes()
                ->where('class_date', '>=', $currentDate)
                // ->where('sub_user_id', $user_id)
                ->whereNull(['note', 'status'])
                ->get();
        }

        $classes = $program
            ->userNotes()
            ->where('class_date', '>=', $currentDate)
            // ->where('user_id', $user_id)
            ->whereNull(['note', 'status'])
            ->get();

        $view = view("components.modals.viewclasses", compact('program', 'classes', 'notes', 'invite', 'subClasses'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    public function viewautoMainclasses(Programs $program, Request $request)
    {

        $currentDate = now()->toDateString();

        $user = auth()->user();
        $user_id = $user->id;


        $classes = $program->userNotes()->where('class_date', '>=', $currentDate)->whereNull(['note', 'status', 'sub_user_id'])->get();

        $view = view("components.modals.viewMainclasses", compact('program', 'classes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function viewMainclasses(Programs $program, Request $request)
    {
        $invite = invite_programs::with('notes.programNote')->findOrFail($request->pivot_id);
        $replacement_start_date = $invite->replacement_start_date;

        if ($replacement_start_date) {
            $currentDate = $replacement_start_date;
        } else {
            $currentDate = now()->toDateString();
        }


        $user = auth()->user();
        $user_id = $user->id;


        $classes = $program->userNotes()->where('class_date', '>=', $currentDate)->whereNull(['note', 'status', 'sub_user_id'])->get();

        $view = view("components.modals.viewMainclasses", compact('program', 'classes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    // #W334 - 2. Instructors end should have program timezone displayed for the invite
     public function viewDeadlineWithTimezone(Programs $program, Request $request)
    {
        $timezone = $program->timezone;
        $view = view("components.modals.viewDeadline" , compact('timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    //end

    public function viewMainUserclasses(Programs $program, Request $request)
    {


        $invite = invite_programs::with('notes.programNote')->findOrFail($request->pivot_id);
        $replacement_start_date = $invite->replacement_start_date;

        if ($replacement_start_date) {
            $currentDate = $replacement_start_date;
        } else {
            $currentDate = now()->toDateString();
        }

        $user = auth()->user();
        $user_id = $user->id;

        if ($request->admin_type == 'StandBy') {
            $classes = $program->userNotes()->where('class_date', '>=', $currentDate)->whereNull(['note', 'status', 'sub_user_id'])->get();
        } else {
            // $classes = $program->userNotes()->where('class_date', '>=', $currentDate)->where('user_id', $user_id)->whereNull(['note', 'status', 'sub_user_id'])->get();

            $classes = $program
                ->userNotes()
                // ->where('class_date', '>=', $currentDate)
                ->where('user_id', $user_id)
                ->orWhere('sub_user_id', $user_id)
                ->where('program_id', $program->id)
                ->whereNull(['note', 'status'])
                ->get();
        }



        $view = view("components.modals.makeupsubviewclasses", compact('program', 'classes', 'user_id'))->render();


        //   $view = view("components.modals.viewMainUserclasses", compact('program', 'classes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getMainRequestForm($id, Request $request)
    {
        $user = auth()->user();
        $userId = $user->id;
        $invite_program = invite_programs::with('program:id,end_date')->findOrFail($id);
        $program_id = $invite_program->program_id;

        $minClassDate = getMinProgramClassDate($user->id, $invite_program->program_id);
        $maxClassDate = getMaxProgramClassDate($userId, $program_id);
        $minDate = $minClassDate ? $minClassDate->format('m/d/Y') : null;
        $maxDate = $maxClassDate ? $maxClassDate->format('m/d/Y') : null;
        $view = view("components.modals.main-replacement", compact('invite_program', 'minDate', 'maxDate'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeMainReplacement($id, Request $request)
    {
        $request->validate([
            'replacement_start_date' => 'bail|required|date_format:m/d/Y|after_or_equal:today'
        ]);

        $invite_program = invite_programs::with('program')->findOrFail($id);
        $program = $invite_program->program;
        $requested_by = auth()->user()->id;
        $replacement_type = $request->replacement_type;
        $program_id = $invite_program->program_id;

        $obj = new  invite_programs();
        $obj->type = $request->instructor_type;
        $obj->requested_by = $requested_by;
        $obj->replacement_type = $replacement_type;
        $obj->replacement_start_date = $request->replacement_start_date;
        $obj->program_id = $program_id;
        $obj->parent_id = $id;
        $obj->save();

        if ($obj) {
            $invite_program->has_requested = 1;
            $invite_program->save();
        }
        $user_id_main = Auth::user();


        $subject = "Request Replacement";
        $programName = $program->name;
        $school = $program->school;
        $schoolName = @$school->full_name ?? '';
        $notificationContent = Notification_content::where("signature", "request-replacement")->first();
        $notificationTemplate = str_replace(['{{schoolName}}', '{{programName}}'], [$schoolName, $programName], @$notificationContent->content ?? '');

        $type = 'request-replacement';

        $template = EmailTemplate::find(26);
        $recruiter = getOwnerDeatils($program->id, 'Recruiter');
        if ($recruiter) {

            NotificationHelper::sendProgramNotification($recruiter, $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);
        }
        NotificationHelper::sendProgramNotification(User::firstWhere('type',1), $template, $program->id, $schoolName, $notificationTemplate, $subject, $type);
        sendNotificationToMainRequestForReplacment($program,$user_id_main,$schoolName,$request->replacement_start_date);

        return response()->json(['status' => true, 'message' => "Requested successfully"]);
    }

    private function processAcceptedStatus($invite)
    {
        $parent = $invite->parent;

        if ($invite->is_standby == 0) {
            if (@$invite->parent->parent_id) {

                $parent->has_requested = 2;
                $parent->save();

                // invite_programs::where('id', $invite->parent->parent_id)->update(['has_requested' => 2]);
            }
            if ($invite->is_makeup != 1) {

                // invite_programs::where('id', $invite->parent_id)->update(['has_requested' => 2]);

                if ($parent) {
                    $parent->has_requested = 2;
                    $parent->save();
                    $requested_by = $parent->requested_by;
                    $invite->type = $invite->admin_type;
                    $invite->is_approved = 1;
                    if (!is_null($parent->user_id)) {
                        $invite->is_standby = 1;
                    } else {
                        $parent->has_requested = 2;
                        $parent->user_id = $invite->user_id;
                        $parent->save();
                        // invite_programs::where('id', $invite->parent_id)->update(['user_id' => $invite->user_id, 'has_requested' => 2]);
                        if (!empty($invite->replacement_start_date)) {

                            if ($invite->admin_type == 1) {
                                $programNoteQuery1 = ProgramNote::where('user_id', $requested_by)
                                    ->where('class_date', '>=', $invite->replacement_start_date->format('Y-m-d'))
                                    ->whereNull(['note', 'status', 'sub_user_id']);
                                $programNoteQuery1
                                    ->update(['user_id' => $invite->user_id]);
                            } else if ($invite->admin_type == 0) {

                                $programNoteQuery1 = ProgramNote::where('class_date', '>=', $invite->replacement_start_date->format('Y-m-d'))
                                    ->whereNull(['note', 'status']);
                                $programNoteQuery1
                                    ->update(['sub_user_id' => $invite->user_id, 'user_sub_requested' => 0]);
                            }
                        } elseif ($parent  && $parent->notes) {
                            $noteIds = $parent->notes->pluck('program_note_id')->toArray();

                            if ($invite->admin_type == 1) {

                                $programNoteQuery1 = ProgramNote::whereIn('id', $noteIds)->whereNull(['note', 'status']);
                                $programNoteQuery1->update(['user_id' => $invite->user_id]);
                            } else if ($invite->admin_type == 0) {

                                $programNoteQuery1 = ProgramNote::whereIn('id', $noteIds)->whereNull(['note', 'status']);
                                $programNoteQuery1->update(['sub_user_id' => $invite->user_id, 'user_sub_requested' => 0]);
                            }
                        }
                    }

                    $invite->save();
                } else if (!is_null($invite->parent_id)) {

                    $invite->type = $invite->admin_type;
                    $invite->is_approved = 1;
                    $invite->save();

                    $orgProgram = Programs::findOrFail($invite->program_id);

                    $orgProgram->save();

                    $orgProgram->createNotes($invite->user_id, $invite);
                } else if ($invite->admin_type == '1') {

                    $invite->type = $invite->admin_type;
                    $invite->is_approved = 1;
                    $invite->save();
                    $orgProgram = Programs::findOrFail($invite->program_id);

                    $orgProgram->save();
                    $orgProgram->createNotes($invite->user_id, $invite);
                } else if ($invite->admin_type == '0') {
                    $invite->type = $invite->admin_type;
                    $invite->is_approved = 1;
                    $invite->save();
                    $orgProgram = Programs::findOrFail($invite->program_id);

                    $orgProgram->save();
                    $orgProgram->createNotes($invite->user_id, $invite);
                }
            }
        }

        if ($invite->is_makeup == 1) {

            $mnoteIds = $invite->notes->pluck('program_note_id')->toArray();


            $invite->type = $invite->admin_type;
            $invite->status = 1;
            $invite->is_approved = 1;
            $invite->save();
            $programNoteQuery1 = ProgramNote::whereIn('id', $mnoteIds)->whereNull(['note', 'status']);
            $programNoteQuery1->update(['user_id' => $invite->user_id]);
            invite_programs::where('program_id', $invite->program_id)->whereNull(['status', 'type', 'is_approved'])
                ->update(['admin_type' => 0, 'is_standby' => 1]);
        } else {
            $mnoteIds = $invite->notes->pluck('program_note_id')->toArray();
            $programNoteQuery1 = ProgramNote::whereNull(['note', 'status']);
            $mainInstructor = invite_programs::where('program_id', $invite->program_id)->where('admin_type', 1)->where('status',1)->get();
            if(count($mainInstructor) > 0 && $invite->is_replace == 0){
                $stdata['is_standby'] = 1;
                $inviteList = invite_programs::where('program_id', $invite->program_id)->where('status', null)->where('is_makeup', 0)->where('is_sub_only', 0)->where('is_standby', 0)->where('id', '!=', $invite->id)->update($stdata);
            }




            $programs = Programs::find($invite->program_id);
            if ($programs->school_name) {
                $school_name = institudeName($programs->school_name);
            } else {
                $school_name = '';
            }

            $programNoteQuery1->where('program_id', $invite->program_id);
            $current_user_id = Auth::user()->id;
				$classes = ($programNoteQuery1->where('sub_user_id',$current_user_id)->pluck('class_date')->toArray());
                // dd($classes,$programs->userNotesWhioutMakupdesc->user_id);
            if ($invite->admin_type == 1 && $invite->is_standby == 0) {
                if($programs->delivery_type == 'In-Person'){
                    createProgramCommanNotification($invite->user_id, '', $programs, '24', 'user', 'user', $school_name, $classes);
                } else {
                    createProgramCommanNotification($invite->user_id, '', $programs, '79', 'user', 'user', $school_name, $classes);
                }
                createAdminProgramCommanNotification($invite->user_id, '', $programs, '25', 'Operations', 'Admin', $school_name);


                if ($programs->created_by && $programs->created_by != 1) {
                    createAdminProgramOperationsNotification($invite->user_id, $programs->created_by, '', $programs, '25', 'Operations', 'Operations', $school_name);

                }
            }

            if ($invite->admin_type == 0 && $invite->is_standby == 0) {
                createProgramCommanNotification($invite->user_id, '', $programs, '21', 'user', 'user', $school_name, $classes);
                createAdminProgramCommanNotification($invite->user_id, '', $programs, '22', 'Operations', 'Admin', $school_name);
                sendMainIntructorNotification($programs ,$programs->userNotesWhioutMakupdesc->user_id, '73', 'Main-Instructor', $school_name,$classes);
                if ($programs->created_by && $programs->created_by != 1) {
                    createAdminProgramOperationsNotification($invite->user_id, $programs->created_by, '', $programs, '22', 'Operations', 'Operations', $school_name);
                    sendMainIntructorNotification($programs ,$programs->userNotesWhioutMakupdesc->user_id, '73', 'Main-Instructor', $school_name,$classes);

                }
            }

            if ($invite->is_standby == 1) {
                createProgramCommanNotification($invite->user_id, '', $programs, '15', 'user', 'user', $school_name, $classes);
                createAdminProgramCommanNotification($invite->user_id, '', $programs, '16', 'Operations', 'Admin', $school_name);

                if ($programs->created_by && $programs->created_by != 1) {
                    createAdminProgramOperationsNotification($invite->user_id, $programs->created_by, '', $programs, '16', 'Operations', 'Operations', $school_name);
                }
            }
        }
    }
}
