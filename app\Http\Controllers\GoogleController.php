<?php
  
namespace App\Http\Controllers;
use Session;
use Illuminate\Http\Request;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Exception;
use App\Users;
use App\User;
use App\InvitationModel;
use App\invite_application_recruiter;
use Auth;
use Mail;
use DB;
class GoogleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }
          
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
        
            $user = Socialite::driver('google')->user();

          
            $finduser = Users::where('social_id', $user->id)->first();
         
            if($finduser){
                Session::forget('schoolloginsession');
                   $user = User::where("id",$finduser['id'])->first();
           
                   Auth::login($user);
           
                   if (Auth::user()) {
                           $request
                               ->session()
                               ->put("userewlogin", [
                                   "id" => Auth::user()->id,
                                   "name" => Auth::user()->first_name,
                                   "email" => Auth::user()->email,
                                   "type" => Auth::user()->type,
                                   'social_type' => 'Google',
                                   'status'=>'1',
                               ]);
                       }
                return redirect("/onboarding-step/" . encrypt($finduser["id"]));
           
         
            }else{
                Session::forget('schoolloginsession');
                $newUser = Users::updateOrCreate(['email' => $user->email],[
                        'first_name' => $user->name,
                        'last_name' => $user->family_name,
            
                        'social_id'=> $user->id,
                        'email_verify_status'=>'1',
                        'type'=>'5',
                        'status'=>'1',
                        'email_verify_time'=>date("Y-m-d H:i:s"),
                        'social_type' => 'Google'
                    ]);

                 
                   
            
                $email=   $newUser['email'];
                $template1 = DB::table("tbl_email_templates")->where("email_template_id", "16")->first();
                $body1 =  $template1->description;
                $body1= str_replace('{{NAME}}', 'Whizara team', $body1);
                $body1= str_replace('{{UserName}}', $newUser['first_name'], $body1);
                $body1= str_replace('{{userEmail}}', $email, $body1);
    
              
                $subject1=$template1->subject;
                $data1=array('template'=>$body1);
                $email1='<EMAIL>';
            
                Mail::send('template', $data1, function (
                    $message
                ) use ($email1,$subject1) {
                    $message->to($email1)->subject($subject1);
                });


                $user = User::where("id",$newUser['id'])->first();
           
                Auth::login($user);
        
                if (Auth::user()) {


                    $invitation = InvitationModel::where("email", $email)->first();

                    if($invitation){
        
                    $datai["application_id"]=$newUser['id'];
                    $datai["user_id"]=$invitation->created_by;
                    $datai["type"]='Recruiter';
                    $datai["status"]='1';
                    invite_application_recruiter::insertGetId($datai);
            
                    }


                        $request
                            ->session()
                            ->put("userewlogin", [
                                "id" => Auth::user()->id,
                                "name" => Auth::user()->first_name,
                                "email" => Auth::user()->email,
                                "type" => Auth::user()->type,
                                'social_type' => 'Google',
                                'status'=>'1',
                            ]);
                    }

                return redirect("/onboarding-step/" . encrypt($newUser["id"]));
               
            }
        
        } catch (Exception $e) {
            dd($e->getMessage());
        }
    }
}