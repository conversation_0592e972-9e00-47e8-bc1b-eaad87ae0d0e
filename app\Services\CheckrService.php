<?php 

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
class CheckrService
{
    protected $client;
    protected $apiKey;
    protected $apiSecret;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => config('services.checkr.api_url'),
        ]);

      
        $this->apiSecret = config('services.checkr.api_secret');
    
    }

    public function createCandidates($data)
    {
        try {
        $response = $this->client->post('candidates', [
            'auth' => [$this->apiSecret,''],
            'json' => $data,
        ]);

        return json_decode($response->getBody(), true);
        
    } catch (Exception $e) {
        // Handle exceptions (e.g., connection error, HTTP error)
        echo 'Error: ' . $e->getMessage();
        }
    }

    public function createInvitations($data)
    {
        try {
        $response = $this->client->post('invitations', [
            'auth' => [$this->apiSecret,''],
            'json' => $data,
        ]);

        return json_decode($response->getBody(), true);
    } catch (Exception $e) {
        // Handle exceptions (e.g., connection error, HTTP error)
        echo 'Error: ' . $e->getMessage();
        }
    }

    public function getInvitations($id)
    {

        try {
          
            $response = $this->client->get('invitations/'.$id, [
                'auth' => [$this->apiSecret,'']
            ]);
        
          return  $responseData = json_decode($response->getBody(), true);
           
        } catch (ClientException $e) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
        
            // Check if response content is JSON and decode it
            $errorData = json_decode($body, true);
            $errorMessage = isset($errorData['error']) ? $errorData['error'] : 'Unknown error';
        
            echo 'Canceled';
        } catch (Exception $e) {
            echo 'Error: ' . $e->getMessage();
        }
       
    }

    public function getreportDetails($id)
    {
        try {
        $response = $this->client->get('reports/'.$id, [
            'auth' => [$this->apiSecret,'']
        ]);

        return json_decode($response->getBody(), true);
    } catch (ClientException $e) {
        $response = $e->getResponse();
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
    
        // Check if response content is JSON and decode it
        $errorData = json_decode($body, true);
        $errorMessage = isset($errorData['error']) ? $errorData['error'] : 'Unknown error';
    
        echo 'Canceled';
    } catch (Exception $e) {
        echo 'Error: ' . $e->getMessage();
    }
    }

    
   
}
