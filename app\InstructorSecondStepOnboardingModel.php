<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class InstructorSecondStepOnboardingModel extends Model
{
    protected $table = 'onboarding_instructor_experiences';

    protected $fillable = [
        'user_id', 'certification', 'profile_type', 'specify', 'teaching_certification_year', 'teaching_certification_states', 'certified_special_education', 'teaching_since', 'experience_teaching_ages', 'highest_level_of_education', 'month_and_year_graduation', 'GPA', 'certified_other', 'resume', 'other_degree', 'tools'
    ];

    public function education()
    {
        return $this->hasMany(InstructorOnboardingCertification::class, 'step_id');
    }

    public function teching()
    {
        return $this->hasMany(InstructorExperienceTeaching::class, 'step_id');
    }

    public function otherExper()
    {
        return $this->hasMany(InstructorOtherExperienceOnboardingModel::class, 'step_id');
    }
    
    public function references()
    {
        return $this->hasMany(InstructorOnboardingReferences::class, 'step_id');
    }
}