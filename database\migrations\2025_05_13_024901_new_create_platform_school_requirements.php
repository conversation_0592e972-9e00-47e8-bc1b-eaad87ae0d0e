<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class NewCreatePlatformSchoolRequirements extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
     Schema::create('platform_school_requirements', function (Blueprint $table) {

        $table->id();
        $table->enum('status', ['draft', 'open', 'filled', 'closed', 'completed'])->default('draft')->comment('The current status of the requirement');
        $table->enum('requirement_type', ['teaching', 'non-teaching'])
                ->comment('Type of requirement, either teaching or non-teaching');
        $table->string('requirement_name', 100)
                ->comment('The name of the requirement');
        
        $table->unsignedBigInteger('school_id')
                ->comment('Foreign key referencing users table');   
         
        $table->string('class_type', 50)
                ->comment('Foreign key referencing k12_connection_categorized_data table');

        $table->enum('delivery_mode', ['online', 'in-person'])
                ->comment('Mode of delivery for the requirement');

        $table->string('grade_levels_id')->nullable()
                ->comment('Referencing tbl_classes'); 
                
         $table->integer('capacity')
                ->comment('Number of participants or students required');
    
        $table->string('description', 500)
                ->comment('Description of the requirement');
        $table->string('address', 150)
                ->nullable()
                ->comment('Address of the school or event location');

        $table->string('city', 150)->nullable()
                ->comment('City where the requirement is located');
        $table->string('state', 150)->nullable()
                ->comment('State where the requirement is located');
        
        $table->string('zip_code', 10)->nullable()
                ->comment('ZIP or postal code');     
        
         $table->string('country', 150)->nullable()
                ->comment('Country where the requirement is located');
         
        $table->date('start_date')
                ->comment('Start date of the requirement'); 

       $table->date('end_date')
                ->comment('End date of the requirement');

       $table->string('time_zone', 50)
                ->comment('Time zone for scheduling');  

       $table->string('no_class_dates', 500)
                ->nullable()
                ->comment('Dates when no classes will be conducted');
                
        $table->string('schedules', 500)
                ->nullable()
                ->comment('Detailed schedule information');  
                
        $table->string("totalHours")->nullable();
        
        $table->string("profileType_requirements")->nullable();
        $table->string("language_requirements")->nullable();
        $table->string('sw_requirements', 300)
                ->nullable()
                ->comment('Software requirements');

        $table->string('bg_check_requirements', 300)
                ->nullable()
                ->comment('Background check requirements');
                
        $table->string("medical_requirement")->nullable();
        
        $table->string('other_requirements', 300)
                ->nullable()
                ->comment('Other specific requirements');

        $table->string('certifications_valid', 300)
                ->nullable()
                ->comment('Required certifications');

         $table->enum('compensation_type', ['hourly', 'fixed'])
                ->comment('Type of compensation offered');
                
        $table->decimal('compensation_amount_min', 10, 2)
                ->comment('Minimum compensation amount');

        $table->decimal('compensation_amount_max', 10, 2)
                ->nullable()
                ->comment('Maximum compensation amount');      
        
          $table->string('benefits', 300)
                ->nullable()
                ->comment('Benefits offered with the requirement');
                
         $table->bigInteger('parent_id')->nullable(); 
         
          $table->enum('is_valid', [1, 0])->default(0)->comment('When all field are fullfiled');
          $table->enum('finalize_setup', ['false', 'true'])->default('false')->comment('Finalize setup complete or not');
          $table->timestamps();
          $table->softDeletes();
        $table->unsignedBigInteger('subject_area_id')->comment('...');
       $table->unsignedBigInteger('subject_id')->comment('...');
       $table->unsignedBigInteger('proctor_id')->nullable()->comment('...');








                
                
                
                


                
                


                
                
                 
                

        
       












    //for the indexing
      $table->index('school_id', 'idx_school_id');
            $table->index('subject_area_id', 'idx_subject_area_id');
            $table->index('subject_id', 'idx_subject_id');

         //for the foreign_id
        $table->foreign('subject_area_id')->references("id")->on("subject_area_v1");
        $table->foreign('subject_id')->references('id')->on("subjects_v1");      
        $table->foreign('proctor_id')->references("id")->on("platform_school_proctors");

       
      });




    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
