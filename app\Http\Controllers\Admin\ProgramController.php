<?php

namespace App\Http\Controllers\Admin;
use Carbon\Carbon;
use App\BackgroundMedicalModel;
use App\cbo;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Classes;
use App\Programs;
use App\District;
use App\ProgramNote;
use App\program_slots;
use App\ZoomModel;
use App\ProgramRequirementsModel;
use App\program_contact_info;
use App\invite_programs;
use App\invite_program_owners;
use Crypt;
use App\document_form;
use App\EmailTemplate;
use App\Helpers\CustomHelper;
use App\Helpers\DataTableHelper;
use App\Helpers\NotificationHelper;
use App\Http\Requests\Admin\ProgramRequest;
use App\Imports\ProgramClassesImport;
use App\MailModel;
use App\Notification_content;
use App\ProgramNoClassDate;
use App\StateModel;
use App\Subject;
use App\SubsubjectModel;
use App\User;
use App\user_references;
use Excel;
use Illuminate\Support\Facades\DB;
use App\Exports\Admin\ExportPrograms;

class ProgramController extends Controller
{

    public $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            $currentDate = now()->toDateString();
            $id = $request->id;
            $qry = Programs::with(['userNotes','subject','school', 'schedules']);

            $whereInIds = getAdminUserProgramIds();

            if ($adminType == '4') {
                $qry->whereIn('id', $whereInIds);
            } else {
                if (!empty($whereInIds)) {
                    $qry->whereIn('id', $whereInIds);
                }
            }


            switch ($id) {
                case "Today":
                    $qry->where("program_status", "!=", "Deleted");
                    $qry->whereHas('userNotes', fn($query) => $query->where('class_date', $currentDate));
                    break;

                case "Active":
                    $qry->where("program_status", "!=", "Deleted")
                        ->whereHas('mainAssignedUser')
                        ->where("start_date", "<=", $currentDate)
                        ->where("end_date", ">=", $currentDate);
                    break;

                case "Upcoming":
                    $qry->where("program_status", "!=", "Deleted")
                        ->where("start_date", ">",  $currentDate)
                        ->where("end_date", ">",  $currentDate);
                    break;

                case "ALL":
                    if ($request->filled('missing_instructor')) {
                        $request->missing_instructor == '1'
                            ? $qry->doesntHave('mainAssignedUser')
                            : $qry->has('subAssignedUser');
                    }
                    break;

                case "Publish":
                    $qry->where("program_status", "!=", "Deleted")
                        ->where("program_status", $id)
                        ->whereDoesntHave('mainAssignedUser');
                    break;

                case "Completed":
                    $qry->where("program_status", "!=", "Deleted")
                        ->whereHas('mainAssignedUser')
                        ->where("start_date", "<",  $currentDate)
                        ->where("end_date", "<",  $currentDate);
                    break;

                case "Archived":
                    $qry->where("program_status", "!=", "Deleted")
                        ->whereDoesntHave('mainAssignedUser')
                        ->where("start_date", "<",  $currentDate)
                        ->where("end_date", "<",  $currentDate);
                    break;

                default:
                    $qry->where("program_status", $id);
                    break;
            }

            if (!empty($params['searchValue'])) {
                $searchValue = strtolower($params['searchValue']);
                $qry->where(function ($qry) use ($searchValue, $params) {
                    $qry->where(function ($query) use ($searchValue) {
                        $userIds = User::whereRaw('LOWER(full_name) LIKE ?', ['%' . $searchValue . '%'])->pluck('id')->toArray();
                        $query->whereIn('school_name', $userIds);
                    })->orWhereHas('nextCommingProgram.user', function ($subQuery) use ($searchValue) {
                        $subQuery->whereRaw('LOWER(first_name) LIKE ?', ['%' . $searchValue . '%'])
                                ->orWhereRaw('LOWER(last_name) LIKE ?', ['%' . $searchValue . '%']);
                    })->orWhere(function ($subQuery) use ($params) {
                        DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], $params['columns']);
                    });
                });
            }

            if ($params['columnName']) {
                switch($params['columnName']){
                    case 'id':
                        $params['columnName'] = 'id';
                        break;

                    case 'name':
                        $params['columnName'] = 'name';
                        break;

                    case 'school_name':
                        $qry->with('users');
                        $qry->addSelect([
                            'schoolName' => User::selectRaw('users.full_name')
                            ->whereColumn('tbl_programs.school_name', 'users.id')
                            ->limit(1)
                        ]);

                        $params['columnName'] = 'schoolName';
                        break;

                    case 'course':
                        $qry->addSelect([
                            'subSubjectName' => SubsubjectModel::selectRaw('tbl_sub_subjects.name')
                            ->whereColumn('tbl_programs.sub_subject_id', 'tbl_sub_subjects.id')
                            ->limit(1)
                        ]);
                        $params['columnName'] = 'subSubjectName';
                        break;

                    case 'instructore':
                        $qry->addSelect([
                            'instId' => ProgramNote::selectRaw('program_notes.user_id')
                                ->whereColumn('program_notes.program_id', 'tbl_programs.id')
                                ->where(function ($query) {
                                    $currentDate = now()->toDateString();
                                    $query->where(function ($subQuery) use ($currentDate) {
                                        $subQuery->whereNull('is_makeup_class')
                                            ->where(function ($q) use ($currentDate) {
                                                $q->whereDate('class_date', '=', $currentDate)
                                                  ->orWhere('class_date', '>', $currentDate);
                                            });
                                    })->orWhere('class_date', '<', $currentDate);
                                })
                                ->orderByRaw("CASE WHEN class_date >= '{$currentDate}' THEN 0 ELSE 1 END")
                                ->orderBy('class_date', 'asc')
                                ->limit(1) // Get the next upcoming program's user_id
                        ]);

                        $qry->addSelect([
                            'instName' => User::selectRaw("CONCAT(users.first_name, ' ', users.last_name)")
                            ->whereColumn('instId', 'users.id')
                            ->limit(1)
                        ]);

                        $params['columnName'] = 'instName';
                        break;

                    case 'standby':
                        $qry->addSelect([
                            'subInstId' => invite_programs::selectRaw('tbl_invite_programs.user_id')
                                ->whereColumn('tbl_invite_programs.program_id', 'tbl_programs.id')
                                ->where([
                                    'tbl_invite_programs.status' => 1,
                                    'tbl_invite_programs.is_standby' => 1
                                ])
                            ->limit(1)
                        ]);

                        $qry->addSelect([
                            'subInstName' => User::selectRaw("CONCAT(users.first_name, ' ', users.last_name)")
                            ->whereColumn('subInstId', 'users.id')
                            ->limit(1)
                        ]);

                        $params['columnName'] = 'subInstName';
                        break;

                    case 'delivery_type':
                        $params['columnName'] = 'delivery_type';
                        break;

                    case 'start_date':
                        $params['columnName'] = 'start_date';
                        break;

                    case 'end_date':
                        $params['columnName'] = 'end_date';
                        break;

                    case 'status':
                        $params['columnName'] = 'status';
                        break;

                    case 'program_type':
                        $params['columnName'] = 'program_type';
                        break;

                    case 'program_type':
                        $params['columnName'] = 'created_at';
                        break;
                }
                if($params['columnName'] != 'departing') {
                    $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
                }
            }

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            if ($params['columnName'] == 'departing'){
                foreach ($result as $row) {
                    $uniqueUserIds = CustomHelper::allProgramUsers($row->id);
                    $departingNotices = MailModel::whereIn('user_id', array_filter($uniqueUserIds))->get(['user_id']);
                    $departingCount = $departingNotices->count();
                    $row->departing_instructor_count = $departingCount;
                }
                $result = $result->sortByDesc('departing_instructor_count')->values();
            }
            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);
                $viewButton = "";
                if (($id == 'Publish' || $id == 'Draft') && $row->end_date >= $currentDate  && (!$row->mainAssignedUser || !$row->mainUser)) :

                    $viewButton .= "<input type='checkbox' form='inviteprogram' value='{$row->id}' name='program_id[]'
                class='program_id'> ";
                    $viewButton .= @$row->mainAssignedUser->id;
                endif;

                $viewRoute =  url('view-program/step1/' . $encryptedStrId);

                $viewButton .= " <a href='{$viewRoute}'>{$row->id}</a>";

                if ($row->program_status != 'Deleted') {
                    $action = $this->generateActionButtons($encryptedStrId,$res, $row);
                } else {
                    $action = '';
                }


                if (strlen($row->name) > 20) {
                    $programName = substr($row->name, 0, 20) . '...';
                } else {
                    $programName = $row->name;
                }
                if ($row->school_name) {
                    $schoolName = schoolusername($row->school_name);
                } else {
                    $schoolName = '';
                }

                $uniqueUserIds = CustomHelper::allProgramUsers($row->id);

                /**
                 * get departing instructors
                 */
                $notices = MailModel::with('user:id,first_name,last_name')
                    ->whereIn('user_id', array_filter($uniqueUserIds))
                    // ->whereDate('created_at', '<=', Carbon::now()->subDays(14))
                    ->get(['user_id']);


                $departing = [];

                if ($notices->isNotEmpty()) {
                    foreach ($notices as $notice) {
                        if (!$notice->user) {
                            continue;
                        }
                        $viewUrl = url('viewinstructordetails/step1/' . encrypt_str($notice->user_id));
                        $viewName = $notice->user->first_name . ' '  . $notice->user->last_name;
                        $departing[] = "<a href='{$viewUrl}'>{$viewName}</a> ";
                    }
                }
                $departing = !empty($departing) ? implode(', ', $departing) : "-";
                if(isset($row->userNotes[0]) > 0){
                    if ($row->userNotes[0]->class_date) {
                        $classDate = explode(" ", $row->userNotes[0]->class_date)[0];
                        $startTime = $row->userNotes[0]->start_time;
                        $dateTimeString = $classDate . ' ' . $startTime;
                    } else {
                        $formattedDateTime = '';
                    }
                }
                if(isset($row->nextCommingProgram->user_id)){
                    $inst = username($row->nextCommingProgram->user_id);
                }
                else{
                    $inst = '';
                }

                if ($row->sub_subject_id) {
                    $subsubject = subsubjectname($row->sub_subject_id);
                } else {
                    $subsubject = '';
                }
                if ($row->id) {
                    $standby = getstandbyusername($row->id);
                } else {
                    $standby = '';
                }

                $schedules_data = $row->schedules->map(function ($schedule) {
                    $days = [
                        1 => 'Monday',
                        2 => 'Tuesday',
                        3 => 'Wednesday',
                        4 => 'Thursday',
                        5 => 'Friday',
                        6 => 'Saturday',
                        7 => 'Sunday',
                    ];
            
                    return isset($days[$schedule->slot_day])
                        ? "{$days[$schedule->slot_day]} - " . date('h:i A', strtotime($schedule->start_time)) . " - " . date('h:i A', strtotime($schedule->end_time))
                        : null;
                })->filter()->toArray();
                $schedules = implode("\n", $schedules_data);
                $data[] = [
                    "id" => $viewButton,
                    "name" => "<a href='{$viewRoute}'>{$programName}</a>",
                    "school_name" => $schoolName,
                    "course" => $subsubject ?? '',
                    "instructore" => $inst,
                    "delivery_type" => $row->delivery_type,
                    "standby" => $standby,
                    "start_date" => date('m-d-Y', strtotime($row->start_date)),
                    "end_date" => date('m-d-Y', strtotime($row->end_date)),
                    "status" => $this->generateStatusButton($row, json_decode($res['manageprogram'], true)),
                    "program_type" => $row->program_type,
                    "departing" => $departing,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                    'schedule' => $schedules,
                    // "name" => $viewButton. ' '.$row->id,
                    // "class_date" => $formattedDateTime ?? '',
                ];

                $i++;
            }

            $additional_data = [
                "searchValue" => $params['searchValue'],
                "columnName" => $params['columns'],
            ];
            // dd($additional_data);

            return DataTableHelper::generateResponse($params['draw'], $count, $data, $additional_data);
        }


        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["subject"] = Subject::get();
        $data["status"] = DB::table("tbl_profile_status")->where(['status_type' => 'Instructor'])->get();
        $data["users"] =  User::active()->special()->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

        return view("admin.program.list", $data);
    }

    private function generateActionButtons($encryptedStrId,$res, $row)
    {
        $orgId = $row->id;
        $viewRoute =  url('view-program/step1/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $logisticsButton = '';

        $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>&nbsp;";
        if (isset($res['manageprogram'])) :
            if (array_key_exists('manageprogram', $res)) :
                if (in_array('update', json_decode($res['manageprogram'], true))) :
                    $editRoute = url('edit-program/' . $encryptedStrId);

                    $editButton = "<a href='{$editRoute}' class='btn btn-rounded btn-block btn-xs btn-outline-secondary mt-0'><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
                endif;

                if (in_array('delete', json_decode($res['manageprogram'], true))) :
                    $hasActiveClasses = $row->userNotes()
                        ->where(function ($qry) {
                            $qry->whereNotNull('user_id')
                                ->orWhereNotNull('sub_user_id');
                        })
                        ->whereNull('status')
                        ->where('class_date', '>=', now()->toDateString())
                        ->exists();
                    $deleteRoute = route('admin.program.delete', ['program' => $orgId]);

                    $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute','$hasActiveClasses')  class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";

                endif;

                if (in_array('logistics/prerequisites', json_decode($res['manageprogram'], true))) :

                    $logisticsRoute = route('admin.program.logistics', ['program_id' => $encryptedStrId]);

                    $logisticsButton = "<a href='{$logisticsRoute}'   class='btn btn-rounded btn-block btn-xs btn-outline-info mt-0'>Logistics/Prerequisites</a>&nbsp;";

                endif;

            endif;
        endif;



        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$logisticsButton}{$editButton}{$viewButton}{$deleteButton}</div>";
    }

    private function generateStatusButton($row, $userPermissions)
    {
        $updatePermission = in_array('update', $userPermissions);

        $hasActiveClasses = $row->userNotes()
            ->where(function ($qry) {
                $qry->whereNotNull('user_id')
                    ->orWhereNotNull('sub_user_id');
            })
            ->whereNull('status')
            ->where('class_date', '>=', now()->toDateString())
            ->exists();


        switch ($row->status) {
            case 0:
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $row->id . ',  ' . $hasActiveClasses . ')" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $row->id . '">Deactive</a>'
                    : 'Deactive';
            case 1:
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $row->id . ',' . $hasActiveClasses . ')" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $row->id . '">Active</a>'
                    : 'Active';
            default:
                return '';
        }
    }


    public function publishprogram(Request $request)
    {
        $where = [];
        $program = Programs::where("status", "1")->get();

        return view("admin.program.publish", compact("program"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function add()
    {

        $cbos = cbo::pluck('name', 'id');
        $district = District::where("status", "1")->get();
        $states = StateModel::where(["country_id" => "239"])->pluck('name', 'id');
        $grade = DB::table("tbl_classes")
            ->where("status", "1")
            ->get();

        $subjects = Subject::pluck('subject_name', 'id');

        $schools = User::where("type", 6)
            ->get();
        return view("admin.program.add", compact("district", "grade", "cbos", "states", "subjects", "schools"));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function save(ProgramRequest $request)
    {
        DB::beginTransaction();

        try {

            $data = $request->validated();

            $data["name"] = $request->program_name;
            $data["cbo_id"] = $request->cbo_id;

            $data["end_date"] = date('Y-m-d', strtotime($request->datesingle1));
            $data["program_status"] = $request->program_status;
            $data["district"] = $request->district;
            $data["school_name"] = $request->school_name ?? null;
            $data["program_type"] = $request->program_type;
            $data["delivery_type"] = $request->delivery_type;
            // if (isset($request->grade)) {
            //     $data["grade"] = implode(",", $request->grade);
            // }

            $data["capacity"] = $request->capacity;
            $data["certifications"] = $request->certifications;
            $data["background_checks"] = $request->background_checks;
            $data["medicalrequirements"] = $request->medicalrequirements;
            $data["address"] = $request->address;
            $data["city"] = $request->city;
            $timezone = $request->timezone ?? 'America/Los_Angeles';

            $data["timezone"] = $timezone;
            $data["zip_code"] = $request->zipcode;
            $data["country"] = $request->country;
            $data["notes"] = $request->notes;

            $data["lat"] = $request->lat;
            $data["lng"] = $request->lng;

            if ($request->program_status === 'Publish') {
                if ($request->lat) {
                } else {

                    return response()->json([
                        "success" => false,
                        "message" => 'Please select a valid address from the picker.',
                    ]);
                }
            }

            $data["created_by"] = session()->get('Adminnewlogin')['id'];

            $data["link"] = $request->link;
            $data["joinlink"] = $request->joinlink;
            $is_imported = $request->is_imported ?? 0;
            $data["is_imported"] = $is_imported;
            $data["status"] = "1";
            $data["created_at"] = date("Y-m-d H:i:s");
            $data["updated_at"] = date("Y-m-d H:i:s");

            $data["subject_id"] = $request->subject_id;
            $data["sub_subject_id"] = $request->sub_subject_id;
            $data["state"] = $request->state;


            if ($request->is_past) {
                $data["is_past"] = $request->is_past;
                $data["past_date"] = date('Y-m-d', strtotime($request->past_date));
                $data["start_date"] = date('Y-m-d', strtotime($request->past_date));
            } else {
                $data["start_date"] = date('Y-m-d', strtotime($request->datesingle));
            }

            $program = Programs::create($data);
            $save = $program->id;
            $program->classes()->sync($request->grade);

            if ($is_imported == 0 && $request->filled('no_class_start_date')) {
                $startDates = $request->no_class_start_date;
                $endDates = $request->no_class_end_date;
                foreach ($startDates as $startIndex => $startDate) {
                    if ($startDate) {

                        $noClassDate = new ProgramNoClassDate();
                        $noClassDate->program_id = $save;
                        $noClassDate->start_date = $startDate;
                        $noClassDate->end_date = $endDates[$startIndex] ?? null;
                        $noClassDate->save();
                    }
                }
            }



            for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                $data1["job_title"] = $_POST["job_title"][$i];
                $data1["email"] = $_POST["cemail"][$i];
                $data1["first_name"] = $_POST["first_name"][$i];
                $data1["last_name"] = $_POST["last_name"][$i];
                $data1["phone"] = $_POST["phone"][$i];
                $data1["program_id"] = $save;
                program_contact_info::insertGetId($data1);
            }
            if ($is_imported == 0) {



                $daysOfWeek = $this->daysOfWeek;


                foreach ($daysOfWeek as $index => $day) {
                    if (isset($request->$day[1]) && isset($request->$day[2])) {
                        $this->insertProgramSlot($save, ($index + 1), $request->$day[1], $request->$day[2], $timezone);
                    }
                }




                $program->createClasses();
            } elseif (request()->file('class_schedule')) {
                Excel::import(new ProgramClassesImport($program), request()->file('class_schedule'));
            }








            DB::commit();
            if ($program->program_status == 'Publish') {
                $program->notification_sent = false;
                // CustomHelper::alertProgramUsers($program);
            }


            $route = route('admin.program.getrezoommodel', ['program_id' => $save]);
            return response()->json([
                "success" => true,
                "message" => "Program  successfully created",
                "redirect" => $route,
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                "success" => false,
                "message" =>  $e->getMessage(),
            ]);
        }
    }


    private function insertProgramSlot($programId, $slotDay, $startTime, $endTime, $timezone = "America/Los_Angeles")
    {
        program_slots::create([
            "program_id" => $programId,
            "slot_day" => $slotDay,
            "start_time" => formatTimeAdminTimezone($startTime, $timezone),
            "end_time" => formatTimeAdminTimezone($endTime, $timezone),
            /* "start_time" => formatTime($startTime),
            "end_time" => formatTime($endTime), */
        ]);
    }

    private function updateProgramSlot($programId, $slotDay, $startTime, $endTime, $timezone = "UTC")
    {
        program_slots::create([
            "program_id" => $programId,
            "slot_day" => $slotDay,
            "start_time" => $startTime,
            "end_time" => $endTime,
            /* "start_time" => formatTime($startTime),
            "end_time" => formatTime($endTime), */
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $district = District::where("status", "1")->get();
        $program = Programs::with('classes', 'subject')->where("id", $id)->first();
        $grade = DB::table("tbl_classes")
            ->where("status", "1")
            ->get();
        $school = User::where("district", $program->district)
            ->where("type", 6)
            ->get();

        $program_contact_info = DB::table("tbl_program_contact_info")
            ->where("program_id", $id)
            ->get();
        $monday_slots = program_slots::where("slot_day", "1")
            ->where("program_id", $id)
            ->first();
        $tue_slots = program_slots::where("slot_day", "2")
            ->where("program_id", $id)
            ->first();
        $wed_slots = program_slots::where("slot_day", "3")
            ->where("program_id", $id)
            ->first();
        $thu_slots = program_slots::where("slot_day", "4")
            ->where("program_id", $id)
            ->first();
        $fri_slots = program_slots::where("slot_day", "5")
            ->where("program_id", $id)
            ->first();
        $sat_slots = program_slots::where("slot_day", "6")
            ->where("program_id", $id)
            ->first();
        $sun_slots = program_slots::where("slot_day", "7")
            ->where("program_id", $id)
            ->first();
        $cbos = cbo::pluck('name', 'id');
        $states = StateModel::where(["country_id" => "239"])->pluck('name', 'id');
        $subjects = Subject::pluck('subject_name', 'id');
        $subSubjects = $program->subject->subSubjects ?? [];

        return view("admin.program.edit", [
            "district" => $district,
            "grade" => $grade,
            "program" => $program,
            "program_contact_info" => $program_contact_info,
            "school" => $school,
            "monday_slots" => $monday_slots,
            "tue_slots" => $tue_slots,
            "wed_slots" => $wed_slots,
            "thu_slots" => $thu_slots,
            "fri_slots" => $fri_slots,
            "sat_slots" => $sat_slots,
            "sun_slots" => $sun_slots,
            "states" => $states,
            "subjects" => $subjects,
            "subSubjects" => $subSubjects,
            "cbos" => $cbos,
        ]);
    }

    public function checkStatus(Request $request){
        $id = $request->query('id');
        $program = Programs::with('mainAssignedUser')->find($id);
        if ($program->mainAssignedUser  && $program->program_status == 'Publish') {
            if (!CustomHelper::canUpdateProgram($program->mainAssignedUser, $program, $request)) {
                return response()->json([
                    "success" => true,
                    "needsConfirmation" => true,
                ]);
                // return response()->json([
                //     "success" => false,
                //     "message" => 'Cannot update program assigned instructor is not availiable during changed schedule',
                // ]);
            }
        }
        return response()->json([
            "success" => true,
            "needsConfirmation" => false,
        ]);
    }



    public function update(ProgramRequest $request)
    {

        $id = $request->id;

        DB::beginTransaction();
        try {

            $program = Programs::with('mainAssignedUser')->findOrFail($id);

            $timezone = $program->timezone ?? 'America/Los_Angeles';
            // if ($program->mainAssignedUser  && $program->program_status == 'Publish') {

            //     if (!CustomHelper::canUpdateProgram($program->mainAssignedUser, $program, $request)) {

            //         return response()->json([
            //             "success" => false,
            //             "message" => 'Cannot update program assigned instructor is not availiable during changed schedule',
            //         ]);
            //     }
            // }

            if (!$program->mainAssignedUser) {

                $data["program_status"] = $request->program_status;
                $data["delivery_type"] = $request->delivery_type;
                $data["timezone"] = $request->timezone;
            }

            /**
             * @var boolean
             */
            $scheduleChanged = false;
            $data["end_date"] = $end_date = date('Y-m-d', strtotime($request->datesingle1));
            $start_date = date('Y-m-d', strtotime($request->datesingle));
            $scheduleChanged = ($program->end_date != $end_date) || ($program->start_date != $start_date);
            $endDateChanged = ($program->end_date != $end_date);

            // logger()->info($scheduleChanged ,[
            //     '$program->end_date'=>$program->end_date,
            //     '$request->end_date'=>$end_date,
            // ]);


            if ($request->program_status == 'Draft') {
                $data["start_date"] = date('Y-m-d', strtotime($request->datesingle));
            }
            $data["name"] = $request->program_name;
            $data["cbo_id"] = $request->cbo_id;

            $data["program_status"] = $request->program_status;
            $data["district"] = $request->district;
            $data["school_name"] = $request->school_name ?? null;
            $data["program_type"] = $request->program_type;

            $data["capacity"] = $request->capacity;
            $data["certifications"] = $request->certifications;
            $data["background_checks"] = $request->background_checks;
            $data["medicalrequirements"] = $request->medicalrequirements;
            $data["address"] = $request->address;
            $data["city"] = $request->city;

            $data["country"] = $request->country;
            $data["zip_code"] = $request->zipcode;
            $data["notes"] = $request->notes;
            $data["lat"] = $request->lat;
            $data["lng"] = $request->lng;
            // $data["link"] = $request->link;
            // $data["joinlink"] = $request->joinlink;
            if ($request->program_status === 'Publish') {
                if ($request->lat) {
                } else {

                    return response()->json([
                        "success" => false,
                        "message" => 'Please select a valid address from the picker.',
                    ]);
                }
            }

            $data["status"] = "1";
            $data["updated_at"] = date("Y-m-d H:i:s");
            $data["subject_id"] = $request->subject_id;
            $data["sub_subject_id"] = $request->sub_subject_id;
            $data["state"] = $request->state;

            $is_imported = $program->is_imported;
            if ($request->is_past) {
                $data["is_past"] = $request->is_past;
                $data["past_date"] = date('Y-m-d', strtotime($request->past_date));
                $data["start_date"] = date('Y-m-d', strtotime($request->past_date));
            } else {
                $data["is_past"] = null;
                $data["past_date"] = null;
            }
            if ($is_imported == '0') {

                // $data["no_classes_start_date"] = date('Y-m-d', strtotime($request->no_classes_start_date));
                // $data["no_classes_end_date"] = date('Y-m-d', strtotime($request->no_classes_end_date));
            }
            $program->fill($data);


            $program->save();




            if ($request->program_status == 'Publish') {

                $deaftInvites = invite_programs::where([
                    'program_id' => $id,
                    'program_invite_type' => 'Draft',

                ])->where('deadline', '>=', now()->toDateString())->pluck('id')->toArray();

                if (!empty($deaftInvites)) {
                    invite_programs::whereIn('id', $deaftInvites)->update(['program_invite_type' => null]);
                }

                $deaftInvites = invite_programs::where([
                    'program_id' => $id,
                    'program_invite_type' => 'Draft',

                ])->where('deadline', '<', now()->toDateString())->pluck('id')->toArray();

                if (!empty($deaftInvites)) {
                    invite_programs::whereIn('id', $deaftInvites)->update(['cancelled_by' => 1, 'status' => '0']);
                }
            }


            $program->classes()->sync($request->grade);

            if ($scheduleChanged == false) {

                // Fetch existing no class dates
                $existingNoClassDates = $program->noClasses()->get()->map(function ($item) {
                    return [
                        'start_date' => $item->start_date->format('Y-m-d'), // Format the date as needed
                        'end_date' => $item->end_date ? $item->end_date->format('Y-m-d') : null,
                    ];
                })->toArray();
            }

            // Extract new no class dates from the request
            $newNoClassDates = [];
            if ($request->filled('no_class_start_date')) {
                $startDates = $request->no_class_start_date;
                $endDates = $request->no_class_end_date;
                foreach ($startDates as $startIndex => $startDate) {
                    if ($startDate) {
                        $newNoClassDates[] = [
                            'start_date' => date('Y-m-d', strtotime($startDate)),
                            'end_date' => $endDates[$startIndex] ? date('Y-m-d', strtotime($endDates[$startIndex])) : null,
                        ];
                    }
                }
            }
            if ($scheduleChanged == false) {

                /**
                 * Check if there are any changes in no class dates
                 */
                $scheduleChanged = $this->noClassDatesHaveChanged($existingNoClassDates, $newNoClassDates);
            }
            if ($is_imported == 0) {
                $program->noClasses()->delete();
                foreach ($newNoClassDates as $noClassDate) {
                    $newNoClassDate = new ProgramNoClassDate();
                    $newNoClassDate->program_id = $id;
                    $newNoClassDate->start_date = $noClassDate['start_date'];
                    $newNoClassDate->end_date = $noClassDate['end_date'];
                    $newNoClassDate->save();
                }
            }

            program_contact_info::where(
                "program_id",
                "=",
                $id
            )->delete();

            for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                $data1["job_title"] = $_POST["job_title"][$i];
                $data1["email"] = $_POST["cemail"][$i];
                $data1["first_name"] = $_POST["first_name"][$i];
                $data1["last_name"] = $_POST["last_name"][$i];
                $data1["phone"] = $_POST["phone"][$i];
                $data1["program_id"] = $id;
                program_contact_info::insertGetId($data1);
            }

            if ($program->is_imported == '0') {

                $programSlots = program_slots::where("program_id", $id)->get();
                if ($scheduleChanged == false) {
                    // Extract current slots data
                    $currentSlots = [];
                    foreach ($programSlots as $slot) {
                        $currentSlots[] = [
                            'slot_day' => $slot->slot_day,
                            'start_time' => $slot->start_time_utc,
                            'end_time' => $slot->end_time_utc,
                        ];
                    }
                }

                // Extract incoming slots data from request
                $newSlots = [];
                $daysOfWeek = $this->daysOfWeek;
                foreach ($daysOfWeek as $index => $day) {
                    if (isset($request->$day[1]) && isset($request->$day[2])) {
                        $newSlots[] = [
                            'slot_day' => $index + 1,
                            'start_time' => formatTimeAdminTimezone($request->$day[1], $timezone),
                            'end_time' => formatTimeAdminTimezone($request->$day[2], $timezone),
                        ];
                    }
                }
                if ($scheduleChanged == false) {
                    /**
                     * Check if there are any changes in slots
                     */
                    $scheduleChanged = $this->slotsHaveChanged($currentSlots, $newSlots);
                }

                program_slots::where("program_id", "=", $id)->delete();
                foreach ($newSlots as $slot) {
                    $this->updateProgramSlot($id, $slot['slot_day'], $slot['start_time'], $slot['end_time']);
                }

                $program->updateClasses();
            } elseif (request()->file('class_schedule')) {

                /**
                 * Check if there are any changes in slots
                 */
                $scheduleChanged = true;
                Excel::import(new ProgramClassesImport($program), request()->file('class_schedule'));
            }
            DB::commit();

            if ($scheduleChanged) {
                $schoolId = $program->school_name;
                $school = User::find($schoolId);
                $schoolName = $school->full_name ?? '';

                $title = "Program Schedule Changed";
                if($endDateChanged){
                    $notificationContent = Notification_content::where("signature", "program-schedule-changed-end-date")->first();
                    $notificationTemplate = str_replace(['{{end_date}}'], [$end_date], @$notificationContent->content ?? '');
                } else {
                    $notificationContent = Notification_content::where("signature", "program-schedule-changed")->first();
                    $notificationTemplate = str_replace(['{{end_date}}'], [$end_date], @$notificationContent->content ?? '');
                }
                $notificationTemplate = str_replace(['{{program_name}}'], [$program->id], @$notificationTemplate ?? '');

                $type = 'program_schedule_changed';

                $template = EmailTemplate::find(25);

                $recipients = [
                    getOwnerDeatils($id, 'Recruiter'),
                    ...User::whereIn('id', CustomHelper::allProgramUsers($id))->get(),
                ];
                /**
                 * all instructors of the program
                 */
                // logger()->info($recipients);die;

                foreach ($recipients as $key => $recipient) {
                    if ($recipient) {

                        NotificationHelper::sendProgramNotification($recipient, $template, $id, $schoolName, $notificationTemplate, $title, $type);
                    }
                }
            }

            $route = route('admin.program.getrezoommodel', ['program_id' => $id]);
            return response()->json([
                "success" => true,
                "message" => "Program details successfully updated",
                "redirect" => $route,
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                "success" => false,
                "message" =>  $e->getMessage(),
            ]);
        }
    }
    private function noClassDatesHaveChanged($existingDates, $newDates)
    {
        if (count($existingDates) != count($newDates)) {
            return true;
        }
        // logger()->info([
        //     '$existingDates' => $existingDates,
        //     '$newDates' => $newDates,
        // ]);
        foreach ($existingDates as $index => $existingDate) {
            if ($existingDate != $newDates[$index]) {
                return true;
            }
        }

        return false;
    }
    private function slotsHaveChanged($currentSlots, $newSlots)
    {
        // logger()->info([
        //     '$currentSlots' => $currentSlots,
        //     '$newSlots' => $newSlots,
        // ]);
        if (count($currentSlots) != count($newSlots)) {
            return true;
        }

        foreach ($currentSlots as $index => $currentSlot) {
            if ($currentSlot != $newSlots[$index]) {
                return true;
            }
        }

        return false;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Classes::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Classes::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Classes::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function status_change_program(Request $request)
    {
        $id = $request->id;
        $record = Programs::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Programs::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Programs::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }


    public function delete(Programs $program)
    {


        $data["status"] = "0";
        $data["program_status"] = "Deleted";
        $res = Programs::where("id", $program->id)->update($data);
        // $res = $program->delete();
        // $res = true;
        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }

    public function getSchoolBydistrict($id)
    {
        $district = User::where("district", $id)
            ->where("type", 6)
            ->get();

        return response()->json([
            "success" => true,
            "message" => "Successfully",
            "data" => $district,
        ]);
    }

    public function viewprogram($id, $id2, Request $request)
    {

        $id = Crypt::decryptString($id2);

        $program = Programs::where("id", $id)->with('creator')->first();
        $user = @$program->mainUser ?? '';
        $user_id = @$user->id ?? '';
        $meetings = $program->programNoteAmounts()->has('note')->with('note')->get();
        $reimbursements = $program->reimbursements()->where(function($query) {
                            $query->where('status', '1')
                                ->orWhere('status', '3');
                        })
                        ->get();

        $program_contact_info = DB::table("tbl_program_contact_info")
            ->where("program_id", $id)
            ->get();
        $slots = program_slots::where("program_id", $id)
            ->get();

        $medicalform = BackgroundMedicalModel::where([
            "type" => "background_check",
            "program_id" => $id,
        ])->get();

        $background_medical = BackgroundMedicalModel::where([
            "type" => "medical_requirements",
            "program_id" => $id,
        ])->get();
        $user_references = user_references::where([
            "program_id" => $id,
        ])->get();
        $school = User::where(["users.type" => 6, "users.status" => "1"])->get();
        $state = StateModel::where(["country_id" => "239"])->get();
        $form = document_form::where([
            "status" => 1,
        ])->get();

        $users = User::query()
            ->active()
            ->where('users.type', '5')
            ->where('profile_status', '12')->get(['users.id', 'first_name', 'last_name', 'email']);
        $status = null;
        $invites = array();
        if (request()->segment(2) == 'step9') {

            $query = invite_programs::has('program')->with('program', 'user', 'program.school', 'program.schedules','notes.programNote');

            $query->where('program_id',  $id);

            if ($request->has('expired')) {
                $query->where('deadline', '<', now()->toDateTimeString());
                $query->where('is_approved', null);
                $query->where('tbl_invite_programs.status', null);
                $query->whereNotNull('deadline');
            } elseif ($request->has('status')) {
                $status = $request->filled('status') ? $request->status : null;
                $query->where('tbl_invite_programs.status', $status);
            }
            $query->where('is_auto_invite', 0);
            // $query->groupBy('user_id');

            $query->whereNotNull('user_id');
            $invites = $query->orderBy('tbl_invite_programs.id', 'DESC')->get();
        }

        $programBackReq = ProgramRequirementsModel::where([
            "req_type" => "background_check",
            "program_id" => $id,
        ])->get();


        $programMedReq = ProgramRequirementsModel::where([
            "req_type" => "medical_requirements",
            "program_id" => $id,
        ])->get();


        return view("admin.program.program_details", [

            "user" => $user,
            "user_references" => $user_references,
            "users" => $users,
            "medicalform" => $medicalform,
            "background_medical" => $background_medical,
            "application_id" => $user_id,
            "program" => $program,
            "program_contact_info" => $program_contact_info,
            "slots" => $slots,
            "meetings" => $meetings,
            "reimbursements" => $reimbursements,
            "school" => $school,
            "state" => $state,
            "form" => $form,
            "status" => $status,
            "invites" => $invites,
            "programBackReq" => $programBackReq,
            "programMedReq" => $programMedReq,

        ]);
    }

    public function post_invite_program(Request $request)
    {

        $program_ids = $request->program_id;
        $data['user_id'] = $instructor_id = $request->instructor_id;
        $data['title'] = 'new Program Alert';
        $data['notification'] = 'new Program Alert';
        $data['type'] = 'program';
        $data['is_read'] = 0;

        foreach ($program_ids as $program_id) {

            invite_programs::create([
                'program_id' => $program_id,
                'user_id' => $instructor_id,
            ]);

            /*Programs::where("id", $program_id)->update([
                'program_status' => 'Upcoming',
            ]);*/

            // createNotification($data);
        }

        return response()->json([
            "success" => true,
            "message" => "Successfully invited",
            "redirect" => url("/programs/Publish/"),
        ]);
    }

    public function get_invite_instructors(Request $request)
    {
        if (!$request->filled('program_id')) {
            return response()->json([
                "success" => false,
                "message" => "Please select atleast one program",
            ]);
        }
        $programs = Programs::whereIn('id', $request->program_id)->get();

        $deliveryTypes = array_column($programs->toArray(), 'delivery_type');
        $is_approved = [];

        if (in_array('Online', $deliveryTypes)) {
            $is_approved[] = 16;
        }

        if (in_array('In-Person', $deliveryTypes)) {
            $is_approved[] = 17;
        }

        if (in_array('In-Person', $deliveryTypes) && in_array('Online', $deliveryTypes)) {
            $is_approved[] = 20;
        }
        // \DB::enableQuerylog();
        $users = User::query()
            ->active()
            ->where("type", 5)
            ->where("profile_status", "12");
        // ->where('is_sub', '!=', '1')
        // ->whereIn('is_approved', $is_approved);
        if (!empty($is_approved)) {

            foreach ($programs as $program) {
                if (empty($program->lat) || empty($program->lng)) {
                    continue;
                }

                if (!in_array('16', $is_approved)) {
                    $users->whereHas('availableLocations', function ($query) use ($program) {
                        $query->selectRaw(
                            '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                            [$program->lat, $program->lng, $program->lat]
                        )->addSelect('radius')
                            ->havingRaw('distance <=  (radius * ?)', [160009.34]); //convert radius to meters
                    });
                }
            }
        }
        $ins = $users->get();
        return response()->json([
            "success" => true,
            "message" => "Successfully",
            "data" => $ins,
        ]);
    }

    public function get_assign_model(Request $request)
    {
        $type = $request->type;
        $data["program_id"] = $request->program_id;
        $data["title"] = $request->titleName;
        $data["hiddenType"] = $hiddenType = isset($request->hiddenType) ? $request->hiddenType : ''; // $users->where('is_sub', '!=', $hiddenType)
        $userId = isset($request->userId) ? $request->userId : '';
        $data["is_makeup"] = isset($request->is_makeup) ? $request->is_makeup : '';

        $program = Programs::find($request->program_id);


        $users = User::query()
            ->active()
            ->where('id', '!=', $userId)
            ->where('type', $type);


        $data["users"] = $users->get();



        return view("admin.program.assign_model")->with($data);
    }

    public function save_assign(Request $request)
    {

        $data["program_id"] = $request->program_id;
        $data["user_id"] = $request->user_id;
        $data["owner_type"] = $request->type;
        $data["deadline"] = $request->deadline;
        $data["status"] = '1';

        $record = invite_program_owners::where("program_id", $request->program_id)->first();
        /*  if ($record) {
            $udata["status"] = '0';
            Programs::where("program_id", $request->program_id)->update($udata);
        } */

        $save = invite_program_owners::insertGetId($data);
        if ($save) {
            $programs = Programs::find($request->program_id);
            if ($programs->school_name) {
                $school_name = institudeName($programs->school_name);
            } else {
                $school_name = '';
            }

            createAdminProgramCommanNotification($request->user_id, $request->deadline, $programs, '36', 'Recruiter', 'Recruiter', $school_name);




            return response()->json([
                "success" => true,
                "message" => "Successfully assigned"
            ]);
        }
    }

    public function addNoClassDates(Request $request)
    {
        $view = view("components.admin.add-more-no-class-dates")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function schoolprogramadd(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $schools = User::where("id", $id)
            ->get();
        $cbos = cbo::pluck('name', 'id');
        $district = District::where("id", $schools[0]->district)->get();
        $states = StateModel::where(["country_id" => "239"])->pluck('name', 'id');
        $grade = DB::table("tbl_classes")
            ->where("status", "1")
            ->get();

        $subjects = Subject::pluck('subject_name', 'id');

        $schools = User::where("id", $id)
            ->get();
        return view("admin.program.add", compact("district", "grade", "cbos", "states", "subjects", "schools"));
    }

    public function getzoommodel(Request $request)
    {

        $zoom = ZoomModel::select('id', 'account_name')->where("status", 1)
            ->get();
        $view = view("admin.program.zoom.zoommodel", compact('zoom'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    public function getrezoommodel($program_id, Request $request)
    {
        $program = Programs::where("id", $program_id)->with('creator')->first();

        $slots = program_slots::where("program_id", $program_id)
            ->get();
        $zoom = ZoomModel::select('id', 'account_name')->where("status", 1)
            ->get();
        $view = view("admin.program.zoom.editzoommodel", compact('program_id', 'zoom', 'program', 'slots'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function requestProgramform(Request $request)
    {

        $data["state"] = $request->state;
        $data["description"] = $request->description;
        $data["type"] = $request->form_type;
        $data["file"] = $request->form;
        $data["instructions"] = $request->instructions;
        $data["deadline"] = $request->deadline;
        $data["school_id"] = $request->school;
        $data["req_type"] = $request->app_type;
        $data["package_type"] = $request->package_type;
        $data["program_id"] =  @$request->program_id ?? null;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $save = ProgramRequirementsModel::insertGetId($data);

        if ($save) {



            return response()->json([
                "success" => true,
                "message" => "Saved successfully"
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function export(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Programs'.time().'.xlsx';
            return Excel::download(new ExportPrograms($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}
