<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InviteProgramNote extends Model
{
    use SoftDeletes;

    protected $table = 'invite_program_notes';

    protected $fillable = [
        'invite_program_id', 'program_note_id','deleted_at'
    ];

    public function invite()
    {
        return $this->belongsTo(invite_programs::class, 'invite_program_id');
    }

    public function programNote()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    
}
