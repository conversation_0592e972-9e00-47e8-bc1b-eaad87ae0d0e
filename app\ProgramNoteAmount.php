<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramNoteAmount extends Model
{
    use SoftDeletes;
    protected $table = 'program_note_amounts';

    protected $fillable = [
        'program_id', 'user_id', 'program_note_id', 'rate', 'minutes', 'format', 'amount', 'status','hours','type','deleted_at','payment_status','payment_date_updated', 'additional_class_date', 'additional_comment'
    ];

    protected $dates = ['additional_class_date'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function note()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }

}
