<?php

namespace App\Exports\Front;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use App\{Programs, User};

class ExportPayments implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }

    public function collection()
    {
        $user = auth()->user();

        $query = $user->programNoteAmounts()->whereHas('note')->with('note');

        $this->applyFilters($query);

        return $query->orderBy('id','DESC')->get();
    }
    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);


        switch($filters['status']) {
            case 'paid': $query->whereNotNull('payment_date_updated'); break;
            case 'unpaid': $query->whereNull('payment_date_updated'); break;
        }

        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }
        $separator = ' TO ';

        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }


    protected function applyDateRangeFilter($query, $daterange, $separator)
    {
        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

        $query->whereHas('note', function ($qry) use ($startDate, $endDate) {
            $qry->whereBetween('class_date', [$startDate, $endDate]);
        });
    }

    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings =  [
            'School Name',
            'Program Name',
            'Class Date',
            'Class Time',
            'Hours',
            'Minutes',
            'Hourly Pay Rate',
            'Amount',
            'Payment Status',
            'Paid At',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    public function map($row): array
    {
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
        $formattedTime = date('h:i a', strtotime($row->note->start_time)) .'-'.date('h:i a', strtotime($row->note->end_time));
        return [
            @$row->program->school->full_name??'NIL',
            $row->program ? $row->program->name : 'NIL',
            optional($row->note)->class_date ? optional($row->note->class_date)->format('m-d-Y') : '',
            $formattedTime,
            $row->hours,
            $row->minutes,
            $row->rate,
            $formattedAmount,
            empty($row->payment_date_updated) ? 'Pending': "Paid",
            $row->payment_date_updated? getAdminTimestamp($row->payment_date_updated): ''
        ];
    }

     // Function to get details of applied filters.
     protected function getFiltersInfo(): string
     {
         $filters = [];
         parse_str($this->requestFilters, $filters);

         $filtersInfo = '';

         if (!empty($filters['program_id'])) {
             $filtersInfo .= 'Program: ' . Programs::find($filters['program_id'])->name . PHP_EOL;
         }

         if (!empty($filters['daterange'])) {
             $filtersInfo .= 'Date Range: ' . $filters['daterange'] . PHP_EOL;
         }

         if (!empty($filters['status'])) {
             $filtersInfo .= 'Payment Status: ' . $filters['status']  . PHP_EOL;
         }

         return $filtersInfo;
     }
}
