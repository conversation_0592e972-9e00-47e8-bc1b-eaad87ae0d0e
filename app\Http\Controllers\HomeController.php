<?php

namespace App\Http\Controllers;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\SettingTermsModel;
use Hash;
use Mail;

DB::enableQueryLog();


class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(!Session::has('Adminnewlogin')){ return redirect('/admin'); }
        return view('admin.client.addclient');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      
        if(!Session::has('Adminnewlogin')){ return redirect('/admin'); }
        $email = $request->email;
        if($email!=""){
             $userExits = Users::where('email','=',$email)->get();
             if(count($userExits)){
                return response()->json(['success'=>false, 'message' => 'Client already exits']);  
             }

        else{

        $logoName = '';
        //check logo if exists
        if ($request->hasfile('profile_upload')) {
            // move | upload file on server
            $file      = $request->file('profile_upload');
            $extension = $file->getClientOriginalExtension(); // getting image extension
            $logoName  = 'image-'.time() . '.' . $extension;
             $destinationPath = public_path('/uploads');
             $file->move($destinationPath, $logoName);
        }

        $data['first_name']= $request->first_name;
        $data['last_name']= $request->last_name;
        $data['gender']= $request->gender;
        $data['dob'] = date('Y-m-d', strtotime($request->input('dob')));
        $data['email']= $request->email;
        $data['phone_number'] = $request->phone_number;
        $data['about']  = $request->about;
        $data['image'] = $logoName;
        $data['type'] = 2;
        $data['password'] = Hash::make($request->password);
        // $data['passwordStr'] = $request->password;
        $data['status'] = '1';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
       
         $save=Users::insert($data);

           /*
         * Email CompanyCreated
         */


         $dataEmail = array(
              'email' => $request->email,
              'subject' => 'Created Client',
              'mailbody' => 'New client',
              'first_name'=>$request->first_name,
              'last_name'=>$request->last_name,
              'password' =>  $request->password,
            );

         // return view('admin.forgotpassword', $data); die;

        //  Mail::send('admin.email-temp.signup', $dataEmail, function($message) use ($dataEmail) {
        //   $message->from(env('MAIL_USERNAME'),'Certification');
        //   $message->to($dataEmail['email']);
        //   $message->subject($dataEmail['subject']);
        // });
       
     


        if($save){
            return response()->json(['success'=>true, 'message' => 'Client successfully added']);  
        }else{

            return response()->json(['success'=>false, 'message' => 'Something went wrong']);  
        }
         

        /*
         * Email CompanyCreated
         */
        // Mail::send(
        //     'emails.companyCreated',
        //     ['data' => $data],
        //     function ($message) use ($data) {
        //         $message->to($data['email'], $data['email']);
        //         $message->replyTo(config('mail.from.address'), config('mail.from.name'));
        //         $message->subject('Company Registered!');
        //     }
        // );
    }
     }  
     } 

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    { 
        if(!Session::has('Adminnewlogin')){ return redirect('/admin'); }
       
        $id = decrypt_str($request->id);
        $email = $request->email;
        $old_profile_name = $request->old_profile_name;
        if($email!=""){
             $userExits = Users::where('email','=',$email)->where('id','!=',$id)->where('type','=',2)->get();
             if(count($userExits)){
                return response()->json(['success'=>false, 'message' => 'Email already exits']);  
             }

        else{

        $logoName = '';
        //check logo if exists
        if ($request->hasfile('profile_upload')) {
            // move | upload file on server
            $file      = $request->file('profile_upload');
            $extension = $file->getClientOriginalExtension(); // getting image extension
            $logoName  = 'image-'.time() . '.' . $extension;
             $destinationPath = public_path('/uploads');
             $destinationPath1 = public_path('/uploads/');
             $file->move($destinationPath, $logoName);
             if($logoName!=''){
                 if($old_profile_name!="" )
                 {
                    if(file_exists($destinationPath1.$old_profile_name))
                    {
                      unlink($destinationPath1.$old_profile_name);
                   }
                 }
             }
           }

        $data['first_name']= $request->first_name;
        $data['last_name']= $request->last_name;
        $data['gender']= $request->gender;
        $data['dob'] = date('Y-m-d', strtotime($request->input('dob')));
        $data['email']= $request->email;
        $data['phone_number'] = $request->phone_number;
        $data['about']  = $request->about;
        $data['image'] = $logoName;
        $data['type'] = 2;
        if($request->password!=""){
        $data['password'] = Hash::make($request->password);
        // $data['passwordStr'] = $request->password;
        }
       
        $data['status'] = '1';
        $data['updated_at'] = date('Y-m-d H:i:s');
        $save= Users::where('id',$id)->update($data);
        // dd(DB::getQueryLog());
        if($save){
            return response()->json(['success'=>true, 'message' => 'Details successfully updated']);  
        }else{

            return response()->json(['success'=>false, 'message' => 'Something went wrong']);  
        }
         

    }

   }     
  }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        //
    }



 public function statuschange(Request $request){
        if(!Session::has('Adminnewlogin')){ return redirect('/admin'); }
         $data['status'] =  $request->id1;
         $id =  $request->id;
         $record = Users::where('id', $id)->first();
      if($record) {
         $res = Users::where('id', $id)->update($data);
             if($res){
                 return response()->json(['success'=>true, 'message' => 'Status Successfully Changed']);  

             }else{
                return response()->json(['success'=>false, 'message' => 'Something went worng']);  
             }
        }else{
             return response()->json(['success'=>false, 'message' => 'Record not found']);
        }
  }

 




    public function test()
    {
     
    }


    public function termscondition()
    {
        $content = SettingTermsModel::where('id','=',2)->first();
        return view("web.content.termscondition", compact("content"));
        
    }

    public function privacypolicy()
    {
        $content = SettingTermsModel::where('id','=',3)->first();
        return view("web.content.privacypolicy", compact("content"));
    }


  

}
