<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OnboardingInstructorMarketplaceContract extends Model
{
    use SoftDeletes;
    protected $table = 'onboarding_instructor_marketplace_educator_contract';
    protected $fillable = [
        'user_id',
        'isWhizaraEducator',
        'terms_accepted',
        'work_eligibility_confirmed',
        'privacy_policy_accepted',
        'marketplace_fee_accepted',
        'first_name',
        'last_name',
        'address',
        'contract_sign',
        'marketplace_contract',
    ];
}
