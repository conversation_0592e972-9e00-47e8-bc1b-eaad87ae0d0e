<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultiplesColumnsOnPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->integer('no_instrtructional_days')->nullable()->after('totalHours')->comment('Number of instructional Days');
            $table->integer('class_duration')->nullable()->after('no_instrtructional_days')->comment('Class duration');
            $table->integer('no_non_instructional_hr')->nullable()->after('class_duration')->comment('Number of non-instructional hours');
            $table->string('schedule_type')->nullable()->after('no_non_instructional_hr')->comment('Schedule Type');
            $table->longText('regular_days')->nullable()->after('schedule_type')->comment('Schedule Type Regular');
            $table->longText('schedule_1_days')->nullable()->after('regular_days')->comment('Schedule Type Alternating Schedule 1');
            $table->longText('schedule_2_days')->nullable()->after('schedule_1_days')->comment('Schedule Type Alternating Schedule 2');
            $table->string('sch_cal_screenshot')->nullable()->after('schedule_2_days')->comment('School Calendar Screenshot');
            $table->string('district_cal_screenshot')->nullable()->after('sch_cal_screenshot')->comment('District Calendar Screenshot');
            $table->string('teacher_schedule_screenshot')->nullable()->after('district_cal_screenshot')->comment('Teacher Schedule Screenshot');
            $table->longText('class_details')->nullable()->after('teacher_schedule_screenshot');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn(['no_instrtructional_days', 'class_duration', 'no_non_instructional_hr', 'schedule_type', 'regular_days', 'schedule_1_days', 'schedule_2_days', 'sch_cal_screenshot', 'district_cal_screenshot', 'teacher_schedule_screenshot', 'class_details']);
        });
    }
}
