<?php

namespace App\Http\Controllers\Admin;

use App\CancelSubRequest;
use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\invite_programs;
use App\InviteProgramNote;
use App\ProgramNote;
use Illuminate\Http\Request;

class CancelSubRequestController extends Controller
{

    public function index(Request $request)
    {
        $sidebarGroup ='requestMenu';

        $sidebarMenu = 'program-cancel-sub';
        $status = null;
        if ($request->has('status')) {
            $status = $request->filled('status') ? $request->status : null;
        }
        if ($request->ajax()) {
            $query = CancelSubRequest::with('subs', 'user', 'program');
            $adminSession = session()->get('Adminnewlogin');
            $adminType = $adminSession['type'];

            if ($adminType != '1') {
                $whereInIds = getAdminUserProgramIds();
                if (!empty($whereInIds)) {
                    $query->whereHas('program', function ($qry) use ($whereInIds) {
                        $qry->whereIn('program_id', $whereInIds);
                    });
                }
            }

            $params = DataTableHelper::getParams($request);

            $query->where('status', $status);


            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            $query->where(function ($que) use ($searchValue) {
                $que->whereHas('program', function ($query) use ($searchValue) {
                    $query->where('id', 'LIKE', "%{$searchValue}%");
                })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    });
            });


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $action =  $this->generateActionButtons($row,[]);
                $viewUser = $viewProgram = "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a href='{$viewProgramRoute}'>{$row->program->id}</a>";
                }

                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a href='{$viewUserRoute}'>{$userName}</a>";
                }

                $data[] = [
                    "id" => $row->id,
                    "name" => $viewProgram,
                    "user_id" => $viewUser,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.program.requests.cancellation-list", compact("sidebarMenu", "status","sidebarGroup"));
    }
    private function generateActionButtons($row ,$res = [])
    {
        $id = $row->id;
        $javascriptVoid = "javascript:void(0);";
        $acceptButton = $declineButton = $viewClassButton = '';
        if ($row->subs->isNotEmpty()) {
            $viewClassRoute = route('admin.program.view-sub-classes', ['id' => $id]);

            $viewClassButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=openAdminModal('{$viewClassRoute}')>View Substitute Classes</a>  &nbsp;";
        }

        if (is_null($row->status)) {
            $acceptRoute = route('admin.program.update.cancel-sub-request', ['id' => $id, 'status' => 1]);

            $acceptButton = "<a href='{$javascriptVoid}' class='btn btn-outline-success btn-rounded' onclick=updateRequest('{$acceptRoute}')>Accept</a>  &nbsp;";

            $declineRoute = route('admin.program.update.cancel-sub-request', ['id' => $id, 'status' => 0]);

            $declineButton = "<a href='{$javascriptVoid}' class='btn btn-outline-danger btn-rounded' onclick=updateRequest('{$declineRoute}')>Decline</a>  &nbsp;";
        }


        $html = "<div class='w-100 d-flex justify-content-around align-items-around'>{$viewClassButton}{$acceptButton}{$declineButton}</div>";
        return $html;
    }
    public function update($id, Request $request)
    {
        $currentDate = now()->toDateString();

        $status = $request->status;
        $message = $status == 1 ? 'Accepted' : 'Declined';

        $cancelSubRequest = CancelSubRequest::findOrFail($id);


        $programNoteIds = $cancelSubRequest->subs()->whereHas('programNote',function ($qry) use($currentDate){
            $qry->where('class_date', '>=', $currentDate);
        })->pluck('program_note_id')->toArray();

        $cancelSubRequest->update(['status' => $status]);
        $sub_userIds = ProgramNote::where('class_date', '>=', $currentDate)
            ->whereNull(['note', 'status'])
            ->where(function ($query) {
                $query->whereNotNull('sub_user_id')
                    ->orWhere('is_sub_requested', 1);
            })
            ->whereIn('id', $programNoteIds)
            ->pluck('sub_user_id')->toArray();

        if (!empty($programNoteIds)) {
            InviteProgramNote::whereIn('program_note_id', $programNoteIds)->forceDelete();
            if (!empty($sub_userIds)) {
            invite_programs::where('program_id',$cancelSubRequest->program_id)
            ->whereNotNull('is_approved')
            ->where('is_standby', '!=', 1)
            ->whereIn('user_id',$sub_userIds)
            // ->where('replacement_type',0)
            ->whereDoesntHave('notes')
            ->forceDelete();
            }
            ProgramNote::whereIn('id', $programNoteIds)->when($status == '1', function ($query) {
                $query->update(['sub_user_id' => null, 'user_sub_requested' => 4]);
            })->when($status != '1', function ($query) {
                $query->update(['user_sub_requested' => 3]);
            });
        }

       $res= ProgramNote::where('program_id',  $cancelSubRequest->program_id)->where('user_id',  $cancelSubRequest->user_id)->get();
        if(count($res) == 0){
            invite_programs::where('program_id',$cancelSubRequest->program_id)
            ->where('user_id',$cancelSubRequest->user_id)
            ->whereNotNull('is_approved')
            ->where('is_standby',0)
            ->forceDelete();
        }

        return response()->json(['status' => true, 'message' => $message . ' successfully']);
    }

    public function view($id, Request $request)
    {

        $subRequest = CancelSubRequest::with('subs.user' , 'subs.programNote')->findOrFail($id);
        $subs = $subRequest->subs;

        $view = view("components.admin.modals.view-sub-classes", compact('subs'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
}
