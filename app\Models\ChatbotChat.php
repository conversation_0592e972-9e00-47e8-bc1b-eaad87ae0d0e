<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ChatbotChat extends Model
{
    protected $table = 'chatbot_chats';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = true;

    protected $fillable = [
        'id',
        'userId',
        'platform',
        'userMessage',
        'botResponse',
    ];

    protected $casts = [
        'id' => 'string',
        'userId' => 'string',
        'platform' => 'string',
        'userMessage' => 'string',
        'botResponse' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }
}
