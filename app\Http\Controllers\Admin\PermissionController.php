<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\Permission;
use Hash;
use Mail;

DB::enableQueryLog();

class PermissionController extends Controller
{
    public function index(Request $request)
    {
        $id = $request->id;
        $user_id = decrypt_str($id);
        $modules = DB::table("tbl_modules")
            ->where("status", "1")
            ->orderBy("sort_order", "asc")
            ->get();
        return view("admin.permission-activity.permissionpage", [
            "user_id" => $user_id,
            "modules" => $modules,
        ]);
    }

    public function add_permission(Request $request)
    {
        $userid = $request->input("userid");

        try {
            $managment = DB::table("tbl_modules")
                ->orderBy("sort_order", "asc")
                ->where("status", "1")
                ->get();
            foreach ($managment as $key => $value) {
                $wherep = ["user_id" => $userid, "module" => $value->type];
                $rowp = DB::table("tbl_permissions")
                    ->where($wherep)
                    ->first();

                $type = $request->input($value->type);
                if (!$type) {
                    $type = "[]";
                }

                if (!empty($rowp)) {
                    $up = DB::table("tbl_permissions")
                        ->where($wherep)
                        ->update([
                            "module" => $value->type,
                            "permission_setting" => $type,
                        ]);
                } else {
                    if ($type !== "[]") {
                        $result = '"' . implode('", "', $type) . '"';
                        $objP["user_id"] = $userid;
                        $objP["module"] = $value->type;
                        $objP["permission_setting"] = "[" . $result . "]";
                        $saveP = Permission::insert($objP);
                    }
                }
            }
        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => "Pesmissison not added",
            ]);
        }

        return response()->json([
            "success" => true,
            "message" => "Pesmissison addd successfully",
        ]);
    }

    function nopermission(){
        return view("admin.permissionpage");
    }
}
