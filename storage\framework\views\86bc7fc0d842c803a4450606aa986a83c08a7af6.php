

<?php $__env->startSection('title'); ?> Role List | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('messages.rolemanagement')); ?></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('messages.rolelist')); ?> </li>

                <li class="breadcrumb-item active float-right" aria-current="page"><a href="<?php echo e(url('add-role')); ?>"><?php echo e(__('messages.addrole')); ?></a></li>

            </ol>

        </nav>
        <!-- BREADCRUMB END -->

        <!-- MONTHLY INVOICES SECTION START -->
        <div class="table-responsive">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <!-- <th>#</th> -->
                        <th><?php echo e(__('messages.rolename')); ?></th>
                        <th><?php echo e(__('messages.status')); ?></th>
                        <th><?php echo e(__('messages.action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(!empty($admin) && $admin->count()): ?>
                    <?php $__currentLoopData = $admin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <!-- <th scope="row"><?php echo e(++$key); ?></th> -->
                        <td><?php echo e($data->name); ?></td>
                        
                        <td>
                            <?php if($data->status == 0): ?>
                            <button onclick="status_update('<?php echo e($data->id); ?>',1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-<?php echo e($data->id); ?>">
                                <?php echo e(__('messages.deactive')); ?></button>
                            <?php endif; ?>
                            <?php if($data->status == 1): ?>
                            <button onclick="status_update('<?php echo e($data->id); ?>',0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-<?php echo e($data->id); ?>">
                                <?php echo e(__('messages.active')); ?></button>
                            <?php endif; ?>
                        </td>

                        <td>
                            <div class="w-100 d-flex justify-content-center align-items-center">
                            <?php if($data->id != 1): ?>
                                <!-- <a class="role_delete" href="javascript:void;" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                </a> -->
                                <?php endif; ?>
                                &nbsp;
                                 <a  href="<?php echo e(url('add-permission/'.encrypt_str($data->id))); ?>" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-lock" aria-hidden="true"></i></button>
                                </a>
                                &nbsp;
                                <a href="<?php echo e(url('edit-role/'.encrypt_str($data->id))); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil" aria-hidden="true"></i></button>
                                </a>
                            </div>
                        </td>

                    </tr>


                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="10">No data found.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
                <!-- <tfoot>

                    <tr>
                        <th>#</th>
                        <th><?php echo e(__('messages.rolename')); ?></th>
                        <th><?php echo e(__('messages.status')); ?></th>
                        <th><?php echo e(__('messages.action')); ?></th>
                      
                    </tr>
                </tfoot> -->
            </table>
        </div>
        <!-- END -->

        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    function status_update(id) {
        var url = base_url + 'role-update-status';

        var status = $('.changestatuscls-' + id).data('data');

        if (status == 0)
            confirm_message = 'Are you sure you want to Deactivate ?';
        else
            confirm_message = 'Are you sure you want to activate ?';
        update_status(id, url, confirm_message);
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/role/rolelist.blade.php ENDPATH**/ ?>