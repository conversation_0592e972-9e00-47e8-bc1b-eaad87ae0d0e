<?php

namespace App\Models;

use App\OnboardingInstructor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InstructorBudgetApprovedModel extends Model
{
    use SoftDeletes;

    protected $table = 'instructor_budget_approved';

    protected $fillable = [
        'user_id',
        'in_person',
        'bilingual_inc',
        'case_management',
        'status_updated_at'
    ];

    public function user()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }

    public function lines()
    {
        return $this->hasMany(InstructorBudgetLine::class, 'approved_id');
    }
}
