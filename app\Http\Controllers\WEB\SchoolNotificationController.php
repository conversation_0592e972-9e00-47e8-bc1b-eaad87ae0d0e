<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\notification;
use Illuminate\Support\Facades\Auth;

class SchoolNotificationController extends Controller
{

    public function index()
    {

        $user= Auth::user();
        $user_id = @$user->id;

        if(checkAuth($user->status)) {
            return redirect("/logout");
        } 

        notification::where(["user_id"=>$user_id,"is_read"=>0])->update(['is_read'=>1]);

        $data['notification'] = notification::where("user_id",$user_id) ->orderBy("id", "desc")->paginate(10);
        return view('school.notification.notification')->with($data);
        
    }

    public function deletenotification(Request $request)
    {
           
        $id = $request->id;
        if (isset($id)) {
            
                $res = notification::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
          
        }
    }


}