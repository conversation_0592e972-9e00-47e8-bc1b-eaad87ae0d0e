<?php

namespace App\Http\Controllers\Admin;

use App\EmailTemplate;
use App\Helpers\CustomHelper;
use App\Helpers\DataTableHelper;
use App\Helpers\NotificationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProgramAdminNotesRequest;
use App\Http\Requests\Admin\ProgramViewClassRequest;

use App\Meeting;
use App\ProgramAdminNote;
use App\ProgramNote;
use App\ProgramNoteAmount;
use App\Programs;
use App\ProgramNoteStudent;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ProgramClassController extends Controller
{

    public function addComment(ProgramNote $programNote, Request $request)
    {
        $view = view("components.admin.modals.add-notes-comment", compact('programNote'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeComment(ProgramNote $programNote, Request $request)
    {
        $request->validate(
            [
                'comment' => 'required',
            ]
        );

        $programNote->comment = $request->comment;
        $programNote->commented_by = session()->get('Adminnewlogin')['id'];
        $programNote->save();

        return response()->json(['status' => true, 'message' => "Comment saved successfully", 'reload' => true]);
    }

    public function addMeeting(Programs $program, Request $request)
    {

        $view = view("components.admin.modals.add-notes-meeting", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeMeeting(Programs $program, Request $request)
    {

        $request->validate([
            'meeting_type' => 'required|max:255',
            'date' => 'required_if:meeting_type,MAKEUP_CLASS|nullable|date',
            'start_time' => 'required_if:meeting_type,MAKEUP_CLASS|nullable|date_format:h:i A',
            'end_time' => 'required_if:meeting_type,MAKEUP_CLASS|nullable|date_format:h:i A|after:start_time',
            // 'link' => 'required|url',
            'zoom_link' => 'nullable|url',
        ], [
            'date.required_if' => 'The date field is required.',
            'start_time.required_if' => 'The start time field is required.',
            'end_time.required_if' => 'The end time field is required.',
            'end_time.after' => 'The end time must be  after start time.',
        ]);

        $created_by = session()->get('Adminnewlogin')['id'];
        $request->merge([
            'program_id' => $program->id,
            'created_by' => $created_by,
        ]);
        // $meeting = Meeting::create($request->except('program_note_id'));
        $user = $program->mainAssignedUser;
        if ('MAKEUP_CLASS' == $request->meeting_type) {
            $user_id = $user ? $user->id : null;
            $start_time = formatTimeAdminTimezone($request->start_time, $program->timezone);
            $end_time = formatTimeAdminTimezone($request->end_time, $program->timezone);

            ProgramNote::create([
                'user_id' => null,
                'program_id' => $program->id,
                'class_date' => date('Y-m-d', strtotime($request->date)),
                'day' => date('N', strtotime($request->date)),
                'start_time' => $start_time,
                'end_time' => $end_time,
                'is_reassigned' => 0,
                'invited_by' => session()->get('Adminnewlogin')['id'],
            ]);
        return response()->json(['status' => true, 'message' => "Meeting saved successfully", 'reload' => true]);

        } elseif ($user && $user->email_notification) {
            $meeting_date = date('m-d-Y', strtotime($request->date));
            $timezone = optional(@$user->availability)->teach_in_person_timezone ?? null;
            $startTime = convertTime($request->start_time,$program->timezone,$timezone);
            $endTime = convertTime($request->end_time,$program->timezone,$timezone);
            $body =  "You have {$request->meeting_type} meeting on date {$meeting_date} time {$startTime}-{$endTime}. <br>For meeting link <a href='{$request->link}'>click here </a>.";

            $full_name = $user->first_name . ' ' . $user->last_name;
            $email = $user->email ;
            $template = EmailTemplate::find(26);

            $body = str_replace(['{{ NAME }}', '{{ notification }}'], [$full_name, $body], $template->description);
            $subject = "New Meeting Alert";

            NotificationHelper::sendEmail($email, $subject, $body);
        }



        return response()->json(['status' => true, 'message' => "Meeting saved successfully"]);
    }

    public function storeAdminNotes(Programs $program, ProgramAdminNotesRequest $request)
    {

        $created_by = session()->get('Adminnewlogin')['id'];
        $data = [
            'program_id' => $program->id,
            'created_by' => $created_by,
            'user_id' => @$program->activeUser->id ?? null,
        ];


        $obj = ProgramAdminNote::create(array_merge($request->validated(), $data));


        return response()->json(['status' => true, 'message' => "Notes saved successfully", 'reload' => true]);
    }

    public function updateAdminNotes(Programs $program, ProgramAdminNotesRequest $request)
    {

        $created_by = session()->get('Adminnewlogin')['id'];
        $data = [
            'program_id' => $program->id,
            'created_by' => $created_by,
            'user_id' => @$program->activeUser->id ?? null,
        ];
        ProgramAdminNote::where(["program_id" => $program->id])->delete();

        $obj = ProgramAdminNote::create(array_merge($request->validated(), $data));


        return response()->json(['status' => true, 'message' => "Notes updated successfully", 'reload' => true]);
    }

    public function deleteAdminNotes(ProgramAdminNote $programAdminNote)
    {


        $res = $programAdminNote->delete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
                'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }

    public function list($eId, Request $request)
    {
        $programId = decrypt($eId);
        $program = Programs::findOrFail($programId);
        $params = DataTableHelper::getParams($request);

        $qry = $program->userNotes();
        $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');

        $qry->where(function ($que) use ($params) {
            DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
        });

        [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

        $data = [];
        $i = 1;
        $res = get_permission(session('Adminnewlogin')['type']);
        foreach ($result as $row) {
            $action = $this->generateActionButtons($row->id,$res);

            $data[] = [
                "id" => $i,
                "class_date" => date('m-d-Y', strtotime($row->class_date)),
                "day" => getDayName($row->day),
                "start_time" => date('H:i A', strtotime($row->start_time)),
                "end_time" => date('H:i A', strtotime($row->end_time)),
                "action" => $action,
            ];

            $i++;
        }

        return DataTableHelper::generateResponse($params['draw'], $count, $data);
    }

    private function generateActionButtons($orgId,$res)
    {
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = '';

        if (isset($res['manageprogram'])) :
            if (array_key_exists('manageprogram', $res)) :
                if (in_array('update', json_decode($res['manageprogram'], true))) :
                    $editRoute = route('admin.program.view-class.edit', ['programNote' => $orgId]);

                    $editButton = "<a href='{$actionUrl}' onclick=openCommanModal('$editRoute') class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
                endif;

                if (in_array('delete', json_decode($res['manageprogram'], true))) :

                    $deleteRoute = route('admin.program.view-class.delete', ['programNote' => $orgId]);

                    $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')  class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";

                endif;

            endif;
        endif;



        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$deleteButton}</div>";
    }


    public function editViewClass(ProgramNote $programNote, Request $request)
    {
        $view = view("components.admin.modals.edit-view-class", compact('programNote'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateViewClass(ProgramNote $programNote, ProgramViewClassRequest $request)
    {
        $programNote->class_date = date('Y-m-d', strtotime($request->class_date));
        $programNote->start_time = date('H:i', strtotime($request->class_date));
        $programNote->end_time = date('H:i', strtotime($request->class_date));
        $programNote->day = date('N', strtotime($request->class_date));
        $programNote->save();

        return response()->json(['status' => true, 'message' => "Class saved successfully"]);
    }

    public function deleteViewClass(ProgramNote $programNote)
    {
        $res = $programNote->delete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
                'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }

    public function addViewClass(Programs $program, ProgramViewClassRequest $request)
    {

        $programNote = new ProgramNote();
        $programNote->user_id = @$program->mainAssignedUser->id ?? null;
        $programNote->program_id = @$program->id ?? null;

        $programNote->class_date = date('Y-m-d', strtotime($request->class_date));
        $programNote->start_time = date('H:i', strtotime($request->class_date));
        $programNote->end_time = date('H:i', strtotime($request->class_date));
        $programNote->day = date('N', strtotime($request->class_date));
        $programNote->save();

        return response()->json(['status' => true, 'message' => "Class saved successfully"]);
    }

    public function viewattendance(ProgramNote $programNote, Request $request)
    {

        $view = view("components.admin.modals.view-attendance", compact('programNote'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function attendancelist(Request $request)
    {

        $class_id = $request->class_id;

        if ($request->ajax()) {

            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'program_note_students.id';
            }

            $qry = ProgramNoteStudent::select("r.*")->where("program_note_id", "=", $class_id)
                ->join("program_rosters as r", "program_note_students.student", "=", "r.id");



            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });



            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;


            foreach ($result as $row) {

                $viewButton = "";
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);


                $data[] = [
                    "id" => $row->id,
                    "student_name" =>  $row->student_name,
                    "class_id" => $row->class_id,

                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }

    public function completeClass(ProgramNote $programNote, Request $request)
    {

        DB::beginTransaction();
        try {
            $currentdatetime = date('Y-m-d H:i:s');
            $program_id = $programNote->program_id;

            $programNote->status = 1;
            $programNote->admin_complete_datetime = $currentdatetime;
            $programNote->save();


            // if ($programNote->sub_user_id && $programNote->subUser && in_array($programNote->user_sub_requested, [1, 2, 3])) {
            if ($programNote->sub_user_id && $programNote->subUser) {

                $user = $programNote->subUser;
                $user_id = $programNote->sub_user_id;
                $instructorType = 0;
            } elseif ($programNote->user_id && $programNote->user) {

                $user = $programNote->user;
                $user_id = $programNote->user_id;
                $instructorType = 1;
            }

            UpdateInstructorClassCompletedByAdmin($programNote);



            $programNote->amount()->delete();


            $payData = CustomHelper::generateClassPayment($user, $programNote);

            $programNoteAmount = new ProgramNoteAmount();
            $programNoteAmount->program_id = $program_id;
            $programNoteAmount->user_id = $user_id ?? null;
            $programNoteAmount->program_note_id = $programNote->id;
            $programNoteAmount->hours = $payData['hours'];
            $programNoteAmount->rate = $payData['rate'];
            $programNoteAmount->minutes = $payData['minutes'];
            $programNoteAmount->format = $payData['format'];
            $programNoteAmount->type = $instructorType;
            $programNoteAmount->amount = $payData['amount'];
            $programNoteAmount->save();


            DB::commit();

            return response()->json(['status' => true, 'message' => "Class completed successfully", 'reload' => true]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                "status" => false,
                "message" =>  $e->getMessage(),
            ]);
        }
    }


    public function markAsHoliday(ProgramNote $programNote, Request $request)
    {
        $programNote->status = 2;
        $programNote->save();
        $programNote->amount()->delete();
        return response()->json(['status' => true, 'message' => "No Class added successfully", 'reload' => true]);
    }
}
