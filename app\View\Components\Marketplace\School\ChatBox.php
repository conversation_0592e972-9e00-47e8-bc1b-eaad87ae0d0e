<?php

namespace App\View\Components\Marketplace\School;

use App\Models\Chat;
use Illuminate\View\Component;

class ChatBox extends Component
{
    public $chats;
    public $recipient;
    public $recipient_role;
    public $message_type;
    public $referance_id;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($recipient = null, $recipient_role = null, $message_type = 'normal', $referance_id = null)
    {
        $this->recipient = $recipient;
        $this->recipient_role = $recipient_role;
        $this->message_type = $message_type;
        $this->referance_id = $referance_id;

         // Fetch messages from chat table based on recipient and message type
         $this->chats = Chat::where('recipient', $recipient)
         ->where('recipient_role', $recipient_role)
         ->where('message_type', $message_type)
         ->when($referance_id, function ($query) use ($referance_id) {
             return $query->where('referance_id', $referance_id);
         })
         ->orderBy('created_at', 'asc')
         ->get();
    }
    // public function completeChatObject() {
    //     $this->chats.
    // }


    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.marketplace.school.chat-box', [
            'chats' => $this->chats]
    );
    }
}
