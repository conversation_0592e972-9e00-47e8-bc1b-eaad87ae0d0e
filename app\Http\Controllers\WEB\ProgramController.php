<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use Session;
use DB;
use App\User;
use App\UserFirstStepModel;
use App\StateModel;
use App\UserThirdStepModel;
use App\Classes;
use App\Subject;
use App\AssessmentsModel;
use App\AvailabilityModel;
use App\AvailabilityRangeModel;
use App\AvailabilityDayTimeSlotsModel;
use App\AdministrativeInfoModel;
use App\AdministrativeAuthorizationModel;
use App\AvailablityLocationModel;
use App\Helpers\CustomHelper;
use App\user_contract;
use App\MailModel;
use App\Programs;
use Validator;
use Auth;
use Hash;
use Illuminate\Http\Request;


class ProgramController extends Controller
{

    public function index()
    {

        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        if (Auth::user()->profile_status == 12) {
            return view('web.user.program.myprogram');
        }
    }

    public function newprogramalerts()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        if (Auth::user()->profile_status == 12) {
            return view('web.user.program.newprogramalerts');
        }
    }

    public function programdetail()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        if (Auth::user()->profile_status == 12) {
            return view('web.user.program.programdetail');
        }
    }



    public function teachingpreferences()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        $data['id'] = $id = Auth::user()->id;
        // $data['state'] = StateModel::where(['country_id' => '239'])->get();
        // $data['grade'] = GradeLevelModel::get();
        $data["subject"] = Subject::orderBy("subject_name", "asc")->get();
        // $data['question'] = QuestionsModel::get();
        $data['class'] = Classes::get();

        $data['user'] = $user = User::where('id', $id)->first();
        // $data['first'] = UserFirstStepModel::where(['user_id' => $id])->first();
        // $data['second'] = UserSecondStepModel::where(['user_id' => $id])->first();
        $data['third'] = UserThirdStepModel::where(['user_id' => $id])->first();
        // $data['four'] = UserFourthStepModel::where(['user_id' => $id])->first();
        // $data['intro'] = AssessmentsModel::where(['user_id' => $id, 'type' => 'introduction'])->first();
        // $data['teachings'] = AssessmentsModel::where(['user_id' => $id, 'type' => 'teaching'])->orderBy('id', 'DESC')->get();
        // $data['classroom'] = AssessmentsModel::where(['user_id' => $id, 'type' => 'classroom'])->get();

        // $data['five'] = AssessmentsModel::where(['user_id' => $id])->first();
        // $data['quiz'] = UserQuizModel::where(['user_id' => $id])->first();
        // $data['education_list'] = EducationListModel::get();
        if (
            $user["profile_status"] == 14 ||
            $user["profile_status"] == 15 ||
            $user["profile_status"] == 17 ||
            $user["profile_status"] == 12 ||
            $user["profile_status"] == 20 ||
            $user["profile_status"] == 8 ||
            $user["profile_status"] == 16
        ) {
            return view('web.user.preferences.teachingpreferences')->with($data);
        } else {
            return redirect("/web-dashboard");
        }
    }

    public function myavailabilityold()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        $user = auth()->user();

        $id = $user->id;
        $id=Auth::user()->id;
        $user=User::where('id',$id)->first();
        $data['user']=$user;
        $data['availability'] =$availability=$user->availability;
        $data['orgCount'] = 0;
        if($availability){
            $data['orgCount'] = $availability->ranges()->count();
        }

        if (
            $user["profile_status"] == 14 ||
            $user["profile_status"] == 15 ||
            $user["profile_status"] == 17 ||
            $user["profile_status"] == 12 ||
            $user["profile_status"] == 20 ||
            $user["profile_status"] == 8 ||
            $user["profile_status"] == 16
        ) {
        if($user->is_approved=='16'){
            return view('web.user.preferences.myavailability_online')->with($data);
        }elseif($user->is_approved=='17'){
            return view('web.user.preferences.myavailability_in_person')->with($data);
        }else{
            return view('web.user.preferences.myavailability')->with($data);
        }

        }else{
            return redirect("/web-dashboard");
        }

    }



    public function payments()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {

            return redirect("/logout");
        }
        $user = auth()->user();
        $user->load('reimbursements','programNoteAmounts');
        $reimbursementProgramIds = $user->reimbursements()->where(function ($que) {
            $que->where('status', 1)->orWhere('status', 3);
        })->pluck('program_id')->toArray();
        $paymentProgramIds =$user->programNoteAmounts->pluck('program_id')->toArray();
        $paymentPrograms = Programs::whereIn('id',$paymentProgramIds)->pluck('name','id');
        $reimbursementPrograms = Programs::whereIn('id',$reimbursementProgramIds)->pluck('name','id');
        return view('web.user.payments.payments',compact('paymentPrograms','reimbursementPrograms'));


    }
    public function administrativeinformation()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        $data["first"] = UserFirstStepModel::where(["user_id" => Auth::user()->id])->first();
        $data["user"] = User::where(["id" => Auth::user()->id])->first();
        $data['admin'] = AdministrativeInfoModel::where(["user_id" => Auth::user()->id])->first();
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        return view('web.user.settings.administrativeinformation')->with($data);
    }


    public function profileinformation()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        $data['users'] = User::where(["id" => Auth::user()->id])->first();
        $data['intros'] = $intro = AssessmentsModel::where(["user_id" => Auth::user()->id, 'type' => 'introduction'])->get();
        $data['teachings'] = AssessmentsModel::where(["user_id" => Auth::user()->id, 'type' => 'teaching'])->get();
        $data['classrooms'] = AssessmentsModel::where(["user_id" => Auth::user()->id, 'type' => 'classroom'])->get();

        return view('web.user.settings.profileinformation')->with($data);
    }

    public function notificationssettings()
    {

        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        return view('web.user.settings.notificationssettings');
    }


    public function passwordmanagement()
    {
        if(empty(Auth::user()->id)){
            return redirect("/");
        }
        if(checkAuth(Auth::user()->status)) {
            return redirect("/logout");
        }
        $data['user'] = User::where(["id" => Auth::user()->id])->first();
        $data['user_contract'] = user_contract::where([
            "user_id" => Auth::user()->id,
        ])->first();
        $data['notice'] = MailModel::where("created_by",  Auth::user()->id)->where("f_type",  'Notice')->first();
        return view('web.user.settings.passwordmanagement')->with($data);
    }


    public function contract()
    {
        return view('web.user.settings.contract');
    }

    public function status()
    {
        return view('web.user.settings.status');
    }

    public function message()
    {

        $data['support'] = User::where(["type" => 12])->orderBy('id', 'desc')->first();


        return view('web.user.message.message')->with($data);;
    }



    public function faq()
    {
        return view('web.user.faq.faq');
    }

    // public function submitAvailability(Request $request){


    //     $validator = Validator::make($request->all(), [
    //         'teach_minimum' => 'required',
    //         'teach_maximum' => 'required',
    //         'teach_online_timezone' => 'required',
    //         'teach_in_person_location' => 'required',

    //     ]);

    //     if ($validator->fails()) {
    //         return response()
    //             ->json([
    //                 'success' => false, 'error' => "Please enter valid details",
    //                 'message' => $validator->errors(),
    //             ], 400);
    //     }
    //     $data = $request->except([
    //         '_token',
    //         'from_date',
    //         'to_date',
    //         'day',
    //         'from_time',
    //         'to_time',
    //     ]);

    //     $data['user_id']=Auth::user()->id;
    //     $data['created_at'] = date('Y-m-d H:i:s');
    //     $data['updated_at'] = date('Y-m-d H:i:s');
    //     $first=AvailabilityModel::where(['user_id'=>Auth::user()->id])->first();
    //     if(!empty($first)){
    //             AvailabilityModel::where(['user_id'=>Auth::user()->id])->delete();
    //     }
    //     $result=AvailabilityModel::insertGetId($data);
    //     if($result) {

    //         $availabilityrangearray=[];
    //         $availabilityrangearrayy=[];
    //         $tdated=$request->to_date;
    //         if($fdate=$request->from_date){
    //             if(!empty(array_filter($fdate['online']))){
    //                 foreach($fdate['online'] as $key=>$fdates){

    //                     $availabilityrangearray[]=['availability_id'=>$result,'type'=>'online','from_date'=>$fdates,'to_date'=>$tdated['online'][$key],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];


    //                 }
    //             }

    //             if(!empty(array_filter($fdate['inperson']))){
    //                 foreach($fdate['inperson'] as $key=>$infdates){

    //                     $availabilityrangearrayy[]=['availability_id'=>$result,'type'=>'inperson','from_date'=>$infdates,'to_date'=>$tdated['inperson'][$key],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
    //                 }
    //             }

    //         }

    //         $rangeData=array_merge($availabilityrangearray,$availabilityrangearrayy);

    //         $rangeid=AvailabilityRangeModel::insert($rangeData);
    //         $day=$request->day;
    //         $from_time=$request->from_time;
    //         $to_time=$request->to_time;
    //         $availabilitytimesloatrray=[];
    //         $availabilitytimesloatrrayy=[];


    //         if($day=$request->day){
    //             if(count(array_filter($day['online']))>0){
    //                 foreach(array_filter($day['online']) as $key=>$ondayrow){

    //                     foreach(array_filter($ondayrow) as $keys=> $rowonlinedata){

    //                          $availabilitytimesloatrray[]=['user_id'=>Auth::user()->id,'position'=>$key,'type'=>'online','day'=>$rowonlinedata,'from_time'=>$from_time['online'][$key][$keys],'to_time'=>$from_time['online'][$key][$keys],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
    //                     }

    //                 }
    //             }

    //             if(count(array_filter($day['inperson']))>0){
    //                 foreach(array_filter($day['inperson']) as $keyy=>$indayrow){
    //                     foreach(array_filter($indayrow) as $keyys=> $rowinpersondata){
    //                     $availabilitytimesloatrrayy[]=['user_id'=>Auth::user()->id,'position'=>$keyy,'type'=>'inperson','day'=>$rowinpersondata,'from_time'=>$from_time['inperson'][$keyy][$keyys],'to_time'=>$to_time['inperson'][$keyy][$keyys],'created_at'=>date('Y-m-d H:i:s'),'updated_at'=>date('Y-m-d H:i:s')];
    //                     }
    //                 }
    //             }

    //         }

    //         $rangeDatas=array_merge($availabilitytimesloatrray,$availabilitytimesloatrrayy);
    //         $already= AvailabilityDayTimeSlotsModel::where(['user_id'=>Auth::user()->id])->get();
    //         if(count($already)>0){
    //             AvailabilityDayTimeSlotsModel::where(['user_id'=>Auth::user()->id])->delete();
    //         }
    //         AvailabilityDayTimeSlotsModel::insert($rangeDatas);
    //         return response()->json(['success' => true, 'message' => 'Submitted successfully']);
    //     }else{
    //         return response()->json(['success' => false, 'error' => 'Something went wrong']);
    //     }
    // }
    public function submitAvailabilityold(Request $request)
    {

        if ($request->avtype == "both") {


            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',
                // 'teach_online_timezone' => 'required',


            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_in_person_location_same',
                'teach_in_person_timezone_same',
                'teach_online_timezone',
                "from_teach_in_person_location",
                "to_teach_in_person_location",
                "avtype"
            ]);
            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;
            $data['teach_in_person_location'] = $request->teach_in_person_location_same;
            $data['to_teach_in_person_location_same'] = $request->to_teach_in_person_location_same;

            $data['user_id'] = Auth::user()->id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $first = AvailabilityModel::where(['user_id' => Auth::user()->id])->first();
            if (!empty($first)) {
                AvailabilityModel::where(['user_id' => Auth::user()->id])->delete();
            } else {
                $dataps["profile_status"] = 8;
                $id = Auth::user()->id;
                $recordps = User::where("id", $id)->first();
                if ($recordps) {
                    // $res = User::where("id", $id)->update($dataps);
                }
            }
            $result = AvailabilityModel::insertGetId($data);
            if ($result) {
                if (isset($request->from_teach_in_person_location) && isset($request->to_teach_in_person_location)) {
                    $location = $request->from_teach_in_person_location;
                    $radius = $request->to_teach_in_person_location;
                    $locationArray = array();
                    foreach ($location as $keyl => $rowslocation) {
                        $locationArray[] = ['user_id' => Auth::user()->id, 'location' => $rowslocation, 'radius' => $radius[$keyl]];
                    }
                    $a = AvailablityLocationModel::where(['user_id' => Auth::user()->id])->get();
                    if (count($a) > 0) {
                        AvailablityLocationModel::where(['user_id' => Auth::user()->id])->delete();
                    }


                    AvailablityLocationModel::insert($locationArray);
                }
                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];
                    $availabilityrangearrayy = [];
                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {
                        if (isset($fdate['online'])) {
                            if (!empty(array_filter($fdate['online']))) {
                                $k = 0;
                                foreach ($fdate['online'] as $key => $fdates) {
                                    $k++;
                                    $availabilityrangearray[] = ['availability_id' => $result, 'type' => 'online', 'step' => $k, 'position' => $k, 'from_date' => $fdates, 'to_date' => $tdated['online'][$key], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                }
                            }
                        }
                        if (isset($fdate['inperson'])) {
                            if (!empty(array_filter($fdate['inperson']))) {
                                foreach ($fdate['inperson'] as $key => $infdates) {

                                    foreach ($infdates as $keys => $rowsdate) {

                                        $availabilityrangearrayy[] = ['availability_id' => $result, 'type' => 'inperson', 'step' => $key, 'position' => $keys, 'from_date' => $rowsdate[0], 'to_date' => $tdated['inperson'][$key][$keys][0], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                            }
                        }
                    }


                    $rangeData = array_merge($availabilityrangearray, $availabilityrangearrayy);

                    $rangeid = AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;

                    $from_teach_in_person_location = $request->from_teach_in_person_location;
                    $to_teach_in_person_location = $request->to_teach_in_person_location;

                    $teach_in_person_timezone = $request->teach_in_person_timezone;
                    $teach_online_timezone = $request->teach_online_timezone;



                    $availabilitytimesloatrray = [];
                    $availabilitytimesloatrrayy = [];


                    if ($day = $request->day) {
                        if (isset($day['online'])) {
                            $arr12 = array_values(array_filter($day['online']));
                            $arr123 = array_combine(range(1, count($arr12)), $arr12);
                            if (count($arr123) > 0) {
                                $fo1 = array_values(array_filter($from_time['online']));
                                $fo12 = array_combine(range(1, count($fo1)), $fo1);

                                $to1 = array_values(array_filter($to_time['online']));
                                $to12 = array_combine(range(1, count($to1)), $to1);

                                foreach ($arr123 as $key => $ondayrow) {

                                    foreach (array_filter($ondayrow) as $keys => $rowonlinedata) {



                                        $availabilitytimesloatrray[] = ['user_id' => Auth::user()->id, 'step' => $key, 'position' => $key, 'type' => 'online', 'day' => $rowonlinedata, 'from_time' => $fo12[$key][$keys], 'to_time' => $to12[$key][$keys], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                            }
                        }
                        if (isset($day['inperson'])) {
                            $arr1 = array_values(array_filter($day['inperson']));
                            $arr = array_combine(range(1, count($arr1)), $arr1);

                            if (count($arr) > 0) {
                                $fi1 = array_values(array_filter($from_time['inperson']));
                                $fi12 = array_combine(range(1, count($fi1)), $fi1);

                                $ti1 = array_values(array_filter($to_time['inperson']));
                                $ti12 = array_combine(range(1, count($ti1)), $ti1);



                                foreach ($arr as $keyy => $indayrow) {

                                    foreach (array_filter($indayrow) as $keyys => $rowinpersondata) {


                                        foreach ($rowinpersondata as $k => $rowspersondata) {

                                            $availabilitytimesloatrrayy[] = ['user_id' => Auth::user()->id, 'step' => $keyy, 'position' => $keyys, 'type' => 'inperson', 'day' => $rowspersondata, 'from_time' => $fi12[$keyy][$keyys][$k], 'to_time' => $ti12[$keyy][$keyys][$k], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $rangeDatas = array_merge($availabilitytimesloatrray, $availabilitytimesloatrrayy);

                    $already = AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id])->get();
                    if (count($already) > 0) {
                        AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id])->delete();
                    }
                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        } elseif ($request->avtype == "online") {

            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',
                // 'teach_online_timezone' => 'required',


            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_online_timezone',
                "avtype"

            ]);
            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;


            $data['user_id'] = Auth::user()->id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $first = AvailabilityModel::where(['user_id' => Auth::user()->id])->first();
            if (!empty($first)) {
                AvailabilityModel::where(['user_id' => Auth::user()->id])->update($data);
                $result = $first->id;
            } else {
                $result = AvailabilityModel::insertGetId($data);
            }

            if ($result) {

                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];

                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {
                        if (isset($fdate['online'])) {
                            if (!empty(array_filter($fdate['online']))) {
                                $k = 0;
                                foreach ($fdate['online'] as $key => $fdates) {
                                    $k++;
                                    $availabilityrangearray[] = ['availability_id' => $result, 'type' => 'online', 'step' => $k, 'position' => $k, 'from_date' => $fdates, 'to_date' => $tdated['online'][$key], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                }
                            }
                        }
                    }


                    $rangeData = $availabilityrangearray;
                    $avi = AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'online'])->get();
                    if (count($avi) > 0) {
                        AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'online'])->delete();
                    }
                    $rangeid = AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;

                    $teach_in_person_timezone = $request->teach_in_person_timezone;

                    $availabilitytimesloatrray = [];

                    if ($day = $request->day) {
                        if (isset($day['online'])) {
                            $arr12 = array_values(array_filter($day['online']));
                            $arr123 = array_combine(range(1, count($arr12)), $arr12);
                            if (count($arr123) > 0) {
                                $fo1 = array_values(array_filter($from_time['online']));
                                $fo12 = array_combine(range(1, count($fo1)), $fo1);

                                $to1 = array_values(array_filter($to_time['online']));
                                $to12 = array_combine(range(1, count($to1)), $to1);

                                foreach ($arr123 as $key => $ondayrow) {

                                    foreach (array_filter($ondayrow) as $keys => $rowonlinedata) {
                                        $availabilitytimesloatrray[] = ['user_id' => Auth::user()->id, 'step' => $key, 'position' => $key, 'type' => 'online', 'day' => $rowonlinedata, 'from_time' => $fo12[$key][$keys], 'to_time' => $to12[$key][$keys], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                            }
                        }
                    }

                    $rangeDatas = $availabilitytimesloatrray;

                    $already = AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'online'])->get();
                    if (count($already) > 0) {
                        AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'online'])->delete();
                    }
                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        } elseif ($request->avtype == "in_person") {

            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',
                // 'teach_online_timezone' => 'required',


            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_in_person_location_same',
                'teach_in_person_timezone_same',
                "from_teach_in_person_location",
                "to_teach_in_person_location",
                "avtype"
            ]);
            if (isset($request->same_as_online)) {
                $data['same_as_online'] = $request->same_as_online;
            } else {
                $data['same_as_online'] = 0;
            }
            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;
            $data['teach_in_person_location'] = $request->teach_in_person_location_same;
            $data['to_teach_in_person_location_same'] = $request->to_teach_in_person_location_same;

            $data['user_id'] = Auth::user()->id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $first = AvailabilityModel::where(['user_id' => Auth::user()->id])->first();
            if (!empty($first)) {
                AvailabilityModel::where(['user_id' => Auth::user()->id])->update($data);
                $result = $first->id;
            } else {
                $result = AvailabilityModel::insertGetId($data);
            }

            if ($result) {
                if (isset($request->from_teach_in_person_location) && isset($request->to_teach_in_person_location)) {
                    $location = $request->from_teach_in_person_location;
                    $radius = $request->to_teach_in_person_location;
                    $locationArray = array();
                    foreach ($location as $keyl => $rowslocation) {
                        $locationArray[] = ['user_id' => Auth::user()->id, 'location' => $rowslocation, 'radius' => $radius[$keyl]];
                    }
                    $a = AvailablityLocationModel::where(['user_id' => Auth::user()->id])->get();
                    if (count($a) > 0) {
                        AvailablityLocationModel::where(['user_id' => Auth::user()->id])->delete();
                    }
                    AvailablityLocationModel::insert($locationArray);
                }
                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];
                    $availabilityrangearrayy = [];
                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {

                        if (isset($fdate['inperson'])) {
                            if (!empty(array_filter($fdate['inperson']))) {
                                foreach ($fdate['inperson'] as $key => $infdates) {

                                    foreach ($infdates as $keys => $rowsdate) {

                                        $availabilityrangearrayy[] = ['availability_id' => $result, 'type' => 'inperson', 'step' => $key, 'position' => $keys, 'from_date' => $rowsdate[0], 'to_date' => $tdated['inperson'][$key][$keys][0], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                            }
                        }
                    }


                    $rangeData = $availabilityrangearrayy;
                    $avi = AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'inperson'])->get();
                    if (count($avi) > 0) {
                        AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'inperson'])->delete();
                    }

                    $rangeid = AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;

                    $from_teach_in_person_location = $request->from_teach_in_person_location;
                    $to_teach_in_person_location = $request->to_teach_in_person_location;

                    $teach_in_person_timezone = $request->teach_in_person_timezone;
                    $teach_online_timezone = $request->teach_online_timezone;



                    $availabilitytimesloatrray = [];
                    $availabilitytimesloatrrayy = [];


                    if ($day = $request->day) {

                        if (isset($day['inperson'])) {
                            $arr1 = array_values(array_filter($day['inperson']));
                            $arr = array_combine(range(1, count($arr1)), $arr1);

                            if (count($arr) > 0) {
                                $fi1 = array_values(array_filter($from_time['inperson']));
                                $fi12 = array_combine(range(1, count($fi1)), $fi1);

                                $ti1 = array_values(array_filter($to_time['inperson']));
                                $ti12 = array_combine(range(1, count($ti1)), $ti1);



                                foreach ($arr as $keyy => $indayrow) {

                                    foreach (array_filter($indayrow) as $keyys => $rowinpersondata) {


                                        foreach ($rowinpersondata as $k => $rowspersondata) {

                                            $availabilitytimesloatrrayy[] = ['user_id' => Auth::user()->id, 'step' => $keyy, 'position' => $keyys, 'type' => 'inperson', 'day' => $rowspersondata, 'from_time' => $fi12[$keyy][$keyys][$k], 'to_time' => $ti12[$keyy][$keyys][$k], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $rangeDatas = $availabilitytimesloatrrayy;

                    $already = AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'inperson'])->get();
                    if (count($already) > 0) {
                        AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'inperson'])->delete();
                    }
                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        }
    }

    public function getLocation(Request $request){
        $text=$request->text;
        $details=str_replace(' ','+',$text);
        $data=location($details);

    }

    public function submitAdministrative(Request $request)
    {

        $validator = Validator::make($request->all(), [

            "first_name" => "required",
            "middle_name" => "required",
            "last_name" => "required",
            "address" => "required",
            "apt" => "required",
            "city" => "required",
            "state" => "required",
            "zip_code" => "required",
            "email" => "required",
            "mobile" => "required",
            "dob" => "required",

        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "filename",
            "authoziation_file",
            "discription",
            "authfile",
            "authoziation_files",
            "dob",
            "first_name",
            "last_name"

        ]);

        $data["user_id"] = Auth::user()->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $data["dob"] =  date('Y-m-d',strtotime($request->dob));
        if ($request->hasFile("filename")) {
            $image = $request->file("filename");
            $name = time() . "." . $image->getClientOriginalExtension();
            $destinationPath = public_path("/administrative");
            $image->move($destinationPath, $name);
            $data["valid_id"] = $name;
        }

        // if ($request->hasFile("authoziation_file")) {
        //     $image = $request->file("authoziation_file");
        //     $name1 = time() . "." . $image->getClientOriginalExtension();
        //     $destinationPath = public_path("/authoziation");
        //     $image->move($destinationPath, $name1);
        //     $data["proof_authorization"] = $name1;
        // }
        $user = AdministrativeInfoModel::where(['user_id' => Auth::user()->id])->first();
        if (!empty($user)) {
            AdministrativeInfoModel::where(['user_id' => Auth::user()->id])->update($data);

            $obju["first_name"] = $request->input("first_name");
            $obju["last_name"] = $request->input("last_name");
            $obju["state"] = $request->input("state");
            $obju["city"] = $request->input("city");
           $objl["state"] = $request->input("state");
           $objl["city"] = $request->input("city");
           $objl["zip_code"] = $request->input("zip_code");

            $first = UserFirstStepModel::where([
                "user_id" => Auth::user()->id,
            ])->first();
            if (!empty($first)) {
                UserFirstStepModel::where("user_id", Auth::user()->id)->update($objl);
                User::where("id", Auth::user()->id)->update($obju);
            } else {
                User::where("id", Auth::user()->id)->update($obju);
                UserFirstStepModel::insert($objl);
            }



            AdministrativeAuthorizationModel::where(['admin_auth_id' => $user->id])->delete();
            $indorductionarray = [];
            if ($files = $request->file("authoziation_file")) {

                foreach ($files as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();
                    $destinationPath = public_path("/authoziation_file");
                    $file->move($destinationPath, $name);
                    $indorductionarray[] = [
                        "admin_auth_id" => $user->id,
                        "file" => $name,
                        'description' => $request->discription[$key]

                    ];
                }
                AdministrativeAuthorizationModel::insert($indorductionarray);
            }


            if (count($request->authfile) > 0) {
                foreach ($request->authfile as $kwy => $rowsfile) {
                    $indorductionarrays = [
                        "admin_auth_id" => $user->id,
                        "file" => $rowsfile,
                        'description' => $request->discription[$kwy]

                    ];
                    AdministrativeAuthorizationModel::insert($indorductionarrays);
                }
            }



            $result = true;
        } else {
            $result = AdministrativeInfoModel::insert($data);
            $lastId = DB::getPdo()->lastInsertId();
            $indorductionarray = [];
            if ($files = $request->file("authoziation_file")) {

                foreach ($files as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();
                    $destinationPath = public_path("/authoziation_file");
                    $file->move($destinationPath, $name);
                    $indorductionarray[] = [
                        "admin_auth_id" => $lastId,
                        "file" => $name,
                        'description' => $request->discription[$key]

                    ];
                }
            }

            AdministrativeAuthorizationModel::insert($indorductionarray);
        }

        if ($result) {


            return response()->json([
                "success" => true,
                "message" => "Submitted successfully",
                "redirect" => url("/web-dashboard"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function saveprofile(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "description" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "profile_image",
            "introduction",
            "teaching",
            "classroom",
        ]);

        if ($request->hasFile("profile_image")) {
            $image = $request->file("profile_image");
            $name = time() . "." . $image->getClientOriginalExtension();
            // $destinationPath = public_path("/uploads/institute");
            // $image->move($destinationPath, $name);
            $filename = 'uploads/admin/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $destinationPath =  $filename;

            $data["image"] = $filename;
        }

        User::where(['id' => Auth::user()->id])->update($data);
        $firstintroduction = AssessmentsModel::where(["user_id" => Auth::user()->id,'type'=>'introduction'])->first();
        if (!empty($firstintroduction)) {
        $this->uploadAssessmentsFile($request, "introduction");
       }else{
        $this->uploadnewAssessmentsFile($request, "introduction");
        }

        $firstteaching = AssessmentsModel::where(["user_id" => Auth::user()->id,'type'=>'teaching'])->first();
        if (!empty($firstteaching)) {
        $this->uploadAssessmentsFile($request, "teaching");
       }else{
        $this->uploadnewAssessmentsFile($request, "teaching");
        }

        $firstclassroom= AssessmentsModel::where(["user_id" => Auth::user()->id,'type'=>'classroom'])->first();
        if (!empty($firstclassroom)) {
        $this->uploadAssessmentsFile($request, "classroom");
       }else{
        $this->uploadnewAssessmentsFile($request, "classroom");
        }



        return response()->json([
            "success" => true,
            "message" => "Application Saved",
        ]);
    }
    function uploadnewAssessmentsFile(Request $request, $type)
    {
        $classroomarray = [];
        $teachingarray = [];
        $indorductionarray = [];

        if ($type=='introduction') {

            if ($files = $request->file("introduction")) {


            foreach ($files as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();
                    // $destinationPath = public_path("/introduction");
                    // $file->move($destinationPath, $name);

                    // $name=time() . "." . $image->getClientOriginalName();
                    $filename = 'uploads/introduction/'. $name;
                    uploads3image($filename,$file);
                    $indorductionarray[] = [
                        "user_id" =>  Auth::user()->id,
                        "type" => "introduction",
                        "subject" => null,
                        "file" => $name,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }

            }
        }
        if ($type=='teaching') {
        if ($files = $request->file("teaching")) {

            foreach ($files as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();
                    // $destinationPath = public_path("/teaching");
                    // $file->move($destinationPath, $name);



                    $filename = 'uploads/teaching/'. $name;
                    uploads3image($filename,$file);
                    $teachingarray[] = [
                        "user_id" =>  Auth::user()->id,
                        "type" => "teaching",
                        "file" => $name,
                        "subject" => null,
                        "topic"=>null,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }

        }
    }
    if ($type=='classroom') {
            if ($files = $request->file("classroom")) {

                foreach ($files as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();
                    // $destinationPath = public_path("/classroom");
                    // $file->move($destinationPath, $name);

                    $filename = 'uploads/classroom/'. $name;
                    uploads3image($filename,$file);

                    $classroomarray[] = [
                        "user_id" =>  Auth::user()->id,
                        "type" => "classroom",
                        "file" => $name,
                        "subject" => null,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];

            }
        }
        }
        $newArray = array_merge(
            $indorductionarray,
            $teachingarray,
            $classroomarray
        );

        $result = AssessmentsModel::insert($newArray);
        return true;
    }

    function uploadAssessmentsFile(Request $request, $type)
    {
        if ($request->has($type)) {
            $files = $request->file($type);

            foreach ($files as $key => $file) {
                $image = $file;
                $name = time() . "." . $image->getClientOriginalExtension();

                $filename = 'uploads/'.$type.'/'. $name;

                uploads3image($filename,$image);

                $obj = AssessmentsModel::find($key);
                $obj->file = $name;
                $obj->save();
            }
        } elseif ($request->hasFile($type)) {
            $image = $request->file($type);
            // $name = time() . "." . $image->getClientOriginalExtension();
            // $destinationPath = public_path("/$type");
            // $image->move($destinationPath, $name);
            $name = time() . "." . $image->getClientOriginalExtension();

            $filename = 'uploads/'.$type.'/'. $name;

            uploads3image($filename,$image);
            $fileArray = [
                "user_id" => Auth::user()->id,
                "type" => $type,
                "subject" => null,
                "file" => $name,
            ];

            AssessmentsModel::create($fileArray);
        }

        return true;
    }
    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "old_password" => "required",
            "password" => "required|string|confirmed",
        ]);

        $user = auth()->user();
        if (!Hash::check($request->old_password, $user->password)) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => 'Old password is wrong',
                ],
                200
            );
        };
        if ($request->old_password == $request->password) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => 'Old password and new password cannot be same',
                ],
                200
            );
        };
        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors()->all(),
                ],
                400
            );
        }
        $password = $request->input("password");

        $user = auth()->user();
        $user->password = Hash::make($password);
        $user->save();
        return response()->json([
            "success" => true,
            "message" => "Password updated successfully",
        ]);
    }
    public function deleteAccount(Request $request)
    {

        $user = auth()->user();
        if (count($user->activeClasses) > 0 || count($user->activeSubClasses) > 0) {
            return response()->json([
                'success' => false,
                'message' => 'This change impacts an active program. If you would still like to update your availability, first request a replacement or a sub for the impacted programs. Please contact <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>  for additional support.',
            ]);
        }

        $user->status = "2";
        $user->save();

        Auth::logout();
        Session::forget('userewlogin');

        createDeleteOrdisableNotification($user->id, 'user-delete', 'Operations', 'Admin','');

        return response()->json([
            "success" => true,
            "message" => "Account deleted successfully",
            "redirect" => url("/sign-in"),
        ]);
    }


    public function updateAccountStatus(Request $request)
    {

        $user = auth()->user();
        $status  =$request->status;
        if ($status != 1 && (count($user->activeClasses) > 0 || count($user->activeSubClasses) > 0)) {
            return response()->json([
                'success' => false,
                'message' => 'This change impacts an active program. If you would still like to update your availability, first request a replacement or a sub for the impacted programs. Please contact <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a>  for additional support.',
            ]);
        }

        $user->status = $status;
        $user->save();
        if($status==1){

            createDeleteOrdisableNotification($user->id, 'user-enable', 'Operations', 'Admin','');

            $mesaage='Account status Activated successfully';
        }else{
            createDeleteOrdisableNotification($user->id, 'user-disable', 'Operations', 'Admin','');
        $mesaage='Account status Deactivated successfully';


        }
        return response()->json([
            "success" => true,
            "message" => $mesaage,
        ]);
    }


}
