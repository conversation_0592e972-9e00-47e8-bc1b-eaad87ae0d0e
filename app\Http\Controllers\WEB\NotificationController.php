<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\notification;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user= Auth::user();
        $user_id = @$user->id;
        if(empty($user_id)){
            return redirect("/");
        }
        if(checkAuth($user->status)) {
            return redirect("/logout");
        }

        notification::where(["user_id"=>$user_id,"is_read"=>0])->update(['is_read'=>1]);

        $data['notification'] = notification::where("user_id",$user_id)->orderBy("id", "desc")->paginate(10);
        return view('web.user.notification.notification')->with($data);

    }

    public function deletenotification(Request $request)
    {
        
        $id = $request->id;
        if (isset($id)) {

                $res = notification::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }

        }
    }
}