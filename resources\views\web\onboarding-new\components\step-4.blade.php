{{-- <div class="tab-pane fade" role="tabpanel" id="step3" aria-labelledby="step3-tab"> --}}
<style>
    #in-person-map-search-box {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0.8;
    }

    #in_person_map_search_input {
        width: 40vw;
        min-width: 280px;
        padding: 8px;
        border-radius: 1rem;
        outline: 0;
        border: 1px solid;
    }

    .select2-selection__rendered {
        overflow-x: auto !important;
    }

    .select2-selection__rendered::-webkit-scrollbar {
        height: 4px;
    }

    .select2-selection__rendered::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    .select2-selection__rendered::-webkit-scrollbar-thumb:hover {
        background: #3f3f3f;
    }

    .select2-selection__rendered::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        background-color: #F5F5F5;
    }

    .select2-selection__rendered::-webkit-scrollbar-track:hover {
        background: #e0e0e0;
    }

    .form-select {
        cursor: pointer;
    }

    .curriculum-container .form__check input[type='radio'] {
        width: 20px !important;
    }

    #curriculumOne:checked {
        background-color: #004CBD;
        accent-color: #004CBD;
        /* Modern browsers support this */
    }

    .select2-selection__arrow {
        display: none;
    }

    span.select2-selection.select2-selection--single {
        display: block !important;
        -moz-padding-start: calc(0.75rem - 3px) !important;
        background-image: url(../../img/Vector.svg) !important;
        background-repeat: no-repeat !important;
        background-position: right 18px center !important;
        background-size: 16px 12px !important;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
        border-radius: 30px;
        height: 48px !important;
    }

    .select2-selection.select2-selection--single span.select2-selection__rendered {
        padding: 8px 10px;
        height: 48px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #004CBD !important;
        color: #fff !important;
        border-radius: 0.375rem !important;
        padding: 0.25rem 0.75rem !important;
        margin-right: 0.5rem !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: #fff !important;
        margin-left: 0.5rem !important;
    }

    .select2-container--default .select2-selection--multiple {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }
  .custom-checkbox_new {
    appearance: none;
    width: 13px;
    height: 13px;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    background-color: white;
}

.custom-checkbox_new:checked {
    background-color: #004CBD;
    border-color: #004CBD;
}

.custom-checkbox_new:checked::after {
    content: '✔';
    color: white;
    font-size: 10px;
    position: absolute;
    top: 0;
    left: 2px;
    font-weight: bold;
    line-height: 13px;
}
.custom-checkbox_new.error-border {
   
    border:1px solid red;
    background-color: #ffe6e6
}



   
</style>
<form action="#" method="post" id="thirdstepid">
    {!! csrf_field() !!}
    <input type="hidden" name="id" value="{{ $data['id'] }}">
    <div class="row">
        <div class="login__heading">
            <h5 class="login__title mt-5">Your Preferences</h5>
            {{-- <p class="login__description">
                Please enter your teaching preferences. You will be able edit your preferences later.
            </p> --}}
        </div>
        <div class="col-xl-6 col-md-6 ">
            <h4 class="form-heading onboarding-step-form">Grade Levels*</h4>
            <div class="login__form  sel" style="position: relative;">
                <select multiple placeholder="I prefer to teach" data-allow-clear="1" class="form-select fselect label "
                    id="i_prefer_to_teach" name="i_prefer_to_teach[]">

                    @if (!empty($data['class']))
                    @foreach ($data['class'] as $rowss)
                    <option value="{{ $rowss->id }}"
                        @if (!empty($data['third'])) @php $prvalue=explode(",",$data['third']->i_prefer_to_teach); @endphp
                        @if (in_array($rowss->id, $prvalue)) {{ 'selected' }} @endif
                        @endif>{{ $rowss->class_name }}</option>
                    @endforeach
                    @endif
                </select>

            </div>
            <span id="i_prefer_to_teach_error" class="error rederror"></span>
        </div>
        <?php
        if (!empty($data['third'])) {
            $usersubject = \App\InstructorSubjectsThirdStepOnboardingModel::where(['step_id' => $data['third']->id])
                ->orderBy('id', 'asc')
                ->get();
        }
        ?>
        <div class="col-xl-12 col-md-12 field_wrapper multsubjects">
            <div class="row d-flex ms-0">
                <div class="subjects-heading" style="margin-top: 32px;">
                    <h4 class="form-heading heading-out">Select Subjects*</h4>
                    <small>
                        <i style="color:#6F6C90">Note: Select up to 7 subjects. You will be able to add more subjects later</i>
                    </small>
                </div>
                @for ($i = 0; $i < 7; $i++)
                    <div class="row d-flex flex{{$i}} align removesubjother{{$i}} addremovemargin <?php
                                                                                                    if (!empty($usersubject)) {
                                                                                                        if (isset($usersubject[$i]['subject'])) {
                                                                                                            echo 'displayblock';
                                                                                                        } else {
                                                                                                            echo 'displaynone';
                                                                                                        }
                                                                                                    } elseif ($i == 0) {
                                                                                                        echo 'displayblock';
                                                                                                    } else {
                                                                                                        echo 'displaynone';
                                                                                                    } ?>" data-id="{{$i}}">
                    <div class="col-xl-6 col-md-6 ">
                        <div class="login__form sel @if($i > 0) @endif" style="position: relative;@if ($i == 0) margin-top: 63px;  @endif">
                            <input type="hidden" id="select_subject" value="no">
                            <select class="form-select selectRound subj{{$i}} select2" id="subject_{{$i}}" @if(!empty($usersubject) && !empty($usersubject[$i])) value="{{ $usersubject[$i]['sub_subject'] }}" @endif aria-label="Select Subjects" name="subject[]"
                                id="sub{{$i}}" data-id="{{$i}}">
                                <option value="" hidden>Select Subject</option>
                                @if (!empty($data['subject']))
                                @foreach ($data['subject'] as $rows)
                                <optgroup label="{{$rows->subject_area}}" data-id="{{$rows->id}}" data-selected="@if(!empty($usersubject) && !empty($usersubject[$i])){{ $usersubject[$i]['sub_subject'] }}@endif">
                                </optgroup>
                                @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-4 ps-4">
                        @if($i == 0)
                        <h4 class="form-heading" style="margin-top: 32px; margin-bottom: 0px; margin-left:12px;">Select Proficiency</h4>
                        @endif
                        <div class="login__form select rangeselect" style="position: relative; margin-top: 22px;">
                            <div slider id="slider-distance">
                                <p class="rangenumber" style="margin-bottom:0px;">0</p>
                                <fieldset class="slider-container w-100">
                                    <?php

                                    if (!empty($usersubject)) {
                                        if (isset($usersubject[$i]['proficiency'])) {
                                            $ra1 = (($usersubject[$i]['proficiency'] - 0) / (5 - 0)) * 100;
                                            $sty1 = "linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) $ra1%, #E2E2E2 $ra1%, #E2E2E2 100%)";
                                        } else {
                                            $ra1 = ((5 - 0) / (5 - 0)) * 100;
                                            $sty1 = "linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) $ra1%, #E2E2E2 $ra1%, #E2E2E2 100%)";
                                        }
                                    } else {
                                        $ra1 = ((5 - 0) / (5 - 0)) * 100;
                                        $sty1 = "linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) $ra1%, #E2E2E2 $ra1%, #E2E2E2 100%)";
                                    }
                                    ?>
                                    <input type="range" id="input-sliderr" name="range[]" class="input-slider"
                                        min="0" max="5" step="1" value="<?php if (!empty($usersubject)) {
                                                                            if (isset($usersubject[$i]['proficiency'])) {
                                                                                echo $usersubject[$i]['proficiency'];
                                                                            } else {
                                                                                echo '5';
                                                                            }
                                                                        } else {
                                                                            echo '5';
                                                                        } ?>"
                                        style="background:{{ $sty1 }}">
                                </fieldset>
                                <p class="number--label rangenumber" style="margin-bottom:0px;">
                                    <?php if (!empty($usersubject)) {
                                        if (isset($usersubject[$i]['proficiency'])) {
                                            echo $usersubject[$i]['proficiency'];
                                        } else {
                                            echo '5';
                                        }
                                    } else {
                                        echo '5';
                                    } ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    @if ($i > 0)
                    <div class="addremovemargin remove-btn-left" style="width: 50px;">
                        <a href="javascript:void(0);" class="remove" data-id="{{$i}}" data-subid="{{$i}}">
                            <svg width="25" height="25" viewBox="0 0 25 25" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12.5" cy="12.5" r="12.5" fill="#A7A9B7"></circle>
                                <rect x="6" y="11" width="12.94" height="2.41935" rx="1.20968" fill="white">
                                </rect>
                            </svg>
                        </a>
                    </div>
                    @endif
            </div>
            @endfor
        </div>
        <span id="subject_error" class="error rederror"></span>
        <div class="addremovemargin ">
            <a href="javascript:void(0);" class="add_button" title="Add field"><svg class="plusclass"
                    width="31" height="31" viewBox="0 0 31 31" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_1511_1023)">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M15.5 0C6.944 0 0 6.944 0 15.5C0 24.056 6.944 31 15.5 31C24.056 31 31 24.056 31 15.5C31 6.944 24.056 0 15.5 0ZM21.7 17.05H17.05V21.7C17.05 22.5525 16.3525 23.25 15.5 23.25C14.6475 23.25 13.95 22.5525 13.95 21.7V17.05H9.3C8.4475 17.05 7.75 16.3525 7.75 15.5C7.75 14.6475 8.4475 13.95 9.3 13.95H13.95V9.3C13.95 8.4475 14.6475 7.75 15.5 7.75C16.3525 7.75 17.05 8.4475 17.05 9.3V13.95H21.7C22.5525 13.95 23.25 14.6475 23.25 15.5C23.25 16.3525 22.5525 17.05 21.7 17.05Z"
                            fill="#004CBD" />
                    </g>
                    <defs>
                        <clipPath id="clip0_1511_1023">
                            <rect width="31" height="31" fill="white" />
                        </clipPath>
                    </defs>
                </svg>&nbsp; Add More</a>
        </div>
    </div>

    <div class="col-xl-12 col-md-12 mt-5">
        <div class="row login__form form-title case_management">
            <h4 class="form-heading">Case Management</h4>
            <div class="check-box">
                <div class="form__check d-flex align-items-center">
                    <input type="checkbox" @if(!empty($data['third']) && $data['third']->case_management) checked @endif name="case_management" id="case_management">
                    <span class="custom-checkbox"></span>
                    <label for="forgot">I am interested in case management opportunities</label>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-12 col-md-12 mt-5">
        <div class="row login__form form-title">
            <h4 class="form-heading">Languages*</h4>
            <div class="col-xl-6 col-md-6 login__form  sel">
                <select multiple placeholder="Language(s) that I teach in" data-allow-clear="1"
                    class="form-select fselect label " id="language_teach_that_i_teach"
                    name="language_teach_that_i_teach[]">
                    @if (!empty($data['languages']) && !empty($data['languages']->value))
                    @php $prvalue = !empty($data['third']) ? explode(",", $data['third']->language_teach_that_i_teach) : []; @endphp
                    @foreach (json_decode($data['languages']->value) as $rowss)
                    <option value="{{ $rowss }}"
                        @if (in_array($rowss, $prvalue)) {{ 'selected' }} @endif>{{ $rowss }}
                    </option>
                    @endforeach
                    {{-- <option value="other"
                                @if (in_array('other', $prvalue)) {{ 'selected' }} @endif
                    >Other</option> --}}
                    @endif
                    <option value="Other"
                        @if (!empty($data['third']) && in_array('Other', explode(",", $data['third']->language_teach_that_i_teach)))
                        {{ 'selected' }}
                        @endif>
                        Other
                    </option>
                </select>
            </div>
            <div class="col-xl-6 col-md-6 login__form sel @if (!empty($data['third']) && in_array('Other', explode(" ,", $data['third']->language_teach_that_i_teach)))
                @else
                d-none
                @endif" id="other-language-container">
                <input type="text" class="common__login__input other_language" name="other_language" id="other_language" placeholder="Other Language*" @if (!empty($data['third']->other_language)) value="{{ $data['third']->other_language }}"@endif>
            </div>
        </div>
    </div>

    <div class="col-xl-12 col-md-12 mt-5">
        <div class="row login__form form-title">
            <h4 class="form-heading">Class Type*</h4>
            <div class="col-xl-6 col-md-6 login__form sel">
                <select multiple placeholder="Program Type" data-allow-clear="1"
                    class="form-select fselect label" id="program_type" name="program_type[]">
                    @if (!empty($data['programs']) && !empty($data['programs']->value))
                    @foreach (json_decode($data['programs']->value) as $rowss)
                    <option value="{{ $rowss }}"
                        @if (!empty($data['third']))
                        @php $prvalue=explode(",", $data['third']->program_type); @endphp
                        @if (in_array($rowss, $prvalue))
                        {{ 'selected' }}
                        @endif
                        @endif>
                        {{ $rowss }}
                    </option>
                    @endforeach
                    @endif
                    <option value="Other"
                        @if (!empty($data['third']) && in_array('Other', explode(",", $data['third']->program_type)))
                        {{ 'selected' }}
                        @endif>
                        Other
                    </option>
                </select>
            </div>
            <div class="col-xl-6 col-md-6 login__form sel @if (!empty($data['third']) && in_array('Other', explode(" ,", $data['third']->program_type))) @else d-none @endif" id="other-program-container">
                <input type="text" class="common__login__input other_program" name="other_program_type" id="other_program" placeholder="Other Program Type*" @if (!empty($data['third']->other_program_type)) value="{{ $data['third']->other_program_type }}"@endif>
            </div>
        </div>
    </div>

    <div class="col-xl-12 col-md-12 mt-5">
        <div class="login__form form-title format">
            <h4 class="form-heading">Delivery Mode*</h4>
            <p>I would like to teach </p>
            <div class="check-box d-flex">
                <div class="login__form d-flex justify-content-between flex-wrap gap-2" style="margin-top: 0px; margin-right: 15px;">
                    <div class="form__check d-flex align-items-center">
                        <input type="checkbox" name="format[]" value="online" id="formatone"
                            @if (!empty($data['third'])) <?php $for = explode(',', $data['third']->format);
                                                            if (in_array('online', $for)) {
                                                                echo 'checked';
                                                            } ?> @endif
                            onchange="updateCompensation('online', this)">
                        <span class="custom-checkbox"></span>
                        <label for="forgot">Online</label>
                    </div>
                </div>
                <div class="login__form d-flex justify-content-between flex-wrap gap-2" style="margin-top: 0px; margin-right:15px;">
                    <div class="form__check d-flex align-items-center">
                        <input type="checkbox" name="format[]" value="in-person" id="formatwo"
                            @if (!empty($data['third'])) <?php $for = explode(',', $data['third']->format);
                                                            if (in_array('in-person', $for)) {
                                                                echo 'checked';
                                                            } ?> @endif
                            onchange="updateCompensation('in-person', this)">
                        <span class="custom-checkbox"></span>
                        <label for="forgot">In-person</label>
                    </div>
                </div>
                <div class="login__form d-flex justify-content-between flex-wrap gap-2" style="margin-top: 0px;">
                    <div class="form__check d-flex align-items-center">
                        <input type="checkbox" name="format[]" value="hybrid" id="formathree"
                            @if (!empty($data['third'])) <?php $for = explode(',', $data['third']->format);
                                                            if (in_array('hybrid', $for)) {
                                                                echo 'checked';
                                                            } ?> @endif
                            onchange="updateCompensation('hybrid', this)">
                        <span class="custom-checkbox"></span>
                        <label for="forgot">Hybrid</label>
                    </div>
                </div>
            </div>
            <p> <i>Hybrid classes meet in-person a few times, although most classes meet online</i></p>

            <!-- This section will be initially hidden and shown only when the "Online" checkbox is checked -->
            @php $for = []; if (!empty($data['third']->format)) {$for = explode(',', $data['third']->format);}@endphp
            <div class="login__form d-flex justify-content-between flex-wrap gap-2
                                {{(empty($data['third']) || !in_array('online', $for))? 'hide': ''}}
                                " style="margin-top: 0px; margin-right: 15px;" id="internetConnectionSection">
                <div class="form__check d-flex align-items-center">
                    <input type="checkbox" name="internet_connection" id="internet_connection"
                        @if (!empty($data['third'])) <?php if ($data['user']['internet_connection'] == '1') {
                                                            echo 'checked';
                                                        } ?> @endif>
                    <span class="custom-checkbox"></span>
                    <label for="internet_connection">I have stable high speed internet connection</label>
                </div>
            </div>
            <div class="login__form d-flex justify-content-between flex-wrap gap-2
                {{(empty($data['third']) || !in_array('online', $for))? 'hide': ''}}
                " style="margin-top: 0px; margin-right: 15px;" id="workFromHomeSection">
                <div class="form__check d-flex align-items-center">
                    <input type="checkbox" name="work_from_home_setup" id="work_from_home_setup"
                        @if (!empty($data['third'])) <?php if ($data['user']['work_from_home_setup'] == '1') {
                                                            echo 'checked';
                                                        } ?> @endif>
                    <span class="custom-checkbox"></span>
                    <label for="work_from_home_setup">I have a work from home setup to ensure quiet and well lit space to conduct online classes</label>
                </div>
            </div>
        </div>
    </div>
    @php
    $formt = [];
    @endphp

    <div class="col-xl-12 col-md-12 mt-1 @if (empty($data['third']->format) && empty($data['third']->compensation)) hide @else show @endif"
        id="compensation-section">
        @if (!empty($data['third']->format))
        @php
        $formt = explode(',', $data['third']->format);
        @endphp
        @endif
        <div class="row login__form form-title">
            <h4 class="form-heading">Compensation*</h4>
            <p>What is your expected hourly pay?</p>
            <div class="d-flex gap-3 transitions-1s">
                @if (!empty($data['third']) && !empty($data['third']->compensation))
                @php
                $comp = !empty($data['third']->compensation) ? $data['third']->compensation : '';
                $compArray = (in_array('hybrid', $formt) && !empty($comp)) ? explode(',', $comp) : [$comp];
                @endphp
                @endif
                <div class="mt-0 login__form col-xl-6 col-md-6 position-relative @if (!empty($data['third']) && (in_array('online', $formt) || in_array('hybrid', $formt))) show @else hide @endif"
                    id="online-compensation">
                    <label class="form__label">Online classes*</label>
                    <input class="common__login__input mt-2" type="number" placeholder="" name="compensation[]" min="0" step="1"
                        id="compensation_online"
                        value="@if ($data['third'] && !empty($data['third']->compensation)){{ in_array('hybrid', $formt) && !empty($compArray[0]) ? $compArray[0] : (in_array('online', $formt) ? $comp : '') }}@endif">
                        <div class="position-absolute" style="top:59%;left:10px">
                            $

                        </div>
                </div>
                <div class="mt-0 login__form col-xl-6 col-md-6 position-relative @if (!empty($data['third']) && (in_array('in-person', $formt) || in_array('hybrid', $formt))) show @else hide @endif"
                    id="in-person-compensation">
                    <label class="form__label">In-person classes*</label>
                    <input class="common__login__input mt-2" type="number" placeholder="" name="compensation[]" min="0" step="1"
                        id="compensation_in_person"
                        value="@if ($data['third'] && !empty($data['third']->compensation)){{ in_array('hybrid', $formt) && !empty($compArray[1]) ? $compArray[1] : (in_array('in-person', $formt) ? $comp : '') }}@endif">
                          <div class="position-absolute" style="top:59%;left:10px">
                            $

                        </div>
                </div>
            </div>
            <span id="compensation_error" class="error rederror"></span>
            <div class="row mt-4">
                <p style="font-style: italic; color: #6F6C90;">*Your expected hourly rate is not guaranteed. Please account for lesson
                    planning, grading or data recording time in your hourly rate.</p>
            </div>
        </div>
    </div>
    <div class="col-xl-12 col-md-12 maps hide">
        <div class="row login__form form-title">
            <h4 class="form-heading" style="border-bottom: 1px solid #dadada; padding-bottom: 10px;">Location Settings</h4>
            <p>Enter your mailing address or select a point on the map to set your tutoring location.</p>
            <div>
                <p>Next, set your <b>Travel radius</b> to specify the distance you would be willing to travel for
                    opportunities.</p>
            </div>
            <div class="login__form col-xl-12 col-md-12">
                <label class="fw-600" for="">Travel radius
                    {{-- <svg height="20px" width="20px" version="1.1" id="_x32_"
                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 512 512" xml:space="preserve" fill="#000000">
                            <g id="SVGRepo_bgCarrier" stroke-width="0" />
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                            <g id="SVGRepo_iconCarrier">
                                <style type="text/css">
                                    .st0 {
                                        fill: #0077b3;
                                    }
                                </style>
                                <g>
                                    <path class="st0"
                                        d="M256,0C114.616,0,0,114.612,0,256s114.616,256,256,256s256-114.612,256-256S397.385,0,256,0z M207.678,378.794 c0-17.612,14.281-31.893,31.893-31.893c17.599,0,31.88,14.281,31.88,31.893c0,17.595-14.281,31.884-31.88,31.884 C221.959,410.678,207.678,396.389,207.678,378.794z M343.625,218.852c-3.596,9.793-8.802,18.289-14.695,25.356 c-11.847,14.148-25.888,22.718-37.442,29.041c-7.719,4.174-14.533,7.389-18.769,9.769c-2.905,1.604-4.479,2.95-5.256,3.826 c-0.768,0.926-1.029,1.306-1.496,2.826c-0.273,1.009-0.558,2.612-0.558,5.091c0,6.868,0,12.512,0,12.512 c0,6.472-5.248,11.728-11.723,11.728h-28.252c-6.475,0-11.732-5.256-11.732-11.728c0,0,0-5.645,0-12.512 c0-6.438,0.752-12.744,2.405-18.777c1.636-6.008,4.215-11.718,7.508-16.694c6.599-10.083,15.542-16.802,23.984-21.48 c7.401-4.074,14.723-7.455,21.516-11.281c6.789-3.793,12.843-7.91,17.302-12.372c2.988-2.975,5.31-6.05,7.087-9.52 c2.335-4.628,3.955-10.067,3.992-18.389c0.012-2.463-0.698-5.702-2.632-9.405c-1.926-3.686-5.066-7.694-9.264-11.29 c-8.45-7.248-20.843-12.545-35.054-12.521c-16.285,0.058-27.186,3.876-35.587,8.62c-8.36,4.776-11.029,9.595-11.029,9.595 c-4.268,3.718-10.603,3.85-15.025,0.314l-21.71-17.397c-2.719-2.173-4.322-5.438-4.396-8.926c-0.063-3.479,1.425-6.81,4.061-9.099 c0,0,6.765-10.43,22.451-19.38c15.62-8.992,36.322-15.488,61.236-15.429c20.215,0,38.839,5.562,54.268,14.661 c15.434,9.148,27.897,21.744,35.851,36.876c5.281,10.074,8.525,21.43,8.533,33.38C349.211,198.042,347.248,209.058,343.625,218.852 z" />
                                </g>
                            </g>
                        </svg> --}}
                </label>
                <div class="d-flex align-items-center justify-content-start gap-3 mt-2">
                    <input type="number" class="common__login__input" name="travel_radius" value="@if($data['google_maps']){{$data['google_maps']['travel_address']}}@endif"
                        style="width: 10%; border-radius: 20px;" id="travel_radius">
                    <p class="mb-0">miles</p>
                </div>
            </div>
            <div class="login__form col-xl-12 col-md-12">
                <p>Your tutoring location will be listed as.</p>
            </div>

            <div class="col-xl-12 col-md-12 position-relative">
                <div
                    id="in-person-map"
                    style="min-height: 300px; height: 50vh"></div>
                <div id="in-person-map-search-box">
                    <input id="in_person_map_search_input" name="in_person_map_search_input" value="@if($data['google_maps']){{$data['google_maps']['address_text']}}@endif" type="text" placeholder="Search for a location...">
                </div>
            </div>

            <input type="hidden" id="in_person_latitude" name="in_person_latitude" value="@if($data['google_maps']){{$data['google_maps']['latitude']}}@endif">
            <input type="hidden" id="in_person_longitude" name="in_person_longitude" value="@if($data['google_maps']){{$data['google_maps']['longitude']}}@endif">
            <input type="hidden" id="in_person_full_address" name="in_person_full_address" value="@if($data['google_maps']){{$data['google_maps']['address_text']}}@endif">
        </div>
    </div>



    <div class="curriculum-container col-xl-12 col-md-12 mt-5">
        <div class="row login__form form-title curriculum">
            <h4 class="form-heading">Curriculum Requirement</h4>
            <div class="check-box">
                <div class="login__form d-flex flex-wrap gap-2">
                    <div class="">
                        <input
                            type="checkbox"
                            class="mt-0 custom-checkbox_new"
                            name="curriculum"
                            value="yes"
                            id="curriculumOne"
                            @if(!empty($data['third']) && $data['third']->curriculum === 'yes') checked @endif
                        >
                        

                    </div>
                    <label for="forgot">I will bring lesson plans *</label>


                    <!-- <div class="form__check d-flex align-items-center">
                            <input type="radio" @if(!empty($data['third']) && !empty($data['third']->curriculum) && $data['third']->curriculum == 'yes') checked @endif class="form-check-input curriculum_radio mt-0" name="curriculum" value="yes" id="curriculumOne">
                            <label for="forgot">Yes</label>
                        </div>
                        <div class="form__check d-flex align-items-center">
                            <input type="radio" @if(!empty($data['third']) && !empty($data['third']->curriculum) && $data['third']->curriculum == 'no') checked @endif class="form-check-input curriculum_radio mt-0" name="curriculum" value="no" id="curriculumTwo">
                            <label for="forgot">No</label>
                        </div> -->
                </div>
                <div class="form__check d-flex align-items-center pt-3">
                    <input type="checkbox" @if(!empty($data['third']) && !empty($data['third']->scope_sequence)) checked @endif name="scope_sequence" value="1" id="curriculumThree">
                    <span class="custom-checkbox"></span>
                    <label for="forgot">I am open to aligning with school provided scope and sequence*</label>
                </div>
            </div>
        </div>
    </div>


    </div>



</form>
{{-- <div class="d-flex bnt">

        <div class="right-btn">
            <a class="btn btn-secondary previous" data-tab="step2"> Previous step</a>
        </div>
        <div class="left-btn">
            <a class="btn btn-info next default__button text-right text-left nextsave" onclick="submitthirdstep('save',this)">Save</a>
            <a class="btn btn-info next default__button text-right thirdloading" onclick="submitthirdstep('save_continue',this)">Save &amp; Continue</a>
        </div>

    </div> --}}
<!-- <div class="d-flex justify-content-between">
                <a class="btn btn-secondary previous"><i class="fas fa-angle-left"></i> Back</a>
                <a class="btn btn-info next">Continue <i class="fas fa-angle-right"></i></a>
            </div> -->
{{-- </div> --}}

<script>
    var pathArray = window.location.pathname.split('/');
    if (pathArray[1] == 'k12connections/application' || pathArray[1] == 'k12connections/application') {
        const sliderInput = document.querySelector(".input-slider");
        const numberLabel = document.querySelector(".number--label");
        const slider = document.getElementById("input-sliderr")
        sliderInput.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel.style.transform = `translateX(${newPosition}px)`;
            // numberLabel.textContent = value;
            $('.number--label').text(value)

            slider.style.background = 'linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) ' +
                percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';

        });

        const sliderInput2 = document.querySelector(".input-slider2");
        const numberLabel2 = document.querySelector(".number--label2");
        const slider2 = document.getElementById("input-slider2")
        sliderInput2.addEventListener("input", () => {

            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput2;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel2.style.transform = `translateX(${newPosition}px)`;
            numberLabel2.textContent = value;
            slider2.style.background = 'linear-gradient(to right, #FC697D 0%, #FC697D ' + percent +
                '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });


        const sliderInput3 = document.querySelector(".input-slider3");
        const numberLabel3 = document.querySelector(".number--label3");
        const slider3 = document.getElementById("input-slider3")
        sliderInput3.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput3;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel3.style.transform = `translateX(${newPosition}px)`;
            numberLabel3.textContent = value;
            slider3.style.background = 'linear-gradient(to right, #7EC7A9 0%, #7EC7A9 ' + percent +
                '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });
        const sliderInput4 = document.querySelector(".input-slider4");
        const numberLabel4 = document.querySelector(".number--label4");
        const slider4 = document.getElementById("input-slider4")
        sliderInput4.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput4;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
            numberLabel4.textContent = value;
            slider4.style.background = 'linear-gradient(to right, #FC9F43 0%, #FC9F43 ' + percent +
                '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });


        const sliderInput5 = document.querySelector(".input-slider5");
        const numberLabel5 = document.querySelector(".number--label5");
        const slider5 = document.getElementById("input-slider5")
        sliderInput5.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput5;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
            numberLabel5.textContent = value;
            slider5.style.background = 'linear-gradient(to right, rgb(60 137 251) 0%, rgb(60 137 251) ' +
                percent + '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });

        const sliderInput6 = document.querySelector(".input-slider6");
        const numberLabel6 = document.querySelector(".number--label6");
        const slider6 = document.getElementById("input-slider6")
        sliderInput6.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput6;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
            numberLabel6.textContent = value;
            slider6.style.background = 'linear-gradient(to right, #FC697D 0%, #FC697D ' + percent +
                '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });

        const sliderInput7 = document.querySelector(".input-slider7");
        const numberLabel7 = document.querySelector(".number--label7");
        const slider7 = document.getElementById("input-slider7")
        sliderInput7.addEventListener("input", () => {
            const {
                value,
                min,
                max,
                offsetWidth
            } = sliderInput7;
            const percent = ((value - min) / (max - min)) * 100;
            const newPosition = percent * (offsetWidth / 100);
            //   numberLabel4.style.transform = `translateX(${newPosition}px)`;
            numberLabel7.textContent = value;
            slider7.style.background = 'linear-gradient(to right, #fc9f43 0%, #fc9f43 ' + percent +
                '%, #E2E2E2 ' + percent + '%, #E2E2E2 100%)';
        });


    }
</script>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCjbhs5R7IoIq8x7SE5AfQ6bx1gylGrcLI&loading=async&callback=initOnboardingAutocompletemap&libraries=places&v=weekly" async defer></script>