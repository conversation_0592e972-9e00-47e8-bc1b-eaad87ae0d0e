<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CancelledSub extends Model
{
    protected $table = 'cancelled_subs';

    protected $fillable = [
        'user_id', 'program_note_id', 'request_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function programNote()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    public function cancelRequest()
    {
        return $this->belongsTo(CancelSubRequest::class, 'request_id');
    }
}
