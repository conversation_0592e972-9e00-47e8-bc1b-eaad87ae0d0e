<?php

namespace App\Http\Controllers;

use App\ActivitiesModel;

use App\Http\Requests;
use Validator;
use View;
use URL;
use DateTime;
use Illuminate\Http\Request;
use Session;
use App\Users;
use App\CommomModel;
use App\notification;
use App\ProgramNote;
use App\Programs;
use Carbon\Carbon;
use Hash;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{

    public function index(Request $request)
    {
        if (Session::has("Adminnewlogin")) {
            if (get_childpermission(get_permission(session('Adminnewlogin')['type']), 'dashboard', 'view') == true) {

                $notification = notification::where('user_type', 'Admin')->where('is_read', 0)->orderBy("id", "desc")->get();
                $notificationList = notification::where('user_type', 'Admin')->where('is_read', 0)->orderBy("id", "desc")->limit(10)->get();

                $totaluser = Users::where("id", "!=", 1)->get();
                $totalApplication = Users::where("id", "!=", 1)->where("type", 5)->where("type", "!=", 12)->get();
                $totalInstructor = Users::where("type", 5)->where("profile_status", 12)->get();
                $instructor = Users::where("type", 5)
                    ->orderBy("id", "desc")
                    ->limit(10)
                    ->get();
                $totaActiveRequest = Users::where("type", 5)
                    ->where("status", "2")
                    ->get();
                $totalSchool = Users::where("type", 6)->get();
                $schools = Users::where("type", 6)
                    ->orderBy("id", "desc")
                    ->limit(10)
                    ->get();
                $totalRecruiter = Users::where("type", 4)->get();
                $totalProgram = DB::table("tbl_programs")->get();
                $programs = DB::table("tbl_programs")
                    ->orderBy("id", "desc")
                    ->limit(10)
                    ->get();
                $totalPublishProgram = DB::table("tbl_programs")
                    ->where("program_status", "Publish")
                    ->get();



                $startYear = now()->format('Y') . '-01';
                $endYear = now()->format('Y') . '-12';

                $InstructorMap = DB::table('users')
                    ->select(DB::raw('count(id) as userid'))
                    ->whereBetween(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'), [$startYear, $endYear])
                    ->groupBy(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'))
                    ->where("type", 5)
                    ->where("profile_status", 12)
                    ->pluck('userid')->toArray();

                $SchoolMap = DB::table('users')
                    ->select(DB::raw('count(id) as userid'))
                    ->whereBetween(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'), [$startYear, $endYear])
                    ->groupBy(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'))
                    ->where("type", 6)
                    ->pluck('userid')->toArray();
                $currentDate = now()->toDateString();

                $CompleteprogrmsMap = DB::table('tbl_programs')
                    ->select(DB::raw('count(id) as programid'))
                    ->whereBetween(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'), [$startYear, $endYear])
                    ->groupBy(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'))
                    ->where("program_status", '!=', 'Draft')
                    ->where("end_date", "<",  $currentDate)
                    ->pluck('programid')->toArray();

                $InprogressprogrmsMap = DB::table('tbl_programs')
                    ->select(DB::raw('count(id) as programid'))
                    ->whereBetween(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'), [$startYear, $endYear])
                    ->groupBy(DB::raw('DATE_FORMAT(created_at,"%Y-%m")'))
                    ->where("program_status", '!=', 'Draft')
                    ->where("end_date", ">",  $currentDate)
                    ->pluck('programid')->toArray();

                    $currentDate = now()->toDateString();
                    $todayNotAssigned = ProgramNote::where('class_date', $currentDate)->whereNull(['user_id','sub_user_id','status'])->count();

                    $mainNotAssigned =Programs::published()->where("end_date", "<",  $currentDate)->doesntHave('mainAssignedUser')->count();
                    $subNotAssigned =Programs::published()->where("end_date", "<",  $currentDate)->doesntHave('mainAssignedUser')->doesntHave('subAssignedUser')->count();

                return view("admin.dashboard.dashboard", [
                    "totalSchool" => $totalSchool,
                    "schools" => $schools,
                    "totaluser" => $totaluser,
                    "totalInstructor" => $totalInstructor,
                    "instructor" => $instructor,
                    "programs" => $programs,
                    "totalProgram" => $totalProgram,
                    "totalRecruiter" => $totalRecruiter,
                    "totalPublishProgram" => $totalPublishProgram,
                    "totaActiveRequest" => $totaActiveRequest,
                    "totalApplication" => $totalApplication,
                    "CompleteprogrmsMap" => $CompleteprogrmsMap,
                    "InprogressprogrmsMap" => $InprogressprogrmsMap,
                    "InstructorMap" => $InstructorMap,
                    "SchoolMap" => $SchoolMap,
                    "notification" => $notification,
                    "notificationList" => $notificationList,
                    "todayNotAssigned" => $todayNotAssigned,
                    "mainNotAssigned" => $mainNotAssigned,
                    "subNotAssigned" => $subNotAssigned,
                ]);
            } else {
                return view("admin.permissionpage");
            }
        } else {
            return redirect("/");
        }
    }

    public function profile_details(Request $request)
    {
        if (Session::has("Adminnewlogin")) {
            $id = session("Adminnewlogin")["id"];
            $type = session("Adminnewlogin")["type"];
            $condition = ["id" => $id, "type" => $type];
            $userData = Users::get_single_record($condition);
            return view("admin.admin-profile-details")->with([
                "user" => $userData
            ]);
        } else {
            return redirect("/");
        }
    }

    public function change_password(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        return view("admin.change-password");
    }

    public function updateChangePassword(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $this->validate(
            $request,
            [
                "old_password" => "required",
                "new_password" => "required",
                "confirm_password" => "required",
            ],
            [
                "old_password" => "Old Password Field is required",
                "new_password" => "New Password Field is required",
                "confirm_password" => "Confirm Password Field is required",
            ]
        );

        $id = session("Adminnewlogin")["id"];
        $old_password = $request->input("old_password");
        $new_password = $request->input("new_password");
        $confirm_password = $request->input("confirm_password");

        $login = Users::where("id", $id)->first();

        if (!empty($login)) {
            if (!Hash::check($old_password, $login->password)) {
                return response()->json([
                    "success" => false,
                    "message" => "Current password not matched",
                ]);
            }

            if ($new_password == $confirm_password) {
                $insertpwd = bcrypt($new_password);
                Users::where("id", $id)->update(["password" => $insertpwd]);

                return response()->json([
                    "success" => true,
                    "message" => "Password successfully changed",
                ]);
            } else {
                return response()->json([
                    "success" => true,
                    "message" =>
                    "New password and Confirm password does not matchd",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Password does not exist",
            ]);
        }
    }
    //  edit profile

    public function edit_profile_details(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        $id = session("Adminnewlogin")["id"];
        $type = session("Adminnewlogin")["type"];
        $user = Users::where(["id" => $id, "type" => $type])->get();

        return view("admin.edit-profile")->with([
            "user" => $user,

        ]);
    }

    public function personalInfo(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }

        if ($request->ajax()) {
            $id = session("Adminnewlogin")["id"];
            $type = session("Adminnewlogin")["type"];
            $user = Users::where(["id" => $id, "type" => $type])->first();

            $obj = Users::find($id);
            $obj->first_name = $request->input("first_name");
            $obj->last_name = $request->input("last_name");
            $obj->gender = $request->input("gender");
            $obj->about = $request->input("about");
            $save = $obj->save();
            if ($save) {
                $data["success"] = true;
                $data["message"] = "Successfully updated ";
                if (session("Adminnewlogin")["id"]) {
                    $data["redirect"] = url("edit-admin-profile");
                }
            } else {
                $data["success"] = false;
                $data["message"] = "Something went wrong";
            }
            return response()->json($data);
        }
    }

    public function contactUpdate(Request $request)
    {
        if (!Session::has("Adminnewlogin")) {
            return redirect("/admin");
        }
        if ($request->ajax()) {
            $id = session("Adminnewlogin")["id"];
            $type = session("Adminnewlogin")["type"];
            $user = Users::where(["id" => $id, "type" => $type])->first();
            $obj = Users::find($id);
            $obj->phone_number = $request->input("phone_number");
            $obj->email = $request->input("email");
            $save = $obj->save();
            if ($save) {
                $data["success"] = true;
                $data["message"] = "Contact Info Updated successfully";
                if (session("Adminnewlogin")["id"]) {
                    $data["redirect"] = url("edit-admin-profile");
                }
            } else {
                $data["success"] = false;
                $data["message"] = "Something went wrong";
            }
            return response()->json($data);
        }
    }

    public function imageupload(Request $request)
    {
        if ($request->hasFile("profile_upload")) {
            $image = $request->file("profile_upload");
            $filename = 'uploads/admin/' . uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename, $image);
            $url = generateSignedUrl($filename);
            $adminprofile = $filename;
        }
        $id = session("Adminnewlogin")["id"];
        $type = session("Adminnewlogin")["type"];
        $user = Users::where(["id" => $id, "type" => $type])->first();
        $obj = Users::find($id);
        $obj->image = $adminprofile;
        $result = $obj->save();
        if ($result) {
            $data["success"] = true;
            $data["message"] = "Profile Image Updated successfully";
            $data["img"] = $url;
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }

    
}
