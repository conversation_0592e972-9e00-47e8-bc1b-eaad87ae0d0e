<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBudgetStatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
         Schema::create('budget_states', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->integer('inperson')->default(0);
            $table->integer('case_management')->default(0);
            $table->integer('bilingual_inc')->default(0);
            $table->integer('sped_rec_comp')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('budget_states');
    }
}
