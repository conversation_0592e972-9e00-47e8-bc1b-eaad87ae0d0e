<?php

namespace App\Http\Controllers\Admin;


use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\ReviewModel;
use App\Programs;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ManageReviewsController extends Controller
{

    public function index(Request $request)
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');

        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];

        $sidebarMenu = 'manage-reviews';

        if ($request->ajax()) {
            $query = ReviewModel::with('program','user','fromuser');
            $params = DataTableHelper::getParams($request);


            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            $query->where(function ($que) use ($searchValue) {
                $que->whereHas('program', function ($query) use ($searchValue) {
                    $query->where('tbl_programs.name', 'LIKE', "%{$searchValue}%");
                })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    })
                    ->orWhereHas('fromuser', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    });
            });


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);


            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {

                $action =  '';
                $viewUser = $viewProgram =$viewFromUser= "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a target='_blank' href='{$viewProgramRoute}'>{$row->program->id}</a>";
                }

                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->to_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a target='_blank' href='{$viewUserRoute}'>{$userName}</a>";
                }
                if ($row->from_id) {
                    if($row->fromuser->type=='5'){
                        $viewUserFromRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->first_name . ' ' . $row->fromuser->last_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }elseif($row->fromuser->type=='6'){

                        $viewUserFromRoute =  url('viewschooldetails/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->full_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }else{
                        $viewUserFromRoute =  url('viewdetails/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->first_name . ' ' . $row->fromuser->last_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }


                }

                $encryptedStrId = encrypt_str($row->id);
                $action .= $this->generateActionButtons($encryptedStrId,$res);

                $data[] = [
                    "id" => $row->id,
                    "program_id" => $viewProgram,
                    "to_id" => $viewUser,
                    "rating" => $row->rating ? $row->rating : '',
                    "review" => $row->review,
                    "from_id" => $viewFromUser ? $viewFromUser : '',
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.manage-reviews.reviews", compact("sidebarMenu"));
    }
    private function generateActionButtons($encryptedStrId,$res)
    {

        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = '';
        if (isset($res['manageReview'])) :
            if (array_key_exists('manageReview', $res)) :

                if (in_array('update', json_decode($res['manageReview'], true))) :

                    $editRoute = route('admin.review.edit', ['reviewId' => $encryptedStrId]);
                    $editButton = "<a href='{$actionUrl}' onclick=openAdminModalmd('$editRoute')><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>&nbsp;";
                endif;

                if (in_array('delete', json_decode($res['manageReview'], true))) :
                    $deleteButton = "<a class='delete_data_review' href='{$actionUrl}' data-id='{$encryptedStrId}'><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>&nbsp;";

                endif;

            endif;
        endif;




        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$deleteButton}</div>";
    }

    public function deletereview(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = ReviewModel::where("id", $id)->first();
            if ($record) {
                $res = ReviewModel::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
    public function add($user_id, Request $request)
    {
        $id = $user_id;

        $view = view("components.admin.modals.reviewadd", compact('id'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    public function edit($reviewId, Request $request)
    {
        $id = decrypt_str($reviewId);
        $review = ReviewModel::where("id", $id)->first();
        $view = view("components.admin.modals.reviewedit", compact('id','review'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    public function update($reviewId, Request $request)
    {
        $id = $reviewId;
        $request->validate(
            [
                'feedback' => 'required',

            ]
        );


        $data["review"] = $request->feedback;
        $data["rating"] = $request->rating;

        $save = ReviewModel::where("id", $id)->update($data);
        return response()->json(['status' => true, 'message' => "Review updated successfully"]);
    }
    public function save($user_id, Request $request)
    {

        $request->validate(
            [
                'feedback' => 'required',

            ]
        );

    $obj = new ReviewModel();

    $obj->from_id =session()->get('Adminnewlogin')['id'];;
    $obj->to_id =  $user_id;
    $obj->rating = $request->rating?$request->rating:0;
    $obj->review = $request->feedback;

    $obj->save();
    return response()->json(['status' => true, 'message' => "Saved successfully"]);
}
    public function ins_rating($id,Request $request)
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');

        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];


        if ($request->ajax()) {
            $query = ReviewModel::with('program','user','fromuser');
            $query->where('to_id', $id);
            $params = DataTableHelper::getParams($request);


            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            $query->where(function ($que) use ($searchValue) {
                $que->whereHas('program', function ($query) use ($searchValue) {
                    $query->where('tbl_programs.name', 'LIKE', "%{$searchValue}%");
                })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    })
                    ->orWhereHas('fromuser', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    });
            });


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);


            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {

                $action =  '';
                $viewUser = $viewProgram =$viewFromUser= "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a target='_blank' href='{$viewProgramRoute}'>{$row->program->name}</a>";
                }

                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->to_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a target='_blank' href='{$viewUserRoute}'>{$userName}</a>";
                }
                if ($row->from_id) {
                    if($row->fromuser->type=='5'){
                        $viewUserFromRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->first_name . ' ' . $row->fromuser->last_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }elseif($row->fromuser->type=='6'){

                        $viewUserFromRoute =  url('viewschooldetails/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->first_name . ' ' . $row->fromuser->last_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }else{
                        $viewUserFromRoute =  url('viewdetails/' . encrypt_str($row->from_id));
                        $userFromName = $row->fromuser->first_name . ' ' . $row->fromuser->last_name;
                        $viewFromUser = " <a target='_blank' href='{$viewUserFromRoute}'>{$userFromName}</a>";
                    }


                }

                $encryptedStrId = encrypt_str($row->id);
                $action .= $this->generateActionButtons($encryptedStrId,$res);

                $data[] = [
                    "id" => $row->id,
                    "program_id" => $viewProgram,
                    "to_id" => $viewUser,
                    "rating" => $row->rating ? $row->rating : '',
                    "review" => $row->review,
                    "from_id" => $viewFromUser ? $viewFromUser : '',
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }


    }
}
