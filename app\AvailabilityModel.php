<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AvailabilityModel extends Model
{
    protected $table = 'tbl_user_availabilities';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'teach_minimum', 'teach_maximum', 'teach_in_person_timezone', 'teach_in_person_location', 'same_as_online', 'to_teach_in_person_location_same'
    ];
    public function ranges()
    {
        return $this->hasMany(AvailabilityRangeModel::class,'availability_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
