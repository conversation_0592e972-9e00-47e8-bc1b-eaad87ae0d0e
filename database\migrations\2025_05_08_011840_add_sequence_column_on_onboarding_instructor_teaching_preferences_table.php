<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSequenceColumnOnOnboardingInstructorTeachingPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('onboarding_instructor_teaching_preferences', function (Blueprint $table) {
            $table->integer('scope_sequence')->nullable()->after('curriculum');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('onboarding_instructor_teaching_preferences', function (Blueprint $table) {
            $table->dropColumn('scope_sequence');
        });
    }
}
