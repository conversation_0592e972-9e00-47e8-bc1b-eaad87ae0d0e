<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatbotChatsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('chatbot_chats', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('userId')->index();
            $table->string('platform');
            $table->text('userMessage');
            $table->text('botResponse')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('chatbot_chats');
    }
}
