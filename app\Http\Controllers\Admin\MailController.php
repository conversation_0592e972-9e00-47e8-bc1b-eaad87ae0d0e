<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\MailModel;
use App\Helpers\DataTableHelper;
use Mail;
class MailController extends Controller
{

    public function index($id,Request $request)
    {
        $dd = decrypt_str($id);
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'tbl_followup.id';
            }
            $qry = MailModel::where("user_id", "=", $dd)->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');


            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });




            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);



                $action = $this->generateActionButtons($encryptedStrId,$res);
                if(strlen($row->message) > 30){
                    $mess=substr($row->message, 0, 30).'...';

                }else{
                   $mess= $row->message;
                }


                $data[] = [
                    "id" => $row->id,
                    "f_type" => $row->f_type,
                    "title" => $row->title,
                    "subject" => $row->subject,
                    "message" => $mess,
                    "created_by" => getstaffname($row->created_by),
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.instructor.viewmail");
    }

    private function generateActionButtons($encryptedStrId ,$res)
    {
        $viewRoute = "javascript:void(0);";
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton =  '';


        $deleteRoute = route('admin.program.maildelete', ['eid' => $encryptedStrId]);

        $viewRoutes = route('admin.program.show-mail-details', ['eid' => $encryptedStrId]);

        $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute')><button class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";



        $viewButton = "<a href='{$viewRoute}' onclick=openAdminModal('$viewRoutes','#common-admin-modal')><button class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";


        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$viewButton}{$deleteButton}</div>";
    }

    public function show_mail_form($id,Request $request){
        $did = decrypt_str($id);

        $view = view("admin.instructor.mailmodel", compact('did'))->render();
        return response()->json(['status' => true, 'view' => $view]);

    }

    public function show_mail_details($id,Request $request){
        $did = decrypt_str($id);
        $record = MailModel::where("id", $did)->first();
        $view = view("admin.instructor.mailmodeldetails", compact('record'))->render();
        return response()->json(['status' => true, 'view' => $view]);

    }


    public function mailstore($id,Request $request)
    {

        $request->validate(
            [
                'mail_type' => 'required',
                'title' => 'required',
                'subject' => 'required',
                'descriptions' => 'required',
            ]
        );
         $MailModel =new MailModel;
        $MailModel->f_type = $request->mail_type;
        $MailModel->title = $request->title;
        $MailModel->user_id =  $id;
        $MailModel->subject = $request->subject;
        $MailModel->message = $_POST['descriptions'];
        $MailModel->created_by = session()->get('Adminnewlogin')['id'];
        $res=$MailModel->save();

         if($res){
            $user = Users::where("id", $id)->first();
            $email = $user["email"];
            $body=$_POST['descriptions'];
            $data=array('template'=>$body);
            $subject=$request->subject;
            Mail::send('template', $data, function (
                 $message
             ) use ($email,$subject) {
                 $message->to($email)->subject($subject);
             });

             $data1['user_id'] = $id;
             $data1['title'] = $request->title;
             $data1['notification'] = $_POST['descriptions'];
             $data1['type'] = $request->mail_type;
             $data1['is_read'] = 0;
            //  createNotification($data1);
             return response()->json(['status' => true, 'message' => "Sent successfully", 'reload' => true]);
         }




    }



    public function maildelete($id,Request $request)
    {
        $did = decrypt_str($id);

        if (isset($id)) {
            $record = MailModel::where("id", $did)->first();
            if ($record) {
                $res = MailModel::where("id", "=", $did)->delete();

                if ($res) {
                    return response()->json([
                        "status" => true,
                        "message" => "Successfully deleted",
                        'reload' => false
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
