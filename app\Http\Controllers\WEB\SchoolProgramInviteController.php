<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{Programs};
use App\Helpers\SchoolCalanderHelper;

use Illuminate\Http\Request;



class SchoolProgramInviteController extends Controller
{

    public function getTabData(Request $request)
    {
        $tab = $request->tab;
        $events=[];
        $view = view("components.schooltabs.{$tab}", compact('events'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getcalendarTabData(Request $request)
    {
        $user = auth()->user();
        $tab = $request->tab;

        $currentDate = now()->toDateString();


        switch ($tab) {

            case 'today-classes':

                $events = Programs::active()->whereDate('start_date', '<=', $currentDate)
                    ->whereDate('end_date', '>=', $currentDate)
                   ->with('userNotes')
                    ->whereHas('userNotes', function ($query) use ($currentDate) {
                        $query->where(['class_date' => $currentDate]);
                    })
                    ->where('program_status','!=', 'Draft')
                    ->where('school_name', auth()->user()->id)
                    ->get();

                break;

            case 'in-progress':
                $events = Programs::active()
                ->with('userNotes')
                ->whereDate('start_date', '<=', $currentDate)
                    ->whereDate('end_date', '>=', $currentDate)
                    ->where('program_status','!=', 'Draft')
                    ->where('school_name', auth()->user()->id)
                    ->with('schedules')->get();


                break;


            case 'upcoming':
                $events = Programs::active()
                ->with('userNotes')
                ->whereDate('start_date', '>', $currentDate)
                    ->where('program_status','!=', 'Draft')
                    ->where('school_name', auth()->user()->id)
                    ->with('schedules')->get();

                break;

            case 'completed':
                $events = Programs::whereDate('end_date', '<', $currentDate)
                ->with('userNotes')
                ->where('program_status','!=', 'Draft')
                ->where('school_name', auth()->user()->id)
                    ->with('schedules')->get();

                break;

            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }

        $events = SchoolCalanderHelper::getCalendarEvents($events,$tab);
        return response()->json($events);
    }


    public function getListData(Request $request)
    {
        $tab = $request->tab;

        switch ($tab) {
            case 'today-classes':
                $list = $this->todayList($request);
                break;

            case 'in-progress':
                $list = $this->inProgressList($request);
                break;

            case 'upcoming':
                $list = $this->upcomingList($request);
                break;

            case 'completed':
                $list = $this->completedList($request);
                break;



            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }


        return $list;
    }

    public function todayList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();

        $qry = Programs::where('program_status','!=', 'Draft');
        $qry->active();
        $qry->with('userNotes');
        $qry->whereHas('userNotes', function ($query) use ($currentDate) {
            $query->where(['class_date' => $currentDate]);
            $query->where(['status' =>null ]);
        });

        $qry->where('school_name', auth()->user()->id);
        if ($request) {
            $qry->filterList($request);
        }
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy("id", "desc")->get();
        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';



            $encryptedId = encrypt($row->id);




            $encryptedId = encrypt($row->id);

            $classId = @$row->dateClassScheduleschool($currentDate)->first()->id;
            $detailUrl = route('school.program-detail', ['encryptedId' => $encryptedId,'pivot_id'=>@$row->pivot->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";

            $data[] = array(
                "id" => "<a href='{$detailUrl}' class='idtextcolor'>{$classId}</a>",
                "school_name" => @$row->school->full_name,
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "schedule" => getClassScheduleHtml($row->dateClassAdminSchedule($currentDate)->get()), //
                "end_time" => $row->end_time, //
                "name" => "<a href='{$detailUrl}'>{$row->name}</a>", //
                "sname" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "action" => $action,
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function inProgressList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();
        $user = auth()->user();


        $qry = Programs::active()->whereDate('start_date', '<=', $currentDate)
            ->whereDate('end_date', '>=', $currentDate)
            ->where('program_status','!=', 'Draft')
            ->where('school_name', auth()->user()->id);
            if ($request) {
                $qry->filterList($request);
            }
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy("id", "desc")->get();
        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';
            $actionUrl = "javascript:void(0);";



            $encryptedId = encrypt($row->id);


            $detailUrl = route('school.program-detail', ['encryptedId' => $encryptedId,'pivot_id'=>@$row->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            $data[] = array(
                "id" => "<a href='{$detailUrl}'  class='idtextcolor'>{$row->id}</a>",
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                "schedule" =>  getClassScheduleHtml($row->userNotes), //
                "end_time" => $row->end_time, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "action" => $action,
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function upcomingList(Request $request)
    {
        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();

        $qry = Programs::active()->whereDate('start_date', '>', $currentDate)
            ->where('program_status','!=', 'Draft')
             ->where('school_name', auth()->user()->id);
             if ($request) {
                $qry->filterList($request);
            }
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy("id", "desc")->get();
        $data = array();
        $i = 1;
        foreach ($result as $row) {

            $action = '';
            $actionUrl = "javascript:void(0);";


            $encryptedId = encrypt($row->id);


            $detailUrl = route('school.program-detail', ['encryptedId' => $encryptedId,'pivot_id'=>@$row->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            $data[] = array(
                "id" => "<a href='{$detailUrl}'  class='idtextcolor'>{$row->id}</a>",
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                "schedule" =>  getClassScheduleHtml($row->userNotes), //
                "end_time" => $row->end_time, //
                "name" => @$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "action" => $action,
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

    public function completedList(Request $request)
    {

        $draw = $request->draw;
        $row = $request->start;
        $rowperpage = $request->length;
        $currentDate = now()->toDateString();

        $qry = Programs::where(function ($query) use ($currentDate) {
                    $query->where('program_status', 'Completed')
                        ->orWhere('end_date', '<', $currentDate);
                });
        $qry->where('school_name', auth()->user()->id);
        if ($request) {
            $qry->filterList($request);
        }
        $count = $qry->count();
        if ($row) {
            $qry->offset($row);
        }
        $result = $qry->take($rowperpage)->orderBy("id", "desc")->get();
        $data = array();
        $i = 1;

        $actionUrl = "javascript:void(0);";

        foreach ($result as $row) {

            $action = '';

            $encryptedId = encrypt($row->id);


            $detailUrl = route('school.program-detail', ['encryptedId' => $encryptedId,'pivot_id'=>@$row->id]);
            $action .= "<a href='{$detailUrl}' class='default__button text-right action' title='View Details'>View Details</a>";
            $data[] = array(
                "id" => "<a href='{$detailUrl}'  class='idtextcolor'>{$row->id}</a>",
                "delivery_type" => $row->delivery_type,
                "state" => $row->state,
                "city" => $row->city,
                "start_date" =>  date('m-d-Y', strtotime($row->start_date)),
                "end_date" =>  date('m-d-Y', strtotime($row->end_date)),
                "schedule" =>  getClassScheduleHtml($row->userNotes), //
                "end_time" => $row->end_time, //
                "name" =>@$row->subSubject->name ?? '',
                "grade" => $row->formatted_classes,
                "program_type" => $row->program_type,
                "action" => $action,
            );

            $i++;
        }


        ## Response
        $response = array(
            "draw" => $draw,
            "iTotalRecords" => $count,
            "iTotalDisplayRecords" => $count,
            "aaData" => $data
        );

        return json_encode($response);
    }

}
