<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\FaqsModel;
use App\Resources;
use Hash;
use Mail;
use Auth;
DB::enableQueryLog();


class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(Auth::user()) {
            if(session('userewlogin'))
            { 
                Session::forget('schoolloginsession');
                return redirect("/web-dashboard");
            }else{
                return redirect("/k12connections/sign-in");  
            }
        }else{
            return redirect("/k12connections/sign-in");
        }
        
        // return view('web.index');
    }

    public function k12userLogin()
    {
        if(Auth::user()) {
            if(session('instructorlogin'))
            {
                return redirect("/k12connections/dashboard");
            }else{
                return redirect("/k12connections/sign-in");  
            }
        } else {
            return redirect("/k12connections/sign-in");  
        }
    }

    public function faq()
    {

        $data['faqs'] = FaqsModel::where("type",'Instructor')->where("status",'1')->get();
        return view('web.user.faq.faq')->with($data);
    }

    public function resourceDetails($type){
        $data['resource'] = Resources::where("type",$type)->where("status",'1') ->orderBy("title", "asc")->get();
        return view('web.user.resource')->with($data);
    }

    public function resources(){
        $data['resource'] = Resources::where("status",'1') ->orderBy("title", "asc")->get();
        return view('web.user.resource')->with($data);
    }


    public function school_resources(){
        $data['resource'] = Resources::where("status",'1') ->orderBy("title", "asc")->get();
        return view('school.resource')->with($data);
    }

    public function school_faq()
    {

        $data['faqs'] = FaqsModel::where("type",'school')->where("status",'1')->get();
        return view('school.faq')->with($data);
    }

    public function get_user(){
        if(Auth::user()->password){
            $pass=Auth::user()->password;
        }else{
            $pass='123456';
        }
        return json_encode(['email'=>Auth::user()->email,'password'=>'123456']);
       }

}