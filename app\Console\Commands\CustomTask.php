<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App;
class CustomTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'custom:task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // app()->call('App\Http\Controllers\CronController@incompleteApplication');
        $this->info('Custom task executed successfully!');
    }
}
