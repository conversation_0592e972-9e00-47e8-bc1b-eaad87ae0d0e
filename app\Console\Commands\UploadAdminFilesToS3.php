<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use File;

class UploadAdminFilesToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload:admin-files-to-s3';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload files from public/uploads/admin to S3 and remove them locally';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $localPath = public_path('uploads/admin');
        $files = File::allFiles($localPath);

        foreach ($files as $file) {
            $relativePath = str_replace(public_path(), '', $file->getRealPath());
            $s3Path = ltrim($relativePath, '/'); // Remove leading slash

            try {
                // Upload to S3
                Storage::disk('s3')->put($s3Path, file_get_contents($file), '/uploads/');

                // Delete local file
                File::delete($file->getRealPath());

                $this->info("Uploaded and deleted: $s3Path");
            } catch (\Exception $e) {
                $this->error("Failed to upload $s3Path: " . $e->getMessage());
            }
        }

        $this->info('Process completed.');
    }
}
