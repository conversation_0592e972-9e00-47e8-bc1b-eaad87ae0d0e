<?php

namespace App\Http\Controllers;

use App\invite_programs;
use App\Programs;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ProgramInviteController extends Controller
{

    public function send(invite_programs $invite ,Request $request)
    {
        $user = $invite->user();
        $email = @$user->email;
        $subject = 'New Program Invite';
        $view = 'emails.program-invite';
        
        Mail::send($view, ['user'=>$user], function (
            $mail
        ) use ($email,$subject) {
            $mail->to($email)->subject($subject);
        });
        return response()->json(['status' => true, 'message' => "Send Successfully"]);
        
    }

}
