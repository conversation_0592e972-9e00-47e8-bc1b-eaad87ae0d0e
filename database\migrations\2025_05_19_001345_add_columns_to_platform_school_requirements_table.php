<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->string('will_choose_educator')->nullable();
            $table->string('credential_check')->nullable();
            $table->string('special_education_certificate')->nullable();
            $table->string('will_follow_provided_curriculum')->nullable();
            $table->string('provide_schedule_access')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn([
                'educator_type',
                'educator_profile',
                'special_education_certificate',
                'provided_curriculum_access',
                'provide_schedule_access'
            ]);
        });
    }
}
