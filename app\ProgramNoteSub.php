<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ProgramNoteSub extends Model
{
    protected $table = 'program_note_subs';

    protected $fillable = [
        'program_note_id', 'user_id', 'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function programNote()
    {
        return $this->belongsTo(ProgramNote::class, 'program_note_id');
    }
    
}
