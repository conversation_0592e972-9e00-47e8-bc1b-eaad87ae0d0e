<?php

namespace App\Http\Controllers\Admin;

use DB;
use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Validator;
use View;
use URL;
use DateTime;
use App\RosterModel;
use App\Programs;
use Session;
use Hash;
use Mail;
use Excel;
use App\Imports\StudentImport;
use Illuminate\Support\Facades\Crypt;

DB::enableQueryLog();
class RosterController extends Controller
{

    public function addRoster(Programs $program, Request $request)
    {
       
        $view = view("components.admin.modals.add-roster", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function addRosterImport(Programs $program, Request $request)
    {
       
        $view = view("components.admin.modals.add-roster-import", compact('program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function editRoster(RosterModel $roster, Request $request)
    {
        
        $view = view("components.admin.modals.edit-roster", compact('roster'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function store(Programs $program, Request $request)
    {
        $request->validate(
            [
                'student_name' => 'required',
                'grade_level' => 'required',
            ]
        );

        $data = [
            'program_id' => $program->id,
            'student_name' => $request->student_name,
            'class_id' => $request->grade_level
        ];
        $obj = RosterModel::create($data);


        return response()->json(['status' => true, 'message' => "Student saved successfully", 'reload' => true]);
    }


    public function importstore(Programs $program, Request $request)
    {
        $request->validate(
            [
                'sutudent_file' => 'required'
            ]
        );

      
        if (request()->file('sutudent_file')) {
            Excel::import(new StudentImport($program), request()->file('sutudent_file'));

            return response()->json(['status' => true, 'message' => "Student saved successfully", 'reload' => true]);
        }else{
            return response()->json(['status' => false, 'message' => "Select student file", 'reload' => false]);
        }
      
    }

    public function deleteStudent(RosterModel $roster)
    {
        $res = $roster->forceDelete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted", 'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }
}