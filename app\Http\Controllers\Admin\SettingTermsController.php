<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\SettingTermsModel;
use Hash;
use Mail;

DB::enableQueryLog();

class SettingTermsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $setting = SettingTermsModel::where("locale", "en")->get();
        return view("admin.settingterms.listsettingterms", compact("setting"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $id = decrypt_str($request->id);

        $data = [
            "description" => $request->input('description_hidden'),
            "status" => $request->status ?? 1,
            "updated_at" => now(),
        ];

        if ($request->hasFile("file_data")) {
            $file = $request->file("file_data");
            $filename = 'uploads/logistics/' . uniqid() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/logistics'), $filename);
            $data["document"] = $filename;
        }

        $save = SettingTermsModel::where("id", $id)
            ->where("locale", "en")
            ->update($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Details successfully updated",
                "redirect" => url("/terms-setting-list"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    }

    public function statuschange(Request $request)
    {
        $id = $request->id;
        $record = SettingTermsModel::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = 0;
            $res = SettingTermsModel::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = 1;
            $res = SettingTermsModel::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function deleteterms(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = SettingTermsModel::where("id", $id)->first();
            if ($record) {
                $res = SettingTermsModel::where(
                    "id",
                    "=",
                    $id
                )->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewtermssetting($id)
    {
        $terms_id = $id;
        $id = decrypt_str($terms_id);
        $where = ["id" => $id, "locale" => "en"];
        $setting = SettingTermsModel::get_all_record($where);

        return view(
            "admin.settingterms.editsettingterms",
            compact("setting", "terms_id")
        );
    }

    public function termsandcondition(Request $request)
    {
        $condition = SettingTermsModel::where([
            "id" => "2",
            "locale" => "en",
        ])->first();
        return view(
            "admin.settingterms.termsandconditions",
            compact("condition")
        );
    }
    public function termsandconditiongerman(Request $request)
    {
        $condition = SettingTermsModel::where([
            "id" => "2",
            "locale" => "de",
        ])->first();
        return view(
            "admin.settingterms.termsandconditions",
            compact("condition")
        );
    }
}
