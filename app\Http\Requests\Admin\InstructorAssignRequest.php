<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class InstructorAssignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_id' => 'required',
            'admin_type' => 'sometimes|required',
            'deadline' => 'required|date',
        ];
    }
    
    
    public function attributes()
    {
        return [
            'user_id' => 'Instructor',
        ];
    }

}
