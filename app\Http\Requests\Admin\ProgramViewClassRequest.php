<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ProgramViewClassRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'class_date' => 'required|date',
            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
        ];
    }

    public function messages()

    {
        return [
            'end_time.after' => 'The end time must be  after start time.',
        ];
    }
}
