<?php 
// app/Services/ZoomService.php

namespace App\Services;

use GuzzleHttp\Client;
use Log;
use Illuminate\Support\Facades\Http;
use DateTime;
class ZoomService
{
    protected $client;
    protected $apiKey;
    protected $apiSecret;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://api.zoom.us',
        ]);

        // $this->apiKey = config('services.zoom.api_key');
        // $this->apiSecret = config('services.zoom.api_secret');
        $this->apiKey = '6Q52OfNSZOG4QZd5I9Riw';
        $this->apiSecret = 'Z2mIbnabE7C9KvisAyw1jub3Yxvs7xft';
    }

    public function getAcoountId($data)
    {
       
        $response = $this->client->get('accounts/me', [
            'auth' => [$this->apiKey, $this->apiSecret],
        ]);

        $data = json_decode($response->getBody(), true);

        return $data['id'] ?? null;
    }

    public function createSubAccount($data)
    {
        $response = $this->client->post('accounts/sub', [
            'auth' => [$this->apiKey, $this->apiSecret],
            'json' => $data,
        ]);

        return json_decode($response->getBody(), true);
    }

    public function createMeeting($data,$zoomAccount)
    {
        $startTime=$data['start_time'];
      
        $response = $this->client->post('/v2/users/me/meetings', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->newAccessToken($zoomAccount),
                'Content-Type' => 'application/json',
            ],
            'json' => $data,
        ]);

        return json_decode($response->getBody(), true);
    }

    function hasConflictingMeeting($startTime,$zoomAccount) {
      
        $response = $this->client->get('/v2/users/me/meetings', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->newAccessToken($zoomAccount),
                'Content-Type' => 'application/json',
            ]
        ]);
        return false;
    }

    function generateJWT()
    {
        $payload = [
            'iss' => $this->apiKey,
            'exp' => strtotime('+2 minute'),
        ];
    
        try {
            return \Firebase\JWT\JWT::encode($payload, $this->apiSecret,'HS512');
        } catch (\Exception $e) {
            
            return null;
        }
    }
    function newAccessToken($zoomAccount){

       
        $tokenUrl = 'https://zoom.us/oauth/token';
        $data = array(
            'grant_type' => 'account_credentials',
            'account_id'=> $zoomAccount['account_key']
        );
        
        $headers = array(
            "Authorization: Basic " . base64_encode($zoomAccount['app_key'] . ':' . $zoomAccount['secret_key']),
            "Host: zoom.us"
        );
        
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $tokenUrl);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        $result = json_decode($response, true);
        
        if ($result && isset($result['access_token'])) {
            $accessToken = $result['access_token'];
            return $accessToken;
        } else {
            echo  "Failed to obtain access token <pre>";
            echo "</pre>";
            exit;
        }
    }

    protected function generateZoomToken()
    {
        $token = base64_encode($this->apiKey . ':' . $this->apiSecret);

        $client = new Client();

        $response = $client->request('POST', 'https://zoom.us/oauth/token', [
            'headers' => [
                'Authorization' => 'Basic ' . $token,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials',
            ],
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        return $data['access_token'];
    }
}
