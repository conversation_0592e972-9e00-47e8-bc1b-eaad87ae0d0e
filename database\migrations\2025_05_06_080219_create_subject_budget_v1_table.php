<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectBudgetV1Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subject_budget_v1', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('subject_id'); // Foreign key for subject
            $table->decimal('base_pay_0_3', 8, 2)->nullable(); // Base pay for 0-2 years
            $table->decimal('pay_3_6', 8, 2)->nullable(); // Incremental pay for 3-5 years
            $table->decimal('pay_6_10', 8, 2)->nullable(); // Incremental pay for 6-10 years
            $table->decimal('pay_10_plus', 8, 2)->nullable(); // Incremental pay for 10+ years
            $table->decimal('masters_inc', 8, 2)->nullable(); // Master's degree increment
            $table->decimal('doctorate_inc', 8, 2)->nullable(); // Doctorate increment
            $table->decimal('non_tech_time', 8, 2)->nullable(); // Non-teaching time compensation
            $table->decimal('bilingual_inc', 8, 2)->nullable(); // Bilingual teacher increment
            $table->decimal('sped_rec_comp', 8, 2)->nullable(); // SPED data recording compensation
            $table->decimal('curriculum_inc', 8, 2)->nullable(); // Curriculum increment
            $table->timestamps(); // created_at and updated_at

            // Foreign key relation to subjects_v1 table
            $table->foreign('subject_id')->references('id')->on('subjects_v1')->onDelete('cascade');
            
            // Index for fast searching by subject ID
            $table->index('subject_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subject_budget_v1');
    }
}
