<?php

namespace App\Console\Commands\Reminders;

use App\invite_programs;
use App\notification;
use App\Notification_content;
use App\Programs;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class StandByInstructorInviteCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:stand-by-instructor-invite';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to instructors One day after sending the invite to be standby instructor";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = "reminder:stand-by-instructor-invite";

        $programs = Programs::with(['invites' => function ($query) {
            $query->whereNull('status')
                  ->where('is_standby', 1)
                  ->where('deadline', '>=', now());
        }, 'school:id,full_name'])->get();

        $link = url("/new-program-alerts/");

        foreach ($programs as $program) {
            foreach ($program->invites as $invite) {

                $lastSentTime = $invite->reminder_sent; // Get the last reminder time
                $currentTime = now(); // Get the current time

                if ($lastSentTime) {
                    $timeSinceLastSent = $currentTime->diffInHours($lastSentTime); // Check the difference between last reminder and current time
                    if ($timeSinceLastSent < 24) { // If difference is less then 10, skip the process
                        continue;
                    }
                }
                 // Update the new time of reminder
                 $invite->update(['reminder_sent' => now()]);

                // Notification placeholders
                $template = Notification_content::where("signature", $signature)->first();
                $body = @$template->content;
                $delivery_type = $program->delivery_type;
                $body = str_replace('{{link}}', $link, $body);
                $body = str_replace('{{delivery_type}}', $delivery_type, $body);
                $body = str_replace('{{start_date}}', date('m/d/Y', strtotime($program->start_date)), $body);
                $body = str_replace('{{end_date}}', date('m/d/Y', strtotime($program->end_date)), $body);

                if ($program->school) {
                    $body = str_replace('{{school_name}}', @$program->school->full_name, $body);
                }
                if ($delivery_type == 'In-Person') {
                    $city = $program->city ? 'in ' . $program->city : '';
                } else {
                    $city = '';
                }
                $body = str_replace('{{city}}', $city, $body);

                if ($invite->deadline) {
                    $body = str_replace('{{deadline_date}}', $invite->deadline->format('m/d/Y'), $body);
                    $body = str_replace('{{deadline_time}}', $invite->deadline->format('h:i A'), $body);
                }
                // Insert the notification in table
                notification::insert([
                    'title' => 'notification',
                    'user_id' => $invite->user_id,
                    'program_id' => $program->id,
                    'notification' => $body,
                    'type' => "user",
                    'user_type' =>  "user",
                ]);


            }
        }
    }
}
