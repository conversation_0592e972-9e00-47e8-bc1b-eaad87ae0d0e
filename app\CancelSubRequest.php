<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CancelSubRequest extends Model
{
    protected $table = 'cancel_sub_requests';

    protected $fillable = [
        'program_id', 'user_id', 'status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function subs()
    {
        return $this->hasMany(CancelledSub::class, 'request_id');
    }
    public function program()
    {
        return $this->belongsTo(Programs::class, 'program_id');
    }
}
