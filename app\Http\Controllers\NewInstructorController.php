<?php

namespace App\Http\Controllers;

use App\AdditionalCertificate;
use App\AdditionalCertificateCategory;
use App\AdditionalCertificateSubcategory;
use App\BudgetCaseManagementModel;
use Illuminate\Http\Request;
use DB;
use Validator;
use Session;
use App\Http\Requests;
use App\User;
use App\InstructorFirstStepOnboardingModel;
use App\CredentialingAgencyModel;
use App\StateModel;
use App\InstructorSubjectsThirdStepOnboardingModel;
use App\InstructorThirdStepOnboardingModel;
use App\GradeLevelModel;
use App\Classes;
use App\Subject;
use App\QuestionsModel;
use App\EducationListModel;
use App\user_references;
use App\SettingTermsModel;
use App\user_contract;
use App\certifications;
use App\InstructorEducationSecondStepOnboardingModel;
use App\InstructorExperienceTeaching;
use App\InstructorFifthStepOnboardingModel;
use App\InstructorForthStepOnboardingModel;
use App\InstructorOnboardingCertification;
use App\InstructorOtherExperienceOnboardingModel;
use App\InstructorSecondStepOnboardingModel;
use App\InstructorSeventhStepOnboardingModel;
use App\InstructorSixthStepOnboardingModel;
use App\OnboardingIinstructorGoogleMapAddress;
use App\InvitationModel;
use App\invite_application_recruiter;
use App\LastChatModel;
use Barryvdh\DomPDF\Facade\Pdf;
use App\link;
use Carbon\Carbon;
use Hash;
use Mail;
use Auth;

DB::enableQueryLog();

use Crypt;
use PhpParser\Node\Stmt\TryCatch;
use Illuminate\Contracts\Encryption\DecryptException;
use App\OnboardingInstructor;
use App\FaqsModel;
use App\FreeResponseQuestionsModel;
use App\InstructorOnboardingAssessmentModel;
use App\InstructorOnboardingReferences;
use App\Jobs\S3VideoUpload;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\SubjectArea;
use App\Models\InstructorBudgetApprovedModel;
use App\notification;
use App\SampleLesson;
use Illuminate\Support\Facades\Storage;

class NewInstructorController extends Controller
{
    public function instructorlogin()
    {
        if (Auth::guard('instructor')->user() && Auth::guard('instructor')->user()->type == 5 && Auth::guard('instructor')->user()->profile_status == 2) {

            return redirect("/k12connections/dashboard");
        }

        if (Auth::guard('instructor')->user() && Auth::guard('instructor')->user()->type == 5 && Auth::guard('instructor')->user()->profile_status == 1) {
            return redirect()->route('newonboarding');
        }

        return view("web.onboarding-new.auth.login");
    }

    public function signupInstructor()
    {
        if (Auth::guard('instructor')->user() && Auth::guard('instructor')->user()->type == 5 && Auth::guard('instructor')->user()->profile_status == 2) {

            return redirect("/k12connections/dashboard");
        }

        if (Auth::guard('instructor')->user() && Auth::guard('instructor')->user()->type == 5 && Auth::guard('instructor')->user()->profile_status == 1) {

            return redirect()->route('newonboarding');
        }
        return view("web.onboarding-new.auth.signup");
    }

    public function createInstructor(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "email" => "required|string",
            "password" => "required|string",
            "first_name" => "required",
            "last_name" => "required",
            "about" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "password", "agree"]);
        $email = $request->email;

        if ($email != "") {
            $plannames = OnboardingInstructor::where("email", "=", $email)->get();
            if (count($plannames)) {
                return response()->json([
                    "success" => "already",
                    "message" =>
                    "An account with this email already exists. Please sign in to continue.",
                ]);
            }
        }
        $length = 6;
        $randpassword = substr(
            str_shuffle("**********ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $data["type"] = "5";
        $data["status"] = "1";
        $data["password"] = Hash::make($request->input("password"));
        $data["user_id"] = substr(str_shuffle("**********"), 1, $length);
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $result = OnboardingInstructor::insert($data);
        if ($result) {
            Session::forget('schoolloginsession');
            $lastid = DB::getPdo()->lastInsertId();
            $email = $request->email;
            $invitation = InvitationModel::where("email", $email)->first();

            if ($invitation) {

                $datai["application_id"] = $lastid;
                $datai["user_id"] = $invitation->created_by;
                $datai["type"] = 'Recruiter';
                $datai["status"] = '1';
                invite_application_recruiter::insertGetId($datai);
            }

            $template = DB::table("tbl_email_templates")->where("email_template_id", "44")->first();
            $body =  $template->description;

            if ($request->last_name) {
                $full_name = $request->first_name . ' ' . $request->last_name;
            } else {
                $full_name = $request->first_name;
            }

            $body = str_replace('{{NAME}}', $full_name, $body);
            $body = str_replace('{{redirect}}', url("/k12connections/instructor-verify-email-link/" . encrypt($lastid)), $body);

            $subject = $template->subject;

            $data = array('template' => $body);

            try {
                //code...
                // logger()->info(url("/k12connections/instructor-verify-email-link/" . encrypt($lastid)));
                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }


            $user_data = [
                "email" => $email,
                "password" => $request->input("password"),
                "type" => "5",
            ];

            if (Auth::guard('instructor')->attempt($user_data)) {
                // dd(Auth::guard('instructor')->id(),Auth::guard('instructor')->user()->first_name);
                $request
                    ->session()
                    ->put("instructorlogin", [
                        "id" => encrypt(Auth::guard('instructor')->user()->id),
                        "name" => Auth::guard('instructor')->user()->first_name,
                        "email" => Auth::guard('instructor')->user()->email,
                        "type" => Auth::guard('instructor')->user()->type,
                    ]);
            }

            $datalink["link"] = url("/k12connections/application/" . encrypt($lastid));
            $datalink["link_type"] = 'onboarding';
            $datalink["user_id"] = $lastid;
            $saveLink = link::insertGetId($datalink);
            return response()->json([
                "success" => true,
                "message" => "Submitted successfully,Please check your email",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function verifyMarketplaceInstructor(Request $request)
    {
        try {
            $id = decrypt($request->id);
        } catch (DecryptException $e) {
            return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
        }
        $instructor = OnboardingInstructor::find($id);

        if (!empty($instructor)) {

            if ($instructor->email_verify_status != 1) {
                $instructor->profile_status = '1';
                $instructor->email_verify_status = 0;
                $instructor->email_verify_time = date("Y-m-d H:i:s");
                $instructor->app_notification = 1;
                $instructor->email_notification = 1;
                $instructor->save();

                $template = DB::table("tbl_email_templates")->where("email_template_id", "31")->first();
                $body =  $template->description;

                if ($instructor->last_name) {
                    $full_name = $instructor->first_name . ' ' . $instructor->last_name;
                } else {
                    $full_name = $instructor->first_name;
                }
                $email = $instructor->email;
                $body = str_replace('{{NAME}}', $full_name, $body);
                $body = str_replace('{{redirect}}', url("/k12connections/instructor-email-link/" . encrypt($instructor->id)), $body);

                $subject = $template->subject;

                $data = array('template' => $body);

                try {
                    //code...
                    // logger()->info(url("/k12connections/instructor-email-link/" . encrypt($instructor->id)));
                    Mail::send('template', $data, function (
                        $message
                    ) use ($email, $subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }

                $adminUser = User::find(3);
                $recruiterUser = User::where('type', 4)->get();
                $source = (!empty($request->about) && $request->about != 'Select') ? 'Source: ' . $request->about : '';
                $id = Crypt::encryptString($instructor->id);
                $redirect_url = url('/admin/k12connections/set-instructor-id/' . encrypt_str($instructor->id));
                $template1 = DB::table("tbl_email_templates")->where("email_template_id", "32")->first();
                $body1 =  $template1->description;
                $body1 = str_replace('{{UserName}}', $full_name, $body1);
                $body1 = str_replace('{{redirect}}', $redirect_url, $body1);
                $body1 = str_replace('{{source}}', $source, $body1);


                $subject1 = $template1->subject;
                $subject1 = str_replace('{{NAME}}', $full_name, $subject1);
                $data1 = array('template' => $body1);
                // $email1='<EMAIL>';
                $email1 = $adminUser->email;
                try {

                    Mail::send('template', $data1, function (
                        $message
                    ) use ($email1, $subject1) {
                        $message->to($email1)->subject($subject1);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                SendNotificationForReviewInstructor($instructor, $adminUser, $recruiterUser);
                if (!empty($recruiterUser)) {
                    foreach ($recruiterUser as $recruiter) {
                        $email1 = $recruiter->email;
                        try {

                            Mail::send('template', $data1, function (
                                $message
                            ) use ($email1, $subject1) {
                                $message->to($email1)->subject($subject1);
                            });
                        } catch (\Throwable $th) {
                            //throw $th;
                        }
                    }
                }
            }
        }
        return redirect('/k12connections/instructor-email-link/' . encrypt($instructor->id));
    }

    public function instructoremaillink(Request $request)
    {
        try {
            // Attempt to decrypt the ID from the request
            $id = decrypt($request->id);
        } catch (DecryptException $e) {
            // Handle decryption failure
            return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
        }

        // Find the token for the given user ID and ensure it's within the valid timeframe
        $token = link::where('user_id', $id)
            ->where('created_at', '>', Carbon::now()->subHours(24))
            ->where("link_type", "=", 'onboarding')
            ->first();

        if ($token) {
            // Find the user by the decrypted ID
            $user = OnboardingInstructor::find($id);
            if ($user) {
                // Log in the user
                Auth::guard('instructor')->login($user);

                // Store user details in session
                $request->session()->put("instructorlogin", [
                    "id" => encrypt($user->id),
                    "name" => $user->first_name,
                    "email" => $user->email,
                    "type" => $user->type,
                ]);

                // Redirect to the onboarding step with the encrypted ID
                return redirect("/k12connections/application");
            } else {
                // User not found (possibly deleted)
                return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
            }
        } else {
            // Token not found or expired
            return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
        }
    }

    public function verifyInstructor()
    {
        if (isset(Auth::guard('instructor')->user()->id) && Auth::guard('instructor')->user()->type == 5) {
        } else {
            return redirect("/");
        }
        $user = OnboardingInstructor::where(["id" => Auth::guard('instructor')->user()->id])->first();
        if ($user) {
            if ($user["email_verify_status"] == "1") {
                $user_data = [
                    "email" => $user["email"],
                    "password" => $user["password"],
                    "type" => "5",
                ];
                if (Auth::guard('instructor')->attempt($user_data)) {
                    $request
                        ->session()
                        ->put("instructorlogin", [
                            "id" => encrypt(Auth::guard('instructor')->user()->id),
                            "name" => Auth::guard('instructor')->user()->first_name,
                            "email" => Auth::guard('instructor')->user()->email,
                            "type" => Auth::guard('instructor')->user()->type,
                        ]);
                }
                $data["id"] = $user["id"];
                return redirect("/k12connections/application");
            } else {
                return view("web.onboarding-new.auth.verify");
            }
        }
        return view("web.onboarding-new.auth.verify");
    }

    public function verify_instructor_email(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required|string",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id"]);

        $user = OnboardingInstructor::where("id", $request->id)->first();
        if ($user) {
            $email = $user["email"];
            $dataEmail = [
                "email" => $user["email"],
                "mailbody" => "New Staff",
                "first_name" => $user["first_name"],
                "last_name" => $user["last_name"],
                "redirect" => url("/k12connections/instructor-email-link/" . encrypt($request->id)),
            ];
            logger()->info(url("/k12connections/instructor-verify-email-link/" . encrypt($request->id)));
            $template = DB::table("tbl_email_templates")->where("email_template_id", "44")->first();
            $body =  $template->description;
            if ($user["last_name"]) {
                $full_name = $user["first_name"] . ' ' . $user["last_name"];
            } else {
                $full_name = $user["first_name"];
            }

            $body = str_replace('{{NAME}}', $full_name, $body);
            $body = str_replace('{{redirect}}', url("/k12connections/instructor-verify-email-link/" . encrypt($request->id)), $body);

            $subject = $template->subject;

            $data = array('template' => $body);


            try {

                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
            } catch (\Throwable $th) {
            }

            $dataa["email_verify_status"] = 0;
            $dataa["email_verify_time"] = date("Y-m-d H:i:s");
            OnboardingInstructor::where("id", $request->id)->update($dataa);

            link::where("user_id", "=", $request->id)->where("link_type", "=", 'onboarding')->delete();
            $datalink["link"] = url("/k12connections/application/" . encrypt($request->id));
            $datalink["link_type"] = 'onboarding';
            $datalink["user_id"] = $request->id;
            $saveLink = link::insertGetId($datalink);
            return response()->json([
                "success" => true,
                "message" =>
                "Verification email sent",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function forgotpassword()
    {
        return view("web.onboarding-new.auth.forgotpassword");
    }

    public function loginwithinstructoremail(Request $request)
    {
        $this->validate(
            $request,
            ["email" => "required", "password" => "required"],
            [
                "email" => "Email Field is required",
                "password" => "Password Field is required",
            ]
        );

        $email = $request->input("email");
        $password = $request->input("password");

        $user = new OnboardingInstructor();

        $login = OnboardingInstructor::where("email", "=", $email)->first();

        if (!$login) {
            return response()->json([
                "success" => false,
                "message" => "Login Failed, please check email id",
            ]);
        }

        $user_data = [
            "email" => $request->input("email"),
            "password" => $request->input("password"),
            "type" => "5",
        ];
        $remember_me = "";
        if (Auth::guard('instructor')->attempt($user_data, $remember_me)) {
            if (
                !Hash::check(
                    $request->input("password"),
                    Auth::guard('instructor')->user()->password
                )
            ) {
                return response()->json([
                    "success" => false,
                    "message" => "Login Failed. Please check your password",
                ]);
            } else {
                Session::forget('schoolloginsession');
                $request
                    ->session()
                    ->put("instructorlogin", [
                        "id" => encrypt(Auth::guard('instructor')->user()->id),
                        "name" => Auth::guard('instructor')->user()->first_name,
                        "email" => Auth::guard('instructor')->user()->email,
                        "type" => Auth::guard('instructor')->user()->type,
                    ]);

                if ($login["email_verify_status"] == 0) {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please verify your account",
                        "redirect" => url("k12connections/verify-instructor"),
                    ]);
                }

                if ($login["status"] == 0) {
                    return response()->json([
                        "success" => false,
                        "message" => "Your account deactivated",
                    ]);
                }

                if ($login["status"] == 2) {
                    return response()->json([
                        "success" => false,
                        "message" => "Your account deleted",
                    ]);
                }


                if ($login["profile_status"] == 1) {
                    OnboardingInstructor::where("id", $login["id"])->update(["login_at" => date("Y-m-d H:i:s")]);
                    $userTimezone = $request->input('timezone');
                    session(['user_timezone' => $userTimezone]);

                    return response()->json([
                        "success" => "deactivate",
                        "email" => $request->email,
                        "password" => '123456',
                        "message" => "Login Successful",
                        "redirect" => url(
                            "/k12connections/application"
                        ),
                    ]);
                } else {
                    OnboardingInstructor::where("id", $login["id"])->update(["login_at" => date("Y-m-d H:i:s")]);
                    $data["success"] = true;
                    $data['email'] = $request->email;
                    $data['password'] = '123456';
                    $data["message"] = "Login Successful";
                    $data["redirect"] = url("/k12connections/dashboard");
                    return response()->json($data);
                }
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Login Fail, please check password",
            ]);
        }
    }
    // forget password
    public function forgot_instructor_password(Request $request)
    {

        $this->validate(
            $request,
            ["email" => "required|email"],
            ["email" => "Email Field is required"]
        );
        $email = $request->input("email");
        $users = OnboardingInstructor::where(["email" => $email])->first();

        if (!empty($users)) {
            link::where("user_id", "=", $users["id"])->where("link_type", "=", 'password')->delete();
            // $id = Crypt::encrypt($users["id"]);

            $message = "Send Reset Link";
            $to_name = $users["first_name"];
            $to_email = $users["email"];
            $url = url("k12connections/reset-instructor-password/" . encrypt($users["id"]));
            $dataEmail = [
                "email" => $to_email,
                "mailbody" => "Forgot user password",
                "first_name" => $users["first_name"],
                "last_name" => $users["last_name"],
                "redirect" => url("/k12connections/reset-instructor-password/" . encrypt($users["id"])),
            ];

            $template = DB::table("tbl_email_templates")->where("email_template_id", "4")->first();
            $body =  $template->description;
            if ($users["last_name"]) {
                $full_name = $users["first_name"] . ' ' . $users["last_name"];
            } else {
                $full_name = $users["first_name"];
            }

            $body = str_replace('{{NAME}}', $full_name, $body);

            $body = str_replace('{{link}}', url("/k12connections/reset-instructor-password/" . encrypt($users["id"])), $body);

            $subject = $template->subject;

            $data = array('template' => $body);
            try {
                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }
            $datalink["link"] = url("/k12connections/reset-instructor-password/" . encrypt($users["id"]));
            $datalink["link_type"] = 'password';
            $datalink["user_id"] = $users["id"];
            $saveLink = link::insertGetId($datalink);


            $data["success"] = true;
            $data["message"] =
                "Email sent successfully, Please check your Email";
            $data["link"] = $url;
        } else {
            $data["success"] = false;
            $data["message"] =
                "Sorry, no user exists on our system with that email";
        }
        return response()->json($data);
    }

    public function resetInstructorPasswords($id)
    {
        $data["id"] = decrypt($id);
        $token = link::where('user_id', '=', decrypt($id))
            ->where('created_at', '>', Carbon::now()->subHours(24))
            ->where("link_type", "=", 'password')
            ->first();
        if ($token) {

            return view("web.onboarding-new.auth.resetpassword")->with($data);
        } else {
            print_r('The password reset link has expired.');
        }
    }

    public function reset_instructor_password(Request $request)
    {

        $this->validate(
            $request,
            ["password" => "required", "cpassword" => "required"],
            [
                "password" => "Password Field is required",
                "cpassword" => "Confirm password field is required",
            ]
        );
        $id = $request->input("id");
        $datas = $request->except(["_token", "password", "cpassword"]);

        $password = Hash::make($request->input("password"));
        OnboardingInstructor::where(["id" => $id])->update(["password" => $password]);

        $datas["success"] = true;
        $datas["message"] = "Reset Password successfully";
        $datas["redirect"] = url("k12connections/sign-in");
        return response()->json($datas);
    }

    // public function get_subsubjects(Request $request)
    // {
    //     $data['subsubject'] = subsubject($request->id);
    //     $data['step'] = $request->step;
    //     return view('web.onboarding.subsubject')->with($data);
    // }

    public function get_subsubjects(Request $request)
    {
        $data['subsubject'] = v1SubSubject($request->id);
        $data['step'] = $request->step;
        return view('web.onboarding.subsubject')->with($data);
    }

    public function get_certificates(Request $request)
    {
        $agency = CredentialingAgencyModel::where('agency', $request->id);
        $certificates = $agency->with('certificates')->get();
        return response()->json(['status' => true, 'data' => $certificates[0]]);
    }

    public function newonboarding(Request $request)
    {

        if (empty(Auth::guard('instructor')->user()->id)) {
            return redirect("/");
        }

        $encryptedId = Session::get('instructorlogin.id');
        if (!$encryptedId) {
            Auth::guard('instructor')->logout();
            return redirect("/");
        }
        if (Auth::guard('instructor')->user()->id != decrypt($encryptedId)) {
            return redirect("/");
        }

        if (!empty(Auth::guard('instructor')->user()->user_status) && Auth::guard('instructor')->user()->user_status != 'InProgress' && Auth::guard('instructor')->user()->user_status != 'ChangeRequested') {

            return redirect()->route('instructorDashboard');
        }

        $data["id"] = decrypt($encryptedId);
        $data["encryptId"] = $encryptedId;
        $id = $data["id"];
        OnboardingInstructor::where("id", $id)->update(["email_verify_status" => 1, "profile_status" => 1]);
        $user = OnboardingInstructor::where("id", $id)->first();
        if (empty($user)) {
            return redirect("/");
        }
        if ($user["last_name"]) {
            $full_name = $user["first_name"] . ' ' . $user["last_name"];
        } else {
            $full_name = $user["first_name"];
        }
        $adminUser = User::find(3);
        $user["full_name"] = $full_name;
        $data['adminUser'] = $adminUser;
        $data['user'] = $user;
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["grade"] = GradeLevelModel::get();
        // $data["subject"] = Subject::orderBy("subject_name", "asc")->get();
        $data["subject"] = SubjectArea::with('subjects')->get();

        // $data["subSubject"] = V1Subject::orderBy("title", "asc")->get();
        $data["subjectdata"] = Subject::orderBy("subject_name", "asc")->get();
        $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
        $data["assessment"] = FreeResponseQuestionsModel::orderBy("id", "asc")->get();
        $data["certifications"] = certifications::where(["status" => 1])->orderBy("name", "asc")->get();
        $data["class"] = Classes::get();
        $data['credentialing_agency'] = CredentialingAgencyModel::whereHas('certificates')->get();
        $data['profile_type'] = DB::table('school_management_setting')->where('type', 'profile_type')->first();
        $data["first"] = InstructorFirstStepOnboardingModel::where(["user_id" => $id])->first();
        $data["second"] = InstructorSecondStepOnboardingModel::with(['education', 'teching', 'otherExper', 'references'])->where([
            "user_id" => $id,
        ])->first();

        $data["second_addtional_certificate"] = DB::table("additional_certificates")->where("instructor_id", $id)->get();
        $data["category"] = AdditionalCertificateCategory::with('subCategory')->get();
        $data["sub_category"] = AdditionalCertificateSubcategory::get();

        // dd($data["second"]);
        $data["third"] = InstructorThirdStepOnboardingModel::where(["user_id" => $id])->first();
        $data["four"] = InstructorForthStepOnboardingModel::where(["user_id" => $id])->first();
        $data["five"] = InstructorFifthStepOnboardingModel::where(["user_id" => $id])->first();
        $data["seven"] = InstructorSeventhStepOnboardingModel::where(["user_id" => $id])->first();
        $data["languages"] = DB::table('school_management_setting')->where('type', 'language')->first();
        $data["programs"] = DB::table('school_management_setting')->where('type', 'program_type')->first();
        $whizaraWorks = SettingTermsModel::where('type', 'How Whizara Works')->first();
        $whizaraWorks->description = str_replace('{{NAME}}', $full_name, $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{DATE}}', date('F d, Y'), $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{IN_PERSON_RATE}}', $user["inpersonrate"], $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{ONLINE_RATE}}', $user["onlinerate"], $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{CITY}}', $user["city"], $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{STATE}}', $user["state"], $whizaraWorks->description);
        $whizaraWorks->description = str_replace('{{ZIPCODE}}', $user["zipcode"], $whizaraWorks->description);
        $data["wizara_works"] = $whizaraWorks;
        $data["quiz"] = InstructorSixthStepOnboardingModel::where(["user_id" => $id])->first();
        $data["assessments_ans"] = InstructorOnboardingAssessmentModel::where(["user_id" => $id])->first();
        $data["education_list"] = EducationListModel::get();
        $data["google_maps"] = OnboardingIinstructorGoogleMapAddress::where(["instructor_onboarding_id" => $id])->first() ?? null;
        $data["user_education"] = InstructorEducationSecondStepOnboardingModel::where([
            "user_id" => $id,
        ])->get();
        $data["user_references"] = user_references::where([
            "user_id" => $id,
        ])->get();
        if ($user) {
            $user_data = [
                "email" => $user["email"]
            ];

            $newtimestamp = strtotime($user->email_verify_time . "+ 10 minute");
            $time = date("Y-m-d H:i:s", $newtimestamp);
        }
        // dd($data["second_addtional_certificate"]);


        $data["sample_lesson"] = SampleLesson::where('user_id', $id)->get();
        $data["sample_subject"] = V1Subject::all();
        if ($request->ajax()) {
            switch ($request->type) {
                case 'step1':
                    $view = view('web.onboarding-new.components.step-1', compact('data'))->render();
                    break;

                case 'step2':
                    $view = view('web.onboarding-new.components.step-2', compact('data'))->render();
                    break;


                case 'step3':
                    $view = view('web.onboarding-new.components.step-3', compact('data'))->render();
                    break;


                case 'step4':
                    $view = view('web.onboarding-new.components.step-4', compact('data'))->render();
                    break;


                case 'step5':
                    $view = view('web.onboarding-new.components.step-5', compact('data'))->render();
                    break;


                case 'step6':
                    $view['view'] = view('web.onboarding-new.components.step-6', compact('data'))->render();
                    $_data['pendingSteps'] = $user->pending_onboarding_steps;
                    $view['metaData'] = $_data;
                    break;


                case 'step7':
                    $view = view('web.onboarding-new.components.step-7', compact('data'))->render();
                    break;

                default:
                    $view = view('web.onboarding-new.components.step-1', compact('data'))->render();
                    break;
            }
            return response()->json(['status' => true, 'view' => $view]);
        }
        // dd($data);
        return view("web.onboarding-new.onboarding-layout")->with($data);
    }

    public function newonboardingTabData($tab, Request $request)
    {
        $instructorLogin = $request->session()->get('instructorlogin');
        $id = $instructorLogin['id'];
        $user = OnboardingInstructor::where("id", $id)->first();
        if (empty($user)) {
            return redirect("/");
        }
        $data['user'] = $user;
        $data["quiz"] = InstructorSixthStepOnboardingModel::first();
        $data["assessments_ans"] = InstructorOnboardingAssessmentModel::first();
        $data["assessment"] = FreeResponseQuestionsModel::orderBy("id", "asc")->get();
        $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
        switch ($tab) {
            case 'free_response':
                $view = view('web.onboarding-new.components.free_response', compact('data'))->render();
                break;

            case 'quiz':
                $view = view('web.onboarding-new.components.quiz', compact('data'))->render();
                break;
        }

        return response()->json(['status' => true, 'view' => $view]);
    }

    function submitFirstStepInstructor(Request $request)
    {
        $reside_united_states = $request->reside_united_states;
        if ($reside_united_states == "yes") {
            $validator = Validator::make($request->all(), [
                "reside_united_states" => "required",
                "state" => "required",
                "zip_code" => "required",
                "city" => "required",
                "i_am_authorized" => "required",
                "id" => "required",
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                "reside_united_states" => "required",
                "id" => "required",
            ]);
        }

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id"]);
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $first = InstructorFirstStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            InstructorFirstStepOnboardingModel::where(["user_id" => $request->id])->delete();
        }
        $result = InstructorFirstStepOnboardingModel::insert($data);
        if ($result) {

            $datas["country"] = "United States";
            $datas["state"] = $request->state;
            $datas["city"] = $request->city;
            $datas["zipcode"] = $request->zip_code;
            $datas["start_date"] = date("Y-m-d H:i:s");

            OnboardingInstructor::where("id", $request->id)->update($datas);
            $instructor = OnboardingInstructor::find($request->id);
            $originalStepsData = json_decode($instructor->pending_onboarding_steps);
            $countOriginalSteps = count($originalStepsData);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps);
            $stepToRemove = "step-1:us-work-authorization";
            $key = array_search($stepToRemove, $updatedStepsData);
            if ($key !== false) {
                unset($updatedStepsData[$key]);
            }
            $updatedStepsData = array_values($updatedStepsData);
            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            $request->session()->put('countOriginalSteps', $countOriginalSteps);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    function submitSecondStepInstructor(Request $request)
    {

        // dd($request->all());

        $delete_previous_addtional_certificate_data = DB::table('additional_certificates')
            ->where('instructor_id', $request->id)
            ->delete();


        $additinal_catgory_ids = $request->additional_category;
        $additional_subcategory_ids = $request->additional_sub_category;
        $additional_issued_certificates_dates = $request->additional_issue_date;
        $valid_till_select_dates = json_decode($request->valid_till_select_dataes);
        //these are the checkboxes
        $additional_valid_till_checkboxes = json_decode($request->input('valid_till_checkbox_values'));
        $visible_to_school_checkboxes = json_decode($request->input('visibletoschoolcheckboxes'));
        // dd($additinal_catgory_ids, $additional_subcategory_ids, $additional_issued_certificates_dates, $valid_till_select_dates, $additional_valid_till_checkboxes);


        if ($request->hasFile('additional_certificate_upload')) {
            foreach ($request->file('additional_certificate_upload') as $file) {
                if ($file->isValid()) {
                    $name = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                    $path = 'uploads/marketplace/certificates/' . $name;

                    // Upload to S3 (or wherever)

                    // Store full path or public URL
                    $uploadedCertificates[] = $path; // or use Storage::url($path) if needed
                }
            }
        }


        $fileNames = [];


        // 1. Add existing certificate file names (if any)
        if ($request->filled('exisiting_additional_certificates')) {
            foreach ($request->input('exisiting_additional_certificates') as $existingFile) {
                $fileNames[] = $existingFile;
            }
        }



        if ($request->hasFile('additional_certificate_upload')) {
            $files = $request->file('additional_certificate_upload');

            foreach ($files as $file) {
                if ($file->isValid()) {
                    $originalName = $file->getClientOriginalName(); // e.g., "resume.pdf"
                    $fullPath = 'uploads/marketplace/additional_certificates/' . $originalName;
                    uploads3image($fullPath, $file);
                    $fileNames[] = $fullPath;
                } else {

                    $fileNames[] = null;
                }
            }
        }
        $fileNames = array_values(array_filter($fileNames, function ($value) {
            return $value !== null && $value !== '';
        }));




        if (!empty($additinal_catgory_ids)) {

            foreach ($additinal_catgory_ids as $index => $row) {

                $additional_certificate = DB::table("additional_certificates")->insert([
                    "instructor_id" => $request->id,
                    "certificate" => $fileNames[$index],
                    "category_id" => $additinal_catgory_ids[$index],
                    'sub_category_name' => $additional_subcategory_ids[$index],
                    "visible_to_school" => !empty($visible_to_school_checkboxes) ? $visible_to_school_checkboxes[$index] : null,
                    "do_not_expire" => !empty($additional_valid_till_checkboxes) ? $additional_valid_till_checkboxes[$index] : null,
                    "issued_date" => $additional_issued_certificates_dates[$index],
                    "valid_till_date" => !empty($valid_till_select_dates) ? $valid_till_select_dates[$index] : null,

                ]);
            }
        }



        $data = $request->except([
            "_token",
            "id",
            "resume",
            "resume_name",
            "filename",
            "other_specify",
            "specify",
            "resume_value",
            "file",
            "award_value",
            "certificatelastid",
            "school_college_name",
            "awardlastid",
            "certificate_value",
            "distinction_value",
            "distinctionsid",
            "highest_level_of_education",
            "month_and_year_graduation",
            "GPA",
            "other_degree",
            "educationid",
            "experience_teaching_ages",
            "certified_special_education",
            "certification_visible_to_school",
            "teaching_certification_year",
            "teaching_certification_states",
            "certifications_other",
            "other_employer_name",
            "other_location",
            "other_position",
            "other_end_date",
            "other_start_date",
            "check_certification",
            "employer_name",
            "location",
            "position",
            "start_date",
            "end_date",
            "certificate",
            "transcript_visible_to_school",
            "transcript",
            "check_transcript",
            "transcript_value",
            "transcript_name",
            "certificate_value",
            "certification_name",
            "credentialing_agency",
            "credentialing_agency_other",
            "major",
            "minor",
            "total_experience",
            "main_experience_teaching_ages",
            "currently_working_here",
            'other_currently_working_here',
            'working_here_value',
            'other_working_here_value',
            'full_name',
            'email',
            'phone',
            'reference_visible',
            'reference_visible_value',
            'school_location',
            'additional_category',
            'additional_sub_category',
            'additional_issue_date',
            'additional_till_date',
            'additional_certificate_upload',
            'valid_till_checkbox_values',
            'visible_to_school_checkboxes',
            'additional_valid_till_check_box',
            'visibletoschoolcheckboxes',
            "valid_till_select_dataes",
            "exisiting_additional_certificates",
            "additional_tools",
            "additional_tools",
            "tools_hidden",

        ]);
        if ($request->hasFile("resume")) {
            $resume = $request->file("resume");
            $name = time() . "." . $resume->getClientOriginalExtension();
            $filename = 'uploads/marketplace/resume/' . $name;
            uploads3image($filename, $resume);
            $data["resume"] = $filename;
            $data["resume_name"] = $resume->getClientOriginalName();
        } else {
            $data["resume"] = $request->resume_value;
            $data["resume_name"] = $request->resume_name;
        }

        if ($request->certification == "yes") {
            $data["specify"] = $request->specify;
        } else {
            $data["specify"] = $request->other_specify;
        }
        $data["user_id"] = $request->id;
        if ($request->certification == "yes") {
            $profiletype = "Certified Teacher";
        } else {
            $profiletype = $request->profile_type;
        }

        if ($request->certification == 'no') {

            $instructor = OnboardingInstructor::find($request->id);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
            $stepsToRemove = [
                "step-3:certification",
            ];
            foreach ($stepsToRemove as $stepToRemove) {
                $key = array_search($stepToRemove, $updatedStepsData);

                // Check if the value is found in the array
                if ($key !== false && $key !== null) {
                    unset($updatedStepsData[$key]); // Remove the value
                }
            }
            $updatedStepsData = array_values($updatedStepsData);

            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
        }


        $data["profile_type"] = $profiletype;
        $data["tools"] = $request->tools_hidden;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $first = InstructorSecondStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {

            $result = InstructorSecondStepOnboardingModel::where(["user_id" => $request->id])->update($data);

            if ($result) {
                $lastid = $first->id;
            }
        } else {
            $result = InstructorSecondStepOnboardingModel::insert($data);

            if ($result) {
                $lastid = DB::getPdo()->lastInsertId();
            }
        }

        if ($result) {

            $datasubject = [];
            if ($request->certified_special_education && count($request->certified_special_education) > 0) {
                foreach (array_filter($request->certified_special_education) as $key => $rows) {
                    if ($rows) {
                        $certificateFileName = null;
                        $certification_name = '';
                        if ($request->hasFile("certificate") && !empty($request->file("certificate")[$key])) {
                            $certificate = $request->file("certificate")[$key];
                            $name = time() . '-' . uniqid() . '.' . $certificate->getClientOriginalExtension();
                            $certificateFileName = 'uploads/marketplace/certificate/' . $name;
                            uploads3image($certificateFileName, $certificate);
                            $certification_name = $certificate->getClientOriginalName();
                        } else {
                            $certificateFileName = $request->certificate_value[$key];
                            $certification_name = $request->certification_name[$key];
                        }
                        $selectedStates = $request->teaching_certification_states[$key] ?? [];
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "education" => $rows,
                            'states' => json_encode($selectedStates),
                            "certification_year" => $request->teaching_certification_year[$key],
                            "certificate" => $certificateFileName,
                            "certification_visible_to_school" => $request->check_certification[$key],
                            "credentialing_agency" => $request->credentialing_agency[$key],
                            "certifications_other" => $request->certifications_other[$key],
                            "credentialing_agency_other" => $request->credentialing_agency_other[$key],
                            "certification_name" => $certification_name,
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }
            $first1 = InstructorSecondStepOnboardingModel::where([
                "user_id" => $request->id,
            ])->first();
            if (!empty($first1)) {
                InstructorOnboardingCertification::where(["user_id" => $request->id])->delete();
            }
            if (!empty($datasubject) && $request->certification == 'yes') {
                InstructorOnboardingCertification::insert($datasubject);
                $instructor = OnboardingInstructor::find($request->id);
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
                $stepsToRemove = [
                    "step-3:certification",
                ];
                $key = array_search($stepsToRemove, $updatedStepsData);
                foreach ($stepsToRemove as $stepToRemove) {
                    $key = array_search($stepToRemove, $updatedStepsData);

                    if ($key !== false) {
                        unset($updatedStepsData[$key]);
                    }
                }
                $updatedStepsData = array_values($updatedStepsData);
                OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            } elseif (empty($datasubject) && $request->certification == 'yes') {
                $stepToAdd = "step-3:certification";
                $instructor = OnboardingInstructor::where("id", $request->id)->first();
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps);
                if (!in_array($stepToAdd, $updatedStepsData)) {
                    $updatedStepsData[] = $stepToAdd;
                }
                OnboardingInstructor::where("id", $request->id)->update([
                    'pending_onboarding_steps' => json_encode($updatedStepsData)
                ]);
            }

            $data_2 = [];
            if ($request->certification == "yes") {
                $cert = "yes";
            } else {
                $cert = $request->profile_type;
            }
            if (isset($request->highest_level_of_education) && !is_null($request->highest_level_of_education[0])) {
                if (count($request->highest_level_of_education) > 0) {
                    $data_2["highest_level_of_education"] = implode(
                        ",",
                        $request->highest_level_of_education
                    );
                }
            } else {
                $data_2["highest_level_of_education"] = null;
            }

            $otherEducationArray = [];
            if (isset($request->other_degree) && isset($request->highest_level_of_education) && in_array("Other Professional Degree", $request->highest_level_of_education)) {
                foreach ($request->highest_level_of_education as $index => $edu) {
                    $otherEducationArray[$index] = isset($request->other_degree[$index]) && !empty($request->other_degree[$index])
                        ? $request->other_degree[$index]
                        : "";
                }

                $data_2["other_education"] = implode(",", $otherEducationArray);
            } else {
                $data_2["other_education"] = null;
            }

            if (isset($request->school_college_name) && !is_null($request->school_college_name[0])) {
                if (count($request->school_college_name) > 0) {
                    $data_2["school_college_name"] = implode(
                        ",",
                        $request->school_college_name
                    );
                }
            } else {
                $data_2["school_college_name"] = null;
            }

            if (isset($request->school_location) && !is_null($request->school_location[0])) {
                if (count($request->school_location) > 0) {
                    $data_2["school_location"] = implode(
                        ";",
                        $request->school_location
                    );
                }
            } else {
                $data_2["school_location"] = null;
            }

            if (isset($request->month_and_year_graduation) && !is_null($request->month_and_year_graduation[0])) {
                if (count($request->month_and_year_graduation) > 0) {
                    $data_2["month_and_year_graduation"] = implode(
                        ",",
                        $request->month_and_year_graduation
                    );
                }
            } else {
                $data_2["month_and_year_graduation"] = null;
            }

            if (isset($request->major) && !is_null($request->major[0])) {
                if (count($request->major) > 0) {
                    $data_2["major"] = implode(
                        ",",
                        $request->major
                    );
                }
            } else {
                $data_2["major"] = null;
            }

            if (isset($request->minor) && !is_null($request->minor[0])) {
                if (count($request->minor) > 0) {
                    $data_2["minor"] = implode(
                        ",",
                        $request->minor
                    );
                }
            } else {
                $data_2["minor"] = null;
            }

            if (isset($request->GPA) && !is_null($request->GPA[0])) {
                if (count($request->GPA) > 0) {
                    $data_2["GPA"] = implode(
                        ",",
                        $request->GPA
                    );
                }
            } else {
                $data_2["GPA"] = null;
            }

            if (isset($request->check_transcript) && !is_null($request->check_transcript[0])) {
                if (count($request->check_transcript) > 0) {
                    $data_2["transcript_visible_to_school"] = implode(
                        ",",
                        $request->check_transcript
                    );
                }
            } else {
                $data_2["transcript_visible_to_school"] = null;
            }

            if ($request->has('transcript_value') && $request->has('transcript_name')) {
                $existing_transcripts = $request->transcript_value;
                $existing_transcript_names = $request->transcript_name;
            } else {
                $existing_transcripts = [];
                $existing_transcript_names = [];
            }

            if ($request->hasFile("transcript")) {
                $transcripts = $existing_transcripts;
                $transcript_name = $existing_transcript_names;
                foreach ($request->file('transcript') as $index =>  $transcript) {
                    if ($request->file('transcript')[$index] ?? false) {
                        $name = time() . '-' . uniqid() . '.' . $transcript->getClientOriginalExtension();
                        $filename = 'uploads/marketplace/transcript/' . $name;
                        uploads3image($filename, $transcript);
                        $transcripts[$index] = $filename;
                        $transcript_name[$index] = $transcript->getClientOriginalName();
                    }
                }
                $data_2["transcript"] = implode(',', $transcripts);
                $data_2["transcript_name"] = implode(',', $transcript_name);
            } else {
                $data["transcript"] = $request->transcript_value;
                $data["transcript_name"] = $request->transcript_name;
            }

            if (isset($request->total_experience)) {

                $data_2["total_experience"] = $request->total_experience;
            } else {
                $data_2["total_experience"] = null;
            }

            InstructorSecondStepOnboardingModel::where('user_id', $request->id)->update($data_2);
            if ($data_2["highest_level_of_education"] == null) {
                $stepToAdd = "step-3:education";
                $instructor = OnboardingInstructor::where("id", $request->id)->first();
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps);
                if (!in_array($stepToAdd, $updatedStepsData)) {
                    $updatedStepsData[] = $stepToAdd;
                }
                OnboardingInstructor::where("id", $request->id)->update([
                    'pending_onboarding_steps' => json_encode($updatedStepsData)
                ]);
            } else {
                $instructor = OnboardingInstructor::find($request->id);
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
                $stepsToRemove = [
                    "step-3:education",
                ];
                $key = array_search($stepsToRemove, $updatedStepsData);
                foreach ($stepsToRemove as $stepToRemove) {
                    $key = array_search($stepToRemove, $updatedStepsData);

                    if ($key !== false) {
                        unset($updatedStepsData[$key]);
                    }
                }
                $updatedStepsData = array_values($updatedStepsData);
                OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            }

            $datasubject = [];
            if ($request->employer_name && count($request->employer_name) > 0) {
                foreach (array_filter($request->employer_name) as $key => $rows) {
                    if ($rows) {
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "employer_name" => $rows,
                            'location' => $request->location[$key],
                            "position" => $request->position[$key],
                            "start_date" => $request->start_date[$key],
                            "end_date" => !empty($request->end_date) && count($request->end_date) ? $request->end_date[$key] : null,
                            "currently_working_here" => $request->working_here_value[$key] ?? null,
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $firstedu = InstructorExperienceTeaching::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                InstructorExperienceTeaching::where([
                    "user_id" => $request->id,
                ])->delete();
            }
            if (!empty($datasubject) || $request->total_experience == 0) {

                InstructorExperienceTeaching::insert($datasubject);
                $instructor = OnboardingInstructor::find($request->id);
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
                $stepsToRemove = [
                    "step-3:teaching-experience",
                ];
                $key = array_search($stepsToRemove, $updatedStepsData);
                foreach ($stepsToRemove as $stepToRemove) {
                    $key = array_search($stepToRemove, $updatedStepsData);

                    if ($key !== false) {
                        unset($updatedStepsData[$key]);
                    }
                }
                $updatedStepsData = array_values($updatedStepsData);
                OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            } else {
                $stepToAdd = "step-3:teaching-experience";
                $instructor = OnboardingInstructor::where("id", $request->id)->first();
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps);
                if (!in_array($stepToAdd, $updatedStepsData)) {
                    $updatedStepsData[] = $stepToAdd;
                }
                OnboardingInstructor::where("id", $request->id)->update([
                    'pending_onboarding_steps' => json_encode($updatedStepsData)
                ]);
            }

            $datasubject = [];
            if ($request->other_employer_name && count($request->other_employer_name) > 0) {
                foreach (array_filter($request->other_employer_name) as $key => $rows) {
                    if ($rows) {
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "employer_name" => $rows,
                            'location' => $request->other_location[$key],
                            "position" => $request->other_position[$key],
                            "start_date" => $request->other_start_date[$key],
                            "end_date" => !empty($request->other_end_date) && count($request->other_end_date) ? $request->other_end_date[$key] : null,
                            'currently_working_here' => $request->other_working_here_value[$key] ?? null,
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $firstedu = InstructorOtherExperienceOnboardingModel::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                InstructorOtherExperienceOnboardingModel::where([
                    "user_id" => $request->id,
                ])->delete();
            }
            if (!empty($datasubject)) {
                InstructorOtherExperienceOnboardingModel::insert($datasubject);
            }

            $dataReference = [];
            if ($request->full_name && count($request->full_name) > 0) {
                foreach (array_filter($request->full_name) as $key => $rows) {
                    if ($rows) {
                        $dataReference[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "full_name" => $rows,
                            'email' => $request->email[$key],
                            "phone" => $request->phone[$key],
                            'reference_visible' => $request->reference_visible_value[$key] ?? null,
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $firstedu = InstructorOnboardingReferences::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                InstructorOnboardingReferences::where([
                    "user_id" => $request->id,
                ])->delete();
            }
            if (!empty($dataReference)) {
                InstructorOnboardingReferences::insert($dataReference);
            }


            $datas["teacher_type"] = $cert;
            OnboardingInstructor::where("id", $request->id)->update($datas);
            $instructor = OnboardingInstructor::find($request->id);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
            $stepsToRemove = [
                "step-3:resume",
                "step-3:other-experience"
            ];
            $key = array_search($stepsToRemove, $updatedStepsData);
            foreach ($stepsToRemove as $stepToRemove) {
                $key = array_search($stepToRemove, $updatedStepsData);

                if ($key !== false) {
                    unset($updatedStepsData[$key]);
                }
            }
            $updatedStepsData = array_values($updatedStepsData);
            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitThirdStepinstructor(Request $request)
    {
        $data = $request->except([
            "_token",
            "id",
            "format",
            "in_person_map_search_input",
            "in_person_latitude",
            "in_person_longitude",
            "in_person_full_address",
            "case_management",
            "internet_connection",
            "work_from_home_setup",
            "subject",
            "range",
            "subject_other",
            "i_prefer_to_teach",
            "sub_subject",
            "compensation",
            "privacy_radius",
            "travel_radius",
            "neighborhood",
        ]);
        $data["i_prefer_to_teach"] = implode(",", $request->i_prefer_to_teach);
        $data["format"] = implode(",", $request->format);
        $data["language_teach_that_i_teach"] = implode(",", $request->language_teach_that_i_teach);
        // $data['other_language'] = $request->other_language;
        $data["program_type"] = implode(",", $request->program_type);
        // $data["other_program_type"] = $request->other_program_type;
        $data["curriculum"] = $request->curriculum;
        $data["case_management"] = $request->case_management == "on" ? 1 : 0;
        $data['internet_connection'] = $request->internet_connection;
        $data['scope_sequence'] = $request->scope_sequence;
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        OnboardingInstructor::where("id", $request->id)->update([
            'work_from_home_setup' => $request->work_from_home_setup == "on",
            'internet_connection' => $request->internet_connection == "on"
        ]);
        $first = InstructorThirdStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            InstructorThirdStepOnboardingModel::where(["user_id" => $request->id])->delete();
        }
        $result = InstructorThirdStepOnboardingModel::insert($data);
        if ($result) {
            $lastid = DB::getPdo()->lastInsertId();
            InstructorSubjectsThirdStepOnboardingModel::where(["step_id" => $lastid])->delete();
            $min = $request->range;
            $subject_other = $request->subject_other;
            $sub_subject = $request->sub_subject;
            $datasubject = [];
            if (count($request->subject) > 0) {
                $i = 0;
                foreach (array_filter($request->subject) as $key => $rows) {
                    $i++;

                    if ($rows) {
                        $sub = '';
                        // if (isset($sub_subject[$i])) {
                        //     $sub =   implode(',', $sub_subject[$i]);
                        // }
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "subject" => v1SubjectId($rows),
                            'sub_subject' => $rows,
                            "proficiency" => $min[$key],
                            // "other" => $subject_other[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            InstructorSubjectsThirdStepOnboardingModel::insert($datasubject);
            $compensations = [];
            if (count($request->compensation) > 0) {
                foreach (array_filter($request->compensation) as $key => $rows) {
                    if ($rows) {
                        $compensations[] = $rows;
                    }
                }
            }
            $updateValue = empty($compensations) ? null : implode(',', $compensations);
            InstructorThirdStepOnboardingModel::where('id', $lastid)->where('user_id', $request->id)->update(['compensation' => $updateValue]);
            if (count($request->format) > 0) {
                $datas["teach"] = implode(",", $request->format);
                OnboardingInstructor::where("id", $request->id)->update($datas);
                $instructor = OnboardingInstructor::find($request->id);
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
                $stepsToRemove = [
                    "step-4:grade-levels",
                    "step-4:subject",
                    "step-4:proficiency",
                    "step-4:format",
                    "step-4:schedule",
                    "step-4:compensation",
                    "step-4:curriculum",
                    "step-4:program-type",
                    "step-4:languages",
                ];
                $key = array_search($stepsToRemove, $updatedStepsData);
                foreach ($stepsToRemove as $stepToRemove) {
                    $key = array_search($stepToRemove, $updatedStepsData);

                    if ($key !== false) {
                        unset($updatedStepsData[$key]);
                    }
                }
                $updatedStepsData = array_values($updatedStepsData);
                OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            }
            // google address store
            // if($request->travel_radius){
            //     $data = OnboardingIinstructorGoogleMapAddress::where("instructor_onboarding_id", $request->id)->first();

            //     if ($data) {
            //         $google_address = $data; // Use the existing record
            //     } else {
            //         $google_address = new OnboardingIinstructorGoogleMapAddress(); // Create a new record
            //     }
            //     $google_address->address_text = $request->in_person_full_address;
            //     $google_address->latitude = $request->in_person_latitude;
            //     $google_address->longitude = $request->in_person_longitude;
            //     $google_address->travel_address = $request->travel_radius;
            //     $google_address->instructor_onboarding_id = $request->id;
            //     $google_address->save();
            // }


            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitFifthStepInstructor(Request $request)
    {
        // set_time_limit(300);
        if ($request->hasFile("profile_image")) {
            $image = $request->file("profile_image");
            $name = time() . "." . $image->getClientOriginalExtension();
            $filename = 'uploads/admin/' . uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename, $image);
            $data["profile_image"] = $filename;
        } else {

            $data["profile_image"] = $request->image_data;
        }

        // if (!empty($request->instructor_video_url_s3 && !empty($request->videoname))) {
        //     // $video = $request->file("source");
        //     // $name = time() . "." . $video->getClientOriginalExtension();
        //     // $filename = 'uploads/admin/' .uniqid() . '_' . $video->getClientOriginalName();
        //     // $stream = fopen($video->path(), 'r');

        //     // Storage::disk('s3')->writeStream($filename, $stream);

        //     // $data["video"] = $filename;
        //     // $data["video_name"] = $video->getClientOriginalName();
        //     $data["video"] = $request->instructor_video_url_s3;
        //     $data["video_name"] = $request->videoname;


        // } else {
        //     $data["video"] = $request->video_data;

        //     $data["video_name"] = $request->video_name;
        // }

        $data["profile_title"] = $request->profile_title;
        $data["description"] = $request->profile_desc;

        // $data["video_source"] = $request->video_source;

        $data["profile_tags"] = $request->tags_hidden;
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $five = InstructorFifthStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($five)) {
            $result = InstructorFifthStepOnboardingModel::where(["user_id" => $request->id])->update($data);
        } else {
            $result = InstructorFifthStepOnboardingModel::insert($data);
        }
        if ($result) {
            $datas["description"] = $request->profile_desc;
            $datas["image"] = $data["profile_image"];

            OnboardingInstructor::where("id", $request->id)->update($datas);
            $instructor = OnboardingInstructor::find($request->id);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
            $stepToRemove = "step-5:profile";
            $key = array_search($stepToRemove, $updatedStepsData);
            if ($key !== false) {
                unset($updatedStepsData[$key]);
            }
            $updatedStepsData = array_values($updatedStepsData);
            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function removeProfileVideo(Request $request)
    {
        $profile = InstructorFifthStepOnboardingModel::where(["user_id" => $request->userId])->first();
        if (!empty($profile)) {
            $profile->update(['video' => null, 'video_name' => null]);
            $user = OnboardingInstructor::find($request->userId);
            $pendingSteps = json_decode($user->pending_onboarding_steps, true);
            if (!is_array($pendingSteps)) {
                $pendingSteps = [];
            }
            if (!in_array("step-5:profile", $pendingSteps)) {
                $pendingSteps[] = "step-5:profile"; // Add the step
                $user->pending_onboarding_steps = json_encode($pendingSteps);
                $user->save(); // Save the updated data
            }
        }

        return response()->json(['success' => true]);
    }

    public function updateProcessingVideo(Request $request)
    {
        $request->validate([
            'processing_video' => 'required|boolean',
        ]);
        $userId = Session::get('instructorlogin.id');

        $profile = InstructorFifthStepOnboardingModel::where('user_id', $userId)->first();
        if (!$profile) {
            return response()->json(['message' => 'Profile not found'], 404);
        }
        $profile->processing_video = $request->processing_video ? 1 : 0;
        $profile->save();
        return response()->json(['message' => 'Processing video status updated successfully']);
    }

    public function removeAssessments(Request $request)
    {
        $user = OnboardingInstructor::find($request->userId);
        $pendingSteps = json_decode($user->pending_onboarding_steps, true);
        if (!is_array($pendingSteps)) {
            $pendingSteps = [];
        }
        if (!in_array("step-6:assessment", $pendingSteps)) {
            $pendingSteps[] = "step-6:assessment"; // Add the step
            $user->pending_onboarding_steps = json_encode($pendingSteps);
            $user->save(); // Save the updated data
        }

        return response()->json(['success' => true]);
    }


    public function submit_quiz_instructor(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "total_question"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $total = $request->total_question;

        $quizarray = [];
        for ($i = 0; $i < $total; $i++) {
            $qu = "question" . $i;
            $question = $request->{$qu};
            $op = "option" . $i;
            $option = $request->{$op};
            $quizarray[$question] = $option;
        }

        $assessmentarray = [];
        $notCompleted = false;
        for ($i = 0; $i < $total; $i++) {
            $qu = "assessment_question" . $i;
            $question = $request->{$qu};
            $op = "answer" . $i;
            $option = $request->{$op};
            if (!$request->{$op}) {
                $notCompleted = true;
            }
            $assessmentarray[$question] = $option;
        }

        $usersdetails = OnboardingInstructor::where(["id" => $request->id])->first();
        $exist = InstructorOnboardingAssessmentModel::where('user_id', $request->id)->first();
        $existQuiz = InstructorSixthStepOnboardingModel::where('user_id', $request->id)->first();

        $lessonPLanCompleted = true;
        $videoDemoCompleted = true;
        $sampleLesson = SampleLesson::where('user_id', $request->id)->first();
        $videoDemo = InstructorFifthStepOnboardingModel::where('user_id', $request->id)->where('video', '!=', null)->first();

        if (!empty($sampleLesson) || !$request->lessonEmpty) {
            $lessonPLanCompleted = false;
        } else {

        }

        if (!empty($videoDemo)) {
            $videoDemoCompleted = false;
        }

        if (!empty($exist)) {
            $assessment = InstructorOnboardingAssessmentModel::where('user_id', $request->id)->update([
                "user_id" => $request->id,
                "assessment_answer" => json_encode($assessmentarray),
                "updated_at" => date("Y-m-d H:i:s"),
            ]);
        } else {
            $assessment = InstructorOnboardingAssessmentModel::insert([
                "user_id" => $request->id,
                "assessment_answer" => json_encode($assessmentarray),
                "created_at" => date("Y-m-d H:i:s"),
                "updated_at" => date("Y-m-d H:i:s"),
            ]);
        }
        if (!empty($existQuiz)) {
            $result = InstructorSixthStepOnboardingModel::where('user_id', $request->id)->update([
                "user_id" => $request->id,
                "quiz_question_answer" => json_encode($quizarray),
                "updated_at" => date("Y-m-d H:i:s"),
            ]);
        } else {
            $result = InstructorSixthStepOnboardingModel::insert([
                "user_id" => $request->id,
                "quiz_question_answer" => json_encode($quizarray),
                "created_at" => date("Y-m-d H:i:s"),
                "updated_at" => date("Y-m-d H:i:s"),
            ]);
        }


        if ($result) {
            $date = date("Y-m-d H:i:s");
            $instructor = OnboardingInstructor::find($request->id);
            if ($notCompleted == false && !$videoDemoCompleted && !$lessonPLanCompleted) {
                $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);

                // $stepToRemove = "step-6:assessment";
                // $key = array_search($stepToRemove, $updatedStepsData);
                // if ($key !== false) {
                //     unset($updatedStepsData[$key]);
                // }

                $stepsToRemove = [
                    "step-6:assessment",
                    "step-7:agreement",
                ];
                $key = array_search($stepsToRemove, $updatedStepsData);
                foreach ($stepsToRemove as $stepToRemove) {
                    $key = array_search($stepToRemove, $updatedStepsData);

                    if ($key !== false) {
                        unset($updatedStepsData[$key]);
                    }
                }

                $updatedStepsData = array_values($updatedStepsData);
                $datas['pending_onboarding_steps'] = json_encode($updatedStepsData);

                if (empty($updatedStepsData)) {
                    $datas["submission_date"] = $date;
                }

                $userUp = OnboardingInstructor::where("id", $request->id)->update($datas);

                // OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            }
            $instructor = OnboardingInstructor::find($request->id);

            // return response()->json([
            //     "success" => true,
            //     "message" => "Application Saved",
            //     // "redirect" => url("submit"),
            // ]);
            $user = User::find(3);
            if (empty(json_decode($instructor->pending_onboarding_steps))) {
                return response()->json([
                    "success" => true,
                    "message" => "Application Saved",
                    "redirect" => url("k12connections/dashboard"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Application Saved",
                    "pending" => json_decode($instructor->pending_onboarding_steps),
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function ajaxCheckAndUpdateProfilStatus(Request $request, $instructor, $user)
    {
        $instructor = OnboardingInstructor::find($instructor);
        if (empty(json_decode($instructor->pending_onboarding_steps))) {
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
                "redirect" => url("k12connections/dashboard"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Application Saved",
                "pending" => json_decode($instructor->pending_onboarding_steps),
            ]);
        }
    }

    public function ajaxLaterUpdateProfilStatus(Request $request, $instructor, $user)
    {
        $updated = $this->checkAndUpdateProfilStatus($instructor, $user);
        return response()->json([
            "success" => true,
            "message" => "Application Saved",
            "redirect" => url("k12connections/dashboard"),
        ]);
    }

    public function ajaxSubmitAndUpdateProfilStatus(Request $request, $instructor, $user)
    {
        $updated = $this->checkAndUpdateProfilStatus($instructor, $user);
        return response()->json([
            "success" => true,
            "message" => "Application Saved",
            "redirect" => url("k12connections/dashboard"),
        ]);
    }

    public function sendEmailWithAttachment($email, $subject, $pdfPath, $data)
    {
        Mail::send('template', $data, function ($message) use ($email, $subject, $pdfPath) {
            $message->to($email)->subject($subject);
            $message->attach($pdfPath, [
                'as' => 'agreement.pdf',
                'mime' => 'application/pdf',
            ]);
        });
    }

    public function submit_instructor_agreement(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "legal_first_name" => "required",
            "legal_last_name" => "required",
            "legal_address" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }

        $data["legal_first_name"] = $request->legal_first_name;
        $data["legal_last_name"] = $request->legal_last_name;
        $data["legal_address"] = $request->legal_address;
        $data["agreement_date"] = date("Y-m-d");
        $data["contract"] = $request->agreement_text;
        $data["check1"] = $request->check1;
        $data["check2"] = $request->check2;
        $data["check3"] = $request->check3;
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $profilestatus = "2";
        $five = InstructorSeventhStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($five)) {
            $dataToUpdate = [
                'check1' => $request->check1,
                'check2' => $request->check2,
                'check3' => $request->check3,
                'legal_first_name' => $request->legal_first_name,
                'legal_last_name' => $request->legal_last_name,
                'legal_address' => $request->legal_address,
                'updated_at' => date("Y-m-d H:i:s"),
            ];

            if (!empty($request->agreement_text)) {
                $dataToUpdate['contract'] = $request->agreement_text;
            }
            $result = InstructorSeventhStepOnboardingModel::where(['user_id' => $request->id])->update($dataToUpdate);
        } else {
            $result = InstructorSeventhStepOnboardingModel::insert($data);
        }

        if ($result) {
            $instructor = OnboardingInstructor::find($request->id);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
            $stepToRemove = "step-7:agreement";
            $key = array_search($stepToRemove, $updatedStepsData);
            if ($key !== false) {
                unset($updatedStepsData[$key]);
            }
            $updatedStepsData = array_values($updatedStepsData);
            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            $updatePendingSteps = OnboardingInstructor::find($request->id);
            $user = User::find(3);

            if (!empty($request->agreement_text)) {
                $template = DB::table("tbl_email_templates")->where("email_template_id", "41")->first();
                $body =  $template->description;
                if ($updatePendingSteps->last_name) {
                    $full_name = $updatePendingSteps->first_name . ' ' . $updatePendingSteps->last_name;
                } else {
                    $full_name = $updatePendingSteps->first_name;
                }

                $body = str_replace('{{NAME}}', $full_name, $body);
                $subject = $template->subject;
                $email = $updatePendingSteps->email;
                $data = array('template' => $body);
                $agreementContent = $request->agreement_text;
                $pdf = Pdf::loadView('web.pdf.agreement_template', compact('agreementContent'));
                $pdfPath = storage_path('app/public/Learn2Code.LiveContract.pdf');
                $pdf->save($pdfPath);
                try {
                    $this->sendEmailWithAttachment($email, $subject, $pdfPath, $data);
                } catch (\Throwable $th) {
                    //throw $th;
                }
            }

            if ($request->isLater == 1) {
                return response()->json([
                    "success" => false,
                    "message" => "Application Saved",
                    'isLater' => 1,
                    "redirect" => url("k12connections/dashboard"),
                ]);
            } else {
                if (empty(json_decode($instructor->pending_onboarding_steps))) {
                    return response()->json([
                        "success" => true,
                        "message" => "Application Saved",
                        "redirect" => url("k12connections/dashboard"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Application Saved",
                        "pending" => json_decode($instructor->pending_onboarding_steps),
                    ]);
                }
            }
        }
    }

    public function checkAndUpdateProfilStatus($instructorId, $userId)
    {
        $datas["profile_status"] = '2';
        OnboardingInstructor::where("id", $instructorId)->update($datas);
        $instructor = OnboardingInstructor::find($instructorId);
        $user = User::find($userId);
        if (!empty(json_decode($instructor->pending_onboarding_steps))) {
            return false;
        } else {
            $template = DB::table("tbl_email_templates")->where("email_template_id", "33")->first();
            $body =  $template->description;

            if ($instructor->last_name) {
                $full_name = $instructor->first_name . ' ' . $instructor->last_name;
            } else {
                $full_name = $instructor->first_name;
            }

            $body = str_replace('{{NAME}}', $full_name, $body);
            $subject = $template->subject;
            $email = $instructor->email;
            $data = array('template' => $body);

            try {


                Mail::send('template', $data, function (
                    $message
                ) use ($email, $subject) {
                    $message->to($email)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }
            $id = Crypt::encryptString($instructor->id);
            $redirect_url = url('/admin/set-instructor-id/' . encrypt_str($instructorId));
            $template1 = DB::table("tbl_email_templates")->where("email_template_id", "34")->first();
            $body1 =  $template1->description;
            $body1 = str_replace('{{NAME}}', $full_name, $body1);
            $body1 = str_replace('{{redirect}}', $redirect_url, $body1);
            $subject1 = $template1->subject;
            $subject1 = str_replace('{{NAME}}', $full_name, $subject1);
            $data1 = array('template' => $body1);
            $email1 = $user->email;


            try {
                Mail::send('template', $data1, function (
                    $message
                ) use ($email1, $subject1) {
                    $message->to($email1)->subject($subject1);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }
            $reviewerUser = User::where('type', 3)->get();
            if (!empty($reviewerUser)) {
                foreach ($reviewerUser as $reviewer) {
                    $email1 = $reviewer->email;
                    try {
                        Mail::send('template', $data1, function (
                            $message
                        ) use ($email1, $subject1) {
                            $message->to($email1)->subject($subject1);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                }
            }
            SendNotificationForInstructorSubmitOnboarding($instructor, $user, $reviewerUser);
            $userStatus = 'UnderReview';
            $datas["user_status"] = $userStatus;
            OnboardingInstructor::where("id", $instructor->id)->update($datas);
            return true;
        }
    }

public function instructorDashboard()
{
    // Retrieve the currently logged-in instructor
    $instructor = Auth::guard('instructor')->user();

    // Check authentication and profile requirements
    if (!$instructor || $instructor->type != 5 || $instructor->profile_status != 2) {
        return redirect("/k12connections/sign-in");
    }

    // Load onboarding data
    $whizaraContract = OnboardingInstructorContract::where('user_id', $instructor->id)->first();
    $marketplaceContract = OnboardingInstructorMarketplaceContract::where('user_id', $instructor->id)->first();
    $instructor = OnboardingInstructor::find($instructor->id);

    // Parse educator roles
    $educatorRoles = !empty($instructor->educator) ? explode(',', $instructor->educator) : [];

    // Handle redirection for ChangeRequested status
    if ($instructor->user_status === 'ChangeRequested') {
        return redirect("/k12connections/application");
    }

    // If instructor is approved
    if ($instructor->user_status === 'Approved') {
        // Redirect if contract is declined or educator roles are empty
        if (!empty($instructor->isDeclineContract) || empty($educatorRoles)) {
            return redirect("/k12connections/contract-details");
        }

        $isMarketplaceEducator = in_array("marketplace educator", $educatorRoles);
        $isWhizaraEducator = in_array("whizara educator", $educatorRoles);

        // Redirect if marketplace educator contract is missing
        if ($isMarketplaceEducator && !$marketplaceContract) {
            return redirect("/k12connections/contract-details");
        }

        // Redirect if whizara educator contract is missing
        if ($isWhizaraEducator && !$whizaraContract) {
            return redirect("/k12connections/contract-details");
        }
    }

    // Load pending onboarding steps (if any)
    $pendingData = json_decode($instructor->pending_onboarding_steps);

    // Show dashboard by default for allowed scenarios
    return view('web.onboarding-new.instructor.dashboard', compact('instructor', 'pendingData'));
}


    public function logout()
    {
        Auth::guard('instructor')->logout();
        Session::forget('instructor');
        return redirect("/k12connections/sign-in");
    }

    public function get_certification($id)
    {
        $data["certifications"] = certifications::where(["status" => 1])->orderBy("name", "asc")->get();
        $data["second"] = InstructorSecondStepOnboardingModel::where([
            "user_id" => $id,
        ])->first();
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data['credentialing_agency'] = CredentialingAgencyModel::get();
        $key = 0;

        $view = view('web.onboarding-new.components.add_certification', compact('data', 'key'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function get_academics($id)
    {
        $data["user_education"] = InstructorEducationSecondStepOnboardingModel::where([
            "user_id" => $id,
        ])->get();
        $data["education_list"] = EducationListModel::get();

        $view = view('web.onboarding-new.components.add_academics', compact('data'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function get_experience($id)
    {
        $data["class"] = Classes::get();
        $data["second"] = InstructorSecondStepOnboardingModel::where([
            "user_id" => $id,
        ])->first();

        $view = view('web.onboarding-new.components.add_experience', compact('data'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function get_other_experience($id)
    {
        $view = view('web.onboarding-new.components.add_other_experience')->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function get_reference($id)
    {
        $view = view('web.onboarding-new.components.add_reference')->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function delete_onboarding_step($id, Request $request)
    {
        $data = $request->except([
            "_token",
            "id",
            "resume",
            "filename",
            "other_specify",
            "specify",
            "resume_value",
            "file",
            "award_value",
            "certificatelastid",
            "awardlastid",
            "certificate_value",
            "distinction_value",
            "distinctionsid",
            "highest_level_of_education",
            "month_and_year_graduation",
            "GPA",
            "educationid",
            "experience_teaching_ages",
            "certified_special_education",
            "certification_visible_to_school",
            "teaching_certification_year",
            "teaching_certification_states",
            "certifications_other",
            "other_employer_name",
            "other_location",
            "other_position",
            "other_end_date",
            "other_start_date",
            "check_certification",
            "employer_name",
            "location",
            "position",
            "start_date",
            "end_date",
            "certificate",
            "transcript_visible_to_school",
            "transcript",
            "check_transcript",
            "transcript_value",
            "certificate_value",
            "credentialing_agency",

        ]);

        if ($request->hasFile("resume")) {
            $image = $request->file("resume");
            $name = time() . "." . $image->getClientOriginalExtension();
            $filename = 'uploads/marketplace/resume/' . $name;
            uploads3image($filename, $image);
            $data["resume"] = $name;
        } else {
            $data["resume"] = $request->resume_value;
        }

        if ($request->certification == "yes") {
            $data["specify"] = $request->specify;
        } else {
            $data["specify"] = $request->other_specify;
        }
        $data["user_id"] = $request->id;
        if ($request->certification == "yes") {
            $profiletype = "Certified Teacher";
        } else {
            $profiletype = $request->profile_type;
        }

        $data['certified_other'] = $request->certifications_other;
        $data["profile_type"] = $profiletype;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        // $data['employer_name'] = $request->employer_name;

        $first = InstructorSecondStepOnboardingModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            $result = InstructorSecondStepOnboardingModel::where(["user_id" => $request->id])->update($data);
            if ($result) {
                // Get the ID of the updated record
                $lastid = $first->id; // Since the record exists and is updated, it will still have the same ID
            }
        } else {
            $result = InstructorSecondStepOnboardingModel::insert($data);

            if ($result) {
                $lastid = DB::getPdo()->lastInsertId();
            }
        }

        if ($result) {

            $datasubject = [];

            if ($request->certified_special_education && count($request->certified_special_education) > 0) {
                foreach (array_filter($request->certified_special_education) as $key => $rows) {
                    if ($rows) {
                        $certificateFileName = null;
                        if ($request->hasFile("certificate")) {
                            $certificate = $request->file("certificate")[$key];
                            $name = time() . '-' . uniqid() . '.' . $certificate->getClientOriginalExtension();
                            $certificateFileName = 'uploads/marketplace/certificate/' . $name;
                            uploads3image($certificateFileName, $certificate);
                        } else {
                            $certificateFileName = $request->certificate_value;
                        }

                        $selectedStates = $request->teaching_certification_states[$key] ?? [];
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "education" => $rows,
                            'states' => json_encode($selectedStates),
                            "certification_year" => $request->teaching_certification_year[$key],
                            "certificate" => $certificateFileName,
                            "certification_visible_to_school" => $request->check_certification[$key],
                            "credentialing_agency" => $request->credentialing_agency[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }
            $first1 = InstructorSecondStepOnboardingModel::where([
                "user_id" => $request->id,
            ])->first();
            if (!empty($first1)) {
                InstructorOnboardingCertification::where(["user_id" => $request->id])->delete();
            }
            if (!empty($datasubject)) {
                InstructorOnboardingCertification::insert($datasubject);
            }
            $data_2 = [];
            if ($request->certification == "yes") {
                $cert = "yes";
            } else {
                $cert = $request->profile_type;
            }
            if (isset($request->highest_level_of_education) && !is_null($request->highest_level_of_education[0])) {
                if (count($request->highest_level_of_education) > 0) {
                    $data_2["highest_level_of_education"] = implode(
                        ",",
                        $request->highest_level_of_education
                    );
                }
            } else {
                $data_2["highest_level_of_education"] = null;
            }

            if (isset($request->month_and_year_graduation) && !is_null($request->month_and_year_graduation[0])) {
                if (count($request->month_and_year_graduation) > 0) {
                    $data_2["month_and_year_graduation"] = implode(
                        ",",
                        $request->month_and_year_graduation
                    );
                }
            } else {
                $data_2["month_and_year_graduation"] = null;
            }

            if (isset($request->GPA) && !is_null($request->GPA[0])) {
                if (count($request->GPA) > 0) {
                    $data_2["GPA"] = implode(
                        ",",
                        $request->GPA
                    );
                }
            } else {
                $data_2["highest_level_of_education"] = null;
            }

            if (isset($request->check_transcript) && !is_null($request->check_transcript[0])) {
                if (count($request->check_transcript) > 0) {
                    $data_2["transcript_visible_to_school"] = implode(
                        ",",
                        $request->check_transcript
                    );
                }
            } else {
                $data_2["transcript_visible_to_school"] = null;
            }

            if ($request->hasFile("transcript")) {
                $transcripts = [];
                foreach ($request->file('transcript') as $transcript) {
                    $name = time() . '-' . uniqid() . '.' . $transcript->getClientOriginalExtension();
                    $filename = 'uploads/marketplace/transcript' . $name;
                    uploads3image($filename, $transcript);
                    $transcripts[] = $name;
                }
                $data_2["transcript"] = implode(',', $transcripts);
            } else {
                $data["transcript"] = $request->transcript_value;
            }

            InstructorSecondStepOnboardingModel::where('user_id', $request->id)->update($data_2);

            $datasubject = [];
            if ($request->employer_name && count($request->employer_name) > 0) {
                foreach (array_filter($request->employer_name) as $key => $rows) {
                    if ($rows) {
                        $selectedAges = $request->experience_teaching_ages[$key] ?? [];
                        // dd(json_encode($selectedAges));
                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "employer_name" => $rows,
                            'location' => $request->location[$key],
                            "position" => $request->position[$key],
                            "start_date" => $request->start_date[$key],
                            "end_date" => $request->end_date[$key],
                            "experience_teaching_ages" => json_encode($selectedAges),
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $firstedu = InstructorExperienceTeaching::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                InstructorExperienceTeaching::where([
                    "user_id" => $request->id,
                ])->delete();
            }
            if (!empty($datasubject)) {
                InstructorExperienceTeaching::insert($datasubject);
            }

            $datasubject = [];
            if ($request->other_employer_name && count($request->other_employer_name) > 0) {
                foreach (array_filter($request->other_employer_name) as $key => $rows) {
                    if ($rows) {

                        $datasubject[] = [
                            "user_id" => $request->id,
                            "step_id" => $lastid,
                            "employer_name" => $rows,
                            'location' => $request->other_location[$key],
                            "position" => $request->other_position[$key],
                            "start_date" => $request->other_start_date[$key],
                            "end_date" => $request->other_end_date[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $firstedu = InstructorOtherExperienceOnboardingModel::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                InstructorOtherExperienceOnboardingModel::where([
                    "user_id" => $request->id,
                ])->delete();
            }
            if (!empty($datasubject)) {

                InstructorOtherExperienceOnboardingModel::insert($datasubject);
            }


            $datas["teacher_type"] = $cert;
            OnboardingInstructor::where("id", $request->id)->update($datas);
            $instructor = OnboardingInstructor::find($request->id);
            $updatedStepsData = json_decode($instructor->pending_onboarding_steps, true);
            $stepsToRemove = [
                "step-3:certification",
                "step-3:education",
                "step-3:teaching-experience",
                "step-3:resume",
            ];
            $key = array_search($stepsToRemove, $updatedStepsData);
            foreach ($stepsToRemove as $stepToRemove) {
                $key = array_search($stepToRemove, $updatedStepsData);

                if ($key !== false) {
                    unset($updatedStepsData[$key]);
                }
            }
            $updatedStepsData = array_values($updatedStepsData);
            OnboardingInstructor::where("id", $request->id)->update(['pending_onboarding_steps' => json_encode($updatedStepsData)]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitProfile()
    {
        if (Auth::guard('instructor')->user()->profile_status == 12) {
            return redirect('k12connections/dashboard');
        }
        return view("web.onboarding-new.components.submit");
    }

    public function instructorFaq()
    {
        $data['faqs'] = FaqsModel::where("type", 'Marketplace Instructor')->where("status", '1')->get();
        return view('web.onboarding-new.components.faq')->with($data);
    }

    public function instructorNotifications()
    {
        $user = Auth::guard('instructor')->user();
        $user_id = @$user->id;
        if (empty($user_id)) {
            return redirect("/");
        }
        if (checkAuth($user->status)) {
            return redirect("/k12connections/logout");
        }

        notification::where(["user_id" => $user_id, "is_read" => 0])->update(['is_read' => 1]);

        $data['notification'] = notification::where("user_id", $user_id)->orderBy("id", "desc")->paginate(10);
        return view('web.onboarding-new.components.notification')->with($data);
    }

    public function instructorMessages()
    {
        $data['support'] = User::where(["type" => 12])->orderBy('id', 'desc')->first();
        return view('web.onboarding-new.components.message')->with($data);
    }

    public function deleteProfileImage($id, Request $request)
    {
        $fiveData = InstructorFifthStepOnboardingModel::find($id);
        if (!empty($fiveData)) {
            $fiveData->update(['profile_image' => null]);

            return response()->json(['success' => true, 'message' => 'Deleted Successfully']);
        } else {
            return response()->json(['success' => false, 'message' => 'Something went wrong']);
        }
    }

    public function handleStepValidationLogic(array $stepsToAdd, array $stepsToRemove, string $id): void
    {
        $instructor = OnboardingInstructor::find($id);
        if (!$instructor) {
            throw new \Exception("Instructor with ID $id not found.");
        }

        $steps = json_decode($instructor->pending_onboarding_steps, true) ?? [];

        foreach ($stepsToAdd as $stepName) {
            if (!in_array($stepName, $steps, true)) {
                $steps[] = $stepName;
            }
        }

        $steps = array_filter($steps, fn($step) => !in_array($step, $stepsToRemove, true));
        $steps = array_values($steps);

        $instructor->update([
            'pending_onboarding_steps' => json_encode($steps),
        ]);
    }

    public function contractDetails(Request $request)
    {
        $user = OnboardingInstructor::with('step1', 'step2', 'step3', 'step5', 'step6')
            ->find(Auth::guard('instructor')->user()->id);

        // $user = OnboardingInstructor::with(['step2', 'step3'])->where('id', "27")->first();

        $budget_info = InstructorBudgetApprovedModel::with('lines')->where('user_id', $user->id)->first();

        $marketplaceEducator = OnboardingInstructorMarketplaceContract::where('user_id', Auth::guard('instructor')->user()->id)->first();
        $whizaraEducator = OnboardingInstructorContract::where('user_id', Auth::guard('instructor')->user()->id)->first();
        $agreementRoute = route('agreement-details');

        if ($request->ajax()) {
            $view = view('web.onboarding-new.instructor.contract.info', compact('user', 'agreementRoute', 'marketplaceEducator', 'whizaraEducator', 'budget_info'))->render();
            return response()->json(['success' => true, 'view' => $view]);
        }

        return view('web.onboarding-new.instructor.contract.contract', compact('user', 'agreementRoute', 'marketplaceEducator', 'whizaraEducator', 'budget_info'));
    }

    public function agreement(Request $request)
    {
        $user = OnboardingInstructor::with('step3')->find(Auth::guard('instructor')->user()->id);
        $educator = $this->updateEducator($request, $user);
        $educatorContract = OnboardingInstructorContract::where('user_id', $user->id)->first();
        $format = explode(',', $user->step3->format);
        if (in_array('hybrid', $format)) {
            $contractData = SettingTermsModel::where("locale", "en")->where('id', '14')->first();
        } else {
            if (in_array('online', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '16')->first();
            } elseif (in_array('in-person', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '17')->first();
            }
        }
        $contract_name = 'whizara';
        if ($request->first_name && !$educatorContract) {
            $this->sendAgreementEmail($request, $user, $contractData, $contract_name);
            $this->createEducatorContract($request, $user);
        }

        $educatorContract = OnboardingInstructorContract::where('user_id', $user->id)->first();
        $redirectRoute = route('instructorDashboard');

        if ($request->ajax()) {
            $view = $this->getAgreementView($request, $user, $contractData, $educatorContract);
            return response()->json(['success' => true, 'view' => $view, 'redirectRoute' => $redirectRoute, 'request' => $request->all()]);
        }
    }

    public function submitAgreement(Request $request)
    {
        $user = OnboardingInstructor::with('step3')->find(Auth::guard('instructor')->user()->id);
        $format = explode(',', $user->step3->format);
        if (in_array('hybrid', $format)) {
            $contractData = SettingTermsModel::where("locale", "en")->where('id', '14')->first();
        } else {
            if (in_array('online', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '16')->first();
            } elseif (in_array('in-person', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '17')->first();
            }
        }
        $contract_name = 'marketplace';
        $this->sendAgreementEmail($request, $user, $contractData, $contract_name);
        $educatorData = [
            'user_id' => Auth::guard('instructor')->user()->id,
            'terms_accepted' => $request->terms_of_service_accepted,
            'work_eligibility_confirmed' => $request->work_eligibility_confirmed,
            'marketplace_fee_accepted' => $request->marketplace_fee_accepted,
            'first_name' => !empty($request->first_name) ? trim($request->first_name) : null,
            'last_name' => !empty($request->last_name) ? trim($request->last_name) : null,
            'address' => !empty($request->address) ? trim($request->address) : null,
            'contract_sign' => now(),
            'marketplace_contract' => 'uploads/marketplace/contract/' . Auth::guard('instructor')->user()->id . '-marketplace-educator.pdf',
        ];
        OnboardingInstructorMarketplaceContract::create($educatorData);
        $redirectRoute = route('instructorDashboard');
        return response()->json(['success' => true, 'redirectRoute' => $redirectRoute]);
    }

    public function submitBothAgreement(Request $request)
    {
        $user = OnboardingInstructor::with('step3')->find(Auth::guard('instructor')->user()->id);
        $format = explode(',', $user->step3->format);
        if (in_array('hybrid', $format)) {
            $contractData = SettingTermsModel::where("locale", "en")->where('id', '14')->first();
        } else {
            if (in_array('online', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '16')->first();
            } elseif (in_array('in-person', $format)) {
                $contractData = SettingTermsModel::where("locale", "en")->where('id', '17')->first();
            }
        }
        $contract_name = 'both';
        $this->sendAgreementEmail($request, $user, $contractData, $contract_name);
        $educatorData = [
            'user_id' => Auth::guard('instructor')->user()->id,
            'terms_accepted' => $request->terms_of_service_accepted,
            'work_eligibility_confirmed' => $request->work_eligibility_confirmed,
            'first_name' => !empty($request->first_name) ? trim($request->first_name) : null,
            'last_name' => !empty($request->last_name) ? trim($request->last_name) : null,
            'address' => !empty($request->address) ? trim($request->address) : null,
            'contract_sign' => now(),
            'marketplace_contract' => 'uploads/marketplace/contract/' . Auth::guard('instructor')->user()->id . '-both-educator.pdf',
        ];
        $educatorData = array_merge($educatorData, [
            'terms_accepted' => $request->terms_of_service_accepted,
        ]);

        // Data for OnboardingInstructorMarketplaceContract
        $marketplaceData = array_merge($educatorData, [
            'privacy_policy_accepted' => $request->privacy_policy_accepted,
            'marketplace_fee_accepted' => $request->marketplace_fee_accepted,
        ]);
        OnboardingInstructorContract::create($educatorData);
        OnboardingInstructorMarketplaceContract::create($marketplaceData);
        $redirectRoute = route('instructorDashboard');
        return response()->json(['success' => true, 'redirectRoute' => $redirectRoute]);
    }

    private function updateEducator($request, $user)
    {
        if (!empty($request->whizaraMarketplace) || !empty($request->whizaraEducator)) {
            $educator = array_filter([$request->whizaraMarketplace, $request->whizaraEducator]);
            $user->update(['educator' => implode(',', $educator)]);
        }
        return $user->fresh()->educator;
    }

    private function sendAgreementEmail($request, $user, $contractData, $contract_name)
    {
        $user->update(['user_status' => 'Active']);
        $emailTemplateId = '';
        if (!empty($user->educator)) {
            $educator = explode(',', $user->educator);
            if (in_array('whizara educator', $educator) && in_array('marketplace educator', $educator)) {
                $emailTemplateId = 43;
            } elseif (in_array('whizara educator', $educator)) {
                $emailTemplateId = 41;
            } elseif (in_array('marketplace educator', $educator)) {
                $emailTemplateId = 42;
            }
        }
        // $template = DB::table("tbl_email_templates")->where("email_template_id", "41")->first();
        $template = DB::table("tbl_email_templates")->where("email_template_id", $emailTemplateId)->first();
        $budget_info = InstructorBudgetApprovedModel::with('lines')->where('user_id', $user->id)->first();
        $fullName = !empty($request->last_name) ? trim("{$request->first_name} {$request->last_name}") : Auth::guard('instructor')->user()->first_name . ' ' . Auth::guard('instructor')->user()->last_name;
        $body = str_replace('{{NAME}}', $fullName, $template->description);
        $subject = $template->subject;
        $email = $user->email;
        $address = !empty($request->address) ? $request->address : '';
        $replacements = [
            '{{LEGAL_NAME}}' => $fullName,
            '{{ADDRESS}}' => $address,
            '{{DATE}}' => date('M d, Y')
        ];
        if (strpos($contractData->description, '{{SUBJECTS}}') !== false) {
            $subject_list = view('web.onboarding-new.instructor.contract.subjects', compact('budget_info'));
            $replacements['{{SUBJECTS}}'] = $subject_list;
        }
        if (strpos($contractData->description, '{{IN_PERSON_RATE}}') !== false) {
            $replacements['{{IN_PERSON_RATE}}'] = '$' . $user->inpersonrate;
        }

        if (strpos($contractData->description, '{{ONLINE_RATE}}') !== false) {
            $replacements['{{ONLINE_RATE}}'] = '$' . $user->onlinerate;
        }
        $agreementContent = str_replace(array_keys($replacements), array_values($replacements), $contractData->description);

        $pdf = Pdf::loadView('web.pdf.agreement_template', compact('agreementContent'));
        $pdfFileName = uniqid() . '.pdf';
        $pdfPath = storage_path('app/public/' . $pdfFileName);
        $pdf->save($pdfPath);
        $filename = 'uploads/marketplace/contract/' . Auth::guard('instructor')->user()->id . '-' . $contract_name . '-educator.pdf';
        uploads3image($filename, $pdfPath);
        if ($emailTemplateId == 43 || $emailTemplateId == 41) {
            $this->sendEmailWithAttachment($email, $subject, $pdfPath, ['template' => $body]);
        } elseif ($emailTemplateId == 42) {
            $data = ['template' => $body];
            Mail::send('template', $data, function ($message) use ($email, $subject) {
                $message->to($email)->subject($subject);
            });
        }
    }

    private function createEducatorContract($request, $user)
    {
        $educatorData = [
            'user_id' => $user->id,
            'terms_accepted' => $request->terms_accepted,
            'terms_of_service_accepted' => $request->terms_of_service_accepted,
            'work_eligibility_confirmed' => $request->work_eligibility_confirmed,
            'first_name' => trim($request->first_name),
            'last_name' => trim($request->last_name),
            'address' => trim($request->address),
            'contract_sign' => now(),
            'whizara_contract' => 'uploads/marketplace/contract/' . $user->id . '-whizara-educator.pdf',
        ];
        OnboardingInstructorContract::create($educatorData);
    }

    private function getAgreementView($request, $user, $contractData, $educatorContract)
    {
        $fullName = $user->first_name . ' ' . $user->last_name;
        $replacements = ['{{DATE}}' => date('M d, Y')];
        $budget_info = InstructorBudgetApprovedModel::with('lines')->where('user_id', $user->id)->first();
        if (strpos($contractData->description, '{{SUBJECTS}}') !== false) {
            $subject_list = view('web.onboarding-new.instructor.contract.subjects', compact('budget_info'));
            $replacements['{{SUBJECTS}}'] = $subject_list;
        }
        $contractData = str_replace(array_keys($replacements), array_values($replacements), $contractData->description);
        return view("web.onboarding-new.instructor.contract.agreement.agreement-layout", compact('user', 'contractData', 'educatorContract'))->render();
    }

    public function declinedContract(Request $request)
    {
        $user = Auth::guard('instructor')->user();
        if ($user) {
            OnboardingInstructor::where('id', $user->id)->update(['isDeclineContract' => 1]);
        }
        $redirectRoute = route('instructorDashboard');
        return response()->json(['success' => true, 'redirectRoute' => $redirectRoute]);
    }

    public function s3uploadlink(Request $request)
    {


        $validator = \Validator::make($request->all(), [
            'video' => 'required|mimes:mp4,mov,avi,wmv,mkv|max:20480', // Max 200MB (204800 KB)
        ]);

        // Check if the validation failed
        if ($validator->fails()) {
            // You can handle the validation failure as you like, for example:
            return response()->json([
                'status' => 'error',
                'error' => $validator->errors() // You can customize the error messages here
            ], 422); // 422 Unprocessable Entity

        }


        if ($request->hasFile('video')) {
            // $file = $request->file('video');
            // $filePath = $file->store('videos', 'public'); // Save in storage/app/public/videos
            $video = $request->file("video");
            $videoname = $video->getClientOriginalName();
            $name = time() . "." . $video->getClientOriginalExtension();
            $filename = 'uploads/admin/' . uniqid() . '_' . $video->getClientOriginalName();
            $publicPath = public_path('uploads/admin');
            if (!file_exists($publicPath)) {
                mkdir($publicPath, 0777, true); // Create directory with permissions
            }
            $video->move($publicPath, $filename);
            // uploads3image($filename, $video);
            // Dispatch job to upload video asynchronously
            S3VideoUpload::dispatch($filename, 'uploads/admin/' . $filename);
            return response()->json([
                'success' => true,
                'message' => 'Video uploaded successfully',
                'video_url' => $filename,
                "videoname" => $videoname
            ]);
        }

        return response()->json(['success' => false, 'message' => 'Video upload failed'], 400);
    }

    public function s3recordingUploadlink(Request $request)
    {
        if ($request->hasFile('video')) {
            $video = $request->file("video");
            $filename = 'uploads/admin/' . uniqid() . '_' . $video->getClientOriginalName();
            $videoName = $video->getClientOriginalName();
            uploads3image($filename, $video);
            $userId = Session::get('instructorlogin.id');
            $profile = InstructorFifthStepOnboardingModel::where('user_id', decrypt($userId))->first();
            if ($profile) {
                $profile->update([
                    'video' => $filename,
                    'video_name' => $videoName,
                    'video_source' => 'recording'
                ]);
            } else {
                $data = [
                    'user_id' => decrypt($userId),
                    'video' => $filename,
                    'video_name' => $videoName,
                    'video_source' => 'recording',
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s")
                ];
                InstructorFifthStepOnboardingModel::insert($data);
            }

            return response()->json([
                'success' => true,
                'message' => 'Video uploaded successfully',
                'filename' => $filename,
                'userId' => decrypt($userId),
            ]);
        }

        return response()->json(['success' => false, 'message' => 'Video upload failed'], 400);
    }

    public function getRecording(Request $request)
    {
        $userId = Session::get('instructorlogin.id');
        $video = InstructorFifthStepOnboardingModel::where('user_id', decrypt($userId))->first();
        if (!empty($video->video) && !empty($video->video_source)) {
            return response()->json(['success' => true, 'video_name' => $video->video_name, 'video_url' => $video->video, 'video_source' => $video->video_source]);
        }

        return response()->json(['success' => false, 'userId' => $userId, 'video' => $video]);
    }

    public function additional_certificate_all_category(Request $request)
    {



        $all_additional_certificate_categories = AdditionalCertificateCategory::all();


        return response()->json([
            "all_categories" => $all_additional_certificate_categories,
        ]);
    }

    public function get_all_sub_category(Request $request)
    {

        $id = $request->id;

        $subactegories = AdditionalCertificateSubcategory::where("category_id", $id)->get();




        return response()->json($subactegories);
    }

    public function removeRecordingVideo(Request $request)
    {
        $profile = InstructorFifthStepOnboardingModel::where(["user_id" => $request->userId])->first();
        if (!empty($profile)) {
            $profile->update(['video' => null, 'video_name' => null, 'video_source' => null]);
            $user = OnboardingInstructor::find($request->userId);
            $pendingSteps = json_decode($user->pending_onboarding_steps, true);
            if (!is_array($pendingSteps)) {
                $pendingSteps = [];
            }
            if (!in_array("step-6:assessment", $pendingSteps)) {
                $pendingSteps[] = "step-6:assessment"; // Add the step
                $user->pending_onboarding_steps = json_encode($pendingSteps);
                $user->save(); // Save the updated data
            }
        }

        return response()->json(['success' => true]);
    }

    public function sampleLesssonSubjects()
    {



        $subjects = V1Subject::all();


        return response()->json($subjects);
    }
    public function submit_sample_lesson(Request $request)
    {


        if ($request->hidden_sample_input) {

            $delete = SampleLesson::where('user_id', $request->hidden_sample_input)->delete();

            $existing_file_url = $request->existing_file_url;
            $existingNames = [];

            // Only process if $existing_file_url is an array
            if (is_array($existing_file_url)) {
                // Filter out empty or null paths
                $existing_file_url = array_filter($existing_file_url);

                foreach ($existing_file_url as $path) {
                    $existingNames[] = basename($path);
                }
            }

            $savedFilesurl = [];
            $filenames = [];
            $lessonSubjects = $request->input('Lessonsubject');
            $lessonnotes = $request->input('lessondescription');

            // Add existing files
            $savedFilesurl = array_merge($savedFilesurl, $existing_file_url ?? []);
            $filenames = array_merge($filenames, $existingNames);

            // Handle newly uploaded files
            if ($request->hasFile('lesson_upload')) {
                foreach ($request->file('lesson_upload') as $file) {
                    if ($file->isValid()) {
                        $originalName = $file->getClientOriginalName();

                        if (!empty($originalName)) {
                            $destinationPath = 'uploads/marketplace/samplelessons/' . $originalName;
                            $file->move(public_path('uploads/marketplace/samplelessons'), $originalName);

                            $filenames[] = $originalName;
                            $savedFilesurl[] = $destinationPath;
                        }
                    }
                }
            }

            // Final cleanup (just in case)
            $savedFilesurl = array_filter($savedFilesurl);
            $filenames = array_filter($filenames);

           
            foreach ($savedFilesurl as $index => $url) {
                $samplelesson = new SampleLesson();
                $samplelesson->user_id = Auth::guard('instructor')->user()->id;
                $samplelesson->subject_id = $lessonSubjects[$index] ?? null;
                $samplelesson->file_url = $url;
                $samplelesson->additional_notes = $lessonnotes[$index] ?? null;
                $samplelesson->file_name = $filenames[$index] ?? null;

                $samplelesson->save();
            }

            return response()->json([
                "status" => "updated"
            ]);
        } else {
            try {

                $savedFilesurl = [];
                $filenames = [];

                $lessonSubjects = $request->input('Lessonsubject');
                $lessonnotes = $request->input('lessondescription');


                if ($request->hasFile('lesson_upload')) {
                    $lessonFiles = $request->file('lesson_upload');

     


                    foreach ($lessonFiles as $file) {
                        if ($file->isValid()) {
                            $originalName = $file->getClientOriginalName();
                            $filenames[] = $originalName;
                            $destinationPath = 'uploads/marketplace/samplelessons/' . $originalName;
                             uploads3image($destinationPath, $file);
                            

                            // Save the file URL
                            $savedFilesurl[] = $destinationPath;
                        }
                    }
                }


                foreach ($savedFilesurl as $index => $url) {
                    $samplelesson = new SampleLesson();
                    $samplelesson->user_id = Auth::guard('instructor')->user()->id;
                    $samplelesson->subject_id = $lessonSubjects[$index] ?? null;
                    $samplelesson->file_url = $url;
                    $samplelesson->additional_notes = $lessonnotes[$index] ?? null;
                    $samplelesson->file_name = $filenames[$index] ?? null;

                    $samplelesson->save();
                }


                return response()->json([
                    "status" => "success"
                ]);
            } catch (\Exception $e) {
                // Log the error message for debugging
                \Log::error('Error saving sample lesson: ' . $e->getMessage());

                // Optionally return an error response
                return response()->json(['error' => 'Something went wrong. Please try again later.'], 500);
            }
        }
    }
}
