<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class k12ConnectionInvitePrograms extends Model
{
    protected $table = 'k12_connection_invite_programs';
    protected $fillable = [
        'user_id',
        'program_id',
        'status',
        'is_approved',
        'type',
        'admin_type',
        'replacement_type',
        'requested_by',
        'has_requested',
        'parent_id',
        'is_makeup',
        'is_sub_only',
        'deadline',
        'is_auto_invite',
        'resend_count',
        'is_standby',
        'is_replace',
        'replacement_start_date',
        'cancelled_by',
        'program_invite_type',
        'reminder_sent',
        'timezone',
        'current_time',
    ];
}
