<div class="footerareas">

  <div class="container" style="display:block;">
    <div class="footerarea__newsletter__wrapers" style="padding-bottom: 45px;
    padding-top: 65px;">
      <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12" data-aos="fade-up">
          <div class="footerarea__text " style="display:none;">
            <h3>Still You Need Our <span>Support</span> ?</h3>
            <p>Don’t wait make a smart & logical quote here. Its pretty easy.</p>
          </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12" data-aos="fade-up">
          <div class="footerarea__newsletter" style="display:none;">
            <div class="footerarea__newsletter__input">
              <form action="#">
                <input type="text" placeholder="Enter your email here">
                <div class="footerarea__newsletter__button">
                  <button type="submit" class="subscribe__btn">Subscribe Now</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>





    <div class="footerarea__wrapper footerarea__wrapper__2" style="display:none;">
      <div class="row">
        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12" data-aos="fade-up">
          <div class="footerarea__inner footerarea__about__us">
            <div class="footerarea__heading">
              <h3>About us</h3>
            </div>
            <div class="footerarea__content">
              <p>augue nec auctor fermentum, velit elit mollis turpis, ut condimentum ligula erat at magna. Pellentesque vitae eget leo porttitor laoreet lectus. Nullam mollis tincidunt lo sollicitudin. Nulla varius non dolor vitae elit scelerisque. Donec et iaculis augue. </p>
            </div>


          </div>
        </div>
        <div class="col-xl-2 col-lg-2 col-md-6 col-sm-6" data-aos="fade-up">
          <div class="footerarea__inner">
            <div class="footerarea__heading">
              <h3>Usefull Links</h3>
            </div>
            <div class="footerarea__list">
              <ul>
                <li>
                  <a href="#">About Us</a>
                </li>
                <li>
                  <a href="#">Teachers</a>
                </li>
                <li>
                  <a href="#">Partner</a>
                </li>
                <li>
                  <a href="#">Dolor</a>
                </li>

              </ul>
            </div>


          </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6" data-aos="fade-up">
          <div class="footerarea__inner footerarea__padding__left">
            <div class="footerarea__heading">
              <h3>Course</h3>
            </div>
            <div class="footerarea__list">
              <ul>
                <li>
                  <a href="#">Faq</a>
                </li>
                <li>
                  <a href="#">Contact Us</a>
                </li>
                <li>
                  <a href="#">Privacy Policy</a>
                </li>
                <li>
                  <a href="#">Terms & Condition</a>
                </li>
                <li>
                  <a href="#">Ipsum elit</a>
                </li>
              </ul>
            </div>


          </div>
        </div>

        <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12" data-aos="fade-up">
          <div class="footerarea__right__wraper footerarea__inner">
            <div class="footerarea__heading">
              <h3>Recent Post</h3>
            </div>
            <div class="footerarea__right__list">
              <ul>
                <li>
                  <a href="#">
                    <div class="footerarea__right__img">
                      <img loading="lazy" src="<?php echo e(asset('website/img/footer/footer__1.png')); ?>" alt="footerphoto">
                    </div>
                    <div class="footerarea__right__content">
                      <span>02 Apr 2023 </span>
                      <h6>Program 1</h6>
                    </div>
                  </a>
                </li>

                <li>
                  <a href="#">
                    <div class="footerarea__right__img">
                      <img loading="lazy" src="<?php echo e(asset('website/img/footer/footer__2.png')); ?>" alt="footerphoto">
                    </div>
                    <div class="footerarea__right__content">
                      <span>02 Apr 2023 </span>
                      <h6>Program 2</h6>
                    </div>
                  </a>
                </li>

                <li>
                  <a href="#">
                    <div class="footerarea__right__img">
                      <img loading="lazy" src="<?php echo e(asset('website/img/footer/footer__3.png')); ?>" alt="footerphoto">
                    </div>
                    <div class="footerarea__right__content">
                      <span>02 Apr 2023 </span>
                      <h6>Program 3</h6>
                    </div>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</div>
<div class="modal fade" id="previewmodel" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">Submission Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="append" style="padding: 16PX;">

      </div>

    </div>
  </div>
</div>



<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog ">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staticBackdropLabel">Submit Completion Documents</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row loginarea__wraper ">
          <div class="col-xl-12">
            <div class="inner-form video Classroom">
              <form action="#" method="post" id="uponcompletionid" enctype='multipart/form-data'>
                <?php echo csrf_field(); ?>

                <div class="row">
                  <input type="hidden" id="back_med_id" name="back_med_id" value="">
                  <div class="col-xl-12 col-md-12 ">
                    <div class="login__form">
                      <h4 class="form-heading">Reference Number</h4>
                      <!-- <label class="form__label">Email address*</label> -->
                      <input class="common__login__input" type="text" placeholder="Enter Reference Number" name="reference" id="reference">
                    </div>
                  </div>

                  <div class="col-xl-12 col-md-12 calendar">
                    <div class="login__form">
                      <h4 class="form-heading">Select Date</h4>
                      <label for="date"></label>
                      
                      <input type="text" class="av-datepickerfilter  date-ui entervalue from_date" id="date" name="date">

                    </div>
                  </div>



                  <div class="col-xl-12 col-md-12">
                    <div class="login__form" style="position: relative;">
                      <h4 class="form-heading">Proof of completion</h4>

                      <input type="file" id="myFileb" name="file" class="comfile">
                      <input class="common__login__input second comfiles" type="text" placeholder="Upload File">

                      <a href="javascript:void(0);" class="filecomp" style="top: 50px;position:absolute;right: 20px;">
                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                          <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>

                      </a>
                    </div>
                  </div>

                </div>
              </form>



            </div>
          </div>
        </div>
        <div class="modal-footer" style="border:none;">
          <div class="d-flex bnt justify-content-end m-auto">
            <div class="left-btn">
              <a class="btn btn-info next default__button text-right text-left save-dashboard savecompletion" style="margin-top: 0;" type="button">Submit</a>
              <!-- <a class="btn btn-info next default__button text-right">Save &amp; Complete <i class="fas fa-angle-right"></i></a> -->
            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</div>


<div class="modal fade" id="program-filter-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="program-filter-modalLabel" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="program-filter-modalLabel">Filters</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row loginarea__wraper ">
          <div class="col-xl-12">
            <div class="inner-form video Classroom">
              <form onsubmit="event.preventDefault(); drawTables(); $('#program-filter-modal').modal('hide'); $(this)[0].reset();" id="filter-form">
                <div class="row">


                  <div class="col-xl-6 col-md-12 ">
                    <div class="login__form select">
                      <h4 class="form-heading">District</h4>
                      <?php
                      $districts = \App\District::where("status", "1")->pluck('name','id');
                      ?>

                      <select class="form-select" id="filter_district" onchange="getReplaceHtmlData(this,'<?php echo e(route("program.get-district-schools",["id"=>"id"])); ?>','#filter_school')">

                        <option value=""> Select District</option>
                        <?php $__currentLoopData = $districts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dkey =>$district): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($dkey); ?>"><?php echo e($district); ?></option>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                      </select>
                    </div>
                  </div>

                  <div class="col-xl-6 col-md-12 ">
                    <div class="login__form select">
                      <h4 class="form-heading">School</h4>
                      <?php
                      $schools = \App\User::where("type", 6)->pluck('full_name','id');
                      ?>

                      <select class="form-select" id="filter_school">

                        <option value=""> Select School</option>
                        <?php $__currentLoopData = $schools; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key =>$school): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"><?php echo e($school); ?></option>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                      </select>
                    </div>
                  </div>

                  <div class="col-xl-6 col-md-12 calendar">
                    <div class="login__form select" style="position: relative;">
                      <h4 class="form-heading">Format</h4>
                      <select class="form-select" id="filter_delivery_type">
                        <option value=""> Select Format</option>
                        <option value="Online">Online</option>
                        <option value="In-Person">In-Person</option>
                        <option value="">Both</option>
                      </select>

                    </div>
                  </div>
                  <div class="col-xl-6 col-md-12 ">
                    <div class="login__form select">
                      <h4 class="form-heading">Grade Level</h4>
                      <!-- <label class="form__label">Email address*</label> -->
                      <?php
                      $grades = \App\Classes::where('status',1)->pluck('class_name','id');
                      ?>

                      <select class="form-select" id="filter_grade">

                        <option value=""> Select Grade</option>
                        <?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key =>$grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"><?php echo e($grade); ?></option>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                      </select>


                    </div>
                  </div>
                  
                  

                  <div class="col-xl-6 col-md-12 calendar">
                    <div class="login__form">
                      <h4 class="form-heading">Start Date</h4>

                      <div class="cs-form">
                        <input type="text" class="av-datepickerfilter  date-ui entervalue from_date" id="filter_start_date" name="start_date">
                        
                      </div>
                    </div>
                  </div>


                  <div class="col-xl-6 col-md-12 calendar">
                    <div class="login__form">
                      <h4 class="form-heading">End Date</h4>

                      <div class="cs-form">
                        <input type="text" class="av-datepickerfilter  date-ui entervalue from_date" id="filter_end_date" name="end_date">
                        
                      </div>
                    </div>
                  </div>

                  <div class="col-md-4 ">
                    <div class="login__form">
                      <h4 class="form-heading">Start time</h4>

                      <div class="cs-form">
                        <input type="text" class="form-control time-zone timepicker" value="" id="filter_start_time">
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="login__form">
                      <h4 class="form-heading">End time</h4>

                      <div class="cs-form">
                        <input type="text" class="form-control time-zone timepicker" value="" id="filter_end_time">
                      </div>
                    </div>
                  </div>
                  <div class="col-xl-4 col-md-12 ">
                    <div class="login__form">
                      <h4 class="form-heading">Course name </h4>
                      <!-- <label class="form__label">Email address*</label> -->
                      <input class="common__login__input" type="text" placeholder="Enter Course name" id="filter_name">
                    </div>
                  </div>

                </div>
              </form>

            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="d-flex bnt justify-content-end m-auto">
          <div class="left-btn">
            <button type="submit" form="filter-form" class="btn btn-info next default__button text-right text-left save-dashboard " style="margin-top: 0;">Search </button>


          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="modal fade" id="new-program-listing-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="program-filter-modalLabel" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="program-filter-modalLabel">Filters</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row loginarea__wraper">
          <div class="col-xl-12">
            <div class="inner-form video Classroom">
              <form onsubmit="event.preventDefault(); drawTables(); $('#new-program-listing-modal').modal('hide'); $(this)[0].reset();" id="filter-form">
                <div class="row">
                  <!-- Removed District and School dropdowns -->

                  <div class="col-xl-6 col-md-12 calendar">
                    <div class="login__form select" style="position: relative;">
                      <h4 class="form-heading">Format</h4>
                      <select class="form-select" id="filter_delivery_type" onchange="toggleCityStateFields(this)">
                        <option value=""> Select Format</option>
                        <option value="Online">Online</option>
                        <option value="In-Person">In-Person</option>
                        <option value="">Both</option>
                      </select>
                    </div>
                  </div>

                  
            <div class="col-xl-6 col-md-12 calendar city-field d-none">
              <div class="login__form">
                <h4 class="form-heading">City</h4>
                <input type="text" id="filter_city" class="common__login__input" placeholder="Enter City">
              </div>
            </div>
            <div class="col-xl-6 col-md-12 calendar state-field d-none">
              <div class="login__form select">
                <h4 class="form-heading">State</h4>
                <?php
                $states = \App\StateModel::pluck('name', 'id'); // Replace with actual data source
                ?>
                <select class="form-select" id="filter_state">
                  <option value=""> Select State</option>
                  <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>"><?php echo e($state); ?></option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>

            <div class="col-xl-6 col-md-12">
              <div class="login__form select">
                <h4 class="form-heading">Grade Level</h4>
                <?php
                $grades = \App\Classes::where('status',1)->pluck('class_name','id');
                ?>

                <select class="form-select" id="filter_grade">
                  <option value=""> Select Grade</option>
                  <?php $__currentLoopData = $grades; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key =>$grade): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>"><?php echo e($grade); ?></option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>

            <div class="col-xl-6 col-md-12 calendar">
              <div class="login__form">
                <h4 class="form-heading">Start Month</h4>
                <div class="cs-form">
                  <input type="text" class="av-monthpickerfilter date-ui entervalue from_date" id="filter_start_month" name="start_month">
                </div>
              </div>
            </div>
            <!-- Time fields -->
            <div class="row">
            <div class="col-md-4">
              <div class="login__form">
                <h4 class="form-heading">Start Time</h4>
                <div class="cs-form">
                  <input type="text" class="form-control time-zone timepicker" value="" id="filter_start_time">
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="login__form">
                <h4 class="form-heading">End Time</h4>
                <div class="cs-form">
                  <input type="text" class="form-control time-zone timepicker" value="" id="filter_end_time">
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="login__form">
                <h4 class="form-heading">Subject</h4>
                <input type="text" class="common__login__input " value="" id="filter_subject">
              </div>
            </div>
            </div>
          </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <div class="d-flex bnt justify-content-end m-auto">
      <div class="left-btn">
        <button type="submit" form="filter-form" class="btn btn-info next default__button text-right text-left save-dashboard" style="margin-top: 0;">Search</button>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<script>
  function toggleCityStateFields(selectElement) {
    const format = selectElement.value;
    const cityField = document.querySelector('.city-field');
    const stateField = document.querySelector('.state-field');

    if (format === 'In-Person') {
      cityField.classList.remove('d-none');
      stateField.classList.remove('d-none');
    } else {
      cityField.classList.add('d-none');
      stateField.classList.add('d-none');
    }
  }
</script>


<div class="modal fade" id="commanModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="commanModalLabel" style="display: none;" aria-hidden="true">
  <div class="modal-dialog">

  </div>
</div>


<!-- confirm-modal popup start -->
<div class="modal fade" id="confirm-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body confirm-modal">
        <div class="row loginarea__wraper ">
          <div class="col-xl-12">
            <div class="inner-form video Classroom">
              <form action="#">
                <div class="row">
                  <div class="modal-header d-block text-center">
                    <h5 class="modal-title" id="staticBackdropLabel">Confirm</h5>
                    <p class="mb-0">Are you sure?</p>
                  </div>
                </div>
                <div class="modal-footer confirm-modal-footer">
                  <div class="d-flex bnt justify-content-end m-auto">
                    <div class="left-btn">
                      <a class="btn btn-info next default__button text-right text-left confirm-modal" style="margin-top: 0;" id="confirm-modal-confirm">Yes
                      </a>
                      <button class="btn btn-info next default__button text-right" type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="confirm-modal-cancel">NO</button>
                    </div>
                  </div>
                </div>
              </form>
              <!--  <div class="login__button">
                                 <a class="default__button text-right" href="/verify">Sign Up</a>
                                 </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- confirm-modal popup end -->


<div class="modal fade" id="infoModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="infoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">

  </div>
</div>
<?php /**PATH D:\whizara\whizara\resources\views/web/layouts/footer.blade.php ENDPATH**/ ?>