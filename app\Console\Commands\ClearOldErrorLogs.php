<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ErrorLog;
use Carbon\Carbon;

class ClearOldErrorLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:clear-old';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear error logs older than 2 months.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $twoMonthsAgo = Carbon::now()->subMonths(2);

        $deletedCount = ErrorLog::where('created_at', '<', $twoMonthsAgo)->delete();

        $this->info("{$deletedCount} error logs older than 2 months have been deleted.");
    
    }
}
