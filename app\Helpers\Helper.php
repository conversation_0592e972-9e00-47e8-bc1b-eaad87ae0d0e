
<?php

use App\AdditionalCertificateCategory;
use App\AdditionalCertificateSubcategory;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Hashids\Hashids;
use App\UponCompletionModel;
use App\BackgroundMedicalModel;
use App\Classes;
use App\User;
use App\ViewClassroomModel;
use App\document_form;
use App\SubsubjectModel;
use App\District;
use App\Subject;
use App\AssessmentsModel;
use App\invite_program_owners;
use App\notification;
use App\Notification_content;
use Aws\S3\S3Client;
use App\AdministrativeAuthorizationModel;
use App\AvailabilityRangeModel;
use App\EmailTemplate;
use App\GradeLevelModel;
use App\Helpers\NotificationHelper;
use App\invite_application_recruiter;
use App\ProgramNote;
use App\Programs;
use App\ProgramNoteStudent;
use App\invite_programs;
use App\InviteProgramNote;
use App\Models\k12ConnectionCategorizedData;
use App\Models\k12ConnectionClasses;
use App\Models\k12ConnectionInvitePrograms;
use App\Models\k12ConnectionMeetingLinks;
use App\Models\k12ConnectionPrograms;
use App\Models\k12ConnectionProgramsSchedule;
use App\Models\PlatformSchoolInvites;
use App\Models\PlatformSchoolProctor;
use App\Models\PlatformSchoolRequirements;
use App\Models\SchoolReviewApplicants;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\SubjectArea;
use App\Models\v1\SubjectBudget;
use App\OnboardingInstructor;
use App\RequestChangeOnboardingInstructorModel;
use App\SchoolInstructorHiring;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Services\CheckrService;
use App\StateModel;

function encrypt_str($id)
{
    return $encrypted = Crypt::encryptString($id);
}

function decrypt_str($id)
{
    return $decrypt = Crypt::decryptString($id);
}

function uploadsDir()
{
    return "uploads/";
}

function iconsDir()
{
    return "assets/svgimg/png/";
}

// ================getCities======================

function randomPassword()
{
    $alphabet =
        "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    $pass = []; //remember to declare $pass as an array
    $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
    for ($i = 0; $i < 10; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass); //turn the array into a string
}

function date_function($created_at)
{
    if (@$created_at) {
        $date = Date("M d Y h:i A", strtotime($created_at));
    }
    return @$date;
}

function get_auth()
{
    if (Session::get("Adminnewlogin")) {
        $auth = Session::get("Adminnewlogin");
    } else {
        $auth = Session::get("Clientnewlogin");
    }

    return (object) $auth;
}

function get_user()
{
    $auth = Session::get("Clientnewlogin");
    $user = App\Users::find($auth["id"]);
    return $user;
}
function text_limit($text)
{
    $new_text = Str::limit($text, 30);
    return $new_text;
}

function uplaod_image($request, $old_image = "")
{
    $old_image_path = "/uploads/program/" . $old_image;

    $file = $request->file("image");
    $filetype = $file->getClientOriginalExtension();

    $image_name = $request->pre_image_name;
    $dir = "/uploads/program/";

    $filename = $image_name . time() . uniqid() . "." . $filetype;
    $file->move($dir, $filename);

    if (!empty($old_image) && file_exists($old_image_path)) {
        unlink($old_image_path);
    }
    return $filename;
}


function default_user_placeholder($type = "")
{
    if ($type == "male") {
        return url("/") . "/uploads/default_img/placeholder-user.jpg";
    } elseif ($type == "female") {
        return url("/") . "/uploads/default_img/placeholder-female.png";
    } else {
        return url("/") . "/uploads/default_img/default_image.png";
    }
}

function add_history($row = [])
{
    $data = [];
    $data["admin_id"] = $row["admin_id"];
    $data["message"] = $row["message"];
    $data["created_at"] = date("Y-m-d H:i:s");
    $data["updated_at"] = date("Y-m-d H:i:s");
    DB::table("history")->insert($data);
}

function get_user_email($id)
{
    $user = App\Users::find($id);
    return $user;
}

function get_user_by_id($id)
{
    $user = App\Users::find($id);
    return $user;
}

function institudeName($id)
{
    $users = App\Users::where(["id" => $id])
        ->first();
    if (!empty($users)) {
        return $users->full_name;
    }
}

function getstaffname($id)
{
    $users = App\Users::where(["id" => $id])
        ->first();
    if (!empty($users)) {
        return $users->first_name . ' ' . $users->last_name;
    } else {
        return 'NIL';
    }
}

function getstaffemail($id)
{
    $users = App\Users::where(["id" => $id])
        ->first();
    if (!empty($users)) {
        return $users->email;
    } else {
        return 'NIL';
    }
}

function getprogramname($id)
{
    $res = Programs::where(["id" => $id])
        ->first();
    if (!empty($res)) {
        return $res->name;
    } else {
        return 'NIL';
    }
}

function getemailexits($email)
{
    $res =  App\Users::where(["email" => $email])
        ->first();
    if (!empty($res)) {
        return true;
    } else {
        return false;
    }
}

function profilestatus($id)
{
    $profile_status = DB::table("tbl_profile_status")
        ->where(["status_id" => $id])
        ->first();
    if (!empty($profile_status)) {
        return $profile_status->title;
    }
}

function get_permission($userid)
{
    $permission = DB::table("tbl_permissions")
        ->where(["user_id" => $userid])
        ->get();

    $response = [];
    foreach ($permission as $key => $value) {
        $response[$value->module] = $value->permission_setting;
    }

    return $response;
}

function rangeData($value)
{
    $va = explode("-", $value);

    $things = [
        "0-5" => ["0", "0", "0", "0", "0", "100", "0", "100"],
        "0-4" => ["0", "20", "0", "20", "0", "80", "0", "80"],
        "0-3" => ["0", "40", "0", "40", "0", "60", "0", "60"],
        "0-2" => ["0", "60", "0", "60", "0", "40", "0", "40"],
        "0-1" => ["0", "80", "0", "80", "0", "20", "0", "20"],
        "1-5" => ["20", "0", "20", "0", "20", "100", "20", "100"],
        "1-4" => ["20", "20", "20", "20", "20", "80", "20", "80"],
        "1-3" => ["20", "40", "20", "40", "20", "60", "20", "60"],
        "1-2" => ["20", "60", "20", "60", "20", "40", "20", "40"],
        "2-5" => ["40", "0", "40", "0", "40", "100", "40", "100"],
        "2-4" => ["40", "20", "40", "20", "40", "80", "40", "80"],
        "2-3" => ["40", "40", "40", "40", "40", "60", "40", "60"],
        "3-5" => ["60", "0", "60", "0", "60", "100", "60", "100"],
        "3-4" => ["60", "20", "60", "20", "60", "80", "60", "80"],
        "4-5" => ["80", "0", "80", "0", "80", "100", "80", "100"],
    ];
    if (array_key_exists($value, $things)) {
        $firstarray = $things[$value];

        $html =
            '<div inverse-left style="width:' .
            $firstarray[0] .
            '%;"></div>
        <div inverse-right style="width:' .
            $firstarray[1] .
            '%;"></div>
        <div range style="left:' .
            $firstarray[2] .
            "%;right:" .
            $firstarray[3] .
            '%;"></div>
        <span  style="left:' .
            $firstarray[4] .
            '%;"></span>
        <span thumb style="left:' .
            $firstarray[5] .
            '%;"></span>
        <div sign style="left:' .
            $firstarray[6] .
            '%;display:none;">
        <span id="value">' .
            $va[0] .
            '</span>
        </div>
        <div sign style="left:' .
            $firstarray[7] .
            '%;">
        <span id="value">' .
            $va[1] .
            '</span>
        </div>';
    } else {
        $html = '<div inverse-left style="width:70%;"></div>
        <div inverse-right style="width:70%;"></div>
        <div range style="left:0%;right:0%;"></div>
        <span  style="left:0%;"></span>
        <span thumb style="left:100%;"></span>
        <div sign style="left:0%;display:none;" class="pass-hints">
        <span id="value">0</span>
        </div>
        <div sign style="left:100%;">
        <span id="value">5</span>
        </div>';
    }

    return $html;
}
function rangeData1($value)
{
    $va = explode("-", $value);

    $things = [
        "0-5" => ["0", "0", "0", "0", "0", "100", "0", "100"],
        "0-4" => ["0", "20", "0", "20", "0", "80", "0", "80"],
        "0-3" => ["0", "40", "0", "40", "0", "60", "0", "60"],
        "0-2" => ["0", "60", "0", "60", "0", "40", "0", "40"],
        "0-1" => ["0", "80", "0", "80", "0", "20", "0", "20"],
        "1-5" => ["20", "0", "20", "0", "20", "100", "20", "100"],
        "1-4" => ["20", "20", "20", "20", "20", "80", "20", "80"],
        "1-3" => ["20", "40", "20", "40", "20", "60", "20", "60"],
        "1-2" => ["20", "60", "20", "60", "20", "40", "20", "40"],
        "2-5" => ["40", "0", "40", "0", "40", "100", "40", "100"],
        "2-4" => ["40", "20", "40", "20", "40", "80", "40", "80"],
        "2-3" => ["40", "40", "40", "40", "40", "60", "40", "60"],
        "3-5" => ["60", "0", "60", "0", "60", "100", "60", "100"],
        "3-4" => ["60", "20", "60", "20", "60", "80", "60", "80"],
        "4-5" => ["80", "0", "80", "0", "80", "100", "80", "100"],
    ];
    if (array_key_exists($value, $things)) {
        $firstarray = $things[$value];

        $html =
            '<div inverse-left style="width:' .
            $firstarray[0] .
            '%;"></div>
        <div inverse-right style="width:' .
            $firstarray[1] .
            '%;"></div>
        <div range style="left:' .
            $firstarray[2] .
            "%;right:" .
            $firstarray[3] .
            '%;background-color: #FC697D;"></div>
        <span  style="left:' .
            $firstarray[4] .
            '%;background-color: #FC697D;"></span>
        <span thumb style="left:' .
            $firstarray[5] .
            '%;background-color: #FC697D;"></span>
        <div sign style="left:' .
            $firstarray[6] .
            '%;background-color: #FC697D;display:none;" class="pass-hints">
        <span id="value">' .
            $va[0] .
            '</span>
        </div>
        <div sign style="left:' .
            $firstarray[7] .
            '%;background-color: #FC697D;">
        <span id="value">' .
            $va[1] .
            '</span>
        </div>';
    } else {
        $html = '<div inverse-left style="width:70%;"></div>
        <div inverse-right style="width:70%;"></div>
        <div range style="left:0%;right:0%;background-color: #FC697D;"></div>
        <span  style="left:0%;background-color: #FC697D;"></span>
        <span thumb style="left:100%;background-color: #FC697D;"></span>
        <div sign style="left:0%;background-color: #FC697D;display:none;">
        <span id="value">0</span>
        </div>
        <div sign style="left:100%;background-color: #FC697D;">
        <span id="value">5</span>
        </div>';
    }

    return $html;
}

function rangeData2($value)
{
    $va = explode("-", $value);

    $things = [
        "0-5" => ["0", "0", "0", "0", "0", "100", "0", "100"],
        "0-4" => ["0", "20", "0", "20", "0", "80", "0", "80"],
        "0-3" => ["0", "40", "0", "40", "0", "60", "0", "60"],
        "0-2" => ["0", "60", "0", "60", "0", "40", "0", "40"],
        "0-1" => ["0", "80", "0", "80", "0", "20", "0", "20"],
        "1-5" => ["20", "0", "20", "0", "20", "100", "20", "100"],
        "1-4" => ["20", "20", "20", "20", "20", "80", "20", "80"],
        "1-3" => ["20", "40", "20", "40", "20", "60", "20", "60"],
        "1-2" => ["20", "60", "20", "60", "20", "40", "20", "40"],
        "2-5" => ["40", "0", "40", "0", "40", "100", "40", "100"],
        "2-4" => ["40", "20", "40", "20", "40", "80", "40", "80"],
        "2-3" => ["40", "40", "40", "40", "40", "60", "40", "60"],
        "3-5" => ["60", "0", "60", "0", "60", "100", "60", "100"],
        "3-4" => ["60", "20", "60", "20", "60", "80", "60", "80"],
        "4-5" => ["80", "0", "80", "0", "80", "100", "80", "100"],
    ];
    if (array_key_exists($value, $things)) {
        $firstarray = $things[$value];

        $html =
            '<div inverse-left style="width:' .
            $firstarray[0] .
            '%;"></div>
        <div inverse-right style="width:' .
            $firstarray[1] .
            '%;"></div>
        <div range style="left:' .
            $firstarray[2] .
            "%;right:" .
            $firstarray[3] .
            '%;background-color:#FC9F43;"></div>
        <span  style="left:' .
            $firstarray[4] .
            '%;background-color:#FC9F43;"></span>
        <span thumb style="left:' .
            $firstarray[5] .
            '%;background-color:#FC9F43;"></span>
        <div sign style="left:' .
            $firstarray[6] .
            '%;background-color:#FC9F43;display:none;" class="pass-hints">
        <span id="value">' .
            $va[0] .
            '</span>
        </div>
        <div sign style="left:' .
            $firstarray[7] .
            '%;background-color:#FC9F43;">
        <span id="value">' .
            $va[1] .
            '</span>
        </div>';
    } else {
        $html = '<div inverse-left style="width:70%;"></div>
        <div inverse-right style="width:70%;"></div>
        <div range style="left:0%;right:0%;background-color:#FC9F43;"></div>
        <span  style="left:0%;background-color:#FC9F43;"></span>
        <span thumb style="left:100%;background-color:#FC9F43;"></span>
        <div sign style="left:0%;background-color:#FC9F43;display:none;">
        <span id="value">0</span>
        </div>
        <div sign style="left:100%;background-color:#FC9F43;">
        <span id="value">5</span>
        </div>';
    }

    return $html;
}

function rangeData3($value)
{
    $va = explode("-", $value);

    $things = [
        "0-5" => ["0", "0", "0", "0", "0", "100", "0", "100"],
        "0-4" => ["0", "20", "0", "20", "0", "80", "0", "80"],
        "0-3" => ["0", "40", "0", "40", "0", "60", "0", "60"],
        "0-2" => ["0", "60", "0", "60", "0", "40", "0", "40"],
        "0-1" => ["0", "80", "0", "80", "0", "20", "0", "20"],
        "1-5" => ["20", "0", "20", "0", "20", "100", "20", "100"],
        "1-4" => ["20", "20", "20", "20", "20", "80", "20", "80"],
        "1-3" => ["20", "40", "20", "40", "20", "60", "20", "60"],
        "1-2" => ["20", "60", "20", "60", "20", "40", "20", "40"],
        "2-5" => ["40", "0", "40", "0", "40", "100", "40", "100"],
        "2-4" => ["40", "20", "40", "20", "40", "80", "40", "80"],
        "2-3" => ["40", "40", "40", "40", "40", "60", "40", "60"],
        "3-5" => ["60", "0", "60", "0", "60", "100", "60", "100"],
        "3-4" => ["60", "20", "60", "20", "60", "80", "60", "80"],
        "4-5" => ["80", "0", "80", "0", "80", "100", "80", "100"],
    ];
    if (array_key_exists($value, $things)) {
        $firstarray = $things[$value];

        $html =
            '<div inverse-left style="width:' .
            $firstarray[0] .
            '%;"></div>
        <div inverse-right style="width:' .
            $firstarray[1] .
            '%;"></div>
        <div range style="left:' .
            $firstarray[2] .
            "%;right:" .
            $firstarray[3] .
            '%;background-color:#7EC7A9;"></div>
        <span  style="left:' .
            $firstarray[4] .
            '%;background-color:#7EC7A9;"></span>
        <span thumb style="left:' .
            $firstarray[5] .
            '%;background-color:#7EC7A9;"></span>
        <div sign style="left:' .
            $firstarray[6] .
            '%;background-color:#7EC7A9;display:none;" class="pass-hints">
        <span id="value">' .
            $va[0] .
            '</span>
        </div>
        <div sign style="left:' .
            $firstarray[7] .
            '%;background-color:#7EC7A9;">
        <span id="value">' .
            $va[1] .
            '</span>
        </div>';
    } else {
        $html = '<div inverse-left style="width:70%;"></div>
        <div inverse-right style="width:70%;"></div>
        <div range style="left:0%;right:0%;background-color:#7EC7A9;"></div>
        <span  style="left:0%;background-color:#7EC7A9;"></span>
        <span thumb style="left:100%;background-color:#7EC7A9;"></span>
        <div sign style="left:0%;background-color:#7EC7A9;display:none;">
        <span id="value">0</span>
        </div>
        <div sign style="left:100%;background-color:#7EC7A9;">
        <span id="value">5</span>
        </div>';
    }

    return $html;
}

function completiondata($id)
{
    $result = UponCompletionModel::where(["back_med_id" => $id])->first();
    if ($result) {
        return true;
    } else {
        return false;
    }
}

function get_timeslot($date, $user_id)
{
    $timeslot = DB::table("tbl_schedule_interviews")
        ->where(["date" => $date, "user_id" => $user_id])
        ->orderBy("time", "asc")
        ->groupBy("time")
        ->get();

    return $timeslot;
}

function gradeLevel($id)
{
    $class = Classes::whereIn("id", explode(",", $id))->get();
    $data = "";
    if (!empty($class)) {
        foreach ($class as $rows) {
            $data .= $rows->class_name . ",";
        }
    }
    return $data;
}

function viewclassroom($id)
{
    $viewtraining = ViewClassroomModel::where([
        "user_id" => Auth::user()->id,
        "classroom_video_id" => $id,
    ])->get();

    if (count($viewtraining) > 0) {
        return true;
    } else {
        return false;
    }
}

function get_usertimeslot($id, $user_id)
{
    $timeslot = DB::table("tbl_user_interview_slots")
        ->where(["slot_id" => $id, "user_id" => $user_id])
        ->first();
    if ($timeslot) {
        return true;
    } else {
        return false;
    }
}

function username($id)
{
    $user = App\Users::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->first_name . " " . $user->last_name;
    }
}

function schoolusername($id)
{
    $user = App\Users::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->full_name;
    } else {
        return '';
    }
}

function subjectName($id)
{
    $subject = App\Subject::where(["id" => $id])
        ->first();

    if (!empty($subject)) {
        return $subject->subject_name;
    } else {
        return '';
    }
}

function v1SubjectName($id)
{
    $subject = SubjectArea::where(["id" => $id])
        ->first();

    if (!empty($subject)) {
        return $subject->subject_area;
    } else {
        return '';
    }
}

function v1SubjectId($id)
{
    $sub_subject = V1Subject::find($id);

    if (!empty($sub_subject)) {
        return $sub_subject->subject_area_id;
    } else {
        return '';
    }
}

function location($name)
{
    $apiKey = env('MAP_KEY');

    $url =
        "https://maps.googleapis.com/maps/api/geocode/json?address=" .
        $name .
        "&key=" .
        $apiKey;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_URL, $url);
    $result = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($result, true);

    $components = $data["results"][0]["address_components"];

    // filter the address_components field for type : $type
    function filter($components, $type)
    {
        return array_filter($components, function ($component) use ($type) {
            return array_filter($component["types"], function ($data) use (
                $type
            ) {
                return $data == $type;
            });
        });
    }

    return $components;
}

function getformdetails($id)
{

    $form = document_form::where(["id" => $id])->first();


    return $form;
}


function subname($id)
{
    $subname = Subject::where(['id' => $id])->first();
    if ($subname) {
        return $subname->subject_name;
    } else {
        return '';
    }
}

function subjectDetails($id)
{
    $subname = Subject::where(['id' => $id])->first();
    if ($subname) {
        return $subname;
    } else {
        return '';
    }
}

function subsubject($id)
{
    $subname = SubsubjectModel::orderBy("name", "asc")->where(['subject_id' => $id])->get();
    return $subname;
}

function v1SubSubject($id)
{
    $subname = V1Subject::orderBy("title", "asc")->where(['subject_area_id' => $id])->get();
    return $subname;
}

function v1SubSubjectname($id)
{
    $subname = V1Subject::where(['id' => $id])->first();
    if (!empty($subname)) {
        return $subname->title;
    }
}

function subsubjectname($id)
{
    $subname = SubsubjectModel::where(['id' => $id])->first();
    if (!empty($subname)) {
        return $subname->name;
    }
}

function getstatus($id, $status)
{
    $val = DB::table("tbl_profile_status_histories")->where(['user_id' => $id, 'status' => $status])->first();
    if ($val) {
        return false;
    } else {
        return true;
    }
}


function getAllSubsubject($subject_id)
{
    // logger()->info($subject_id);
    $subsubjects = SubsubjectModel::where('subject_id',  $subject_id)->get();
    return $subsubjects;
}

function getAllV1Subsubject($subject_id)
{
    // logger()->info($subject_id);
    $subsubjects = V1Subject::where('subject_area_id',  $subject_id)->get();
    return $subsubjects;
}

function getDistrictname($id)
{
    $dname = District::where(['id' => $id])->first();
    if (!empty($dname)) {
        return $dname->name;
    } else {
        return '';
    }
}

function getOwnerName($id, $type, $needId = false)
{
    $ownname = invite_program_owners::where(['program_id' => $id, 'owner_type' => $type])
        ->join("users as u", "tbl_invite_program_owners.user_id", "=", "u.id")
        ->where(['tbl_invite_program_owners.status' => 1])
        ->first();
    if (!empty($ownname)) {
        if ($needId) {
            $name = $ownname->first_name . ' ' . $ownname->last_name;
            $id = $ownname->id;
            return [$name, $id];
        }
        return $ownname->first_name . ' ' . $ownname->last_name;
    } else {
        return 'NIL';
    }
}

function getOwnerDeatils($id, $type)
{
    $owner = invite_program_owners::where(['program_id' => $id, 'owner_type' => $type])
        ->join("users as u", "tbl_invite_program_owners.user_id", "=", "u.id")
        ->where(['tbl_invite_program_owners.status' => 1])
        ->first();
    return $owner;
}


function assessments($type, $id)
{
    $ass = AssessmentsModel::where(['type' => $type, 'user_id' => $id])->get();
    return $ass;
}
function authaminstr($id)
{
    return AdministrativeAuthorizationModel::where(['admin_auth_id' => $id])->get();
}


function checkAuth($status)
{

    if ($status == 0 || $status == 2) {
        return true;
    } else {
        return false;
    }
}



function getScheduleHtml($schedulesDates)
{
    $html = "";

    if ($schedulesDates->isNotEmpty()) {

        foreach ($schedulesDates as $schedule) {
            $dayName = getDayAbbreviation($schedule->day);

            $startTime = date("h:i A", strtotime($schedule->start_time));
            $endTime = date("h:i A", strtotime($schedule->end_time));

            $html .= "<a class='d-block list'>{$dayName}: {$startTime} - {$endTime}</a>";
        }
    }

    return $html;
}


function formatTimeForTimezone($time, $timezone)
{

    try {
        $utcTime = Carbon::createFromFormat('h:i a', $time, $timezone)
            ->setTimezone('America/Los_Angeles')
            ->format('H:i:s');
    } catch (\Exception $e) {
        // If parsing with seconds fails, return the original time
        return $time;
    }

    return $utcTime;
}
function formatTimeAdminTimezone($time, $timezone)
{

    try {

        $utcTime = Carbon::createFromFormat('h:i a', $time, $timezone)
            ->setTimezone('America/Los_Angeles')
            ->format('H:i:s');

        /*         $utcTime = Carbon::parse($time)
            ->format('H:i:s'); */
    } catch (\Exception $e) {
        // If parsing with seconds fails, return the original time
        return $time;
    }

    return $utcTime;
}
function getClassScheduleHtml($schedulesDates)
{
    $html = "";
    $dayNames = [];
    if ($schedulesDates->isNotEmpty()) {
        $i = 0;
        foreach ($schedulesDates as $schedule) {
            if ($i >= 7) {
                break;
            }

            $i++;
            $dayName = getDayAbbreviation($schedule->day);
            if (
                in_array($dayName, $dayNames)
            ) {
                continue;
            }
            $dayNames[] = $dayNameLetter = $dayName;

            $startTime = date("h:i A", strtotime($schedule->start_time));
            $endTime = date("h:i A", strtotime($schedule->end_time));

            $html .= "<div class='d-block list'>{$dayNameLetter}: {$startTime} - {$endTime}</div>";
        }
    }

    return $html;
}

function getClassScheduleHtmlDetails($schedulesDates)
{
    $html = "";
    $dayNames = [];
    if ($schedulesDates->isNotEmpty()) {
        $i = 0;
        foreach ($schedulesDates as $schedule) {
            if ($i >= 7) {
                break;
            }

            $i++;
            $dayName = getDayAbbreviation($schedule->day);
            if (
                in_array($dayName, $dayNames)
            ) {
                continue;
            }
            $dayNames[] = $dayNameLetter = $dayName;

            $startTime = date("h:i A", strtotime($schedule->start_time));
            $endTime = date("h:i A", strtotime($schedule->end_time));

            $html .= "<h6 class='timezone-align' style='font-size: 16px;'>{$dayNameLetter}: {$startTime} - {$endTime}</h6>";
        }
    }

    return $html;
}

function createNotification($data)
{
    notification::insertGetId($data);
}

function generateSignedUrl($filename)
{
    $s3 = new S3Client([
        'version' => 'latest',
        'region' => config('filesystems.disks.s3.region'),
        'credentials' => [
            'key'    => config('filesystems.disks.s3.key'),
            'secret' => config('filesystems.disks.s3.secret'),
        ],
    ]);

    $bucket = config('filesystems.disks.s3.bucket');
    $key =  $filename; // Replace with your actual S3 key
    if ($key) {


        // Set the expiration time for the signed URL (in seconds)
        $expires = 600000; // Adjust as needed

        // Generate the signed URL
        $cmd = $s3->getCommand('GetObject', [
            'Bucket' => $bucket,
            'Key'    => $key,
        ]);

        $request = $s3->createPresignedRequest($cmd, "+{$expires} seconds");

        // Get the signed URL
        $signedUrl = (string)$request->getUri();

        return $signedUrl;
    } else {
        return url('website/img/pre.png');
    }
}

function uploads3image($filename, $image)
{
    Storage::disk('s3')->put($filename, file_get_contents($image), '/uploads/');
    return true;
}

function userimg($id)
{
    $user = App\Users::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->image;
    }
}

function useremail($id)
{
    $user = App\Users::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->email;
    }
}

function getDaysInRange($startingDate, $endingDate, $limit = 7)
{
    $datesArray = [];
    $counter = 0;

    $startingDate = strtotime($startingDate);
    $endingDate = strtotime($endingDate);

    for ($currentDate = $startingDate; $currentDate <= $endingDate; $currentDate += (86400)) {
        $date = date('l', $currentDate);

        // Increment the counter
        $counter++;

        // Skip adding Sunday to the array
        /*         if ($date === "Sunday") {
            continue;
        } */

        $datesArray[$date] = $date;


        if ($counter >= $limit) {
            break;
        }
    }

    return $datesArray;
}




function keysname($days)
{
    $indexedArray = array(
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    );
    $keys = range(1, count($indexedArray));

    // Combine keys and values to create the final array
    $arrayStartingFrom1 = array_combine($keys, $indexedArray);
    $key = array_search($days, $arrayStartingFrom1);
    return  $key;

}


function getAppRecName($id, $type)
{
    $ownname = invite_application_recruiter::where(['tbl_invite_application_recruiters.application_id' => $id, 'tbl_invite_application_recruiters.type' => $type])
        ->join("users as u", "tbl_invite_application_recruiters.user_id", "=", "u.id")
        ->where(['tbl_invite_application_recruiters.status' => 1])
        ->first();
    if (!empty($ownname)) {
        return $ownname->first_name . ' ' . $ownname->last_name;
    } else {
        return 'NIL';
    }
}

function getDayName($dayNumber)
{
    $days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    return $days[$dayNumber - 1] ?? '';
}

function getDayAbbreviation($dayNumber)
{
    $abbreviations = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

    return $abbreviations[$dayNumber - 1] ?? null;
}
/**
 * Get Approved Array.
 *
 * @param string $delivery_type
 * @return array
 */

function getApprovedArray($delivery_type)
{
    $is_approved = [];

    if (is_array($delivery_type)) {
        if (in_array('Online', $delivery_type)) {
            $is_approved[] = 16;
        }

        if (in_array('In-Person', $delivery_type)) {
            $is_approved[] = 17;
        }

        if (in_array('In-Person', $delivery_type) && in_array('Online', $delivery_type)) {
            $is_approved[] = 20;
        }
    } else {
        if ('Online' == $delivery_type) {
            $is_approved[] = 16;
        } elseif ('In-Person' == $delivery_type) {
            $is_approved[] = 17;
        }
        $is_approved[] = 20;
    }




    return $is_approved;
}

function getUserDeliveryType($is_approved)
{
    $delivery_type = null;

    if ('16' == $is_approved) {
        $delivery_type = 'Online';
    } elseif ('17' == $is_approved) {
        $delivery_type = 'In-Person';
    } elseif ('20' == $is_approved) {
        $delivery_type = ['Online', 'In-Person'];
    }

    return $delivery_type;
}
/**
 * Get the string representation of an Instructor Type.
 *
 * @param int $type The numeric representation of the Instructor Type (0 for Sub, 1 for Main).
 *
 * @return string Returns 'Sub' if $type is 0, 'Main' if $type is 1, or '' otherwise.
 */
function getInstructorType($type): string
{
    $types = [
        0 => 'Sub',
        1 => 'Main',
        2 => 'StandBy',
    ];

    return $types[$type] ?? '';
}



function generateEventsForDays($event, DateTime $start_date, DateTime $end_date): array
{
    $eventsForDays = [];


    while ($start_date <= $end_date) {

        $day = $start_date->format('N');
        $dateSchedule = $event->dateSchedule($day);
        $eventsForDays[] = [
            'title' => $event->name,
            'start' => $start_date->format('Y-m-d'),
            'url' => route('user.program-detail', ['encryptedId' => encrypt($event->id)]),
            'description' => $event->address,
            'timeSlot' => date('h:i A', strtotime($dateSchedule->value('start_time'))) . ' - ' .
                date('h:i A', strtotime($dateSchedule->value('end_time'))),
        ];

        $start_date->modify('+1 day');
    }

    return $eventsForDays;
}



function generateHourlyPaymentData(User $user, Programs $program, $day, $start_time = null, $end_time = null)
{
    if (!$start_time || !$end_time) {
        $dateSchedule = $program->dateSchedule($day)->first();

        if (!$dateSchedule) {
            return null;
        }

        $start_time = $dateSchedule->start_time;
        $end_time = $dateSchedule->end_time;
    }

    $startDateTime = Carbon::parse($start_time);
    $endDateTime = Carbon::parse($end_time);

    $timeDifference = $startDateTime->diff($endDateTime);
    $hours = $timeDifference->h;
    $minutes = $timeDifference->i;

    $delivery_type = $program->delivery_type;
    $hourlyRate = ($delivery_type == 'In-Person') ? $user->inpersonrate : $user->onlinerate;
    // if ($delivery_type == 'In-Person') {
    //     $hourlyRate = $user->inpersonrate;
    // } elseif ($delivery_type == 'Online') {
    //     $hourlyRate = $user->onlinerate;
    // }
    $amount = number_format(($hours + ($minutes / 60)) * $hourlyRate, 2, '.', '');

    return [
        'start_time' => $start_time,
        'end_time' => $end_time,
        'hours' => $hours,
        'minutes' => $minutes,
        'rate' => $hourlyRate,
        'format' => $delivery_type,
        'amount' => $amount,
    ];
}

function getMeetingTimeHtml($meeting = null)
{
    $html = "";

    if ($meeting) {

        $dayName = date("m-d-Y", strtotime($meeting->start_time));
        $startTime = date("g:iA", strtotime($meeting->start_time));
        $endTime = date("g:iA", strtotime($meeting->end_time));

        $html .= "<a class='d-block list'>{$dayName}</a>";
        $html .= "<a class='d-block list'>{$startTime}-{$endTime}</a>";
    }

    return $html;
}
function getReimbursementStatusTitle($status)
{
    switch ($status) {
        case null:
            $title = 'Pending';
            break;
        case 1:
            $title = 'Approved';
            break;
        case 3:
            $title = 'Paid';
            break;
        default:
            $title = 'Pending';
            break;
    }
    return $title;
}

function getAdminUserProgramIds()
{
    $adminSession = session()->get('Adminnewlogin');
    $adminType = $adminSession['type'];
    $adminId = $adminSession['id'];

    $whereInIds = [];

    if ($adminType != '1') {
        $admin = User::find($adminId);

        // abort_if(!$admin, 404, 'User not found');

        if ($adminType == '4') {
            $whereInIds = optional($admin)->recruitedPrograms->pluck('id')->toArray() ?? [];
        }
        if ($adminType == '9') {
            $whereInIds = Arr::flatten([$whereInIds, optional($admin)->createdPrograms->pluck('id')->toArray() ?? []]);
        }
        if ($adminType == '10') {

            if ($admin->regions) {
                $regions = explode(',', $admin->regions);
                $programId = Programs::whereIn('state', $regions)
                    ->pluck('id');
                $whereInIds = Arr::flatten([$whereInIds, $programId ?? []]);
            } else {
                $whereInIds = Arr::flatten([$whereInIds, optional($admin)->createdPrograms->pluck('id')->toArray() ?? []]);
            }
        }
    }

    return $whereInIds;
}


if (!function_exists('modelHasBeenEdited')) {
    /**
     * Check if a model has been edited.
     *
     * @param \Illuminate\Database\Eloquent\Model $model
     * @return bool
     */
    function modelHasBeenEdited(Model $model)
    {
        return $model->created_at != $model->updated_at;
    }
}

function getClassScheduleHtmladmin($schedulesDates)
{
    $html = "";

    // Make sure $schedulesDates contains unique days
    $schedulesDates = $schedulesDates->unique('day');

    if ($schedulesDates->isNotEmpty()) {
        $i = 0;

        foreach ($schedulesDates as $schedule) {
            if ($i >= 7) {
                break;
            }
            $i++;

            $dayNameLetter = getDayAbbreviation($schedule->day);
            $startTime = date("h:i A", strtotime($schedule->start_time));
            $endTime = date("h:i A", strtotime($schedule->end_time));

            $html .= "<a class='d-block list'>{$dayNameLetter}: {$startTime} - {$endTime}</a>";
        }
    }

    return $html;
}


function get_childpermission($res, $mod, $type)
{

    if (isset($res[$mod])) :
        if (array_key_exists($mod, $res)) :
            if (in_array($type, json_decode($res[$mod], true))) :

                return true;

            endif;
        endif;
    endif;

    return false;
}
function formatTime($time)
{
    return date('H:i:s', strtotime($time));
}

function checkActiveDateRange($range, $deliveryType, $oldCids = [])
{

    $user = auth()->user();
    $user->load('activeClasses.program', 'activeSubClasses.program');



    [$minDate, $maxDate] = getMinMaxDate($user->activeClasses(), $oldCids, $range, $deliveryType);
    [$minSubDate, $maxSubDate] = getMinMaxDate($user->activeSubClasses(), $oldCids, $range, $deliveryType);

    $cIds = getProgramNoteIds($user->activeClasses(), $oldCids, $range, $deliveryType);
    $subCIds = getProgramNoteIds($user->activeSubClasses(), $oldCids, $range, $deliveryType);

    $newIds = array_merge($cIds, $subCIds);


    if (empty($newIds)) {
        return [null, null, null, null, $oldCids];
    }
    // $uniqueIds = array_unique(array_merge($newIds, $oldCids));
    $uniqueIds = $newIds;
    if (is_null($minDate) || is_null($minSubDate)) {
        $minmumDate = max($minDate, $minSubDate);
    } else {
        $minmumDate = min($minDate, $minSubDate);
    }

    if (!is_null($maxDate) && !is_null($maxSubDate)) {

        $maxmumDate = max($maxDate, $maxSubDate->toDateString());
    } elseif (is_null($maxDate)) {


        $maxmumDate = $maxSubDate;
    } elseif (is_null($maxSubDate)) {
        $maxmumDate = $maxDate;
    }


    return [
        $minmumDate ? date('m/d/Y', strtotime($minmumDate)) : null,
        $maxmumDate ? date('m/d/Y', strtotime($maxmumDate)) : null,
        'active-program-class min-date',
        'active-program-class max-date',
        $uniqueIds,
    ];
}

function getMinMaxDate($query, $oldCids, $range, $deliveryType)
{


    $qry = optional($query);
    if (!empty($oldCids)) {

        $qry->whereNotIn('program_notes.id', $oldCids);
    }


    if (!empty($deliveryType)) {

        $qry->whereHas('program', function ($qry) use ($deliveryType) {
            $qry->where('delivery_type', $deliveryType);
        });
    }
    if (!empty($range->from_date_raw)) {

        $qry->where('class_date', '>=', $range->from_date_raw);
    }

    if (!empty($range->to_date_raw)) {

        $qry->where('class_date', '<=', $range->to_date_raw);
    }

    $dates = $qry->pluck('class_date')
        ->toArray();

    return count($dates) > 0 ? [min($dates), max($dates)] : [null, null];
}



function getProgramNoteIds($query, $oldCids, $range, $deliveryType)
{

    $qry = optional($query);


    if (!empty($oldCids)) {

        $qry->whereNotIn('program_notes.id', $oldCids);
    }

    if (!empty($deliveryType)) {

        $qry->whereHas('program', function ($qry) use ($deliveryType) {
            $qry->where('delivery_type', $deliveryType);
        });
    }

    if (!empty($range->from_date_raw)) {

        $qry->where('class_date', '>=', $range->from_date_raw);
    }

    if (!empty($range->to_date_raw)) {

        $qry->where('class_date', '<=', $range->to_date_raw);
    }


    if (!empty($range->timeSlots)) {
        $timeSlots = $range->timeSlots;

        $qry->where(function ($qry) use ($timeSlots) {
            foreach ($timeSlots as $timeSlot) {
                if (!$timeSlot->from_time_utc || !$timeSlot->to_time_utc || !$timeSlot->day) {
                    continue;
                }


            }
        });
    }

    return $qry->pluck('program_notes.id')
        ->toArray();
}
function getProgramIds($query, $oldCids, $range, $deliveryType)
{

    $qry = optional($query);


    if (!empty($oldCids)) {

        $qry->whereNotIn('program_notes.id', $oldCids);
    }

    if (!empty($deliveryType)) {

        $qry->whereHas('program', function ($qry) use ($deliveryType) {
            $qry->where('delivery_type', $deliveryType);
        });
    }

    if (!empty($range->from_date_raw)) {

        $qry->where('class_date', '>=', $range->from_date_raw);
    }

    if (!empty($range->to_date_raw)) {

        $qry->where('class_date', '<=', $range->to_date_raw);
    }


    if (!empty($range->timeSlots)) {
        $timeSlots = $range->timeSlots;
        $qry->where(function ($qry) use ($timeSlots) {
            foreach ($timeSlots as $timeSlot) {
                if (!$timeSlot->from_time_utc || !$timeSlot->to_time_utc || !$timeSlot->day) {
                    continue;
                }
                $qry->orWhere(function ($qry) use ($timeSlot) {

                    if ($timeSlot->from_time_utc > $timeSlot->to_time_utc) {
                        $qry->where('end_time', '>=', $timeSlot->to_time_utc)
                            ->where('start_time', '>=', $timeSlot->from_time_utc)
                            ->where('day', $timeSlot->day);
                    } else {
                        $qry->where('start_time', '>=', $timeSlot->from_time_utc)
                            ->where('end_time', '<=', $timeSlot->to_time_utc)
                            ->where('day', $timeSlot->day);
                    }
                });
            }
        });
    }

    return $qry->pluck('program_notes.id')
        ->toArray();
}

function checkActiveWeekDay($oldDateIds = [], $slot, $oldClassIds = [])
{



    $user = auth()->user();
    $availability = $user->availability;

    $day = $slot->day;
    $start_time = $slot->from_time_utc;
    $end_time = $slot->to_time_utc;


    $qry = ProgramNote::query();

    if (!empty($day)) {

        $qry->where('day', $day);
    }

    $qry->where(function ($q) use ($user) {
        $q->where('user_id', $user->id);
        $q->orWhere('sub_user_id', $user->id);
    });

    if (!empty($oldDateIds)) {

        $qry->whereIn('id', $oldDateIds);
    } else {


        return [null, null, $oldClassIds];
    }

    if (!empty($oldClassIds)) {

        $qry->whereNotIn('id', $oldClassIds);
    }


    if ($start_time > $end_time) {
        if (!empty($start_time)) {

            $qry->where('start_time', '>=', $start_time);
        }

        if (!empty($end_time)) {

            $qry->where('end_time', '>=', $end_time);
        }
    } else {

        if (!empty($start_time)) {

            $qry->where('start_time', '>=', $start_time);
        }

        if (!empty($end_time)) {

            $qry->where('end_time', '<=', $end_time);
        }
    }



    $classes = $qry->select('program_notes.id', 'start_time', 'end_time')
        ->get();

    if ($classes->isNotEmpty()) {
        $ids =  $classes->pluck('id')->toArray();
        $minTime = $classes->min('start_time');
        $maxTime = $classes->max('end_time');
        $uniqueIds = $ids;
        return [$minTime, $maxTime, $uniqueIds];
    }

    return [null, null, $oldClassIds];
}

function checkLocationAv($location, $oldIds = [], $step)
{
    $user = auth()->user();
    $availabilityId = $user->availability->id;
    $user->load('activeClasses', 'activeSubClasses');

    $activePrograms = $user->activeClasses->merge($user->activeSubClasses);
    $uniqueProgramIds = $activePrograms->pluck('program_id')->unique()->toArray();


    if (empty($uniqueProgramIds)) {
        return [];
    }

    $qry = Programs::query();
    if (!empty($uniqueProgramIds)) {

        $qry->whereIn('id', $uniqueProgramIds);
    }

    if (!empty($oldIds)) {

        $qry->whereNotIn('id', $oldIds);
    }

    $qry->where('delivery_type', 'In-Person')
        ->where(function ($query) use ($location) {
            $lat = $location->lat;
            $lng = $location->lng;
            $radius = $location->radius;

            $query->whereRaw('(6371 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) < ?', [$lat, $lng, $lat, $radius]);
        });

    if ($availabilityId) {
        $ranges = AvailabilityRangeModel::where([
            'availability_id' => $availabilityId,
            'type' => 'inperson',
            'step' => $step,
        ])->get();
    }

    if ($ranges && !empty($ranges)) {
        foreach ($ranges as  $range) {

            $qry->whereHas('userNotes', function ($query) use ($range) {
                if (!empty($range->from_date_raw)) {

                    $query->where('class_date', '>=', $range->from_date_raw);
                }
                if (!empty($range->to_date_raw)) {

                    $query->where('class_date', '<=', $range->to_date_raw);
                }

                if (!empty($range->timeSlots)) {
                    $timeSlots = $range->timeSlots;

                    $query->where(function ($qry) use ($timeSlots) {

                        foreach ($timeSlots as $timeSlot) {
                            if (!$timeSlot->from_time_utc || !$timeSlot->to_time_utc || !$timeSlot->day) {
                                continue;
                            }
                            $qry->orWhere(function ($qry) use ($timeSlot) {


                                if ($timeSlot->from_time_utc > $timeSlot->to_time_utc) {
                                    $qry->where('end_time', '>=', $timeSlot->to_time_utc)
                                        ->where('start_time', '>=', $timeSlot->from_time_utc)
                                        ->where('day', $timeSlot->day);
                                } else {
                                    $qry->where('start_time', '>=', $timeSlot->from_time_utc)
                                        ->where('end_time', '<=', $timeSlot->to_time_utc)
                                        ->where('day', $timeSlot->day);;
                                }
                            });
                        }
                    });
                }
            });
        }
    }
    $ids = $qry->pluck('id')->toArray();

    return $ids;
}





function convertToUtcTime($time, $timezone = 'UTC')
{

    if ($timezone == 'UTC') {
        return $time;
    }
    try {
        $utcTime = Carbon::createFromFormat('h:i:s', $time, $timezone)
            ->setTimezone('UTC')
            ->format('H:i:s');
    } catch (\Exception $e) {
        // If parsing with seconds fails, return the original time
        return $time;
    }

    return $utcTime;
}

function totalmarkstudent($classId)
{
    // dd($classId);  7265
    $rowscount = ProgramNoteStudent::where("program_note_id", "=", $classId)->get();
    return count($rowscount);
}

/**
 * Get the string representation of an Invite Status.
 *
 * @param int $type The numeric representation of the Invite Status .
 *
 * @return string Returns 'Invite Status string'
 */
function getInviteStatus($status): string
{
    $statuses = [
        1 => 'Accepted',
        0 => 'Declined',
        2 => 'Archived',
        3 => 'Paid',
        '' => 'Pending',
    ];

    return $statuses[$status] ?? '';
}


function getReimbursementStatus($status): string
{
    $statuses = [
        1 => 'Accepted',
        0 => 'Declined',
        3 => 'Paid',
        '' => 'Pending',
    ];

    return $statuses[$status] ?? '';
}
function getstandbyuser($programId)
{
    $res = invite_programs::where([
        'program_id' => $programId,
        'status' => 1,
        'is_standby' => 1,
    ])->pluck('user_id')->toArray();
    if (!empty($res)) {
        $currentDate = now()->toDateString();

        $sub_userIds = ProgramNote::where('class_date', '>=', $currentDate)
            ->whereIn('user_id', $res)
            ->where('program_id', $programId)
            ->pluck('user_id')->toArray();
        if (!empty($sub_userIds)) {
            return false;
        } else {
            return true;
        }
    } else {
        return false;
    }
}

function getstandbyusername($programId)
{
    $res = invite_programs::join('users','users.id','tbl_invite_programs.user_id')->where([
                        'tbl_invite_programs.program_id' => $programId,
                        'tbl_invite_programs.status' => 1,
                        'tbl_invite_programs.is_standby' => 1,
                    ])->select(DB::raw("CONCAT(users.first_name, ' ', users.last_name) as full_name")) // Concatenate first and last names
                    ->pluck('full_name') // Pluck the concatenated 'full_name'
                    ->implode(', '); // Join names with a comma and space

    if (!empty($res)) {
        return $res;
    }else {
                return '';
            }
}




if (!function_exists('getUserTimestamp')) {
    function getUserTimestamp($value, $format = 'm-d-Y h:i A')
    {
        if (!$value) {
            return "";
        }
        $userTimezone = session('user_timezone') ?? config('app.timezone');

        return $value ? Carbon::parse($value)->timezone($userTimezone)->format($format) : null;
    }
}

if (!function_exists('getUserDate')) {
    function getUserDate($value, $format = 'm-d-Y')
    {
        if (!$value) {
            return "";
        }
        $userTimezone = session('user_timezone') ?? config('app.timezone');

        return $value ? Carbon::parse($value)->timezone($userTimezone)->format($format) : null;
    }
}

if (!function_exists('getAdminTimestamp')) {
    function getAdminTimestamp($value, $format = 'm-d-Y h:i A')
    {
        if (!$value) {
            return "";
        }
        $adminTimezone = session('admin_timezone') ?? config('app.timezone');

        return $value ? Carbon::parse($value)->timezone($adminTimezone)->format($format) : null;
    }
}



function notificationunread()
{

    $adminSession = session()->get('Adminnewlogin');
    $adminType = $adminSession['type'];

    if ($adminType == 1) {
        $notification = notification::where('user_type', 'Admin')->where('is_read', 0)->orderBy("id", "desc")->get();
        return  $notification;
    } elseif ($adminType == 3) {

        $notification = notification::where('user_type', 'Reviewer')->where('is_read', 0)->where('user_id', session()->get('Adminnewlogin')['id'])->orderBy("id", "desc")->limit(4)->get();
        return  $notification;
    } else {
        $notification = notification::where('is_read', 0)->where('user_id', $adminSession['id'])->orderBy("id", "desc")->limit(4)->get();
        return  $notification;
    }
}

function createCommanNotification($data, $tempId, $type, $user_type, $noti_type)
{

    $template = Notification_content::where("id", $tempId)->first();
    $body =  @$template->content;

    if ($noti_type == 'user') {
        $name = $data->first_name . ' ' . $data->last_name;
        $id = Crypt::encryptString($data->id);
        $link = url('view-application/step1/' . $id);
        $body = str_replace('{{name}}', $name, $body);
        $body = str_replace('{{link}}', $link, $body);
    }

    $datares['title'] = 'notification';
    $datares['notification'] = $body;
    $datares['type'] = $type;
    $datares['user_type'] = $user_type;
    notification::insertGetId($datares);
    return;
}

function createInstructorNotesSubmitNotification($user,$date,$program, $school_name){
        $template = Notification_content::where("id", 71)->first();
        $body =  @$template->content;
        $instructor_name = $user->first_name .' '. $user->last_name;
        $insert_date = $date;
        $id = encrypt($program->id);
        if ($id) {
            $link = url('view-program/step1/' . encrypt_str($program->id));
        }

        $body = str_replace('{{school_name}}', $school_name, $body);
        $body = str_replace('{{instructor_name}}', $instructor_name, $body);
        $body = str_replace('{{link}}', $link, $body);
        $body = str_replace('{{insert_date}}', $insert_date, $body);
        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = 0;
        $datares['notification'] = $body;
        $datares['user_type'] = "Admin";
        notification::insertGetId($datares);
}

function createProgramCommanNotification($user, $deadline, $program, $tempId, $user_type, $noti_type, $school_name, $sendFooter = false, $classes = [])
{
    if (!$user) {
        return;
    }
    $udata = User::where("id", $user)->first();
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("id", $tempId)->first();
        $body =  @$template->content;
        $format = $program->delivery_type;
        if(!empty($classes)){
            $class_date = date('m-d-Y', strtotime($classes[0]));
        }
        $start_date = date('m-d-Y', strtotime($program->start_date));
        $end_date = date('m-d-Y', strtotime($program->end_date));
        $deadline_date = date('m-d-Y', strtotime($deadline));
        $deadline_time = date('h:i A', strtotime($deadline));

        if ($format == 'In-Person') {
            $city_name = 'in ' . $program->city;
        } else {
            $city_name = '';
        }
        $id = encrypt($program->id);
        if ($deadline) {
            $link = url('/new-program-alerts');
        } else {
            $link = url('/program-detail/' . $id);
        }

        $body = str_replace('{{school_name}}', $school_name, $body);
        $body = str_replace('{{format}}', $format, $body);
        $body = str_replace('{{start_date}}', $start_date, $body);
        if(isset($class_date)){
            $body = str_replace('{{class_date}}', $class_date, $body);
        }
        $body = str_replace('{{end_date}}', $end_date, $body);

        $body = str_replace('{{city_name}}', $city_name, $body);
        $body = str_replace('{{link}}', $link, $body);
        $body = str_replace('{{deadline_date}}', $deadline_date, $body);
        $body = str_replace('{{deadline_time}}', $deadline_time, $body);
        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $user;
        $datares['program_id'] = $program->id;
        $datares['notification'] = $body;
        $datares['type'] = $user_type;
        $datares['user_type'] = $noti_type;
        notification::insertGetId($datares);
    } else {
        return;
    }

    if ($udata->email_notification) {
        $template = EmailTemplate::find(26);

        $fullName = $udata->first_name . ' ' . $udata->last_name;

        $body = str_replace(['{{ NAME }}', '{{ notification }}'], [$fullName, $body], $template->description);

        if($sendFooter){
            $body = [
                'template' => $body,
                'sendFooter' => true,
            ];
        }

        NotificationHelper::sendEmail($udata->email, $subject, $body);
    }
    return;
}

function createAdminProgramCommanNotification($user, $deadline, $program, $tempId, $user_type, $noti_type, $school_name)
{
    if ($user) {
        $udata = User::where("id", $user)->first();
        if ($udata->app_notification == 1) {
            $template = Notification_content::where("id", $tempId)->first();
            $body =  @$template->content;
            $format = $program->delivery_type;
            $start_date = date('m-d-Y', strtotime($program->start_date));
            $end_date = date('m-d-Y', strtotime($program->end_date));
            $deadline_date = date('m-d-Y', strtotime($deadline));
            $deadline_time = date('h:i A', strtotime($deadline));

            if ($format == 'In-Person') {
                $city_name = 'in ' . $program->city;
            } else {
                $city_name = '';
            }
            $id = Crypt::encryptString($user);


            $udata = User::where("id", $user)->first();
            $name = $udata->first_name . ' ' . $udata->last_name;
            $id = Crypt::encryptString($udata->id);
            if ($noti_type == 'Recruiter') {
                $datares['user_id'] = $user;
                $programid = Crypt::encryptString($program->id);

                $link = url('view-program/step1/' . $programid);
            } else {
                $link = url('viewinstructordetails/step1/' . $id);
            }

            $remainedMainAccept = invite_programs::where('program_id', $program->id)
            ->where('admin_type', 1)
            ->whereNull('status')
            ->count();

            $remainedStandAccept = invite_programs::where('program_id', $program->id)
            ->where('is_standby', 1)
            ->whereNull('status')
            ->count();

            $remainedSubAccept = invite_programs::where('program_id', $program->id)
            ->where('is_makeup', 0)
            ->where('is_standby', 0)
            ->where('admin_type', 0)
            ->whereNull('status')
            ->count();

            // dd($remainedSubAccept);

            $body = str_replace('{{name}}', $name, $body);

            $body = str_replace('{{school_name}}', $school_name, $body);
            $body = str_replace('{{format}}', $format, $body);
            $body = str_replace('{{start_date}}', $start_date, $body);
            $body = str_replace('{{end_date}}', $end_date, $body);

            $body = str_replace('{{city_name}}', $city_name, $body);
            $body = str_replace('{{link}}', $link, $body);
            $body = str_replace('{{deadline_date}}', $deadline_date, $body);
            $body = str_replace('{{deadline_time}}', $deadline_time, $body);
            $body = str_replace('{{remained_users_count}}', $remainedMainAccept, $body);
            $body = str_replace('{{remained_stand_count}}', $remainedStandAccept, $body);
            $body = str_replace('{{remained_sub_count}}', $remainedSubAccept, $body);

            $datares['title'] = 'notification';
            // $datares['user_id'] = $user;
            $datares['program_id'] = $program->id;
            $datares['notification'] = $body;
            $datares['type'] = $user_type;
            $datares['user_type'] = $noti_type;
            notification::insertGetId($datares);

            $template = EmailTemplate::find(26);
            $body = str_replace(['{{ NAME }}', '{{ notification }}'], [$name, $body], $template->description);
            NotificationHelper::sendEmail(User::first()->email, 'Notification', $body);
        } else {
            return;
        }
    }
    return;
}

function createAdminProgramOperationsNotification($user, $op_id, $deadline, $program, $tempId, $user_type, $noti_type, $school_name)
{
    if ($user) {
        $udata = User::where("id", $user)->first();
        if ($udata->app_notification == 1) {
        } else {
            return;
        }
    }
    $template = Notification_content::where("id", $tempId)->first();
    $body =  @$template->content;
    $format = $program->delivery_type;
    $start_date = date('m-d-Y', strtotime($program->start_date));
    $end_date = date('m-d-Y', strtotime($program->end_date));
    $deadline_date = date('m-d-Y', strtotime($deadline));
    $deadline_time = date('h:i A', strtotime($deadline));

    if ($format == 'In-Person') {
        $city_name = 'in ' . $program->city;
    } else {
        $city_name = '';
    }
    $id = Crypt::encryptString($user);


    $udata = User::where("id", $user)->first();
    $name = $udata->first_name . ' ' . $udata->last_name;
    $id = Crypt::encryptString($udata->id);
    if ($noti_type == 'Recruiter') {
        $datares['user_id'] = $user;
        $programid = Crypt::encryptString($program->id);

        $link = url('view-program/step1/' . $programid);
    } else {
        $link = url('viewinstructordetails/step1/' . $id);
    }

    $body = str_replace('{{name}}', $name, $body);

    $body = str_replace('{{school_name}}', $school_name, $body);
    $body = str_replace('{{format}}', $format, $body);
    $body = str_replace('{{start_date}}', $start_date, $body);
    $body = str_replace('{{end_date}}', $end_date, $body);

    $body = str_replace('{{city_name}}', $city_name, $body);
    $body = str_replace('{{link}}', $link, $body);
    $body = str_replace('{{deadline_date}}', $deadline_date, $body);
    $body = str_replace('{{deadline_time}}', $deadline_time, $body);

    $datares['title'] = 'notification';
    if ($user && $noti_type != 'Admin') {
        $datares['user_id'] = $op_id;
    }

    $datares['program_id'] = $program->id;
    $datares['notification'] = $body;
    $datares['type'] = $user_type;
    $datares['user_type'] = $noti_type;
    notification::insertGetId($datares);

    $template = EmailTemplate::find(26);
    $body = str_replace(['{{ NAME }}', '{{ notification }}'], [$name, $body], $template->description);
    NotificationHelper::sendEmail(User::find($datares['user_id'])->email, 'Notification', $body);

    return;
}

function sendMainIntructorNotification($program ,$user = null,$tempId,$noti_type,$school_name,$classes = null,$sendFooter = false){
    $udata = User::where("id", $user)->first();
    if($udata){
        if ($udata->app_notification == 1) {
            $template = Notification_content::where("id", $tempId)->first();
            $body =  @$template->content;
            $format = $program->delivery_type;
            if(!empty($classes)){
                $class_date = date('m-d-Y', strtotime($classes[0]));
            }
            if ($format == 'In-Person') {
                $city_name = 'in ' . $program->city;
            } else {
                $city_name = '';
            }


            $body = str_replace('{{school_name}}', $school_name, $body);
            $body = str_replace('{{format}}', $format, $body);
            if(isset($class_date)){
                $body = str_replace('{{class_date}}', $class_date, $body);
            }
            $body = str_replace('{{city_name}}', $city_name, $body);
            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $user;
            $datares['program_id'] = $program->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Main';
            $datares['user_type'] = 'Main-Instructor';
            notification::insertGetId($datares);
        } else {
            return;
        }

        if ($udata->email_notification) {
            $template = EmailTemplate::find(28);
            $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
            if($sendFooter){
                $body = [
                    'template' => $body,
                    'sendFooter' => true,
                ];
            }

            NotificationHelper::sendEmail($udata->email, $subject, $body);
        }
        return;
    }
    return;
}

function adminAcceptedAppliedProgramNotification($program,$user,$tempId,$noti_type,$school_name,$sendFooter = false){

    $udata = User::where("id", $user)->first();
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("id", $tempId)->first();
        $body =  @$template->content;
        $format = $program->delivery_type;
        $start_date = date('m-d-Y', strtotime($program->start_date));
        if ($format == 'In-Person') {
            $city_name = 'in ' . $program->city;
        } else {
            $city_name = '';
        }
        $body = str_replace('{{school_name}}', $school_name, $body);
        $body = str_replace('{{format}}', $format, $body);
        $body = str_replace('{{city_name}}', $city_name, $body);
        $body = str_replace('{{start_date}}', $start_date, $body);
        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $user;
        $datares['program_id'] = $program->id;
        $datares['notification'] = $body;
        $datares['type'] = 'Main';
        $datares['user_type'] = 'Main-Instructor';
        notification::insertGetId($datares);
    } else {
        return;
    }

    if ($udata->email_notification) {
        $template = EmailTemplate::find(26);
        $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
        if($sendFooter){
            $body = [
                'template' => $body,
                'sendFooter' => true,
            ];
        }

        NotificationHelper::sendEmail($udata->email, $subject, $body);
    }
    return;
}

function sendDeletePaymentReasonToInstructore($notes,$reason,$sendFooter = false){
    if($notes->sub_user_id){
        $udata = User::where("id", $notes->sub_user_id)->first();
    }
    else{
        $udata = User::where("id", $notes->user_id)->first();
    }
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("signature", "delete-payment-notification")->first();
        $body =  @$template->content;

        $body = str_replace('{{reason}}', $reason, $body);
        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $udata->id;
        $datares['program_id'] = $notes->program_id;
        $datares['notification'] = $body;
        $datares['type'] = 'Main-Instructor';
        $datares['user_type'] = 'Main-Instructor';
        notification::insertGetId($datares);
    } else {
        return;
    }

    // if ($udata->email_notification) {
    //     $template = EmailTemplate::find(26);
    //     $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
    //     if($sendFooter){
    //         $body = [
    //             'template' => $body,
    //             'sendFooter' => true,
    //         ];
    //     }

    //     NotificationHelper::sendEmail($udata->email, $subject, $body);
    // }
    return;
}

function remainInstructorSendInfoNotification($remainuser,$program ,$tempId,$school_name,$sendFooter = false){
    if(count($remainuser) == 1){
        $udata = User::where("id", $remainuser[0]->user_id)->first();
        if ($udata->app_notification == 1) {
            $template = Notification_content::where("id", $tempId)->first();
            $body =  @$template->content;
            $body = str_replace('{{school_name}}', $school_name, $body);
            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $remainuser[0]->user_id;
            $datares['program_id'] = $program->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Main';
            $datares['user_type'] = 'Main-Instructor';
            notification::insertGetId($datares);
        } else {
            return;
        }

        if ($udata->email_notification) {
            $template = EmailTemplate::find(26);
            $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
            if($sendFooter){
                $body = [
                    'template' => $body,
                    'sendFooter' => true,
                ];
            }

            NotificationHelper::sendEmail($udata->email, $subject, $body);
        }
        return;
    }
    else{
        foreach($remainuser as $user){
            if ($udata->app_notification == 1) {
                $udata = User::where("id", $user->user_id)->first();
                $template = Notification_content::where("id", $tempId)->first();
                $body =  @$template->content;
                $body = str_replace('{{school_name}}', $school_name, $body);
                $subject = "Notification";
                $datares['title'] = 'notification';
                $datares['user_id'] = $user->user_id;
                $datares['program_id'] = $program->id;
                $datares['notification'] = $body;
                $datares['type'] = 'Main';
                $datares['user_type'] = 'Main-Instructor';
                notification::insertGetId($datares);
            } else {
                return;
            }

            if ($udata->email_notification) {
                $template = EmailTemplate::find(26);
                $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
                if($sendFooter){
                    $body = [
                        'template' => $body,
                        'sendFooter' => true,
                    ];
                }

                NotificationHelper::sendEmail($udata->email, $subject, $body);
            }
            return;
        }
    }
}

function sendNotificationWhenReviewOkorbad(){
    $template = Notification_content::where("signature", 'send-notification-when-instructor-mark-review-okorbad')->first();
    $body =  @$template->content;
    $subject = "Notification";
    $datares['title'] = 'notification';
    $datares['user_id'] = 0;
    $datares['notification'] = $body;
    $datares['user_type'] = "Admin";
    notification::insertGetId($datares);
}
function sendMakeupClassScheduledAdded($program,$formattedclass_date,$user,$sendFooter= false){
    if($user){
        if ($user->app_notification == 1) {
            $template = Notification_content::where("signature", "makeup-class-scheduled-added")->first();
            $body =  @$template->content;
            $class_date = $formattedclass_date;
            $today_date = date('m-d-y');
            $school_name = schoolusername($program->school_name);
            $body = str_replace('{{class_date}}', $class_date, $body);
            $body = str_replace('{{today_date}}', $today_date, $body);
            $body = str_replace('{{school_name}}', $school_name, $body);
            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $user->id;
            $datares['program_id'] = $program->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Main';
            $datares['user_type'] = 'Main-Instructor';
            notification::insertGetId($datares);
        } else {
            return;
        }

        if ($user->email_notification) {
            $template = EmailTemplate::find(26);
            $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
            if($sendFooter){
                $body = [
                    'template' => $body,
                    'sendFooter' => true,
                ];
            }

            NotificationHelper::sendEmail($user->email, $subject, $body);
        }
        return;
    }
    return;
}

function UpdateInstructorClassCompletedByAdmin($programNotes,$sendFooter = false){
$program = Programs::where('id',$programNotes->program_id)->first();
if($programNotes->sub_user_id){
    $udata = User::where("id", $programNotes->sub_user_id)->first();
}
else{
    $udata = User::where("id", $programNotes->user_id)->first();
}
if($udata){
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("signature", "update-instructor-admin-complete-notes")->first();
        $body =  @$template->content;
        $class_date = $programNotes->class_date;
        $school_name = schoolusername($program->school_name);
        $body = str_replace('{{school_name}}', $school_name, $body);
        $body = str_replace('{{class_date}}', Date("m-d-y", strtotime($class_date)), $body);
        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $udata->id;
        $datares['program_id'] = $program->id;
        $datares['notification'] = $body;
        $datares['type'] = 'Main';
        $datares['user_type'] = 'Main-Instructor';
        notification::insertGetId($datares);
    } else {
        return;
    }

    if ($udata->email_notification) {
        $template = EmailTemplate::find(26);
        $body = str_replace(['{{ NAME }}', '{{ notification }}'], $body, $template->description);
        if($sendFooter){
            $body = [
                'template' => $body,
                'sendFooter' => true,
            ];
        }

        NotificationHelper::sendEmail($udata->email, $subject, $body);
    }
    return;
}
return;
}

function createCronUserNotification($data, $tempId, $type, $user_type, $noti_type)
{
    if ($data) {
        $udata = User::where("id", $data->id)->first();
        if ($udata->app_notification == 1) {
        } else {
            return;
        }
    }
    $template = Notification_content::where("id", $tempId)->first();
    $body =  @$template->content;

    if ($noti_type == 'user') {
        $name = $data->first_name . ' ' . $data->last_name;
        $id = Crypt::encryptString($data->id);

        $link = url("/onboarding-step/" . encrypt($id));
        $body = str_replace('{{name}}', $name, $body);
        $body = str_replace('{{link}}', $link, $body);
    }

    $datares['title'] = 'notification';
    $datares['notification'] = $body;
    $datares['type'] = $type;
    $datares['user_type'] = $user_type;
    notification::insertGetId($datares);
    return;
}

function createUserNotification($data, $tempId, $type, $user_type, $noti_type, $school_name, $link, $due_date)
{
    if ($data) {
        $udata = User::where("id", $data->id)->first();
        if ($udata->app_notification == 1) {
        } else {
            return;
        }
    }
    $template = Notification_content::where("id", $tempId)->first();
    $body =  @$template->content;

    if ($noti_type == 'user') {
        $name = $data->first_name . ' ' . $data->last_name;
        $id = Crypt::encryptString($data->id);


        $body = str_replace('{{name}}', $name, $body);
        $body = str_replace('{{school_name}}', $school_name, $body);
        $body = str_replace('{{due_date}}', $due_date, $body);
        $body = str_replace('{{link}}', $link, $body);
    }

    $datares['title'] = 'notification';
    $datares['notification'] = $body;
    $datares['type'] = $type;
    $datares['user_id'] = $data->id;
    $datares['user_type'] = $user_type;
    notification::insertGetId($datares);
    return;
}

function getinvitations($id)
{
    $checkrService = new CheckrService;
    if ($id) {
        $response = $checkrService->getinvitations($id);


        if (isset($response)) {

            if ($response['status'] == 'completed') {

                if($response['report_id']){
                    $ReportResponse = $checkrService->getreportDetails($response['report_id']);

                    if($ReportResponse['status'] == 'completed') {
                    $bgdata['status'] = 1;
                    BackgroundMedicalModel::where("id", $id)->update($bgdata);
                    }

                    if ($ReportResponse['status'] == 'expired') {
                        $bgdata['status'] = 2;
                        BackgroundMedicalModel::where("invitation_id", $id)->update($bgdata);
                    }
                    return $ReportResponse['status'];
                }

            }

            if ($response['status'] == 'expired') {
                $bgdata['status'] = 2;
                BackgroundMedicalModel::where("invitation_id", $id)->update($bgdata);
            }

            return 'Invitation '.ucfirst($response['status']);
        }
    }
}

function tomInstructorRemNotify($id, $class_count, $class_date, $signature, $user_type, $noti_type)
{

    $link = url('/my-program');
    $template = Notification_content::where("signature", $signature)->first();
    $body =  @$template->content;

    $body = str_replace('{{link}}', $link, $body);
    $body = str_replace('{{class_count}}', @$class_count, $body);
    $body = str_replace('{{class_date}}', $class_date, $body);


    notification::insert([
        'title' => 'notification',
        'user_id' => $id,
        'program_id' => null,
        'notification' => $body,
        'type' => $user_type,
        'user_type' =>  $noti_type,
    ]);
    return;
}
function todayInstructorRemNotify($id, $class_count, $class_date, $signature, $user_type, $noti_type)
{

    $link = url('/my-program');
    $template = Notification_content::where("signature", $signature)->first();
    $body =  @$template->content;

    $body = str_replace('{{link}}', $link, $body);
    $body = str_replace('{{class_count}}', @$class_count, $body);
    $body = str_replace('{{class_date}}', $class_date, $body);


    notification::insert([
        'title' => 'notification',
        'user_id' => $id,
        'program_id' => null,
        'notification' => $body,
        'type' => $user_type,
        'user_type' =>  $noti_type,
    ]);
    return;
}

function newProgramAlertNotify($user, $program_id, $options, $title, $user_type, $noti_type, $status)
{
    $user_id = $user['id'];
    $email = $user['email'];

    // logger()->info($user_id);
    // User
    $link = url('/new-program-alerts');
    $template = Notification_content::where("title", $title)->first();
    $body =  @$template->content;

    $body = str_replace('{{link}}', $link, $body);
    $body = str_replace('{{delivery_type}}', $options['delivery_type'], $body);
    $body = str_replace('{{subsubject}}', $options['subsubject'], $body);
    if($options['delivery_type'] == 'In-Person'){
        $body = str_replace('{{location}}', '<br> Locations: ' . $options['location'], $body);
    }
    else{
        $body = str_replace('{{location}}', '', $body);
    }

    if ($status == false){

        if ($user['email_notification']) {
            $emailTemplate = EmailTemplate::find(27);

            $fullName = $user['first_name'] . ' ' . $user['last_name'];


            $emailBody = str_replace(['{{ NAME }}', '{{ notification }}'], [$fullName, $body], $emailTemplate->description);
            $subject = str_replace('{{delivery_type}}', $options['delivery_type'], $emailTemplate->subject);

            NotificationHelper::sendEmail($email, $subject, $emailBody);
        }
    }elseif ($status == true) {

        if ($user['app_notification']) {

            notification::insert([
                'title' => $subject,
                'user_id' => $user_id,
                'program_id' => $program_id,
                'notification' => $body,
                'type' => $user_type,
                'user_type' =>  $noti_type,
            ]);
        }
    }
    return;
}

function incompleteApplicationRemNotify($id, $signature, $user_type, $noti_type)
{
    $link = url("/onboarding-step/" . encrypt($id));
    $template = Notification_content::where("signature", $signature)->first();
    $body =  @$template->content;

    $body = str_replace('{{link}}', $link, $body);

    notification::insert([
        'title' => 'notification',
        'user_id' => $id,
        'program_id' => null,
        'notification' => $body,
        'type' => $user_type,
        'user_type' =>  $noti_type,
    ]);
    return;
}
function pendingContractRemNotify($id, $signature, $user_type, $noti_type)
{
    $link = url("/web-dashboard/");
    $template = Notification_content::where("signature", $signature)->first();
    $body =  @$template->content;

    $body = str_replace('{{link}}', $link, $body);

    notification::insert([
        'title' => 'notification',
        'user_id' => $id,
        'program_id' => null,
        'notification' => $body,
        'type' => $user_type,
        'user_type' =>  $noti_type,
    ]);
    return;
}


function createDeleteOrdisableNotification($user, $tempId, $user_type, $noti_type, $note)
{

    $template = Notification_content::where("signature", $tempId)->first();
    $body =  @$template->content;

    $udata = User::where("id", $user)->first();
    $name = $udata->first_name . ' ' . $udata->last_name;
    $email = $udata->email;
    $id = Crypt::encryptString($udata->id);
    $link = url('viewinstructordetails/step1/' . $id);

    $body = str_replace('{{name}}', $name, $body);
    $body = str_replace('{{email}}', $email, $body);
    $body = str_replace('{{link}}', $link, $body);
    if ($note) {
        $body = str_replace('{{note}}', $note, $body);

        $programList = ProgramNote::where(["user_id" => $udata->id])->orWhere('sub_user_id', $udata->id)->groupBy('program_id')->pluck('program_id');
        if (!empty($programList)) {
            $user_ids = invite_program_owners::whereIn("program_id", $programList)->groupBy('user_id')->pluck('user_id');

            if (!empty($user_ids)) {
                foreach ($user_ids as $rid) {
                    $datarec['title'] = 'notification';
                    $datarec['user_id'] = $rid;
                    $datarec['notification'] = $body;
                    $datarec['type'] = 'Recruter';
                    $datarec['user_type'] = 'Recruter';
                    notification::insertGetId($datarec);
                }
            }
        }
    }

    $datares['title'] = 'notification';
    // $datares['user_id'] = $user;
    $datares['notification'] = $body;
    $datares['type'] = $user_type;
    $datares['user_type'] = $noti_type;
    notification::insertGetId($datares);
    return;
}


function getMinProgramClassDate($userId = null, $program_id,$currentDate=null)
{
    return getProgramClassDate($userId, $program_id, 'ASC',$currentDate);
}

function getMaxProgramClassDate($userId = null, $program_id)
{
    return getProgramClassDate($userId, $program_id, 'DESC');
}

function getProgramClassDate($userId = null, $program_id, $order = 'ASC',$currentDate=null)
{
    $class = ProgramNote::query()
        ->when(!empty($userId), function ($qry) use ($userId) {
            $qry->where('user_id', $userId);
        })->when(!empty($currentDate), function ($qry) use ($currentDate) {
            $qry->where('class_date','>=', $currentDate);
        })
        ->where('program_id', $program_id)
        ->whereNull(['sub_user_id', 'note', 'status'])
        ->select('class_date')
        ->orderBy('class_date', $order)
        ->first();

    return $class ? $class->class_date : null;
}

/**
 * get max class date for an invite
 */

function getInviteMaxClassDate($userId, $program_id)
{
    return getInviteClassDate($userId, $program_id, 'DESC');
}

function getInviteMinClassDate($userId, $program_id)
{
    return getInviteClassDate($userId, $program_id, 'ASC');
}

function getInviteClassDate($id, $program_id, $order = 'ASC')
{
    $noteIds = InviteProgramNote::where([
        'invite_program_id' => $id,
    ])->pluck('program_note_id')->toArray();

    $class = ProgramNote::query()
        ->when(!empty($noteIds), function ($qry) use ($noteIds) {
            $qry->whereIn('id', $noteIds);
        })
        ->when(empty($noteIds), function ($qry) use ($program_id) {
            $qry->where('program_id', $program_id);
        })
        ->select('class_date')
        ->orderBy('class_date', $order)
        ->first();

    return $class ? $class->class_date : null;
}

/**
 * check if invite can be resend
 */
function checkResendInvite($id, $program_id)
{
    return true;
}

function convertTime($time,$programTimezone= 'America/Los_Angeles', $timezone = 'America/Los_Angeles',$format='h:i a')
{

    try {

        $utcTime = Carbon::createFromFormat('h:i a', $time, $programTimezone)
            ->setTimezone($timezone)
            ->format($format);

        /*         $utcTime = Carbon::parse($time)
            ->format('H:i:s'); */
    } catch (\Exception $e) {
        // If parsing with seconds fails, return the original time
        return $time;
    }

    return $utcTime;
}

function sendNotificationmainorSubWantSubstitue($program,$udata,$schoolName,$all_request,$subSubject)
{
    if (!$udata) {
        return;
    }
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("signature", "maininstrutor-or-sub-request-for-substitue")->first();
        $body =  @$template->content;
        $format = $program->delivery_type;
        if ($format == 'In-Person') {
            $city_name = 'in ' . $program->city;
        } else {
            $city_name = '';
        }

        foreach ($all_request as $detail) {
            $classDates[] =  date("m-d-y", strtotime($detail->class_date));
            $times[] =  date("H:m:s A", strtotime($detail->start_time));
        }

        $class_date = implode(', ', $classDates);
        $time = implode(', ', $times);

        $timeZone = $program->timezone;

        $body = str_replace('{{school_name}}', $schoolName, $body);
        $body = str_replace('{{format}}', $format, $body);
        $body = str_replace('{{class_date}}', $class_date, $body);
        $body = str_replace('{{city_name}}', $city_name, $body);
        $body = str_replace('{{instructorname}}', $udata->first_name .''.$udata->last_name, $body);
        $body = str_replace('{{time}}', $time, $body);
        $body = str_replace('{{time_zone}}', $timeZone, $body);
        $body = str_replace('{{sub_subject}}', $subSubject, $body);

        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = 0;
        $datares['program_id'] = $program->id;
        $datares['notification'] = $body;
        $datares['type'] = 'Admin';
        $datares['user_type'] = 'Admin';
        notification::insertGetId($datares);
    } else {
        return;
    }
    return;
}


function sendNotificationToMainRequestForReplacment($program,$udata,$schoolName,$replacmentdate)
{
    if (!$udata) {
        return;
    }
    if ($udata->app_notification == 1) {
        $template = Notification_content::where("signature", "main-instructor-request-for-replacment")->first();
        $body =  @$template->content;
        $format = $program->delivery_type;
        $replacmentdate = date('m-d-Y', strtotime($replacmentdate));

        if ($format == 'In-Person') {
            $city_name = 'in ' . $program->city;
        } else {
            $city_name = '';
        }

        $body = str_replace('{{school_name}}', $schoolName, $body);
        $body = str_replace('{{format}}', $format, $body);
        $body = str_replace('{{replacement_date}}', $replacmentdate, $body);
        $body = str_replace('{{city_name}}', $city_name, $body);

        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $udata->id;
        $datares['program_id'] = $program->id;
        $datares['notification'] = $body;
        $datares['type'] = 'Main-Instructor';
        $datares['user_type'] = 'Main-Instructor';
        notification::insertGetId($datares);
    } else {
        return;
    }
    return;
}

function getGrade($id)
{
    $grades = GradeLevelModel::whereIn('id', explode(",", $id))->get();
    $data = "";
    if (!empty($grades)) {
        foreach ($grades as $rows) {
            $data .= $rows->grade . ",";
        }
    }
    return $data;
}

function sendNotificationRemoveInstructor($program_id,$schoolName,$sub_subject,$class_date,$user)
{
    if ($user->app_notification == 1) {
        $template = Notification_content::where("signature", "remove-main-instructor")->first();
        $body =  @$template->content;

        $body = str_replace('{{school_name}}', $schoolName, $body);
        $body = str_replace('{{sub_subject}}', $sub_subject, $body);
        $body = str_replace('{{start_date}}', $class_date, $body);

        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = $user->id;
        $datares['program_id'] = $program_id;
        $datares['notification'] = $body;
        $datares['type'] = 'Main-Instructor';
        $datares['user_type'] = 'Main-Instructor';
        notification::insertGetId($datares);
    } else {
        return;
    }
    return;
}

function sendNotificationPostrequirements($school, $full_name, $school_email, $position, $requirement, $delivery_mode, $subject,$user)
{
    if ($user->app_notification == 1) {
        $template = Notification_content::where("signature", "post-requirement")->first();
        $body =  @$template->content;


        $body = str_replace('{{school}}', $full_name, $body);
        $body = str_replace('{{school_email}}', $school_email, $body);
        $body = str_replace('{{position}}', $position, $body);
        $body = str_replace('{{requirement}}', $requirement, $body);
        $body = str_replace('{{delivery_mode}}', $delivery_mode, $body);
        $body = str_replace('{{subject}}', $subject, $body);

        $subject = "Notification";
        $datares['title'] = 'notification';
        $datares['user_id'] = 0;
        $datares['notification'] = $body;
        $datares['type'] = 'Post Requirement';
        $datares['user_type'] = 'Admin';
        notification::insertGetId($datares);
    } else {
        return;
    }
    return;
}

if (!function_exists('getScheduleTimestamp')) {
    function getScheduleTimestamp($value, $format = 'h:i A')
    {
        if (!$value) {
            return "";
        }
        $adminTimezone = session('admin_timezone') ?? config('app.timezone');

        return $value ? Carbon::parse($value)->timezone($adminTimezone)->format($format) : null;
    }
}

if (!function_exists('SendNotificationForReviewInstructor')) {
    function SendNotificationForReviewInstructor($instructor, $user, $recruiterUser)
    {
        if ($user->app_notification == 1) {
            $template = Notification_content::where("signature", "new-marketplace-instructor-account-created")->first();
            $body =  @$template->content;
            $id = encrypt_str($instructor->id);
            $redirect_url = url('/admin/k12connections/set-instructor-id/'.$id);
            $body = str_replace('{{link}}', $redirect_url, $body);
            $body = str_replace('{{name}}', $instructor->first_name.' '.$instructor->last_name, $body);

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = 1;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Sign-up';
            $datares['user_type'] = 'Admin';
            notification::insertGetId($datares);
        } else {
            return;
        }

        if (!empty($recruiterUser)) {
            foreach ($recruiterUser as $recruiter) {
                if ($recruiter->app_notification == 1) {
                    $template = Notification_content::where("signature", "new-marketplace-instructor-account-created")->first();
                    $body =  @$template->content;
                    $id = encrypt_str($instructor->id);
                    $redirect_url = url('/admin/k12connections/set-instructor-id/'.$id);
                    $body = str_replace('{{link}}', $redirect_url, $body);
                    $body = str_replace('{{name}}', $instructor->first_name.' '.$instructor->last_name, $body);
        
                    $subject = "Notification";
                    $datares['title'] = 'notification';
                    $datares['user_id'] = $recruiter->id;
                    $datares['notification'] = $body;
                    $datares['type'] = 'Marketplace Instructor Sign-up';
                    $datares['user_type'] = 'Recruiter';
                    notification::insertGetId($datares);
                } else {
                    return;
                }
            }
        }
        return;
    }
}

if (!function_exists('SendNotificationForInstructorSubmitOnboarding')) {
    function SendNotificationForInstructorSubmitOnboarding($instructor, $user, $reviewerUser)
    {
        if ($user->app_notification == 1) {
            $template = Notification_content::where("signature", "marketplace-instructor-complete-onboarding-review")->first();
            $body =  @$template->content;
            $id = encrypt_str($instructor->id);
            $redirect_url = url('/admin/k12connections/set-instructor-id/'.$id);
            $body = str_replace('{{link}}', $redirect_url, $body);
            $body = str_replace('{{name}}', $instructor->first_name.' '.$instructor->last_name, $body);

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = 1;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Complete Onboarding Review';
            $datares['user_type'] = 'Admin';
            notification::insertGetId($datares);
        } else {
            return;
        }
        
        if ($instructor->app_notification == 1) {
            $template = Notification_content::where("signature", "marketplace-instructor-complete-onboarding")->first();
            $body =  @$template->content;

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $instructor->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Complete Onboarding';
            $datares['user_type'] = 'marketplace user';
            notification::insertGetId($datares);
        } else {
            return;
        }

        if (!empty($reviewerUser)) {
            foreach ($reviewerUser as $reviewer) {
                if ($reviewer->app_notification == 1) {
                    $template = Notification_content::where("signature", "marketplace-instructor-complete-onboarding-review")->first();
                    $body =  @$template->content;
                    $id = encrypt_str($instructor->id);
                    $redirect_url = url('/admin/k12connections/set-instructor-id/'.$id);
                    $body = str_replace('{{link}}', $redirect_url, $body);
                    $body = str_replace('{{name}}', $instructor->first_name.' '.$instructor->last_name, $body);

                    $subject = "Notification";
                    $datares['title'] = 'notification';
                    $datares['user_id'] = $reviewer->id;
                    $datares['notification'] = $body;
                    $datares['type'] = 'Marketplace Instructor Sign-up';
                    $datares['user_type'] = 'Reviewer';
                    notification::insertGetId($datares);
                } else {
                    return;
                }
            }
        }
        return;
    }
}

if (!function_exists('SendNotificationForInstructorProfileApproval')) {
    function SendNotificationForInstructorProfileApproval($instructor, $user)
    {
        if ($user->app_notification == 1) {
            $template = Notification_content::where("signature", "marketplace-instructor-profile-approved-by-admin")->first();
            $body =  @$template->content;
            $id = encrypt_str($instructor->id);
            $redirect_url = url('/admin/k12connections/set-instructor-id/'.$id);
            $body = str_replace('{{link}}', $redirect_url, $body);
            $body = str_replace('{{name}}', $instructor->first_name.' '.$instructor->last_name, $body);

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = 1;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Profile Approved By Admin';
            $datares['user_type'] = 'Admin';
            notification::insertGetId($datares);
        } else {
            return;
        }
        
        if ($instructor->app_notification == 1) {
            $template = Notification_content::where("signature", "marketplace-instructor-profile-approve")->first();
            $body =  @$template->content;

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $instructor->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Profile Approve';
            $datares['user_type'] = 'marketplace user';
            notification::insertGetId($datares);
        } else {
            return;
        }
        return;
    }
}

if (!function_exists('SendNotificationForInstructorProfileResubmit')) {
    function SendNotificationForInstructorProfileResubmit($instructor, $user)
    {   
        if ($instructor->app_notification == 1) {
            $template = Notification_content::where("signature", "marketplace-instructor-profile-resubmit")->first();
            $body =  @$template->content;

            $subject = "Notification";
            $datares['title'] = 'notification';
            $datares['user_id'] = $instructor->id;
            $datares['notification'] = $body;
            $datares['type'] = 'Marketplace Instructor Profile Resubmit';
            $datares['user_type'] = 'marketplace user';
            notification::insertGetId($datares);
        } else {
            return;
        }
        return;
    }
}

function getAdminUserk12ConnectionProgramIds()
{
    $adminSession = session()->get('Adminnewlogin');
    $adminType = $adminSession['type'];
    $adminId = $adminSession['id'];

    $whereInIds = [];
    
    if ($adminType != '1') {
        $admin = User::find($adminId);

        if ($adminType == '4') {
            $whereInIds = optional($admin)->recruitedPrograms->pluck('id')->toArray() ?? [];
        }
        if ($adminType == '9') {
            $whereInIds = Arr::flatten([$whereInIds, optional($admin)->createdPrograms->pluck('id')->toArray() ?? []]);
        }
        if ($adminType == '10') {

            if ($admin->regions) {
                $regions = explode(',', $admin->regions);
                $programId = k12ConnectionPrograms::whereIn('state', $regions)
                    ->pluck('id');
                $whereInIds = Arr::flatten([$whereInIds, $programId ?? []]);
            } else {
                $whereInIds = Arr::flatten([$whereInIds, optional($admin)->createdPrograms->pluck('id')->toArray() ?? []]);
            }
        }
    }

    return $whereInIds;
}

function k12username($id)
{
    $user = OnboardingInstructor::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->first_name . " " . $user->last_name;
    }
}

function proctorName($id)
{
    $user = PlatformSchoolProctor::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->proctor_name;
    } else {
        return '';
    }
}

function categorizedProgramName($id)
{
    $program_type = k12ConnectionCategorizedData::where(["id" => $id, 'type' => 'program_type'])
        ->first();

    if (!empty($program_type)) {
        return $program_type->description;
    }
}

function categorizedTimezoneName($id)
{
    $timezone = k12ConnectionCategorizedData::where(["id" => $id, 'type' => 'timezone'])
        ->first();

    if (!empty($timezone)) {
        return $timezone->description;
    }
}

function getstandbyk12username($programId)
{
    $res = k12ConnectionInvitePrograms::join('new_onboarding_instructor','new_onboarding_instructor.id','k12_connection_invite_programs.user_id')->where([
                        'k12_connection_invite_programs.program_id' => $programId,
                        'k12_connection_invite_programs.status' => 1,
                        'k12_connection_invite_programs.is_standby' => 1,
                    ])->select(DB::raw("CONCAT(new_onboarding_instructor.first_name, ' ', new_onboarding_instructor.last_name) as full_name")) // Concatenate first and last names
                    ->pluck('full_name') // Pluck the concatenated 'full_name'
                    ->implode(', '); // Join names with a comma and space

    if (!empty($res)) {
        return $res;
    }else {
                return '';
            }
}

function timezone($id) 
{
    $timezone = k12ConnectionCategorizedData::where(["id" => $id])
        ->first();

    if (!empty($timezone)) {
        return $timezone->description;
    } else {
        return '';
    }
}

function certificatesState($id) 
{
    $states = StateModel::whereIn("id", explode(",", $id))->get();
    $data = "";
    if (!empty($states)) {
        foreach ($states as $state) {
            $data .= $state->name . ",";
        }
    }
    return $data;
}

function classType($id) 
{
    $classType = k12ConnectionCategorizedData::where(["id" => $id])
        ->first();

    if (!empty($classType)) {
        return ucfirst($classType->description);
    } else {
        return '';
    }
}

function k12userImg($id) 
{
    $user = OnboardingInstructor::where(["id" => $id])
        ->first();

    if (!empty($user)) {
        return $user->image;
    } else {
        return '';
    }
}

function userProposalId($instructorId, $reqId) 
{
    $proposal = SchoolReviewApplicants::where(["requirement_id" => $reqId, 'instructor_id' => $instructorId])->first();

    if (!empty($proposal)) {
        return encrypt_str($proposal->id);
    } else {
        return '';
    }
}

function getRequirementsAndPrograms($currentDate) {
    $requirements = PlatformSchoolRequirements::where('school_id', auth()->user()->id)
        ->where('status', 'filled')
        ->whereDate('start_date', '<=', $currentDate)
        ->whereDate('end_date', '>=', $currentDate)
        ->pluck('id');
    
    $programs = k12ConnectionPrograms::whereIn('requirement_id', $requirements)
        ->pluck('id')
        ->toArray();

    return [$requirements, $programs];
}

function hireUser($reqId, $schoolId) 
{
    $hireDetails = SchoolInstructorHiring::where(["requirment_id" => $reqId, 'school_id' => $schoolId])->whereIn('status', ['accepted', 'pending'])->first();
    if (!empty($hireDetails)) {
        $userDetails = OnboardingInstructor::find($hireDetails->instructor_id);
        if (!empty($userDetails)) {
            return ucfirst($userDetails->first_name) .' '. ucfirst($userDetails->last_name);
        } else {
            return '';
        }
    } else {
        return '';
    }
}

function isInvitedText($schoolId, $reqId, $userId) 
{
    $invitation = PlatformSchoolInvites::where(['school_id' => $schoolId, 'requirement_id' => $reqId, 'user_id' => $userId])->first();
    if (!empty($invitation)) {
        if ($invitation->status == 'pending') {
            return 'Withdraw';
        } elseif ($invitation->status == 'accepted') {
            return 'Accepted';
        } elseif ($invitation->status == 'declined') {
            return 'Declined';
        } else {
            return 'Invite';
        }
    } else {
        return '';
    }
}

function contractStatus($hireStatus, $reqId) 
{
    // $hireDetails = SchoolInstructorHiring::where(["requirment_id" => $reqId, 'school_id' => $schoolId])->whereIn('status', ['accepted', 'pending'])->first();
    $hasmeetingLinks = k12ConnectionMeetingLinks::where('requirement_id', $reqId)->first();

    if (empty($hasmeetingLinks) && $hireStatus != 'declined' && $hireStatus != 'withdraw') {
        return false;
    } else {
        return true;
    }
}

function isStatusDeclined($instructorId, $requirementId)
{
   $statusdeclined =  PlatformSchoolInvites::where('requirement_id', $requirementId)->where('user_id', $instructorId)->first();

    $declinedStatus = $statusdeclined->status;
    
    if($declinedStatus === 'declined')
    {
        return true;
    }
    else{
        return false;
    }
}

function requestChangeReson($userId) 
{
    $reason = RequestChangeOnboardingInstructorModel::where('onboarding_instructor_id', $userId)->first();
    if (!empty($reason)) {
        return $reason->reason;
    } else {
        return '';
    }
}

function payScale($teaching,  $subjectBudget)
{
   
    // use Illuminate\Support\Str;
    // use Carbon\Carbon;
    

   

    // Default incentive
    $educationIncentive = 0;
    // $subjectBudget = $subject->subjectBudget ?? null;

    if ( !empty($teaching->highest_level_of_education)) {
        $education = strtolower($teaching->highest_level_of_education); // Normalize

        if (Str::contains($education, ['doctor', 'phd'])) {
            $educationIncentive = $subjectBudget->doctorate_inc ?? 0;
        } elseif (Str::contains($education, 'master')) {
            $educationIncentive = $subjectBudget->masters_inc ?? 0;
        }
    }

   return $educationIncentive;

}

function totalBudget($boi, $boe = 0)
{
    return $boi += $boe;
}

if (!function_exists('proposedRate')) {
    function proposedRate($instructorId, $requirementId)
    {
        $proposedRate = 0;
        $rate = SchoolReviewApplicants::where(['school_id' => auth()->user()->id, 'instructor_id' => $instructorId, 'requirement_id' => $requirementId])->first();
        if (!empty($rate)) {
            $proposedRate = $rate->proposed_rate;
        }
        
        return $proposedRate;
    }
}

if (!function_exists('classCompletedCost')) {
    function classCompletedCost($instructorId, $requirementId, $payHour)
    {
        $program = k12ConnectionPrograms::where('requirement_id', $requirementId)->first();
        if (empty($program)) {
            return '';
        }

        $classes = k12ConnectionClasses::where('main_instructor_id', $instructorId)->where('program_id', $program->id)->get();
        $classCompletedCost = 0;
        $totalClassHours = 0;
        foreach ($classes as $class) {
            if ($class->status == 'completed') {
                $startTime = Carbon::parse($class->start_time);
                $endTime = Carbon::parse($class->end_time);
                $classHours = $endTime->diffInHours($startTime);
                $totalClassHours += $classHours;
            }
        }
        $classCompletedCost = $payHour * $totalClassHours;
        return $classCompletedCost;
    }
}

if (!function_exists('getHiresDetails')) {
    function getHiresDetails($reqObject, $schoolId)
    {
        $reqId = $reqObject->id;
        $hiring = SchoolInstructorHiring::where('requirment_id', $reqId)->where('school_id', $schoolId)->first();
        $userId = $hiring->instructor_id;
        $instructor = OnboardingInstructor::find($userId);
        $data = [];
        $data[] = [
            'hiring' => $hiring, // SchoolInstructorHiring ka pura data
            'onboarding_instructor' => $instructor, // Onboarding instructor ka data
        ];

        return [
            'requirement' => $reqObject, // Requirement ka pura object
            'hiring_details' => $data,  // Hiring aur instructor details
        ];
    }
}

if (!function_exists('acceptInvite')) {
    function acceptInvite($instructor, $requirement, $schoolId, $status)
    {
        // Invite Object Find Karna
        $invite = PlatformSchoolInvites::where([
            'school_id' => $schoolId,
            'requirement_id' => $requirement->id,
            'user_id' => $instructor->id
        ])->first();
        
        if (!$invite) {
            return null; // Invite nahi mila
        }

        if ($invite->status == 'pending') {
            // Invite Status Update Karna
            // $invite->status = "accepted";
            $invite->status = $status;
            $invite->save();
            return $invite; // Invite Object Return Karna
        } else {
            return null;
        }
    }
}

if (!function_exists('sendProposal')) {
    function sendProposal($proposedAmount, $requirement, $instructor)
    {
        $existingProposal = SchoolReviewApplicants::where([
            'instructor_id' => $instructor->id,
            'school_id' => $requirement->school_id,
            'requirement_id' => $requirement->id,
        ])->first();

        if ($existingProposal) {
            return null;
        }

        // Proposal Data Create Karna
        $proposal = SchoolReviewApplicants::create([
            'instructor_id' => $instructor->id,
            'school_id' => $requirement->school_id,
            'requirement_id' => $requirement->id,
            'proposed_rate' => $proposedAmount,
            'status' => 1,
        ]);

        $proposal->requirement = $requirement;
        $proposal->instructor = $instructor;

        return $proposal;
    }
}

if (!function_exists('offerStatusUpdate')) {
    function offerStatusUpdate($schoolId, $instructor, $requirement, $status, $school)
    {
        $hire = SchoolInstructorHiring::where(['school_id' => $schoolId, 'instructor_id' => $instructor->id, 'requirment_id' => $requirement->id])->first();

        if (!$hire || ($hire->status !== 'pending' && $hire->status !== 'withdraw')) {
            return null;
        }

        $hire->status = ($status == 'reject') ? 'declined' : $status;
        $hire->save();
        if ($status == 'reject') {
            $requirement->update(['status' => 'open']);
        } else {
            $requirement->update(['status' => 'filled']);
        }
        
        $requirement->update(['status' => 'filled']);
        generateProgramsClasses($instructor, $requirement, $school);
        return $hire;
    }
}

if (!function_exists('generateProgramsClasses')) 
{
    function generateProgramsClasses($instructor, $requirement, $school)
    {
        $delivery_modes = explode(',', $requirement->delivery_mode);
        $delivery_type = in_array('hybrid', $delivery_modes) ? 'hybrid' : $requirement->delivery_mode;

        $today = now()->format('Y-m-d');
        if ($requirement->start_date > $today) {
            $program_status = 'upcoming';
        } elseif ($requirement->start_date <= $today && $requirement->end_date >= $today) {
            $program_status = 'active';
        } else {
            $program_status = 'inactive';
        }
        $programsData = [];
        $programsData = [
            'requirement_id' => $requirement->id,
            'school_id' => $school->id,
            'name' => $requirement->requirement_name,
            'start_date' => $requirement->start_date,
            'end_date' => $requirement->end_date,
            'delivery_type' => $delivery_type,
            'capacity' => $requirement->capacity,
            'managed_under' => 'school',
            'program_status' => $program_status,
            'background_checks' => 0,
            'medical_requirements' => 0,
            'is_imported' => 0,
            'notes_id' => null,
            'cbo_id' => $school->cbo,
            'district_id' => $school->district,
        ];

        $program = k12ConnectionPrograms::create($programsData);
        $mainInstructorId = $instructor->id;
        $schedule = json_decode($requirement->schedules, true); 

        $noClassDates = collect(json_decode($requirement->no_class_dates ?? '[]', true))
        ->map(function ($date) {
            return Carbon::createFromFormat('m/d/Y', str_replace('\/', '/', $date))->format('Y-m-d');
        })->toArray();

        $startDate = Carbon::parse($requirement->start_date);
        $endDate = Carbon::parse($requirement->end_date);
        $dateRange = collect(Carbon::parse($requirement->start_date)->daysUntil(Carbon::parse($requirement->end_date)->addDay()))
            ->flatMap(function ($date) use ($program, $mainInstructorId, $schedule, $noClassDates) {
                $formattedDate = $date->format('Y-m-d');
                $dayName = $date->format('l'); // Get day name (e.g., "Monday")

                // Skip if date is in no_class_date
                if (in_array($formattedDate, $noClassDates)) {
                    return [];
                }

                // Filter schedule based on the day
                $matchingSchedules = array_filter($schedule, fn($s) => $s['day'] === $dayName);

                // Return an array of class entries for the matched schedule
                return array_map(fn($s) => [
                    'program_id' => $program->id,
                    'class_date' => $date->format('Y-m-d'),
                    'day' => $dayName,
                    'start_time' => Carbon::parse($s['start_time'])->format('H:i:s'), // Get from schedule JSON
                    'end_time' => Carbon::parse($s['end_time'])->format('H:i:s'),     // Get from schedule JSON
                    'main_instructor_id' => $mainInstructorId,
                    'meeting_id' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ], $matchingSchedules);
            })->toArray();

        k12ConnectionClasses::insert($dateRange);
        // $requirement->update(['finalize_setup' => 'true']);

        $dayMapping = [
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6,
            'Sunday' => 7,
        ];
        $schedule = json_decode($requirement->schedules, true);
        $scheduleData = collect($schedule)
            ->filter(fn($s) => !is_null($s['start_time']) && !is_null($s['end_time'])) // Exclude empty schedules
            ->map(fn($s) => [
                'program_id' => $program->id,
                'class_day' => $dayMapping[$s['day']] ?? null, // Convert to number
                'start_time' => $s['start_time'],
                'end_time' => $s['end_time'],
                'created_at' => now(),
                'updated_at' => now(),
            ])
            ->toArray();

        if (!empty($scheduleData)) {
            k12ConnectionProgramsSchedule::insert($scheduleData);
        }
    }
}


function additional_category_name($id)

{

   $category = AdditionalCertificateCategory::find($id);

   if ($category) {
       return $category->name;
   } else {
       return null; // or return 'Not Found';
   }



}

function additional_subcategory_name($id){
    $subcategoryname=AdditionalCertificateSubcategory::find($id);
    if ($subcategoryname) {
        return $subcategoryname->name;
    } else {
        return null; // or return 'Not Found';
    }


}





?>
