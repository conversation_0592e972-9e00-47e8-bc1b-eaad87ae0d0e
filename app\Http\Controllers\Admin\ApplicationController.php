<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Exports\Admin\ExportApplications;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\scheduledInterview;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\ViewClassroomModel;
use App\UserUponCompletionNoteModel;
use App\ProfileStatusHistoryModel;
use App\rubric;
use App\user_references;
use App\UserEducationModel;
use App\user_interview_slots;
use App\user_contract;
use App\document_form;
use App\StateModel;
use App\Subject;
use Hash;
use Mail;
use Auth;
use App\AvailabilityModel;
use App\QuestionsModel;
use App\AdministrativeInfoModel;
use App\AvailablityLocationModel;
use App\invite_application_recruiter;
use App\User;
use App\StatesTerritoriesModel;


use Excel;
DB::enableQueryLog();
use Illuminate\Support\Facades\Input;
use App\Services\CheckrService;
class ApplicationController extends Controller
{
    protected $checkrService;

    public function __construct(CheckrService $checkrService)
    {
        $this->checkrService = $checkrService;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        // if(session('Adminnewlogin')['type']==9){
        //     return redirect("/Approved-application-list");
        // }

        // if(session('Adminnewlogin')['type']==8){
        //     return redirect("/Active-application-list");
        // }

        // if(session('Adminnewlogin')['type']==3){
        //     $application = Users::where("type", "5")
        //     ->where("profile_status", "=", "2")
        //     // ->orwhere("profile_status", "=", "2")
        //     ->orderBy("id", "desc")
        //     ->get();
        // }elseif(session('Adminnewlogin')['type']==4){
        //     $application = Users::select('users.*')
        //     ->join(
        //         "tbl_invite_application_recruiters",
        //         "tbl_invite_application_recruiters.application_id",
        //         "=",
        //         "users.id"
        //     )
        //     ->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']])
        //     -> where("users.type", "5")
        //     ->orderBy("tbl_invite_application_recruiters.id", "desc")
        //     ->get();
        // }else{
        //     $application = Users::where("type", "5")
        //     ->where("profile_status", "=", "1")
        //     ->orwhere("profile_status", "=", "2")
        //     ->orwhere("profile_status", "=", null)
        //     ->where("type", "=", "5")
        //     ->orderBy("id", "desc")
        //     ->get();
        // }

        $res=get_permission(session('Adminnewlogin')['type']);

       if(get_childpermission($res,'manageapplication','new')==true){

        if(session('Adminnewlogin')['type']==4){
                $application = Users::select('users.*')
                ->join(
                    "tbl_invite_application_recruiters",
                    "tbl_invite_application_recruiters.application_id",
                    "=",
                    "users.id"
                )
                ->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']])
                ->where("users.type", "5")
                ->orderBy("tbl_invite_application_recruiters.id", "desc")
                ->get();
            }else{
                $application = Users::where("type", "5")
                ->where("profile_status", "=", "1")
                ->orwhere("profile_status", "=", "2")
                ->orwhere("profile_status", "=", null)
                ->where("type", "=", "5")
                ->orderBy("id", "desc")
                ->get();
            }


        return view("admin.application.list", compact("application"));
       }elseif(get_childpermission($res,'manageapplication','Under Review')==true){
        return redirect("/Under-Reveiw-application-list");
       }elseif(get_childpermission($res,'manageapplication','Resubmit request')==true){
        return redirect("/Resubmit-request-application-list");
       }elseif(get_childpermission($res,'manageapplication','Pending Interview')==true){
        return redirect("/Pending-Interview-application-list");
       }elseif(get_childpermission($res,'manageapplication','Approved')==true){
        return redirect("/Approved-application-list");
       }elseif(get_childpermission($res,'manageapplication','Active')==true){
        return redirect("/Active-application-list");
       }elseif(get_childpermission($res,'manageapplication','Rejected')==true){
        return redirect("/Reject-application-list");
       }elseif(get_childpermission($res,'manageapplication','All')==true){
        return redirect("/All-application-list");
       }else{
        return view("admin.permissionpage");
       }


    }

    public function Reveiw()
    {

            $application = Users::where("type", "5")
            ->where("profile_status", "=", "3")
            ->orwhere("profile_status", "=", "5")
            ->orwhere("profile_status", "=", "7")
            ->orwhere("profile_status", "=", "6")
            ->orwhere("profile_status", "=", "19")
            ->orderBy("id", "asc")
            ->get();

        return view("admin.application.list", compact("application"));
    }

    public function show_filter(){

        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["subject"] = Subject::get();
        $data["status"] = DB::table("tbl_profile_status")->where(['status_type'=>'Instructor'])->get();
        return view("admin.application.filter")->with($data);
    }

    public function show_filter_ins(){

        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["subject"] = Subject::get();
        $data["status"] = DB::table("tbl_profile_status")->where(['status_type'=>'Instructor'])->get();
        return view("admin.instructor.filter")->with($data);
    }


    public function Resubmit()
    {
        $application = Users::where("type", "5")
            ->where("profile_status", "=", "10")
            ->orwhere("profile_status", "=", "18")

            ->orderBy("id", "desc")
            ->get();
        return view("admin.application.list", compact("application"));
    }

    public function PendingInterview()
    {
        $application = Users::where("type", "5")
            ->where("profile_status", "=", "11")
            ->orwhere("profile_status", "=", "9")
            ->orderBy("id", "desc")
            ->get();
        return view("admin.application.list", compact("application"));
    }

    public function Approved()
    {

        if(session('Adminnewlogin')['type']==9){
            $application = Users::where("type", "5")
            ->where("profile_status", "=", "4")
            ->orwhere("profile_status", "=", "14")
            ->orwhere("profile_status", "=", "5")
            ->orwhere("profile_status", "=", "6")
            ->orwhere("profile_status", "=", "19")
            ->orwhere("profile_status", "=", "22")
            ->orwhere("profile_status", "=", "16")
            ->orwhere("profile_status", "=", "17")
            ->orwhere("profile_status", "=", "20")
            ->orderBy("id", "desc")
            ->get();
        }else{
            $application = Users::where("type", "5")
            ->where("profile_status", "=", "4")
            ->orwhere("profile_status", "=", "14")

            ->orwhere("profile_status", "=", "16")
            ->orwhere("profile_status", "=", "17")
            ->orwhere("profile_status", "=", "20")
            ->orderBy("id", "desc")
            ->get();
        }


        return view("admin.application.list", compact("application"));
    }

    public function Activeapplication()
    {


        $application = Users::where("type", "5")
            ->where("profile_status", "=", "12")
            ->orwhere("profile_status", "=", "8")
            // ->orwhere("profile_status", "=", "16")
            // ->orwhere("profile_status", "=", "17")
            ->orderBy("id", "desc")
            ->get();
        return view("admin.application.list", compact("application"));
    }

    public function Rejectapplication()
    {
        $application = Users::where("type", "5")
            ->where("profile_status", "=", "13")
            ->orderBy("id", "desc")
            ->get();
        return view("admin.application.list", compact("application"));
    }

    public function allapplication()
    {

        if(session('Adminnewlogin')['type']==4){
            $application = Users::select('users.*')
            ->join(
                "tbl_invite_application_recruiters",
                "tbl_invite_application_recruiters.application_id",
                "=",
                "users.id"
            )
            ->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']])
            -> where("users.type", "5")
            ->orderBy("tbl_invite_application_recruiters.id", "desc")
            ->get();
        }else{
        $application = Users::where("type", "5")->orderBy("id", "desc")->get();
        }
        return view("admin.application.list", compact("application"));
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function viewapplication($id, $id2)
    {
        $application_id = decrypt_str($id2);

        $users = Users::where("id", $application_id)
            ->first();

        if ($users->profile_status == 2) {
            $datas["profile_status"] = 3;
            Users::where("id", $application_id)->update($datas);
        }
        $user = User::find( $application_id);
        $pStatus = DB::table("tbl_profile_status")
            ->where("status_type", "Instructor")
            ->where("status", "1")
            ->get();
        $user_first_step = DB::table("tbl_user_work_authorizations")
            ->where("user_id", $application_id)
            ->first();
        $user_second_step = DB::table("tbl_user_experiences")
            ->where("user_id", $application_id)
            ->first();
        $user_third_step = DB::table("tbl_user_teaching_preferences")
            ->where("user_id", $application_id)
            ->first();
        $user_fourth_step = DB::table("tbl_user_hourly_rates")
            ->where("user_id", $application_id)
            ->first();
        $intro = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "introduction",
        ])->get();

        $teaching = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "teaching",
        ])
            ->orderBy("id", "DESC")
            ->get();
        $classroom = AssessmentsModel::where([
            "user_id" => $application_id,
            "type" => "classroom",
        ])->get();
        $scheduledInterview = scheduledInterview::where([
            "user_id" => $application_id,
        ])->get();
        $five = AssessmentsModel::where(["user_id" => $application_id])->first();
        $quiz = UserQuizModel::where(["user_id" => $application_id])->first();
        $profilehistory = ProfileStatusHistoryModel::where([
            "user_id" => $application_id,
        ])
            ->orderBy("id", "DESC")
            ->get();
            if(request()->segment(2) =='step101'){
                $reqid=$_GET['reqid'];

        $background = BackgroundMedicalModel::select(
            "tbl_user_docs.*",
            "tbl_user_doc_requests.state"
        )
            ->join(
                "tbl_user_docs",
                "tbl_user_doc_requests.id",
                "=",
                "tbl_user_docs.back_med_id"
            )
            ->where(["type" => "background_check", "user_id" => $application_id,    "back_med_id" => $reqid])
            ->get();
        }else{
            $background=array();
        }
         $availrangelocation=AvailablityLocationModel::where(['user_id'=>$application_id])->get();
         if(request()->segment(2) =='step111'){
            $reqid=$_GET['reqid'];

        $medical_requirements = BackgroundMedicalModel::select(
            "tbl_user_docs.*",
            "tbl_user_doc_requests.state"
        )->join(
                "tbl_user_docs",
                "tbl_user_doc_requests.id",
                "=",
                "tbl_user_docs.back_med_id"
            )
            ->where([
                "type" => "medical_requirements",
                "user_id" => $application_id,
                "back_med_id" => $reqid
            ])
            ->get();
            }else{
                $medical_requirements=array();
            }

        $viewclassroom = ViewClassroomModel::select(
            "tbl_training_videos.video","tbl_training_videos.title",
            "tbl_user_view_videos.*"
        )
            ->join(
                "tbl_training_videos",
                "tbl_training_videos.id",
                "=",
                "tbl_user_view_videos.classroom_video_id"
            )
            ->where(["tbl_user_view_videos.user_id" => $application_id])
            ->get();
        $rubric = rubric::where(["user_id" => $application_id])->first();
        $user_references = user_references::where([
            "user_id" => $application_id,
        ])->get();
        $user_education = UserEducationModel::where([
            "user_id" => $application_id,
        ])->get();



        $user_interview_slots = DB::table("tbl_user_interview_slots")
                ->select(
                    "tbl_user_interview_slots.*",
                    "tbl_schedule_interviews.date as date",
                    "tbl_schedule_interviews.time as time",
                    "tbl_schedule_interviews.timezone as timezone",
                    "tbl_schedule_interviews.end_time as end_time",
                    "tbl_schedule_interviews.link as link"
                )
                ->join(
                    "tbl_schedule_interviews",
                    "tbl_schedule_interviews.id",
                    "=",
                    "tbl_user_interview_slots.slot_id"
                )
                ->where(["tbl_user_interview_slots.user_id" => $application_id])
                ->orderBy("tbl_user_interview_slots.id", "DESC")
                ->get();

                $user_contract = user_contract::where([
                    "user_id" => $application_id,
                ])->first();

                $availability=AvailabilityModel::where(['user_id'=>$application_id])->first();
                $quiz=UserQuizModel::where(['user_id'=>$application_id])->first();

                $question = QuestionsModel::orderBy("question_id", "asc")->get();

                $form = document_form::where([
                    "status" => 1,
                ])->get();

                $school = Users::where(["type" => 6,"status"=>"1"])->get();
                $state = StateModel::where(["country_id" => "239"])->get();
                $medicalform = BackgroundMedicalModel::where([
                    "type"=>"background_check",
                    "application_id" => $application_id,
                ])->get();

                $background_medical= BackgroundMedicalModel::where([
                    "type"=>"medical_requirements",
                    "application_id" => $application_id,
                ])->get();

        $admin=AdministrativeInfoModel::where(['user_id'=>$application_id])->first();
        return view(
            "admin.application.viewdetails",
            compact(
                "availrangelocation",
                "admin",
                "question",
                "quiz",
                "availability",
                "user_education",
                "profilehistory",
                "viewclassroom",
                "user",
                "application_id",
                "pStatus",
                "user_first_step",
                "user_second_step",
                "user_third_step",
                "user_fourth_step",
                "intro",
                "teaching",
                "classroom",
                "five",
                "quiz",
                "scheduledInterview",
                "background",
                "medical_requirements",
                "rubric",
                "user_references",
                "user_interview_slots",
                "user_contract",
                "form",
                "school",
                "state",
                'background_medical',
                'medicalform'
            )
        );
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {

        $id = decrypt_str($request->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    }

    public function changestatus(Request $request)
    {

        if($request->id1==21){
            $data["profile_status"] ='6';
            $data["contract_sent_date"] =now()->toDateString();

        }else{

            $data["profile_status"] = $request->id1;
        }

        if($request->onlinerate){
            $data["onlinerate"] = $request->onlinerate;
        }

        if($request->inpersonrate){
            $data["inpersonrate"] = $request->inpersonrate;
        }

        if($request->id1==16 || $request->id1==17 || $request->id1==20 ){
            $data["is_approved"] = $request->id1;
        }


        $id = $request->id;
        $record = Users::where("id", $id)->first();
        if ($record) {



            if($request->id1==14 || $request->id1==16 || $request->id1==17 || $request->id1==20 || $request->id1==4 || $request->id1==21 ){
                $rubricExits = rubric::where("user_id", "=", $id)->get();
                if (count($rubricExits)) {


            $res = Users::where("id", $id)->update($data);
            if ($res) {

                if($request->id1==16 || $request->id1==17 || $request->id1==20){
                    $adminSession = session()->get('Adminnewlogin');
                    $adminType = $adminSession['type'];

                    if($adminType !=1 && $record->app_notification==1){
                    createCommanNotification($record,'3','Operations','Admin','user');
                    }
                }


                    ProfileStatusHistoryModel::insert([
                        "from_id" => session("Adminnewlogin")["id"],
                        "user_id" => $id,
                        "status" => $request->id1,
                        "note" => $request->note,
                        "created_at" => date("Y-m-d"),
                    ]);

                    if($request->id1==10){
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "6")->first();
                        $body =  $template->description;

                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);
                        $body = str_replace('{{note}}', $request->note, $body);
                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try {
                            //code...

                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                     }

                     if($request->id1==5){
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "9")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);

                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{
                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                     }

                     if($request->id1==21){
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "10")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);
                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{
                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                     }

                     if($request->id1==12){
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "12")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);
                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{
                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                     }

                     if($request->id1==13){
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "13")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);
                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{

                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                     }


                    return response()->json([
                        "success" => true,
                        "message" => "Status Successfully Changed",
                    ]);
                }
                }else{
                    return response()->json([
                        "success" => false,
                        "message" => "Enter rubric form before approve",
                    ]);
                }



               }else{
                $res = Users::where("id", $id)->update($data);
                if ($res) {



                ProfileStatusHistoryModel::insert([
                    "from_id" => session("Adminnewlogin")["id"],
                    "user_id" => $id,
                    "status" => $request->id1,
                    "note" => $request->note,
                    "created_at" => date("Y-m-d"),
                ]);

                if($request->id1==10){
                    $template = DB::table("tbl_email_templates")->where("email_template_id", "6")->first();
                    $body =  $template->description;
                    if($record->last_name){
                        $full_name=$record->first_name.' '.$record->last_name;
                    }else{
                        $full_name=$record->first_name;
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);
                    $body = str_replace('{{note}}', $request->note, $body);
                    $subject=$template->subject;
                    $email=$record->email;
                    $data=array('template'=>$body);
                    try{

                   Mail::send('template', $data, function (
                        $message
                    ) use ($email,$subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                 }

                 if($request->id1==5){
                    $template = DB::table("tbl_email_templates")->where("email_template_id", "9")->first();
                    $body =  $template->description;
                    if($record->last_name){
                        $full_name=$record->first_name.' '.$record->last_name;
                    }else{
                        $full_name=$record->first_name;
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);

                    $subject=$template->subject;
                    $email=$record->email;
                    $data=array('template'=>$body);
                    try{

                   Mail::send('template', $data, function (
                        $message
                    ) use ($email,$subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                 }

                 if($request->id1==21){
                    $template = DB::table("tbl_email_templates")->where("email_template_id", "10")->first();
                    $body =  $template->description;
                    if($record->last_name){
                        $full_name=$record->first_name.' '.$record->last_name;
                    }else{
                        $full_name=$record->first_name;
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);

                    $subject=$template->subject;
                    $email=$record->email;
                    $data=array('template'=>$body);
                    try{

                   Mail::send('template', $data, function (
                        $message
                    ) use ($email,$subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                 }

                 if($request->id1==12){
                    $template = DB::table("tbl_email_templates")->where("email_template_id", "12")->first();
                    $body =  $template->description;
                    if($record->last_name){
                        $full_name=$record->first_name.' '.$record->last_name;
                    }else{
                        $full_name=$record->first_name;
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);

                    $subject=$template->subject;
                    $email=$record->email;
                    $data=array('template'=>$body);
                    try{

                   Mail::send('template', $data, function (
                        $message
                    ) use ($email,$subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                 }

                 if($request->id1==13){
                    $template = DB::table("tbl_email_templates")->where("email_template_id", "13")->first();
                    $body =  $template->description;
                    if($record->last_name){
                        $full_name=$record->first_name.' '.$record->last_name;
                    }else{
                        $full_name=$record->first_name;
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);

                    $subject=$template->subject;
                    $email=$record->email;
                    $data=array('template'=>$body);
                    try{

                   Mail::send('template', $data, function (
                        $message
                    ) use ($email,$subject) {
                        $message->to($email)->subject($subject);
                    });
                } catch (\Throwable $th) {
                    //throw $th;
                }
                 }


                return response()->json([
                    "success" => true,
                    "message" => "Status Successfully Changed",
                ]);
            }else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went worng",
                ]);
            }
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Record not found",
            ]);
        }
    }

    public function addInterview(Request $request)
    {

        $id = $request->id;

        if ($id) {
            // $res = scheduledInterview::where("user_id", "=", $id)->delete();
            for ($i = 0; $i < count($_POST["date"]); $i++) {

                $data1["link"] = $request->link;
                $data1["date"] = $_POST["date"][$i];
                $data1["time"] = $_POST["time"][$i];
                $data1["end_time"] = $_POST["end_time"][$i];
                $data1["timezone"] = $_POST["timezone"][$i];

                $data1["user_id"] = $id;
                // $data1['reviewer_id'] = $id;
                if($_POST["scheduledid"][$i]){
                    scheduledInterview::where("id", $_POST["scheduledid"][$i])->update($data1);
                }else{
                    scheduledInterview::insertGetId($data1);
                }

            }

            $datap["profile_status"] = 11;
            Users::where("id", $id)->update($datap);
            $record = Users::where("id", $id)->first();
            $template = DB::table("tbl_email_templates")->where("email_template_id", "7")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);
                        $body = str_replace('{{note}}', $request->note, $body);
                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{

                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }

            return response()->json([
                "success" => true,
                "message" => "Details successfully saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function change_status_model(Request $request)
    {
        $id = $request->id;
        $data["userid"] = $request->userid;
        $data["upon"] = UponCompletionModel::where(["id" => $id])->first();
        return view("admin.application.statusmodel")->with($data);
    }

    public function showNoteHistory(Request $request)
    {
        $id = $request->id;
        $user_id = $request->userid;
        $data["upon"] = UserUponCompletionNoteModel::where([
            "up_com_id" => $id,
            "user_id" => $user_id,
        ])->get();
        return view("admin.application.nothistory")->with($data);
    }

    public function statusChange(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "status" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "note", "id", "userid"]);
        if ($request->status == 2 || $request->status == 0) {
            $data["note"] = encrypt($request->note);
        }
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $result = UponCompletionModel::where(["id" => $request->id])->update(
            $data
        );
        $result1 = BackgroundMedicalModel::where(["id" => $request->back_med_id])->update(
            [

                "status" => $request->status
            ]
        );




        UserUponCompletionNoteModel::insert([
            "up_com_id" => $request->id,
            "note" => $request->note,
            "user_id" => $request->userid,
            "status" => $request->status,
            "created_at" => date("Y-m-d"),
            "updated_at" => date("Y-m-d"),
        ]);
        $res = BackgroundMedicalModel::where("id",$request->back_med_id)->first();;
        $record = User::where("id",$res->application_id)->first();
        if($res->school){
            $school_name= institudeName($res->school);
         }else{
             $school_name='';
         }

         $link=url('/onboarding-details');
         $due_date='';
         if($res->type=='background_check'){
            if($request->status == 1){
                createUserNotification($record,'29','user','user','user',$school_name,$link,$due_date);

             }elseif($request->status == 2){
                createUserNotification($record,'30','user','user','user',$school_name,$link,$due_date);

             }
         }

         if($res->type=='medical_requirements'){
            if($request->status == 1){
                createUserNotification($record,'34','user','user','user',$school_name,$link,$due_date);

             }elseif($request->status == 2){
                createUserNotification($record,'35','user','user','user',$school_name,$link,$due_date);

             }
         }


        return response()->json([
            "success" => true,
            "message" => "Submitted successfully",
        ]);
    }

    public function showStatusModel(Request $request)
    {
        $data["status"] = $request->status;
        $data["userid"] = $request->userid;
        $data["pStatus"] = DB::table("tbl_profile_status")
            ->where("status_type", "Instructor")
            ->get();
            $data["user"] = Users::where("users.id",  $request->userid)
            ->first();
        return view("admin.application.showstatus")->with($data);
    }

    public function save_rubric(Request $request)
    {
        $id = $request->user_id;

        if ($id) {
            $data["user_id"] = $request->user_id;
            $data["notes"] = $request->notes;
            $data["overallrating"] = $request->overallrating;
            $data["proficiency"] = $request->proficiency;
            $data["energylevel"] = $request->energylevel;
            $data["communicaiton"] = $request->communicaiton;
            $data["simplify"] = $request->simplify;
            $data["engage"] = $request->engage;
            $data["interactiveclass"] = $request->interactiveclass;

            $data["connectstudents"] = $request->connectstudents;
            $data["patience"] = $request->patience;
            $data["learningobjectives"] = $request->learningobjectives;
            $data["empathy"] = $request->empathy;
            $data["analogies"] = $request->analogies;
            $data["classroommanagement"] = $request->classroommanagement;
            $data["talking"] = $request->talking;

            $data["created_at"] = date("Y-m-d H:i:s");
            $data["updated_at"] = date("Y-m-d H:i:s");

            $rubricExits = rubric::where("user_id", "=", $id)->get();
            if (count($rubricExits)) {
                $save = rubric::where("user_id", $id)->update($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Rubric successfully created",
                        "redirect" => url("/view-application"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            } else {
                $save = rubric::insertGetId($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Rubric successfully created",
                        "redirect" => url("/view-application"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    public function deleteinterview(Request $request)
    {
        $id = $request->id;
        if (isset($id)) {
            $record = scheduledInterview::where("id", $id)->first();
            if ($record) {
                $res = scheduledInterview::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function cancelinterview(Request $request)
    {
        $id = $request->id;
        if (isset($id)) {
            $record = user_interview_slots::where("user_id", $id)->first();
            if ($record) {
                $data["status"] = "2";
                $res = user_interview_slots::where("user_id", $id)->update($data);

                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully cancelled",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }


    public function requestform(Request $request)
    {

                $data["state"] = $request->state;
                $data["description"] = $request->description;
                $data["label"] = $request->form_type;
                 if($request->form_type!='Checkr'){
                    $data["file"] = $request->form;
                 }

                $data["instructions"] = $request->instructions;
                // $data["deadline"] = $request->deadline;
                $data["deadline"] =  date("Y-m-d", strtotime($request->deadline));
                $data["school"] = $request->school;
                $data["type"] = $request->app_type;
                $data["application_id"] =  $request->app_id;
                $data["program_id"] =  @$request->program_id??null;

                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = BackgroundMedicalModel::insertGetId($data);

                if ($save) {

                     if($request->app_type=='background_check'){
                        $record = User::where("id",  $request->app_id)->first();
                        if($request->form_type=='Checkr' && $request->package_type && $request->app_id){
                            $user_first_step = DB::table("tbl_user_work_authorizations")
                            ->where("user_id", $request->app_id)
                            ->first();

                            if(isset($user_first_step->state)){
                               $state=$user_first_step->state;
                               $zipcode=$user_first_step->zip_code;
                            }else{
                                $state='';
                                $zipcode='';
                            }

                            $work_locations=array(['country'=>"US"]);
                            $CandidateData = [
                                'first_name' => $record->first_name,
                                'middle_name' => '',
                                'last_name' => $record->last_name,
                                'email' => $record->email,
                                // 'zipcode' => $zipcode,
                                'work_locations' => $work_locations,

                            ];

                            $getStateCode = StatesTerritoriesModel::where("state_territory_name", $request->state)
                            ->first();

                            if($getStateCode){
                                $inwork_locations=array(['country'=>"US",'state'=>$getStateCode->state_territory_short_name]);
                            }else{
                                $inwork_locations=array(['country'=>"US",'state'=>"TX"]);
                            }

                            if($record->candidate_id){

                                $invitationsData = [
                                    'package' => $request->package_type,
                                    'candidate_id' => $record->candidate_id,
                                    'work_locations' => $inwork_locations

                                ];
                                $responseInvitations = $this->checkrService->createInvitations($invitationsData);


                                if($responseInvitations['id']){
                                    $bgdata['invitation_id'] = $responseInvitations['id'];
                                    $bgdata['invitation_link'] = $responseInvitations['invitation_url'];

                                   BackgroundMedicalModel::where("id",$save)->update($bgdata);

                                   }

                            }else{

                                $response = $this->checkrService->createCandidates($CandidateData);

                                  if($response['id']){
                                    $userdata['candidate_id'] = $response['id'];
                                    $res = Users::where("id",$request->app_id)->update($userdata);

                                    $invitationsData = [
                                        'package' => $request->package_type,
                                        'candidate_id' => $response['id'],
                                        'work_locations' => $inwork_locations

                                    ];

                                    $responseInvitations = $this->checkrService->createInvitations($invitationsData);


                                   if($responseInvitations['id']){
                                    $bgdata['invitation_id'] = $responseInvitations['id'];
                                    $bgdata['invitation_link'] = $responseInvitations['invitation_url'];

                                   BackgroundMedicalModel::where("id",$save)->update($bgdata);

                                   }

                               }

                            }

                        }

                        if($request->app_id){

                        $template = DB::table("tbl_email_templates")->where("email_template_id", "14")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);

                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{

                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                        if($request->school){
                            $school_name= institudeName($request->school);
                         }else{
                             $school_name='';
                         }
                         $link='onboarding-details';
                         $due_date=date('m-d-Y', strtotime($request->deadline));

                        createUserNotification($record,'26','user','user','user',$school_name,$link,$due_date);
                        }

                     }



                     if($request->app_type=='medical_requirements'){
                        if($request->app_id){
                        $record = Users::where("id",  $request->app_id)->first();
                        $template = DB::table("tbl_email_templates")->where("email_template_id", "15")->first();
                        $body =  $template->description;
                        if($record->last_name){
                            $full_name=$record->first_name.' '.$record->last_name;
                        }else{
                            $full_name=$record->first_name;
                        }

                        $body= str_replace('{{NAME}}', $full_name, $body);

                        $subject=$template->subject;
                        $email=$record->email;
                        $data=array('template'=>$body);
                        try{

                       Mail::send('template', $data, function (
                            $message
                        ) use ($email,$subject) {
                            $message->to($email)->subject($subject);
                        });
                    } catch (\Throwable $th) {
                        //throw $th;
                    }

                        if($request->school){
                            $school_name= institudeName($request->school);
                         }else{
                             $school_name='';
                         }
                         $link='onboarding-details';
                         $due_date=date('m-d-Y', strtotime($request->deadline));

                        createUserNotification($record,'31','user','user','user',$school_name,$link,$due_date);
                        }
                     }


                    return response()->json([
                        "success" => true,
                        "message" => "Request send successfully"
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }


    }


    public function submitFilter(Request $request){
        $q = Users::query();
        $q->select('users.*');

        if ($request->input('state'))
        {

           $q->where('state','like',$request->input('state'));
        }
        if ($request->input('city'))
        {
           $q->orWhere('city','like',$request->input('city'));
        }
        if ($request->input('subject'))
        {
            $q->join('tbl_user_teaching_preferences','tbl_user_teaching_preferences.user_id','=','users.id')->join('tbl_user_subjects','tbl_user_subjects.step_id','=','tbl_user_teaching_preferences.id')
             ->orWhere('tbl_user_subjects.subject','like',$request->input('subject'));
        }
        if ($request->input('certified_teacher'))
        {
            $q->join('tbl_user_experiences','tbl_user_experiences.user_id','=','users.id')
              ->orWhere('tbl_user_experiences.profile_type','like',$request->input('certified_teacher'));
        }
        if ($request->input('profile_status'))
        {
           $q->orWhere('profile_status','like',$request->input('profile_status'));
        }
        if($request->input('format')){

            $q->join('tbl_user_teaching_preferences as third','third.user_id','=','users.id');
                $str='';
                $i=1; // to append AND in query

                foreach ($request->input('format') as $key => $value) {
                $str .= 'FIND_IN_SET("'.$value.'" ,third.format)';
                if($i < count($request->input('format'))){
                    $str .=' OR '; // use OR as per use
                }
                $i++;
                }

                $q->whereRaw($str);
        }

        $q->where(["type" =>5]);
        $application = $q->orderBy('users.id','desc')->get();

        return view("admin.application.application_filter", compact("application"));
    }

    public function submitFilterins(Request $request){
        $q = Users::query();
        $q->select('users.*');

        if ($request->input('state'))
        {

           $q->where('state','like',$request->input('state'));
        }
        if ($request->input('city'))
        {
           $q->orWhere('city','like',$request->input('city'));
        }
        if ($request->filled('subject')) {
            $q->join('tbl_user_teaching_preferences', 'tbl_user_teaching_preferences.user_id', '=', 'users.id')
                ->join('tbl_user_subjects', 'tbl_user_subjects.step_id', '=', 'tbl_user_teaching_preferences.id')
                ->where('tbl_user_subjects.subject', trim($request->subject));

            if ($request->filled('sub_subject')) {
                $q->where('tbl_user_subjects.sub_subject', trim($request->sub_subject));
            }
        }
        if ($request->input('certified_teacher'))
        {
            $q->join('tbl_user_experiences','tbl_user_experiences.user_id','=','users.id')
              ->orWhere('tbl_user_experiences.profile_type','like',$request->input('certified_teacher'));
        }
        if ($request->input('profile_status'))
        {
           $q->orWhere('profile_status','like',$request->input('profile_status'));
        }
        if($request->input('format')){

            $q->join('tbl_user_teaching_preferences as third','third.user_id','=','users.id');
                $str='';
                $i=1; // to append AND in query

                foreach ($request->input('format') as $key => $value) {
                $str .= 'FIND_IN_SET("'.$value.'" ,third.format)';
                if($i < count($request->input('format'))){
                    $str .=' OR '; // use OR as per use
                }
                $i++;
                }

                $q->whereRaw($str);
        }

        $q->where(["type" =>5]);
        $admin = $q->orderBy('users.id','desc')->get();

        return view("admin.instructor.application_filter", compact("admin"));
    }

    public function saveassub(Request $request)
    {

        $id = $request->application_id;
        if (isset($id)) {

                $data[$request->name] = $request->value;
                $res = Users::where("id", $id)->update($data);

                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully saved",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }

        }
    }


    public function getratemodel(Request $request)
    {

        $data["userid"] = $request->id;

            $data["user"] = Users::where("id",  $request->id)
            ->first();
        return view("admin.application.showratemodel")->with($data);
    }

    public function update_payrate(Request $request)
    {

        if($request->onlinerate){
            $data["onlinerate"] = $request->onlinerate;
        }

        if($request->inpersonrate){
            $data["inpersonrate"] = $request->inpersonrate;
        }


        $id = $request->userid;
        $record = Users::where("id", $id)->first();
        if ($record) {
            $res = Users::where("id", $id)->update($data);
            if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully updated",
                    ]);
                }
               }else{
                return response()->json([
                    "success" => false,
                    "message" => "Something went worng",
                ]);

            }
       }

       public function get_assign_app_model(Request $request)
       {
           $type = $request->type;
           $data["app_id"]=$request->app_id;
           $data["title"]=$request->titleName;
           $data["users"] = Users::where(["type" =>$type,"status" => "1"])->get();
           return view("admin.application.assign_model")->with($data);
       }

       public function save_assign(Request $request)
{

    $data["application_id"]=$request->app_id;
    $data["user_id"]=$request->user_id;
    $data["type"]=$request->type;
    $data["status"]='1';
    $record = invite_application_recruiter::where("application_id", $request->app_id)->first();
            if($record) {
                $udata["status"]='1';
                $udata["user_id"]=$request->user_id;
                $save= invite_application_recruiter::where("application_id", $request->app_id)->update($udata);
            }else{
                $save=invite_application_recruiter::insertGetId($data);
            }


            if($save){
            return response()->json([
                "success" => true,
                "message" => "Successfully assigned"
            ]);
        }
}

public function get_user_admin(){
    $id = session("Adminnewlogin")["id"];
    $condition = ["id" => $id];
    $userData = Users::get_single_record($condition);
    return json_encode(['email'=>$userData->email,'password'=>'123456']);
   }

   public function export(Request $request)
    {

        try {
            ob_end_clean();
            ob_start();
            $fileName = 'applications-'.time().'.xlsx';
            return Excel::download(new ExportApplications($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}


