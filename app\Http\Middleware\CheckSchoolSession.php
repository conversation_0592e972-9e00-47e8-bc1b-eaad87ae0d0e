<?php
namespace App\Http\Middleware;
use Closure;
use Auth;
class CheckSchoolSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
       
        if(!session('schoolloginsession'))
        { 
            return redirect('/schools'); 
        }
     
        return $next($request);
    }
}
