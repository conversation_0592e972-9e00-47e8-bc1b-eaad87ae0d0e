<?php

namespace App\Http\Controllers\Admin;

use App\certifications;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProgramCertificateRequest;
use App\ProgramCertificate;
use App\Programs;
use App\StateModel;
use Illuminate\Http\Request;


class ProgramCertificateController extends Controller
{


    public function add(Request $request)
    {
        $states = StateModel::where(["country_id" => "239"])->pluck('name');
        $certifications = certifications::pluck('name');
        $view = view("components.admin.add-program-certificates", compact('states','certifications'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function store(Programs $program, ProgramCertificateRequest $request)
    {
        $program->certificates()->delete();
        foreach ($request->certificate as $index => $certificate) {
            $obj = new ProgramCertificate();
            $obj->program_id = $program->id;
            $obj->certificate = $certificate;
            $obj->state = $request->state[$index];
            $obj->save();
        }

        return response()->json(['status' => true, 'message' => "Certificates saved successfully", 'reload' => true]);
    }
}
