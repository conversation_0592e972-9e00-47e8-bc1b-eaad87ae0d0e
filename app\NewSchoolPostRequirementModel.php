<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewSchoolPostRequirementModel extends Model
{
    use SoftDeletes;
    protected $table = 'new_school_post_requirement';
    protected $fillable = [
        'position_title',
        'school_id',
        'requirement', 
        'delivery_mode', 
        'address', 
        'position', 
        'instructor_language', 
        'states_id', 
        'certifications', 
        'grade_level_id', 
        'subject',
        'total_program_duration',
        'position_start_date',
        'position_end_date',
        'schedule',
        'schedule_start_time',
        'schedule_end_time',
        'job_description',
        'compensation',
        'per_hour',
        'benefits',
        'status',
    ];

    public function school()
    {
        return $this->belongsTo(User::class, 'school_id');
    }
}
