<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEnumColumnOnPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            //
        });
        DB::statement("ALTER TABLE platform_school_requirements MODIFY delivery_mode ENUM('online', 'in-person', 'hybrid') NOT NULL COMMENT 'Mode of delivery for the requirement';");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            //
        });
        DB::statement("ALTER TABLE platform_school_requirements MODIFY delivery_mode ENUM('online', 'in-person') NOT NULL COMMENT 'Mode of delivery for the requirement';");
    }
}
