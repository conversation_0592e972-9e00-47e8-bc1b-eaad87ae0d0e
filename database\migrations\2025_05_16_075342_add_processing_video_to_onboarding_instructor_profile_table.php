<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProcessingVideoToOnboardingInstructorProfileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('onboarding_instructor_profile', function (Blueprint $table) {
            $table->boolean('processing_video')->default(false)->after('video'); // Adjust position
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('onboarding_instructor_profile', function (Blueprint $table) {
            $table->dropColumn('processing_video');
        });
    }
}
