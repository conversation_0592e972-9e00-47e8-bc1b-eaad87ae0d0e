<?php

namespace App\Http\Controllers\WEB;

use App\Http\Controllers\Controller;
use App\{User, Programs, invite_programs, ProgramNote, ProgramNoteAmount, ProgramNoteStudent, MailModel, RosterModel, ProgramAdminNote, ReviewModel, Logistics, FeedbackModel, SettingTermsModel, sub_subjects};
use App\Helpers\DataTableHelper;
use App\Http\Requests\Front\ProgramClassRequest;
use Illuminate\Http\Request;
use stdClass;
use \Illuminate\Support\Str;

class ProgramDetailsController extends Controller
{
    public function index($encryptedId, Request $request)
    {
        $user = auth()->user();
        $instructorType = null;
        if (checkAuth($user->status)) {
            return redirect('/logout');
        }
        if ($user->profile_status == 12) {
            $id = decrypt($encryptedId);

            $program = Programs::query()
                ->with('school', 'creator')
                ->findOrFail($id);

            $invite_program = new stdClass();
            if ($request->filled('pivot_id')) {
                $invite_program = invite_programs::find($request->pivot_id);
            }

            if ($program->mainAssignedUser && ($user->id == $program->mainAssignedUser->id)) {

                $instructorType = 'Main Instructor';
            } elseif ($program->userNotes && in_array($user->id, $program->userNotes->pluck('sub_user_id')->toArray())) {

                $instructorType = 'Sub Instructor';
            }
            $currentDate = now()->toDateString();

            $requestSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                ->where('user_id', $user->id)
                ->whereNull(['note', 'status', 'sub_user_id'])
                ->exists();

            $cancelSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                ->where('user_id', $user->id)
                ->whereNull(['note', 'status'])
                ->where(function ($query) {
                    $query->whereNotNull('sub_user_id')
                        ->orWhereIn('user_sub_requested', [0, 3]);
                })
                ->exists();
            $isStandByOnly = true;


            $isStandBy = invite_programs::where('user_id', $user->id)
                ->where('program_id', $id)
                ->where('is_standby', 0)
                ->where('status', 1)
                ->where('is_approved', 1)
                ->exists();

            if (!$isStandBy) {
                $isStandByOnly = true;
            } else {
                $isStandByOnly = false;
            }

            return view('web.user.program.programdetail', compact('program', 'invite_program', 'instructorType', 'requestSub', 'cancelSub', 'isStandByOnly'));
        }
    }


    public function inviteprogramdetails($encryptedId, Request $request)
    {
        $user = auth()->user();
        $instructorType = null;
        if (checkAuth($user->status)) {
            return redirect('/logout');
        }
        if ($user->profile_status == 12) {
            $id = decrypt($encryptedId);

            $program = Programs::query()
                ->with('school', 'creator')
                ->findOrFail($id);

            $invite_program = new stdClass();
            if ($request->filled('pivot_id')) {
                $invite_program = invite_programs::find($request->pivot_id);
            }

            if ($program->mainAssignedUser && ($user->id == $program->mainAssignedUser->id)) {

                $instructorType = 'Main Instructor';
            } elseif ($program->userNotes && in_array($user->id, $program->userNotes->pluck('sub_user_id')->toArray())) {

                $instructorType = 'Sub Instructor';
            }
            $currentDate = now()->toDateString();

            $requestSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                ->where('user_id', $user->id)
                ->whereNull(['note', 'status', 'sub_user_id'])
                ->exists();

            $cancelSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                ->where('user_id', $user->id)
                ->whereNull(['note', 'status'])
                ->where(function ($query) {
                    $query->whereNotNull('sub_user_id')
                        ->orWhereIn('user_sub_requested', [0, 3]);
                })
                ->exists();
            $isStandByOnly = true;


            $isStandBy = invite_programs::where('user_id', $user->id)
                ->where('program_id', $id)
                ->where('is_standby', 0)
                ->where('status', 1)
                ->where('is_approved', 1)
                ->exists();

            if (!$isStandBy) {
                $isStandByOnly = true;
            } else {
                $isStandByOnly = false;
            }

            return view('web.user.program.inviteprogramdetail', compact('program', 'invite_program', 'instructorType', 'requestSub', 'cancelSub', 'isStandByOnly'));
        }
    }

    public function list(Programs $program, Request $request)
    {
        $currentDate = now()->toDateString();
        $user = auth()->user();
        $invite_programs = $request->invite_programs;
        $params = DataTableHelper::getParams($request);
        $qry = $program
            ->userNotes()
            ->where('user_id', $user->id)
            ->orWhere('sub_user_id', $user->id)
            ->where('program_id', $program->id);
        [$count, $result] = DataTableHelper::applyPagination($qry->orderBy('class_date'), $params['row'], $params['rowperpage']);
        $data = [];
        $i = 1;
        $javascriptVoid = 'javascript:void(0);';
        foreach ($result as $row) {
            if($row->status == '4') continue;
            $rowscount = ProgramNoteStudent::where('program_id', '=', $program->id)
                ->where('program_note_id', '=', $row->id)
                ->get();
            $action = '<div class="d-flex">';
            $editRoute = route('user.program-list.edit-notes', ['programNote' => $row->id, 'invite_programs' => $invite_programs]);
            $editmRoute = route('user.program-list.edit-attendance-notes', ['programNote' => $row->id, 'invite_programs' => $invite_programs]);
            $editOption = false;
            $noteOption = '';
            if ($row->status != '0' && $row->status != '2' && $row->status != '4' && is_null($row->note)) {
                if ($currentDate >= $row->class_date->toDateString()) {
                    $isUserMatch = ($user->id == $row->user_id && is_null($row->sub_user_id)) || ($user->id == $row->sub_user_id);
                    $btnAttr = $btnClass = $noteBtnClick = $attendanceBtnClick = '';
                    if (!$isUserMatch) {
                        $btnAttr = 'disabled';
                        $btnClass = 'cursor-not-allowed';
                    } else {
                        $noteBtnClick = "onclick=\"openCommanModal('{$editRoute}')\"";
                        $attendanceBtnClick = "onclick=\"openCommanModal('{$editmRoute}')\"";
                    }
                    $noteOption .= "<button type='button' href='{$javascriptVoid}' class='btn btn-primary {$btnClass}' title='Enter Note' {$btnAttr} {$noteBtnClick}>
                        <span class='text-white'>
                            Submit Class Log
                        </span>
                    </button>";
                    $action .= "&nbsp;<button type='button' href='{$javascriptVoid}' class='btn btn-primary {$btnClass}' title='Mark Attendance'   {$btnAttr} {$attendanceBtnClick}>
                        <span class='text-white'>
                            Mark Attendance
                        </span>
                    </button>";
                } else {
                    $action .= '<span>
                    <button type="button" class="btn btn-primary grey">
                    <i class="fa fa-spinner fa-spin save-loader" style="display: none;"></i>
                    Upcoming Class
                    </button>
                    </span>';
                }
            } elseif ($row->status == '2') {
                $action .= 'Holiday';
            } elseif ($row->status == '1' || !is_null($row->note)) {
                $isUserMatch = ($user->id == $row->user_id && is_null($row->sub_user_id)) || ($user->id == $row->sub_user_id);
                    $btnAttr = $btnClass = $noteBtnClick = $attendanceBtnClick = '';
                    if (!$isUserMatch) {

                        $btnAttr = 'disabled';
                        $btnClass = 'cursor-not-allowed';

                    } else {
                        $noteBtnClick = "onclick=\"openCommanModal('{$editRoute}')\"";
                        $attendanceBtnClick = "onclick=\"openCommanModal('{$editmRoute}')\"";
                    }
                if ($row->status == '1') {
                    $action .= "<button disabled class='btn btn-primary cursor-not-allowed' title='Enter Note' type='button'>
                <span class='text-white'>
                Completed
                </span>
                </button>";
                }
                if ($row->status != '1') {
                //     $action .= "&nbsp;<a href='{$javascriptVoid}' {$btnAttr} class='btn btn-primary {$btnClass}' title='Mark Attendance' onclick=\"openCommanModal('{$editmRoute}')\">
                // <span class='text-white'>
                // Mark Attendance
                // </span>
                // </a>"
                $action .= "&nbsp;<button type='button' href='{$javascriptVoid}' class='btn btn-primary {$btnClass}' title='Mark Attendance'   {$btnAttr} {$attendanceBtnClick}>
                <span class='text-white'>
                    Mark Attendance
                </span>
            </button>";;
                }
            } elseif ($row->status == '0') {
                $action .= 'Cancelled';
            } elseif ($row->status == '4') {
                $action .= 'Removed';
            } elseif ($row->status == '3') {
                $isUserMatch = ($user->id == $row->user_id && is_null($row->sub_user_id)) || ($user->id == $row->sub_user_id);
                $btnAttr = $btnClass = $noteBtnClick = $attendanceBtnClick = '';
                if (!$isUserMatch) {
                    $btnAttr = 'disabled';
                    $btnClass = 'cursor-not-allowed';
                } else {
                    $noteBtnClick = "onclick=\"openCommanModal('{$editRoute}')\"";
                    $attendanceBtnClick = "onclick=\"openCommanModal('{$editmRoute}')\"";
                }
                $action = "&nbsp;<button type='button' class='btn btn-primary {$btnClass}' title='Mark Attendance'   {$btnAttr} {$attendanceBtnClick}>
                    <span class='text-white'>
                        Mark Attendance
                    </span>
                </button>";


            }
             elseif (is_null($row->status) && $row->class_date < $currentDate) {
                $action .= 'Missed';
            }
            $action .= '</div>';
            $mainuser = $row->user ? $row->user->first_name . ' ' . $row->user->last_name : '';
            $fullname = '';
            $hideclass = '';
            if ($row->subUser && $row->user_sub_requested != '2') {
                $fullname  = '' . $row->subUser->first_name . ' ' . $row->subUser->last_name;
                if ($row->user_sub_requested == '1') {
                    $fullname =  $fullname . '(Sub Requested)';
                } elseif ($row->user_sub_requested == '2') {
                    $fullname = $fullname . '(Cancel Sub Requested)';
                } elseif ($row->user_sub_requested == '3') {
                    $fullname = $fullname . '(Declined Cancel Sub Request)';
                }
            } else {
                if ($row->sub_user_id !=  $user->id) {
                    $hideclass = 1;
                }
                if ($row->subUser) {
                    $fullname  = '' . $row->subUser->first_name . ' ' . $row->subUser->last_name;
                } else {
                    $fullname = $mainuser;
                }
                if ($row->sub_user_id !=  $user->id) {
                    if ($row->user_sub_requested == '1') {
                        $fullname =  $fullname . '(Sub Requested)';
                    } elseif ($row->user_sub_requested == '2') {
                        $fullname = $fullname . '(Cancel Sub Requested)';
                    } elseif ($row->user_sub_requested == '3') {
                        $fullname = $fullname . '(Declined Cancel Sub Request)';
                    } else {
                        $fullname = $mainuser;
                    }
                }
            }
            $input = '';
            // dd($row->note,$row->status,$currentDate <= $row->class_date->toDateString(),$row->user_id,$user->id,$row->sub_user_id);
            if (is_null($row->note) && is_null($row->status) && $currentDate <= $row->class_date->toDateString() && ($row->user_id == $user->id || $row->sub_user_id == $user->id )) {
                $input .= "<input type='checkbox' onchange='syncCheckboxes(this)' form='requestSubForm' value='{$row->id}' name='program_note_id[]'
            class='program_note_id'> <input type='checkbox'  form='cancelSubForm' value='{$row->id}' name='program_note_id[]'
            class='program_note_id' style='display:none;'>";
            }
            $startTime = date('h:i A', strtotime($row->start_time));
            $endTime = date('h:i A', strtotime($row->end_time));
            if ($row->user_id != $user->id && $hideclass == "1") {
            } else {
                if (!is_null($row->parent_id)) {
                    $type = 'Makeup class';
                } else {
                    if ($row->sub_user_id == $user->id) {
                        $type = 'Substitute class';
                    } else {
                        $type = 'Regular Class';
                    }
                }
                // $startTime = '<time-converter date='.$row->class_date.' set-time='.$startTime.' format="hh:mm A"></time-converter>';
                // $endTime = '<time-converter date='.$row->class_date.' set-time='.$endTime.' format="hh:mm A"></time-converter>';

                $data[] = [
                    'id' => $i,
                    'class_date' => '<label>' . $input . date('m-d-Y', strtotime($row->class_date)) . '</label>',
                    'class_time' => $startTime . '-' . $endTime,
                    'user_id' => $fullname,
                    'attendance' => count($rowscount),
                    'rating' => $row->rating,
                    'content_taught' => str::limit($row->content_taught, 10),
                    'note' => str::limit($row->note, 100) ?? $noteOption,
                    'class_type' => $type,
                    'action' => $action,
                ];
                $i++;
            }
        }
        return DataTableHelper::generateResponse($params['draw'], $count, $data);
    }

    public function getCompletedClasses(Programs $program)
    {
        $completedClasses = $program->userNotes()->whereNotNull(['note', 'attendance', 'status'])->get();
        foreach($completedClasses as $row){
            $row->startTime = date('h:i A', strtotime($row->start_time));
            $row->endTime = date('h:i A', strtotime($row->end_time));
        }
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        return response()->json([
            'completedClasses' => $completedClasses,
            'timezone' => 'Timezone - ' . $timezone,
        ]);
    }

    public function getNotesForm(Programs $program, Request $request)
    {
        $invite_program = new stdClass();
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view('components.modals.notes-for-program', compact('program', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeNotesForm(Programs $program, ProgramClassRequest $request)
    {
        $user = auth()->user();
        $user_id = $user->id;

        $program_id = $program->id;

        $day = date('N');
        $payData = generateHourlyPaymentData($user, $program, $day);

        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }

        $obj = new ProgramNote();
        $obj->fill($request->only('attendance', 'rating', 'content_taught', 'note'));

        $obj->user_id = $user_id;
        $obj->program_id = $program_id;

        $obj->class_date = now()->toDateString();
        $obj->day = $day;
        $obj->start_time = $payData['start_time'];
        $obj->end_time = $payData['end_time'];
        $obj->save();

        if (!empty($payData)) {
            $programNoteAmount = new ProgramNoteAmount();
            $programNoteAmount->program_id = $program_id;
            $programNoteAmount->user_id = $user_id;
            $programNoteAmount->program_note_id = $obj->id;
            $programNoteAmount->hours = $payData['hours'];
            $programNoteAmount->rate = $payData['rate'];
            $programNoteAmount->minutes = $payData['minutes'];
            $programNoteAmount->format = $payData['format'];
            $programNoteAmount->type = getInstructorType(@$invite_program->type ?? '');
            $programNoteAmount->amount = $payData['amount'];
            $programNoteAmount->save();

            /* $students = $request->student;
            $class_ids = $request->class_id;
            foreach ($students as $key => $student) {
                $programNoteStudent = new ProgramNoteStudent();
                $programNoteStudent->program_id = $program_id;
                $programNoteStudent->user_id = $user_id;
                $programNoteStudent->program_note_id = $obj->id;
                $programNoteStudent->student = $student;
                $programNoteStudent->class_id = $class_ids[$key];
                $programNoteStudent->save();
            } */
        }

        return response()->json(['status' => true, 'message' => 'Note saved successfully', 'resetForm' => true]);
    }

    public function getSchoolBydistrict($id)
    {
        $values = User::where('district', $id)->where('type', 6)->pluck('full_name', 'id');
        $view = view('components.options', compact('values'))->render();

        return response()->json([
            'status' => true,
            'view' => $view,
        ]);
    }
    public function addStudent(Programs $program)
    {
        $program->load('classes');
        $classes = $program->classes;
        $view = view('components.add-student', compact('classes'))->render();
        return response()->json(['view' => $view], 200);
    }

    public function editNotesForm(ProgramNote $programNote, Request $request)
    {
        $invite_program = new stdClass();
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view('components.modals.edit-notes-for-program', compact('programNote', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNotesForm(ProgramNote $programNote, ProgramClassRequest $request)
    {
        if ($programNote->status == '0') {
            return response()->json(['status' => false, 'message' => 'Class cancelled', 'resetForm' => true]);
        }
        $editor = auth()->user();
        $user = $programNote->user;
        $user_id = $programNote->user_id;

        $program = $programNote->program;
        $program_id = $programNote->program_id;
        $currentdatetime = date('Y-m-d H:i:s');
        if ($program->school_name) {
            $school_name = institudeName($program->school_name);
        } else {
            $school_name = '';
        }

        $day = $programNote->day;

        $payData = generateHourlyPaymentData($user ?? $editor, $program, $day, $programNote->start_time, $programNote->end_time);

        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }

        $programNote->fill($request->only('attendance', 'rating', 'content_taught', 'note'));
        $programNote->entered_by = $editor->id;
        if ($file = $request->file('file')) {
            $name = time() . '.' . $file->getClientOriginalExtension();
            $filename = 'uploads/classdocument/' . $name;
            uploads3image($filename, $file);
            $programNote->document = $name;
        }
        $programNote->status = 3;
        $programNote->completed_class_log_timestamp = $currentdatetime;
        $programNote->save();

        if (!empty($payData)) {
        }
        $currentDate = (new \DateTime())->format('m-d-Y');

        createInstructorNotesSubmitNotification($user ?? $editor ,$currentDate,$program, $school_name);
        if($request->rating == 'ok' || $request->rating == 'bad'){
            sendNotificationWhenReviewOkorbad();
        }

        return response()->json(['status' => true, 'message' => 'Note saved successfully', 'resetForm' => true]);
    }

    public function getNoticeForm(Request $request)
    {
        $view = view('components.modals.notice')->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNoticeForm(Request $request)
    {
        $request->validate([
            'reason' => 'required',
        ]);

        $user = auth()->user();

        $record = MailModel::where('created_by', $user->id)
            ->where('f_type', 'Notice')
            ->first();
        if ($record) {
            return response()->json(['status' => false, 'message' => 'Already Appiled', 'resetForm' => true]);
        } else {
            $obj = new MailModel();

            $obj->user_id = $user->id;
            $obj->title = 'Notice';
            $obj->f_type = 'Notice';
            $obj->subject = 'Notice';
            $obj->created_by = $user->id;
            $obj->message = $request->reason;
            $obj->save();

            createDeleteOrdisableNotification($user->id, 'user-notice', 'Operations', 'Admin', $request->reason);
            return response()->json(['status' => true, 'message' => 'Notice sent successfully', 'resetForm' => true]);
        }
    }

    public function editNotesattendanceForm(ProgramNote $programNote, Request $request)
    {
        $invite_program = new stdClass();
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }
        $validRatings = ProgramNote::$validRatings;
        $view = view('components.modals.edit-attendancenotes-for-program', compact('programNote', 'validRatings', 'invite_program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updateNotesattendanceForm(ProgramNote $programNote, Request $request)
    {
        if (!$request->filled('student')) {
            // return response()->json(['status' => false, 'message' => "Please select at least one student", "resetForm" => false]);
        }

        $editor = auth()->user();
        $user = $programNote->user;
        $user_id = $programNote->user_id;

        $program = $programNote->program;
        $program_id = $programNote->program_id;
        $students = $request->student;
        $userID = $request->user_id;


        $row = ProgramNoteStudent::where('program_id', '=', $program_id)
            ->where('program_note_id', '=', $programNote->id)
            ->where('user_id', '=', $user_id)
            ->where('student', '=', $userID)
            ->first();

        if (isset($row->student)) {
            $row = ProgramNoteStudent::where('program_id', '=', $program_id)
                ->where('program_note_id', '=', $programNote->id)
                ->where('user_id', '=', $user_id)
                ->where('student', '=', $userID)
                ->delete();
        } else {
            $programNoteStudent = new ProgramNoteStudent();
            $programNoteStudent->program_id = $program_id;
            $programNoteStudent->user_id = $user_id;
            $programNoteStudent->program_note_id = $programNote->id;
            $programNoteStudent->student = $userID;
            $programNoteStudent->save();
        }

        return response()->json(['status' => true, 'message' => 'Attendance saved successfully', 'resetForm' => false]);
    }

    public function studentlist($program, $programNote, Request $request)
    {
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName'] = 'program_rosters.id';
            }

            $qry = RosterModel::select('program_rosters.*')
                ->where('program_id', '=', $program)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {
                $viewButton = '';
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);
                $rows = ProgramNoteStudent::where('program_id', '=', $program)
                    ->where('program_note_id', '=', $programNote)
                    ->where('student', '=', $row->id)
                    ->first();

                if ($rows) {
                    $viewButton .= "<input type='checkbox' form='MarkAttendance' onchange='savemark({$row->id})'   value='{$row->id}'  name='student[]' checked='checked'> ";
                } else {
                    $viewButton .= "<input type='checkbox' form='MarkAttendance' onchange='savemark({$row->id})'   value='{$row->id}'  name='student[]'> ";
                }
                $data[] = [
                    'id' => $row->id,
                    'student_name' => $row->student_name,
                    'class_id' => $row->class_id,
                    'action' => $viewButton,
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }


    public function getTabData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $tab = $request->tab;
        $program_id = $request->program_id;
        $ProgramAdminNote = ProgramAdminNote::where('program_id', $program_id)->first();
        $currentDate = now()->toDateString();
        $requestSub = array();
        $cancelSub = array();
        $invite_program = new stdClass();
        if ($request->filled('pivot_id')) {
            $invite_program = invite_programs::find($request->pivot_id);
        }

        $program = Programs::where("id", $program_id)->first();
        $school_id = $program->school_name;
        if ($program->delivery_type == 'Online') {
            $doc = SettingTermsModel::where("id", '12')->first();
            $classroomprerequisites = $doc->description;
            if ($school_id) {
                $classroomprerequisites = str_replace('{{school_name}}', schoolusername($school_id), $classroomprerequisites);
            }
        } elseif ($program->delivery_type == 'In-Person') {
            $doc = SettingTermsModel::where("id", '13')->first();
            $classroomprerequisites = $doc->description;
            if ($school_id) {

                $classroomprerequisites = str_replace('{{school_name}}', schoolusername($school_id), $classroomprerequisites);
            }
        } else {
            $doc = array();
            $classroomprerequisites = '';
        }

        if ($program->sub_subject_id) {
            $res = sub_subjects::where("id", $program->sub_subject_id)->first();
            $subname = $res->name;
            if ($program->delivery_type == 'Online') {
                $course_prerequisites = $res->online_doc;
            } elseif ($program->delivery_type == 'In-Person') {
                $course_prerequisites = $res->inperson_doc;
                // taking online as default since the field is removed from UI
                $course_prerequisites = $res->online_doc;
            } else {

                $course_prerequisites = '';
            }
        } else {

            $course_prerequisites = '';
        }


        $logistics = Logistics::where('program_id', $program_id)->first();


        $notesId = array();


        switch ($tab) {

            case 'overview':

                if ($request->filled('invite_program')) {
                    $invite = invite_programs::with('notes.programNote')->find($request->invite_program);
                    $notesId = $invite->notes->pluck('program_note_id')->toArray();
                }


                $program = Programs::where('id', $program_id)
                    ->first();

                break;

                case 'inviteoverview':

                    if ($request->filled('invite_program')) {
                        $invite = invite_programs::with('notes.programNote')->find($request->invite_program);
                        $notesId = $invite->notes->pluck('program_note_id')->toArray();
                    }


                    $program = Programs::where('id', $program_id)
                        ->first();

                 break;
            case 'notesandinstructions':

                $program = Programs::where('id', $program_id)
                    ->first();

                break;
            case 'logistics':
                $program = Programs::where('id', $program_id)
                    ->first();

                break;

            case 'classes':
                $program = Programs::where('id', $program_id)
                    ->first();


                $requestSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                    ->whereNull(['note', 'status'])
                    ->where(function ($query) use ($user) {
                        $query->where('sub_user_id', $user->id)
                            ->orWhere('user_id', $user->id);
                        })->exists();

                $cancelSub = $program->userNotes()->where('class_date', '>=', $currentDate)
                    ->where(function ($query) use ($user) {
                        $query->where('sub_user_id', $user->id)
                            ->orWhere('user_id', $user->id);
                    })
                    ->whereNull(['note', 'status'])
                    ->where(function ($query) {
                        $query->where('user_sub_requested', [1]);
                    })->exists();
                break;

            case 'reviews':

                $program = Programs::where('id', $program_id)
                    ->first();


                break;

            case 'roster':

                $program = Programs::where('id', $program_id)
                    ->first();


                break;

            default:
                return response()->json(['status' => false, 'message' => 'Invalid tab.']);
        }


        $view = view("web.user.program.tabs.{$tab}", compact('program', 'requestSub', 'cancelSub', 'invite_program', 'ProgramAdminNote', 'logistics', 'classroomprerequisites', 'course_prerequisites', 'doc', 'notesId'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getReviewData(Request $request)
    {

        $program = $request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = ReviewModel::where('program_id', '=', $program)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $data[] = [
                    'id' => $row->id,
                    'review' => $row->review,
                    'rating' => $row->rating,
                    'created_at' => getUserTimestamp($row->created_at)
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }



    public function getReveiwForm($encryptedprogramidId, Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $ins_id = $user_id;
        $programid = decrypt_str($encryptedprogramidId);

        $view = view("components.modals.review", compact('ins_id', 'programid'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function getFeedbackData(Request $request)
    {
        $user = auth()->user();
        $user_id = $user->id;
        $program = $request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = FeedbackModel::where('program_id', '=', $program)->where('user_id', '=', $user_id)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $data[] = [
                    'id' => $row->id,
                    'feedback' => $row->feedback,
                    'rating' => $row->rating,
                    'created_at' => getUserTimestamp($row->created_at)
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }

    public function addprogramfeedback(Request $request)
    {
        $request->validate(
            [
                // 'rating' => 'required',
                'feedback' => 'required',

            ]
        );


        $user = auth()->user();

        $program_id = decrypt_str($request->program_id);

        $obj = new FeedbackModel();

        $obj->user_id = $user->id;
        $obj->rating = $request->rating ? $request->rating : 0;
        $obj->feedback = $request->feedback;
        $obj->program_id = $program_id;
        $obj->save();
        return response()->json(['status' => true, 'message' => "Saved successfully", "resetForm" => true]);
    }
    public function addreview(Request $request)
    {
        $request->validate(
            [
                // 'rating' => 'required',
                'feedback' => 'required',

            ]
        );


        $user = auth()->user();
        $ins_id = decrypt_str($request->to_id);
        $program_id = decrypt_str($request->program_id);

        $obj = new ReviewModel();

        $obj->from_id = $ins_id;
        $obj->rating = $request->rating ? $request->rating : 0;
        $obj->review = $request->feedback;
        $obj->program_id = $program_id;
        $obj->save();
        return response()->json(['status' => true, 'message' => "Saved successfully", "resetForm" => true]);
    }

    public function getRosterData(Request $request)
    {

        $program = $request->program;
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);
            $params['columnName'] = 'id';

            // if ($params['columnName'] == 'id' || empty($params['columnName'])) {
            //     $params['columnName'] = 'id';
            // }

            $qry = RosterModel::select('program_rosters.*')
                ->where('program_id', '=', $program)
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $data[] = [

                    'class_id' => $row->class_id,
                    'student_name' => $row->student_name
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }
}
