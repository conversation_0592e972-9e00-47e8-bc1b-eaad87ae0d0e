<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Resources;

use Hash;
use Mail;
use Crypt;
use App\CommomModel;
DB::enableQueryLog();

class ResourcesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'resources','view')!=true){
            return redirect("/no-permission");
        } 
        $resources = Resources::orderBy("id", "desc")->get();
        return view("admin.resources.index", compact("resources"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function add()
    {
        return view("admin.resources.add");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function save(Request $request)
    {
       
        $type = $request->type;

        $data["type"] = $request->resource_type;
        $data["file_type"] = $request->type;
        $data["title"] = $request->title;
        $data["is_downloadable"] = $request->downloadable;
        
        $data["description"] = $request->description;
       
        if ($request->hasfile("file") && $request->type != "link") {
            $image = $request->file("file");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $logopic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/resources/");
            // $file->move($destinationPath, $logopic);
            $filename = 'uploads/resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["file"] = $filename;
        } else {
            $data["file"] = $request->file;
        }

        if ($request->hasfile("thumbnail")) {
            $image = $request->file("thumbnail");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $thumbnailpic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/resources/");
            // $file->move($destinationPath, $thumbnailpic);
            $filename = 'uploads/resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["thumbnail"] = $filename;
        }

        $data["status"] = "1";
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $save = Resources::insertGetId($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Resources  successfully created",
                "redirect" => url("/manage-training-video"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $resources = Resources::where("id", $id)->first();

        return view("admin.resources.edit", ["resources" => $resources]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
      
        $id = $request->id;

        $type = $request->type;

        $data["type"] = $request->resource_type;
        $data["file_type"] = $request->type;
        $data["title"] = $request->title;
        $data["is_downloadable"] = $request->downloadable;
        
        $data["description"] = $request->description;
       
        if ($request->hasfile("file") && $request->type != "link") {
            $image = $request->file("file");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $logopic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/resources/");
            // $file->move($destinationPath, $logopic);
            $filename = 'uploads/resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["file"] = $filename;
        } else {
            if ($request->type == "link") {
            $data["file"] = $request->file;
            }
        }

        if ($request->hasfile("thumbnail")) {
            $image = $request->file("thumbnail");
            // $extension = $file->getClientOriginalExtension(); // getting image extension
            // $thumbnailpic = "image-" . time() . "." . $extension;
            // $destinationPath = public_path("/uploads/resources/");
            // $file->move($destinationPath, $thumbnailpic);

            $filename = 'uploads/resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);

            $data["thumbnail"] = $filename;
        }


        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = Resources::where("id", $id)->update($data);

        if ($save) {
            return response()->json([
                "success" => true,
                "message" => "Details successfully updated",
                "redirect" => url("/manage-resources"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
        //     }
        // }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Resources::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Resources::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Resources::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = Resources::where("id", $id)->first();
            if ($record) {
                $res = Resources::where(
                    "id",
                    "=",
                    $id
                )->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
