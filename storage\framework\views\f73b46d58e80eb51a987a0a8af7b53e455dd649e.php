

<?php $__env->startSection('title'); ?> Edit School | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('messages.institute_management')); ?></li>
                
                <li class="breadcrumb-item active float-right" aria-current="page">
                    <a href="<?php echo e(url('school-list')); ?>"><?php echo e(__('messages.list_institute')); ?></a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Edit School</li>
               
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- EDIT PROFILE SECTION START -->
        <form method="post" id="editinstitute" enctype='multipart/form-data'>
        <div class="row justify-content-center">
            <div class="col-lg-2 col-md-3 col-6">
                <div class="change_profile_img mx-auto mb-4">
                 
                        <div class="avatar-edit">
                        <?php echo e(csrf_field()); ?>


                            <input type="file" name="profile_upload" class="profileImgUploadinst" id="imageUpload" accept=".png, .jpg, .jpeg" />
                            <label for="imageUpload">
                                <span class="btn btn-primary btn-block dbdb"><?php echo e(__('messages.change_image')); ?></span>
                            </label>


                            <input type="hidden" name="user_id" value="<?php echo e($user_list[0]->id); ?>">

                        </div>

                   

                 
                    <div class="avatar-preview">
                        <?php if(!empty(@$user_list[0]->image)): ?>
                        <div id="imagePreview" style="background-image: url(<?php echo e(generateSignedUrl($user_list[0]['image'])); ?>);"></div>
                        <?php else: ?>
                        <div id="imagePreview" style="background-image: url(<?php echo e(default_user_placeholder()); ?>)"></div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
            <div class="col-lg-10 col-md-9">
                <div class="row">
                   
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0"><?php echo e(__('messages.institute_details')); ?></h5>
                                </div>
                               
                                <div class="card-body">
                                <input type="hidden" name="latitude" class="form-control" id="latitude" value="<?php echo e($user_list[0]['latitude']); ?>">
                                <input type="hidden" name="longitude" class="form-control" id="longitude" value="<?php echo e($user_list[0]['longitude']); ?>">
                                      
                                    <div class="row">
                                        
                                        <input type="hidden" name="id" id="instituteid" value="<?php echo e($user_id); ?>">
                                        <input type="hidden" name="old_profile_name" value="<?php echo e($user_list[0]->image); ?>">

                                        <div class="col-md-4 form-group">
                                            <label class="form-label">School name</label>
                                            
                                            <select class="form-control select2" data-placeholder="Select School" id="full_name" data-toggle="select2" name="full_name"></select>
                                            <input type="text" class="form-control mt-3 d-none" name="other_full_name" id="other_full_name" placeholder="Full name">
                                            <span id="full_name_error" class="err"></span>
                                        </div>
                                        <div class="col-md-4 form-group">
                                        <label class="form-label">Work email</label>
                                           <input type="text" class="form-control" name="email" id="email" placeholder="Work Email" value="<?php echo e($user_list[0]->email); ?>">
                                           <span id="email_error" class="err"></span>
                                       </div>
                                        <div class="col-md-4 form-group">
                                        <label for="comment">Customer Type</label>
                                        <select class="form-control" id="cust_type" data-toggle="select2" name="cust_type">
                                            <option value="">Select Customer Type</option>
                                            <option value="Full Service" <?php if($user_list[0]['cust_type']=='Full Service'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Full Service</option>
                                            <option value="Platform"  <?php if($user_list[0]['cust_type']=='Platform'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Platform</option>
                                        
                                        </select>
                                            <span id="cust_type_error" class="err"></span>
                                    </div>
                                    <div class="col-md-4 form-group">
                                        <label for="comment">CBO</label>
                                        <select class="form-control" id="cbo" data-toggle="select2" name="cbo">
                                            <option value="">Select CBO</option>
                                            <?php $__currentLoopData = @$cbo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e(@$data->id); ?>" <?php if($user_list[0]['cbo']==@$data->id): ?><?php echo e('selected'); ?>  <?php endif; ?>><?php echo e(@$data->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        
                                        </select>
                                            <span id="cbo_error" class="err"></span>
                                    </div>

                                    <div class="col-md-4 form-group">
                                        <label for="comment">Grade levels</label>
                                        <select class="form-control select2" data-placeholder="Select Grade levels" form='editinstitute' id="grade" data-toggle="select2" name="gradelevel[]" multiple>
                                            <option value="">Select Grade levels</option>

                                            
                                            <?php $__currentLoopData = @$gradeLavel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option <?php if(in_array($data->id,explode(',', $user_list[0]['grade']))): ?><?php echo e('selected'); ?> <?php endif; ?> value="<?php echo e(@$data->id); ?>"><?php echo e(@$data->class_name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             
                                        </select>
                                            <span id="grade_error" class="err"></span>
                                    </div>
                                        

                                       
                                    <div class="col-md-4 form-group">
                                            <label class="form-label">Website</label>
                                            <input type="text" class="form-control" name="website" id="website" placeholder="Website" value="<?php echo e($user_list[0]->website_url); ?>">
                                            <span id="website_error" class="err"></span>
                                     </div>


                                      
                                       
                                        <div class="col-md-4 form-group">
                                        <label for="comment">Organization Type</label>
                                        <select class="form-control" id="organizationtype" data-toggle="select2" name="organizationtype">
                                            <option value="">Select Organization Type</option>

                                            <option value="Public School" <?php if($user_list[0]['organizationtype']=='Public school'): ?><?php echo e("selected"); ?> <?php endif; ?>>Public School</option>
                                            <option value="Charter school" <?php if($user_list[0]['organizationtype']=='Charter school'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Charter school</option>
                                            <option value="Private school" <?php if($user_list[0]['organizationtype']=='Private school'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Private school</option>
                                            <option value="Charter school" <?php if($user_list[0]['organizationtype']=='Charter school'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Charter school</option>
                                            <option value="Independent school" <?php if($user_list[0]['organizationtype']=='Independent school'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Independent school</option>
                                            <option value="Community Based Organizations" <?php if($user_list[0]['organizationtype']=='Community Based Organizations'): ?><?php echo e('selected'); ?>  <?php endif; ?>>Community Based Organizations</option>
                                            
                                        </select>
                                        <span id="organizationtype_error" class="err"></span>
                                       </div>

                                       <div class="col-md-4 form-group">
                                            <label class="form-label">Organization Name</label>
                                            <input type="text" id="organizationname" class="form-control" name="organizationname"  placeholder="Organization Name" value="<?php echo e($user_list[0]['organizationname']); ?>">
                                            <span id="p_organizationname_error" class="err"></span>
                                        </div>
                                        <div class="col-md-4" >
                                            <label class="form-label d-block">District</label>
                                            <select class="form-control" name="district" >
                                                <option value="">Select District</option>
                                              
                                                <?php if(!empty($district) && $district->count()): ?>
                                               <?php $__currentLoopData = $district; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option  <?php if($user_list[0]['district']==$data->id): ?><?php echo e('selected'); ?>  <?php endif; ?> value="<?php echo e($data->id); ?>"><?php echo e($data->name); ?></option>
                                               
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
                                                <?php endif; ?>
                                            </select>
                                            <span id="district_error" class="err"></span>

                                        </div>
                                            <div class="col-lg-12">

                                            <label for="comment">Contact info</label>

                                            </div>
                                            <?php if(!empty($school_contact_info) && $school_contact_info->count()): ?>
                                            <?php $__currentLoopData = @$school_contact_info; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                            <div class="row after-add-more" style="margin-left:0 !important;">

                                            <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="job_title[]"  placeholder="Job Title" value="<?php echo e($data->job_title); ?>">
                                            <span id="name_error" class="err"></span>
                                            </div>

                                            <div class="col-md-2 form-group">

                                            <input type="text" class="form-control" name="first_name[]"  placeholder="First name" value="<?php echo e($data->first_name); ?>">
                                            <span id="name_error" class="err"></span>
                                            </div>

                                            <div class="col-md-2 form-group">

                                            <input type="text"  class="form-control" name="last_name[]" placeholder="<?php echo e(__('messages.last_name')); ?>" value="<?php echo e($data->last_name); ?>">
                                            <span id="p_last_name_error" class="err"></span>
                                            </div> 



                                            <div class="col-md-3 form-group">

                                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email" value="<?php echo e($data->email); ?>">
                                            <span id="email_errors" class="err"></span>
                                            </div>



                                            <div class="col-md-2 form-group">

                                            <input type="number" class="form-control" name="phone[]"  placeholder="Phone" value="<?php echo e($data->phone); ?>">
                                            <span id="phone_error" class="err"></span>
                                            </div>


                                            <div class="col-md-1 form-group change">

                                               <?php if($key==0): ?>
                                            <a class="btn btn-success add-more " onclick="add_more()">+</a>
                                            <?php else: ?>
                                            <a class="btn btn-danger remove " >-</a>
                                            <?php endif; ?>
                                            </div>


                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                            <div class="row after-add-more" style="margin-left:0 !important;">

                                   <div class="col-md-2 form-group">
  
    <input type="text" class="form-control" name="job_title[]"  placeholder="Job Title">
    <span id="name_error" class="err"></span>
</div>

<div class="col-md-2 form-group">

    <input type="text" class="form-control" name="first_name[]"  placeholder="First name">
    <span id="name_error" class="err"></span>
</div>

<div class="col-md-2 form-group">
  
    <input type="text"  class="form-control" name="last_name[]" placeholder="<?php echo e(__('messages.last_name')); ?>">
    <span id="p_last_name_error" class="err"></span>
</div> 



<div class="col-md-3 form-group">
   
    <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
    <span id="email_errors" class="err"></span>
</div>



<div class="col-md-2 form-group">
    
    <input type="number" class="form-control" name="phone[]"  placeholder="Phone">
    <span id="phone_error" class="err"></span>
</div>


<div class="col-md-1 form-group change">

 
    <a class="btn btn-success add-more " onclick="add_more()">+</a>

</div>
   

</div>
                                          <?php endif; ?>
                                        <div class="col-lg-12">
        
                                            <label for="comment">Address</label>

                                        </div>
                                        <div class="col-md-12 form-group">
                                       
                                       <textarea  class="form-control" id="address" name="address" placeholder="Street Address"><?php echo e($user_list[0]['address']); ?></textarea>
                                      
                                           <span id="address_error" class="err"></span>
                                   </div>
                                   <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">City</label> -->
                                            <input type="text" id="city" class="form-control" value="<?php echo e($user_list[0]['city']); ?>" name="city" placeholder="City">
                                            <span id="p_city_error" class="err"></span>
                                    </div>
                                      
                                        <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">State</label> -->
                                            <input type="text" id="state" class="form-control" value="<?php echo e($user_list[0]['state']); ?>" name="state" placeholder="State">
                                            <span id="p_state_error" class="err"></span>
                                       </div> 

                                    
                                       
                                    <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">Zip code</label> -->
                                            <input type="text" id="zipcode" class="form-control" name="zipcode" placeholder="zipcode" value="<?php echo e($user_list[0]['zipcode']); ?>">
                                            <span id="p_city_error" class="err"></span>
                                    </div> 

                                    <div class="col-md-3 form-group">
                                            <!-- <label class="form-label">Country</label> -->
                                            <input type="text" id="country" class="form-control" name="country" placeholder="Country" value="<?php echo e($user_list[0]['country']); ?>">
                                            <span id="p_country_error" class="err"></span>
                                       </div> 

                                  

                                   
                                  
                                   <div class="col-md-12 form-group">
                                        <label for="comment">Note</label>
                                        <textarea  class="form-control" name="notes" placeholder="Notes"><?php echo e($user_list[0]['about']); ?></textarea>
                                       
                                            <span id="notes_error" class="err"></span>
                                    </div>

                                    <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                        <a href="<?php echo e(redirect()->getUrlGenerator()->previous()); ?>" class="btn btn-secondary mr-2"><?php echo e(__('messages.cancel')); ?></a>
                                        <button type="button" id="Editinstitute" class="btn btn-primary"><?php echo e(__('messages.update')); ?></button>

                                    </div>
                                </div>
                            </div>
                       
                        </div>
                </div>
                </form>
            </div>
        </div>
    </div>

    <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    function add_more() {
        var html = ` <div class="row after-add-more" style="margin-left:0 !important;"> <div class="col-md-2 form-group">
                                          
                                            <input type="text" class="form-control" name="job_title[]"  placeholder="Job Title">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">
                                        
                                            <input type="text" class="form-control" name="first_name[]"  placeholder="First name">
                                            <span id="name_error" class="err"></span>
                                        </div>

                                        <div class="col-md-2 form-group">
                                          
                                            <input type="text"  class="form-control" name="last_name[]" placeholder="<?php echo e(__('messages.last_name')); ?>">
                                            <span id="p_last_name_error" class="err"></span>
                                        </div> 



                                        <div class="col-md-3 form-group">
                                           
                                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                                            <span id="email_errors" class="err"></span>
                                        </div>

                                      
                                        
                                        <div class="col-md-2 form-group">
                                            
                                            <input type="number" class="form-control" name="phone[]"  placeholder="Phone">
                                            <span id="phone_error" class="err"></span>
                                        </div>

                                        
                                        <div class="col-md-1 form-group change">

                                         
                                        <a class='btn btn-danger remove'>-</a>

                                        </div> </div>`;

    
       
        $(".after-add-more").last().after(html);
        $(html).find(".change").html("<a class='btn btn-danger remove'>-</a>");
      
      
    }

    $("body").on("click",".remove",function(){ 
        $(this).parents(".after-add-more").remove();
    });
</script>
<script>
    $(document).ready(function() {
        let schoolData = [];
        var selectedSchool = "<?php echo e($user_list[0]->full_name ?? ''); ?>";
        $.ajax({
            url: 'http://localhost:3000/nces/schools',
            method: 'GET',
            dataType: 'json',
            success: function (response) {
                schoolData = response; // Store for later use
                console.log("response:", response);
                console.log("final_completion");

                let chunkSize = 20;
                let currentChunk = 0;
                let isSelected = '';

                function processNextChunk() {
                    let options = '';
                    let start = currentChunk * chunkSize;
                    let end = Math.min(start + chunkSize, response.length);
                    let schools = response.slice(start, end);

                    $.each(schools, function (index, school) {
                        let schoolType = (school.CHARTER_TEXT && school.CHARTER_TEXT.toLowerCase() === 'no') ? 'Public' : 'Independent';
                        let street = school.LSTREET1 || school.LSTREET2 || school.LSTREET3 || '';
                        let optionText = `${school.SCH_NAME} - ${schoolType}, ${street} (${school.LCITY}, ${school.LSTATE})`;

                        isSelected = (school.SCH_NAME === selectedSchool) ? 'selected' : '';
                        options += `<option value="${school.SCH_NAME}" ${isSelected}>${optionText}</option>`;
                    });

                    // Append to dropdown
                    $('#full_name').append(options);

                    currentChunk++;

                    if (currentChunk * chunkSize < response.length) {
                        setTimeout(processNextChunk, 100); // Delay to avoid UI freeze
                    }
                }

                // Start processing
                let otherSelected = (selectedSchool === 'Other') ? 'selected' : '';
                $('#full_name').html(`<option value="">Select School</option><option value="Other" ${otherSelected}>Other</option>`);
                processNextChunk();
            },
            error: function (xhr, status, error) {
                console.error("Request error:", status, error);
            }
        });

        $('#full_name').on('change', function() {
            let selectedName = $(this).val();
            if (selectedName === 'other') {
                $('#other_full_name').removeClass('d-none');
            } else {
                $('#other_full_name').addClass('d-none');
                let selectedSchool = schoolData.find(school => school.SCH_NAME === selectedName);
                
                if (selectedSchool) {
                    let details = {
                        district: selectedSchool.district.LEA_NAME || '',
                        website: selectedSchool.WEBSITE || '',
                        phone: selectedSchool.PHONE || '',
                        zip: selectedSchool.LZIP || '',
                        state: selectedSchool.STATENAME || '',
                        city: selectedSchool.LCITY || '',
                        street: selectedSchool.LSTREET1 || selectedSchool.LSTREET2 || selectedSchool.LSTREET3 || '',
                    };

                    $('#website').val(details.website);
                    $('#phone').val(details.phone);
                    $('#address').val(details.street);
                    $('#city').val(details.city);
                    $('#state').val(details.state);
                    $('#zipcode').val(details.zip);

                    $('#district').append(`<option selected value="${details.district}">${details.district}</option>`);
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/institute/editinstitute.blade.php ENDPATH**/ ?>