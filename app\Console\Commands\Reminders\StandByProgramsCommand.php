<?php

namespace App\Console\Commands\Reminders;

use App\invite_programs;
use App\notification;
use App\Notification_content;
use App\Programs;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class StandByProgramsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:stand-by-programs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to All instructors marked as standby";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = $this->signature;
        $instructors = User::active()
            ->where("type", "=", "5")
            ->where("app_notification", "=", "1")
            ->where("profile_status", "=", "12")
            ->whereHas('activeStandByPrograms')
            ->pluck('id')->toArray();

        // logger()->info($instructors);
        // die;

        $link = url("/my-program/");
        if (!empty($instructors)) {
            foreach ($instructors as $id) {


                $template = Notification_content::where("signature", $signature)->first();
                $body =  @$template->content;

                $body = str_replace('{{link}}', $link, $body);

                notification::insert([
                    'title' => 'notification',
                    'user_id' => $id,
                    'program_id' => null,
                    'notification' => $body,
                    'type' => "user",
                    'user_type' =>  "user",
                ]);
            }
        }
    }
}
