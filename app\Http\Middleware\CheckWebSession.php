<?php

namespace App\Http\Middleware;

use Closure;
use Auth;
use Illuminate\Support\Facades\App;

class CheckWebSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if (!session('userewlogin')) {
            return redirect('/sign-in');
        } else {
            if (auth()->check()) {
                $user = auth()->user();

                $userTimezone = optional($user->availability)->teach_in_person_timezone ?? config('app.timezone', 'America/Los_Angeles');
            } else {
                $userTimezone = config('app.timezone', 'America/Los_Angeles');
            }

            App::get('config')->set('app.timezone', $userTimezone);
            date_default_timezone_set($userTimezone);


        }

        return $next($request);
    }
}
