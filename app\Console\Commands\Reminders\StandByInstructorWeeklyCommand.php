<?php

namespace App\Console\Commands\Reminders;

use App\EmailTemplate;
use App\invite_programs;
use App\notification;
use App\Notification_content;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class StandByInstructorWeeklyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminder:stand-by-instructor-weekly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Send reminders to instructors weekly thursday at 06:00PM after sending the invite to be standby instructor";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $signature = "reminder:stand-by-instructor-weekly";

        $currentTime = now();

        $programs = invite_programs::where('status', 1)
            ->where('is_standby', 1)
            ->where('program_id', 497)
            ->whereHas('program', function ($query) use ($currentTime) {
                $query->where('end_date', '>', $currentTime);
            })
            ->get();

        $link = url("/new-program-alerts/");

        foreach ($programs as $program) {

            // Notification placeholders
            $notificationTemplate = Notification_content::where("signature", $signature)->first();
            $templates = EmailTemplate::where('title', 'Stand By Instructor Weekly')
            ->first();
            if (!$notificationTemplate) {
                continue; 
            }
            $body = @$notificationTemplate->content;
            $delivery_type = $program->delivery_type;
            $body = str_replace('{{link}}', $link, $body);
            $body = str_replace('{{delivery_type}}', $delivery_type, $body);
            $body = str_replace('{{start_date}}', date('m/d/Y', strtotime($program->start_date)), $body);
            $body = str_replace('{{end_date}}', date('m/d/Y', strtotime($program->end_date)), $body);

            if ($program->school) {
                $body = str_replace('{{school_name}}', @$program->school->full_name, $body);
            }
            if ($delivery_type == 'In-Person') {
                $city = $program->city ? 'in ' . $program->city : '';
            } else {
                $city = '';
            }
            $body = str_replace('{{city}}', $city, $body);

            // Insert the notification in table
            notification::insert([
                'title' => 'notification',
                'user_id' => $program->user_id,
                'program_id' => $program->id,
                'notification' => $body,
                'type' => "user",
                'user_type' =>  "user",
            ]);

            // Send email notification
            $user = User::find($program->user_id);
            if ($user && $user->email) {
                $emailBody = str_replace('{{ NAME }}', $user->first_name, $templates->description);
                $emailBody = str_replace('{{ notification }}', $body, $emailBody);
                $baseUrl = url('/');
                $emailBody = str_replace('{{ baseUrl }}', $baseUrl, $emailBody);

                $data = [
                    'subject' => $templates->subject,
                    'emailBody' => $emailBody,
                    'user' => $user,
                    'baseUrl' => $baseUrl,
                ];
                Mail::send('admin.email-temp.standby_instructor_weekly', $data, function ($message) use ($user, $templates) {
                    $message->to($user->email)
                        ->subject($templates->subject);
                });

                // Mail::raw($emailBody, function ($message) use ($user, $notificationTemplate) {
                //     $message->to($user->email)
                //         ->subject($notificationTemplate->subject);
                // });
            }    
        }
    }
}
