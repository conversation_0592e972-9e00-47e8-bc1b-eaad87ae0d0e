<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{AvailabilityModel, Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportInstructor implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }

    public function collection()
    {
        $instructorQry = User::select('users.*')->where("users.type", "=", "5")->where("profile_status", "=", "12");
        $this->applyFilters($instructorQry);

        return $instructorQry->orderBy('updated_at', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        if (!empty($filters['location'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['state'])) {
            $query->where('state', $filters['state']);
        }

        if (!empty($filters['city'])) {
            $query->where('city', $filters['city']);
        }

        if (!empty($filters['district'])) {
            $query->where('district', $filters['district']);
        }

        if (!empty($filters['subject'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['sub_subject'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['profile_status'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['certified_teacher'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['is_sub'])) {
            $query->where('is_sub', $filters['is_sub']);
        }

        if (!empty($filters['background_check'])) {
            $query->where('is_background_check', $filters['background_check']);
        }

        if (!empty($filters['medical_requirements'])) {
            $query->where('is_medical_check', $filters['medical_requirements']);
        }

        // if (!empty($filters['format']) && ) {
        //     $query->where('is_sub', $filters['is_sub']);
        // }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'User Id',
            'Name',
            'Email',
            'Programs(main)',
            'Programs(stand by)',
            'Rate(online)',
            'Rate(in-person)',
            'Availability',
            'Teaching Preferences',
            'Format',
            'Approved As',
            'State',
            'City',
            'Status',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        $programsList = $row->mainInsProgram->map(function ($program) {
            return [
                'id' => $program->id,
                'name' => $program->name,
            ];
        })->groupBy('id')->toArray();

        $programLinks = collect($programsList)->map(function ($group) {
            $program = $group[0];
            return $program['name'];
        })->implode(', ');

        $programsListforstandby = $row->programs->filter(function ($program) use ($row) {
            return $program->pivot->status == 1 &&
                   ($program->pivot->user_id == $row->id);
                })->map(function ($program) {
                    return [
                        'id' => $program->id,
                        'name' => $program->name,
                    ];
                })->unique('id')
                ->groupBy('id')
                ->toArray();

        $programLinksforstandby = collect($programsListforstandby)->map(function ($group) {
            return collect($group)->map(function ($program) {
                return $program['name'];
            })->implode('');
        })->implode(', ');

        $user_interview_slots = AvailabilityModel::where(['user_id' => $row->id])->first();
        $prefernces = DB::table("tbl_user_teaching_preferences")->where("user_id",$row->id)->first();
        if($row->is_approved==16){
            $status = "Online";
        }

        if($row->is_approved==17){
            $status = "In-person";
        }

        if($row->is_approved==20){
            $status = "Both (Online & In-person)";
        }

        switch ($row->status) {
            case 0:
                $userstatus = __('messages.deactive');
                break;
            case 1:
                $userstatus = __('messages.active');
                break;
            case 2:
                $userstatus = 'Account Deleted';
                break;
            case 3:
                $userstatus = 'Account Deactivated';
                break;
            default:
                $userstatus = '';
        }

        return [
            $row->id ? $row->id : 'NIL',
            $row->first_name.' '.$row->last_name,
            $row->email ? $row->email : '',
            !empty($programLinks) ? $programLinks : '',
            !empty($programLinksforstandby) ? $programLinksforstandby : '',
            $row->onlinerate ? $row->onlinerate : '',
            $row->inpersonrate ? $row->inpersonrate : '',
            !empty($user_interview_slots) ? 'Availability' : 'null  ',
            !empty($prefernces) && !empty($prefernces->i_prefer_to_teach) ? 'Teaching preferences' : 'null  ',
            $row->is_sub == 1 ? 'Sub' : 'Main/Sub',
            !empty($status) ? $status : '',
            $row->state ? $row->state : '',
            $row->city ? $row->city : '',
            !empty($userstatus) ? $userstatus : '',
        ];
    }
}
