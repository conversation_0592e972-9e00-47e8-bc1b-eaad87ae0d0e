<?php

namespace App\Http\Middleware;

use Closure;
use DB;
class SubscribeCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
       $user = get_user();
       $end_date =DB::table('subscriptions')->where('users_id',$user->id)->pluck('ends_at')->first();
        if($user->stripe_id == "")
        {
            return redirect('/institute-subscription');
        }   
        elseif(@$end_date < date('Y-m-d'))
        {
            return redirect('/institute-subscription');
        }
        return $next($request);
    }
}
