<?php

namespace App\Http\Controllers\Admin;

use App\Exports\Admin\ExportReimbursements;
use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\Programs;
use App\Reimbursement;
use App\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Excel;

class ManageReimbursementsController extends Controller
{

    public function index(Request $request)
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');

        $userId = '';
        $programId = '';
        $range = '';
        $payment_status = '';
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];
        $programsQry = Programs::query();
        $programsQry->whereHas('reimbursements');
        if ($adminType != '1') {
            $whereInIds = getAdminUserProgramIds();
            $programsQry->whereIn('id', $whereInIds);
        }
        $programs = $programsQry->pluck('name', 'id');
        $programIds = array_keys($programs->toArray());
        $userIds = Reimbursement::whereIn('program_id', $programIds)->pluck('user_id')->toArray();
        $users = User::whereIn('id', $userIds)->get(['id', 'first_name', 'last_name']);
        $sidebarMenu = 'manage-payments';

        if ($request->ajax()) {
            $query = Reimbursement::with('user', 'program');

            switch($request->status) {
                case 'paid':
                    $query->where('status', 3);
                    $payment_status = 'paid';
                    break;
                case 'unpaid':
                    $query->where(function ($qry) {
                        $qry->where('status', '!=', 3);
                        $qry->orWhereNull('status');
                    });
                    $payment_status = 'unpaid';
                    break;
                case 'accepted':
                    $query->where('status', 1);
                    $payment_status = 'accepted';
                    break;
                case 'declined':
                    $query->where('status', 0);
                    $payment_status = 'declined';
                    break;
                case 'pending':
                    $query->whereNull('status');
                    $payment_status = 'pending';
                    break;
                default: break;
            }
            if ($adminType != '1') {
                $query->whereHas('program', function ($qry) use ($programIds) {
                    $qry->whereIn('program_id', $programIds);
                });
            }

            if ($request->user_id) {
                $userId = Reimbursement::with('user', 'program')->where('user_id', $request->user_id)->first();
                $userId = $userId->user->first_name .' '. $userId->user->last_name;
                $query->where('user_id', $request->user_id);
            }
            if ($request->program_id) {
                $programId = Reimbursement::with('user', 'program')->where('program_id', $request->program_id)->first();
                $programId = $programId->program->name;
                $query->where('program_id', $request->program_id);
            }
            if ($request->daterange && strpos($request->daterange, ' TO ') !== false) {
                $range = $request->daterange;
                $separator = ' TO ';

                $dateRange = explode($separator, $request->daterange);
                $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
                $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();
                $query->whereBetween('created_at', [$startDate, $endDate]);
                $column = 'created_at';

                $query->whereBetween($column, [$startDate, $endDate]);
            }

            $params = DataTableHelper::getParams($request);
            switch($params['columnName']){
                case 'name':
                    $params['columnName'] = 'program_id';
                    break;
                case 'user_id':
                    $query->addSelect([
                        'user_name' => User::selectRaw('users.first_name')
                        ->whereColumn('reimbursements.user_id', 'users.id')
                        ->limit(1)
                    ]);
                    $params['columnName'] = 'user_name';
                    break;
                case 'hours':
                    $query->addSelect([
                        'hours' => DB::raw('FLOOR(SUM(minutes) / 60) + SUM(hours)')
                    ]);
                    break;
                case 'minutes':
                    $query->addSelect([
                        'program_note_amounts.*',
                        'minutes' => DB::raw('MOD(SUM(minutes) OVER(), 60)')
                    ]);
                    break;
            }

            $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            $searchValue = $params['searchValue'];

            if($searchValue){
                $query->where(function ($que) use ($searchValue) {
                    $que->whereHas('program', function ($query) use ($searchValue) {
                        $query->where('tbl_programs.name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('tbl_programs.id', 'LIKE', "%{$searchValue}%");
                    })
                    ->orWhereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                        $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                    });
                });
            }


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;

            foreach ($result as $row) {

                $status =  $this->generateStatus($row,[]);

                $viewUser = $viewProgram = "NIL";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a href='{$viewProgramRoute}'>{$row->program->id}</a>";
                }

                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a href='{$viewUserRoute}'>{$userName}</a>";
                }


                $formattedAmount = '$'.number_format($row->amount, 2, '.', ',');
                $receiptUrl =  generateSignedUrl($row->receipt) ;

                $updated_time = new DateTime($row->updated_time);
                $updatedDate = $updated_time->format('Y-m-d');
                $updatedTime = $updated_time->format('H:i:s');
                $newUpdatedDate = '<whiz-date date="'.$updatedDate.'"></whiz-date>';
                $newUpdatedTime = '<whiz-time date="'.$updatedDate.'" set-time="'.$updatedTime.'" format="hh:mm A"" convert-to="local"></whiz-time>';
                if($row->updated_time){
                    $updated_time = $newUpdatedDate . ' ' . $newUpdatedTime;
                }
                else{
                    $updated_time = '';
                }
                $action =  $this->generateActionButtons($row,[]);
                $data[] = [
                    "id" => $row->id,
                    "name" => $viewProgram,
                    "user_id" => $viewUser,
                    "amount" => $formattedAmount,
                    "receipt" => "<a href='{$receiptUrl}' target='_blank' title='Receipt'>Receipt</a>",
                    "updated_time" => $updated_time,
                    "type" => $row->type,
                    "created_at" => getAdminTimestamp($row->created_at),
                    "status" => $status,
                    "action" => $action,
                ];

                $i++;
            }
            $additional_data = [
                "programId" => $programId,
                "userId" => $userId,
                "range" => $range,
                "payment_status" => $payment_status
            ];

            return DataTableHelper::generateResponse($params['draw'], $count, $data, $additional_data);
        }

        return view("admin.manage-payments.reimbursements",compact("sidebarMenu", "programs", "users"));
    }
    private function generateStatus($row, $res = [])
    {
        $id = $row->id;
        $javascriptVoid = "javascript:void(0);";
        $acceptButton = $declineButton = $viewClassButton = '';

        if (is_null($row->status)) {
            $acceptRoute = route('admin.manage-payments.update.reimbursement.status', ['reimbursement' => $id, 'status' => 1]);

            $acceptButton = "<a href='{$javascriptVoid}' class='btn btn-outline-success btn-rounded' onclick=updateRequest('{$acceptRoute}')>Accept</a>  &nbsp;";

            $declineRoute = route('admin.manage-payments.update.reimbursement.status', ['reimbursement' => $id, 'status' => 0]);

            $declineButton = "<a href='{$javascriptVoid}' class='btn btn-outline-danger btn-rounded' onclick=updateRequest('{$declineRoute}')>Decline</a>  &nbsp;";
        }else{
            if ($row->status == 1) {
                $acceptButton = '<span class="badge-success rounded-pill p-1">Accepted</span>';
            } elseif ($row->status == 0) {
                $declineButton = '<span class="badge-danger rounded-pill p-1">Declined</span>';
            } elseif ($row->status == 3) {
                $acceptButton = '<span class="badge-primary rounded-pill p-1">Paid</span>'; // For status 3
            }
        }


        $html = "<div class='w-100 d-flex justify-content-around align-items-around'>{$acceptButton}{$declineButton}</div>";
        return $html;
    }
    public function updateStatus(Reimbursement $reimbursement, Request $request)
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminId= $adminSession['id'];
        $status = $request->status;
        $message = $status == 1 ? 'Accepted' : 'Declined';

        $reimbursement->status = $status;
        $reimbursement->updated_by = $adminId;
        $reimbursement->save();

        return response()->json(['status' => true, 'message' => $message . " successfully"]);
    }
    public function updatePaidStatus(Request $request)
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminId = $adminSession['id'];
        $status = $request->status;
        $ids = $request->ids;

        foreach ($ids as $id) {
            $reimbursement = Reimbursement::find($id);
            if ($reimbursement) {
                $reimbursement->status = $status;
                $reimbursement->updated_by = $adminId;
                $reimbursement->updated_time = now();
                $reimbursement->save();
            } else {
                return response()->json(['status' => false, 'message' => "Reimbursement not found for ID: $id"]);
            }
        }
        session()->flash('success', 'Payments updated successfully');

        return response()->json(['status' => true]);
    }
    public function view($id, Request $request)
    {
        $info = Reimbursement::with( 'user', 'program')->findOrFail($id);

        $user = $info->user;
        $program = $info->program;

        $view = view("components.admin.modals.view-reimbursement-details", compact('info', 'program', 'user'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    private function generateActionButtons($row ,$res = [])
    {
        $id = $row->id;
        $javascriptVoid = "javascript:void(0);";
        $acceptButton = $declineButton = $viewClassButton = '';

        $viewClassRoute = route('admin.manage-payments.view-reimbursement', ['id' => $id]);

        $viewClassButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=openAdminModal('{$viewClassRoute}')>View Details</a>  &nbsp;";

        $html = "<div class='w-100 d-flex justify-content-around align-items-around'>{$viewClassButton}</div>";
        return $html;
    }

    public function export(Request $request)
    {
        $filters = [];
        parse_str($request->filter_data, $filters);
        $range = '';
        $status = '';
        $rangeFileName = 'Reimbursements';

        if (!empty($filters)) {
            if (!empty($filters['status']) && $filters['status'] != 'all') {
                $status = $filters['status'];
            }

            if (!empty($filters['daterange'])) {
                if(strpos($filters['daterange'], ' TO ') !== false){
                    $dateRange = explode(' TO ', $filters['daterange']);
                    $startDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[0]))->format('mdY');
                    $endDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[1]))->format('mdY');
                    $range = $startDate . '-' . $endDate;
                    // $rangeFileName = 'Reimbursements:'.$range.'.xlsx';
                }
            }
        }

        if (!empty($status)) {
            $rangeFileName .= '_' . $status;
        }
        if (!empty($range)) {
            $rangeFileName .= '_' . $range;
        }
        if (empty($status) && empty($range)) {
            $rangeFileName .= time();
        }

        $rangeFileName .= '.xlsx';

        try {
            ob_end_clean();
            ob_start();

            $fileName = 'Reimbursements'.time().'.xlsx';
            return Excel::download(new ExportReimbursements($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}
