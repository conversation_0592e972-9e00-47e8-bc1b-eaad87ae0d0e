<?php

namespace App\Exports\Front;
use App\ProgramNoteAmount;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SchoolExportPayments implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }

    public function collection()
    {
        $query = ProgramNoteAmount::with('note', 'user', 'program');

        
        $query->whereHas('program',function ($q)  {
            $q->where('school_name', auth()->user()->id);
        });
       
      
        $this->applyFilters($query);

        return $query->orderBy('id','DESC')->get();
    }
    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);


        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }
        $separator = ' TO ';

        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }


    protected function applyDateRangeFilter($query, $daterange, $separator)
    {
        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

        $query->whereHas('note', function ($qry) use ($startDate, $endDate) {
            $qry->whereBetween('class_date', [$startDate, $endDate]);
        });
    }

    public function headings(): array
    {
        return [
            'Program Name',
            'Class Date',
            'Class Time',
            'Hours',
            'Minutes',
            'Hourly Pay Rate',
            'Amount',
        ];
    }

    public function map($row): array
    {
        
      
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
        $formattedTime = date('h:i a', strtotime(@$row->note->start_time)) .'-'.date('h:i a', strtotime(@$row->note->end_time));
        return [
            $row->program ? $row->program->name : 'NIL',
            optional($row->note)->class_date ? optional($row->note->class_date)->format('m-d-Y') : '',
            $formattedTime,
            $row->hours,
            $row->minutes,
            $row->rate,
            $formattedAmount,
        ];
    }
}
