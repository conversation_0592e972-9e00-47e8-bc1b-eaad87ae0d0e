<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ProgramAdminNotesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'additional_notes' => 'required',
            'instructions' => 'required',
            'internal_notes' => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'additional_notes' => 'Additional Program Notes',
            'instructions' => 'Instructions for Instructor',
            'internal_notes' => 'Internal Operations Notes',

        ];
    }
}
