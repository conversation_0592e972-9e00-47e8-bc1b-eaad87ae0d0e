<?php

namespace App\Exports\Admin;

use App\ProgramNoteAmount;
use App\{AvailabilityModel, invite_programs, ProgramNote, Programs, User, SubsubjectModel, Users};
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportInvite implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->searchInput;
    }

    public function collection()
    {
        $query = invite_programs::has('program')->with('program', 'user', 'program.school', 'program.schedules');
        $query->where('has_requested', '=', '0');
        $query->whereNotNull('deadline');

        $whereInIds = getAdminUserProgramIds();
        if (!empty($whereInIds)) {
            $query->whereIn('program_id', $whereInIds);
        }
        $query->where('is_auto_invite', 0);
        $query->where('user_id', '!=', null);

        $query->where('tbl_invite_programs.program_invite_type', null);
        $this->applyFilters($query);

        return $query->orderBy('id', 'DESC')->get();
    }

    protected function applyFilters($query)
    {
        if (!empty($this->requestFilters)) {
            $query->where(function ($q) {
                // Program ID filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('id', $this->requestFilters);
                });

                // Invite Deadline filter
                $q->orWhere(function ($subQuery) {
                    $filter = $this->requestFilters;
                    if (substr_count($filter, '-') == 2) {
                        [$yearFilter, $monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("YEAR(deadline) = ?", [$yearFilter])
                                 ->whereRaw("MONTH(deadline) = ?", [$monthFilter])
                                 ->whereRaw("DAY(deadline) = ?", [$dayFilter]);
                    } elseif (strpos($filter, '-') !== false) {
                        [$monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("MONTH(deadline) = ?", [$monthFilter])
                                 ->whereRaw("DAY(deadline) = ?", [$dayFilter]);
                    } else {
                        if (strpos($filter, ':') !== false) {
                            [$hour, $minute] = explode(':', $filter);
                            $subQuery->whereRaw("HOUR(deadline) = ?", [$hour]);
                            if ($minute !== '') {
                                $subQuery->whereRaw("MINUTE(deadline) = ?", [$minute]);
                            }
                        } else {
                            $subQuery->whereRaw("DAY(deadline) = ?", [$filter])
                                     ->orWhereRaw("MONTH(deadline) = ?", [$filter])
                                     ->orWhereRaw("YEAR(deadline) = ?", [$filter]);
                        }
                    }
                });

                // Instructor name filter
                $q->orWhereHas('user', function ($subQuery) {
                    $subQuery->where('first_name', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Instructor type filter
                $q->orWhere(function ($subQuery) {
                    $searchTxt = null;

                    if (str_contains(strtolower($this->requestFilters), 'standby')) {
                        $subQuery->whereNotNull('is_standby')->where('is_standby', '!=', 0);
                    } else {
                        if (str_contains(strtolower($this->requestFilters), 'main')) {
                            $searchTxt = 1;
                        } elseif (str_contains(strtolower($this->requestFilters), 'sub')) {
                            $searchTxt = 0;
                        }

                        if ($searchTxt !== null) {
                            $subQuery->where(function ($q) use ($searchTxt) {
                                $q->where(function ($q) use ($searchTxt) {
                                    $q->whereNotNull('type')->where('type', $searchTxt)
                                      ->orWhereNotNull('admin_type')->where('admin_type', $searchTxt)
                                      ->orWhereNotNull('replacement_type')->where('replacement_type', $searchTxt);
                                });
                            });
                        }
                    }

                });

                // Program type filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('program_type', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Delivery type filter
                $q->orWhereHas('program', function ($subQuery) {
                    $subQuery->where('delivery_type', 'LIKE', '%' . $this->requestFilters . '%');
                });

                // Program start date filter
                $q->orWhereHas('program', function ($subQuery) {
                    $filter = $this->requestFilters;
                    if (substr_count($filter, '-') == 2) {
                        [$yearFilter, $monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("YEAR(start_date) = ?", [$yearFilter])
                                 ->whereRaw("MONTH(start_date) = ?", [$monthFilter])
                                 ->whereRaw("DAY(start_date) = ?", [$dayFilter]);
                    } elseif (strpos($filter, '-') !== false) {
                        [$monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("MONTH(start_date) = ?", [$monthFilter])
                                 ->whereRaw("DAY(start_date) = ?", [$dayFilter]);
                    } else {
                        $subQuery->whereRaw("DAY(start_date) = ?", [$filter])
                                 ->orWhereRaw("MONTH(start_date) = ?", [$filter])
                                 ->orWhereRaw("YEAR(start_date) = ?", [$filter]);
                    }
                });

                // Program end date filter
                $q->orWhereHas('program', function ($subQuery) {
                    $filter = $this->requestFilters;
                    if (substr_count($filter, '-') == 2) {
                        [$yearFilter, $monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("YEAR(end_date) = ?", [$yearFilter])
                                 ->whereRaw("MONTH(end_date) = ?", [$monthFilter])
                                 ->whereRaw("DAY(end_date) = ?", [$dayFilter]);
                    } elseif (strpos($filter, '-') !== false) {
                        [$monthFilter, $dayFilter] = explode('-', $filter);
                        $subQuery->whereRaw("MONTH(end_date) = ?", [$monthFilter])
                                 ->whereRaw("DAY(end_date) = ?", [$dayFilter]);
                    } else {
                        $subQuery->whereRaw("DAY(end_date) = ?", [$filter])
                                 ->orWhereRaw("MONTH(end_date) = ?", [$filter])
                                 ->orWhereRaw("YEAR(end_date) = ?", [$filter]);
                    }
                });

                // Status filter
                $q->orWhere(function ($subQuery) {
                    $searchTxt = null;
                    if (str_contains(strtolower($this->requestFilters), 'accepted')) {
                        $searchTxt = 'accepted';
                        $subQuery->where('status', 1);
                    } elseif (str_contains(strtolower($this->requestFilters), 'pending')) {
                        $searchTxt = 'pending';
                        $subQuery->where('resend_count', '>', 0)
                                ->orWhereNull('resend_count');
                    } elseif (str_contains(strtolower($this->requestFilters), 'cancelled')) {
                        $searchTxt = 'cancelled';
                        $subQuery->where('status', 0)
                                ->where('cancelled_by', 1);
                    } elseif (str_contains(strtolower($this->requestFilters), 'declined')) {
                        $searchTxt = 'declined';
                        $subQuery->where('status', 0)
                        ->where(function($query) {
                            $query->where('cancelled_by', '!=', 1)
                                  ->orWhereNull('cancelled_by');
                        });
                    } elseif (str_contains(strtolower($this->requestFilters), 'archived')) {
                        $searchTxt = 'archived';
                        $subQuery->where('status', 2);
                    } elseif (str_contains(strtolower($this->requestFilters), 'expired')) {
                        $searchTxt = 'expired';
                        $subQuery->whereNull('status')
                            ->where('deadline', '<', now()->toDateString());
                    }

                });
            });
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Program Id',
            'Invite Deadline',
            'Instructor Name',
            'Instructor Type',
            'Program Type',
            'Delivery Type',
            'Start Date',
            'End Date',
            'Status',
            'Invite Date',
            'Updated At',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        $updated_at = '';
        $status = '';
        if ($row->status === null){
            if ($row->deadline && $row->deadline < now()->toDateString()){
                $status = 'Expired';
            }elseif($row->resend_count > 0){
                $status = 'Pending';
            }else{
                $status = 'Pending';
            }

        }elseif($row->status === 0){
            if($row->cancelled_by==1){
                $status = 'Cancelled';
            }else{
                $status = 'Declined';
            }
        }elseif($row->status === 1){
            $status = 'Accepted';
        }elseif($row->status === 2){
            $status = 'Archived';
        }

        if($row->status != null || $row->status == 0 || $row->deadline && $row->deadline < now()->toDateString()){
            $updated_at = getAdminTimestamp($row->updated_at);
        }else{
            $updated_at = '';
        }

        return [
            $row->program->id ? $row->program->id : 'NIL',
            $row->deadline ? date('m-d-Y h:i A', strtotime($row->deadline)) : '',
            $row->user ? $row->user->first_name : 'Not Assigned',
            $row->is_standby ? 'StandBy' : getInstructorType($row->type ?? ($row->admin_type ?? $row->replacement_type)),
            $row->program ? $row->program->program_type : '',
            $row->program ? $row->program->delivery_type : '',
            $row->program ? date('m-d-Y', strtotime($row->program->start_date)) : '',
            $row->program ? date('m-d-Y', strtotime($row->program->end_date)) : '',
            $status,
            getAdminTimestamp($row->created_at),
            $updated_at,
        ];
    }
}
