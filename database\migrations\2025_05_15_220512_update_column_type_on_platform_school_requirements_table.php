<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateColumnTypeOnPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->string('requirement_title')->nullable()->after('requirement_type');
        });
        DB::statement("ALTER TABLE platform_school_requirements MODIFY requirement_name VARCHAR(255) NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY start_date DATE NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY end_date DATE NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY time_zone VARCHAR(255) NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY compensation_type ENUM('hourly', 'fixed') NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY compensation_amount_min DECIMAL(10, 2) NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY subject_area_id BIGINT UNSIGNED NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY subject_id BIGINT UNSIGNED NULL;");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn('requirement_title');
        });
        DB::statement("ALTER TABLE platform_school_requirements MODIFY requirement_name VARCHAR(255) NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY start_date DATE NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY end_date DATE NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY time_zone VARCHAR(255) NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY compensation_type ENUM('hourly', 'fixed') NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY compensation_amount_min DECIMAL(10, 2) NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY subject_area_id BIGINT UNSIGNED NOT NULL;");
        DB::statement("ALTER TABLE platform_school_requirements MODIFY subject_id BIGINT UNSIGNED NOT NULL;");
    }
}
