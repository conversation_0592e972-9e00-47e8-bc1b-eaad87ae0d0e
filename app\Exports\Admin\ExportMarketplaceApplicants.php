<?php

namespace App\Exports\Admin;

use App\InstructorThirdStepOnboardingModel;
use App\OnboardingInstructor;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportMarketplaceApplicants implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->all();
    }

    public function collection()
    {
        $userQry = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6']);
        $this->applyFilters($userQry);

        return $userQry->orderBy('id', 'DESC')->get();
    }

    protected function applyFilters($userQry)
    {
        if (!empty($this->requestFilters['status']) && $this->requestFilters['status'] !== 'ALL') {
            $userQry->where('user_status', $this->requestFilters['status']);
            switch ($this->requestFilters['status']) {
                case "InProgress":
                    $userQry->where("user_status", "InProgress");
                    break;

                case "UnderReview":
                    $userQry->where("user_status", "UnderReview");
                    break;

                case "ChangeRequested":
                    $userQry->where("user_status", "ChangeRequested");
                    break;

                case "Active":
                    $userQry->where("user_status", "Active")
                    ->with(['whizaraContract', 'marketplaceContract'])
                    ->where(function ($query) {
                        $query->whereHas('whizaraContract')
                              ->orWhereHas('marketplaceContract');
                    });
                    break;

                case "Declined":
                    $userQry->where("user_status", "Declined");
                    break;

                case "Withdraw":
                    $userQry->where("user_status", "Withdraw");
                    break;

                case "Approved":
                    $userQry->where("user_status", "Approved");
                    break;
                        
    
            }
        }

        if (!empty($this->requestFilters['searchText'])) {
            $searchText = $this->requestFilters['searchText'];
            if (preg_match('/^\d{1,2}-[A-Za-z]+$/', $searchText)) {
                try {
                    // Convert '17-March' to 'Y-m-d'
                    $formattedDate = Carbon::createFromFormat('d-F', $searchText)
                        ->year(now()->year) // Add the current year
                        ->format('Y-m-d');
    
                    // Filter by Application Start Date (created_at)
                    $userQry->whereDate('created_at', $formattedDate);
                } catch (\Exception $e) {
                    // If parsing fails, ignore the date filter
                }
            } else {
                // Normal text search (Name, Email, City, State)
                $userQry->where(function ($q) use ($searchText) {
                    $q->where('first_name', 'LIKE', "%{$searchText}%")
                      ->orWhere('last_name', 'LIKE', "%{$searchText}%")
                      ->orWhere('email', 'LIKE', "%{$searchText}%")
                      ->orWhere('city', 'LIKE', "%{$searchText}%")
                      ->orWhere('state', 'LIKE', "%{$searchText}%");
                });
            }
        }
    }

    // Function to create headings according to the filters if its applied.
    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings = [
            'Email verify',
            'Application Start Date',
            'Name',
            'Email',
            'Rate (Online)',
            'Rate (In-person)',
            'Format',
            'State',
            'City',
            'status',
            'Online',
            'In-person',
            'Approved / Declined Date',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {

        $filtersInfo = '';

        return $filtersInfo;
    }

    public function map($row): array
    {
        $step3 = InstructorThirdStepOnboardingModel::where("user_id",$row->id)->first();
        $format = (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false) ? "Both (Online & In-person)" : (!empty($step3->format) ? ucfirst($step3->format) : null);
        if (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false && !empty($step3->compensation)) {
            $compensation = explode(',', $step3->compensation);
            $online = $compensation[0];
            $inperson = $compensation[1];
        } else {
            $compensation = (!empty($step3) && !empty($step3->format) && !empty($step3->compensation)) ? $step3->compensation : null;
            $online = '';
            $inperson = '';
            if (!empty($step3->format) && !empty($step3->compensation)) {
                if (strpos($step3->format, 'online') !== false) {
                    $online = $compensation;
                } elseif (strpos($step3->format, 'in-person') !== false) {
                    $inperson = $compensation;
                }
            }
        }

        $approvedDeclinedDate = $row->user_status == 'Active' ? Carbon::parse($row->status_updated_at)->format('d-F-Y H:i:s') : (($row->user_status == 'Declined' && !empty($row->rejectInstructor)) ? Carbon::parse($row->rejectInstructor->date)->format('d-F-Y H:i:s') : '');

        return [
            !empty($row->email_verify_status) ? 'Verified' : 'Not Verified',
            Carbon::parse($row->created_at)->format('d-F-Y H:i:s'),
            $row->first_name.' '.$row->last_name,
            $row->email,
            $online,
            $inperson,
            $format,
            $row->state,
            $row->city,
            $row->user_status,
            $row->inpersonrate,
            $row->onlinerate,
            $approvedDeclinedDate,
        ];
    }
}