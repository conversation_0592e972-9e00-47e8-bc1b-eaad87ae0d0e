<?php

namespace App\Http\Controllers\Admin;

use App\Exports\Admin\ExportPayments;
use App\Exports\Admin\ExportinstructorPayments;
use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\{User, ProgramNote, Programs, ProgramNoteAmount, SubsubjectModel};
use Carbon\Carbon;
use Illuminate\Http\Request;
use Excel;
use DB;
use DateTime;
class ManagePaymentsController extends Controller
{

    public function __construct()
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');
    }

    public function index(Request $request)
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');

        $userId = '';
        $programId = '';
        $range = '';
        $payment_status = '';
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];
        $allPrograms = Programs::orderBy('id', 'desc')->get();
        $programsQry = Programs::query();
        $programsQry->whereHas('programNoteAmounts');
        if ($adminType != '1') {
            $whereInIds = getAdminUserProgramIds();
            $programsQry->whereIn('id', $whereInIds);
        }
        $programs = $programsQry->pluck('name', 'id');
        $programIds = array_keys($programs->toArray());
        $q = ProgramNoteAmount::where(function ($query) use ($programIds, $request) {
            if ($request->status == 'paid') {
                $query->whereNotNull('payment_date_updated');
            } elseif ($request->status == 'unpaid') {
                $query->whereNull('payment_date_updated');
            }
            $query->whereIn('program_id', $programIds);
        });
        $userIds = $q->pluck('user_id')->toArray();
        $users = User::whereIn('id', $userIds)->get(['id', 'first_name', 'last_name']);
        $sidebarMenu = 'manage-payments';

        if ($request->ajax()) {
            $query = ProgramNoteAmount::with('note', 'user', 'program.subSubject', 'program.school');

            switch($request->status) {
                case 'paid':
                    $query->whereNotNull('payment_date_updated');
                    $payment_status = 'paid';
                    break;
                case 'unpaid':
                    $query->whereNull('payment_date_updated');
                    $payment_status = 'unpaid';
                    break;
                default: break;
            }
            if ($adminType != '1') {

                $query->whereHas('program', function ($qry) use ($programIds) {
                    $qry->whereIn('program_id', $programIds);
                });
            }
            $params = DataTableHelper::getParams($request);

            if ($request->user_id) {
                $userId = ProgramNoteAmount::with('note', 'user', 'program.subSubject', 'program.school')->where('user_id', $request->user_id)->first();
                $userId = $userId->user->first_name .' '. $userId->user->last_name;
                $query->where('user_id', $request->user_id);
            }
            if ($request->program_id) {
                $programId = ProgramNoteAmount::with('note', 'user', 'program.subSubject', 'program.school')->where('program_id', $request->program_id)->first();
                $programId = $programId->program->name;
                $query->where('program_id', $request->program_id);
            }
            if ($request->daterange && strpos($request->daterange, ' TO ') !== false) {
                $range = $request->daterange;
                $separator = ' TO ';

                $dateRange = explode($separator, $request->daterange);
                $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
                $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();
                $query->whereHas('note', function ($q) use ($startDate, $endDate) {
                    $q->whereBetween('class_date', [$startDate, $endDate]);
                });
            }


            if ($params['columnName']) {
                switch($params['columnName']){
                    case 'name':
                        $params['columnName'] = 'program_id';
                        break;
                    case 'user_id':
                        $query->addSelect([
                            'user_name' => User::selectRaw('users.first_name')
                            ->whereColumn('program_note_amounts.user_id', 'users.id')
                            ->limit(1)
                        ]);
                        $params['columnName'] = 'user_name';
                        break;
                    case 'class_date':
                        $query->addSelect([
                            'class_date' => ProgramNote::selectRaw('program_notes.class_date')
                            ->whereColumn('program_note_amounts.program_note_id', 'program_notes.id')
                            ->limit(1)
                        ]);
                        break;
                    case 'school':
                        $query->addSelect([
                            'school' => Programs::selectRaw('tbl_programs.school_name')
                            ->whereColumn('program_note_amounts.program_id', 'tbl_programs.id')
                            ->limit(1)
                        ]);
                        $query->addSelect([
                            'school_name' => User::selectRaw('users.full_name')
                            ->whereColumn('school', 'users.id')
                            ->limit(1)
                        ]);
                        $params['columnName'] = 'school_name';
                        break;
                    case 'subSubject':
                        $query->addSelect([
                            'subSubjectId' => Programs::selectRaw('tbl_programs.sub_subject_id')
                            ->whereColumn('program_note_amounts.program_id', 'tbl_programs.id')
                            ->limit(1)
                        ]);
                        $query->addSelect([
                            'subSubject' => SubsubjectModel::selectRaw('tbl_sub_subjects.name')
                            ->whereColumn('subSubjectId', 'tbl_sub_subjects.id')
                            ->limit(1)
                        ]);
                        break;

                    case 'minutes':
                        $query->addSelect([
                            'program_note_amounts.*',  // Keep all columns from program_note_amounts
                            'minutes' => DB::raw('MOD(SUM(minutes) OVER(), 60)')  // Use window function to avoid grouping
                        ]);
                        break;
                }
                $query->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
            }
            $searchValue = $params['searchValue'];
            if($searchValue){
                $query->where(function ($query) use ($searchValue, $params) {
                    $query->where(function ($que) use ($searchValue) {
                        $que->whereHas('program', function ($query) use ($searchValue) {
                            $query->where('tbl_programs.name', 'LIKE', "%{$searchValue}%")
                            ->orWhere('tbl_programs.id', 'LIKE', "%{$searchValue}%")
                            ->orWhereHas('school', function ($query) use ($searchValue) {
                                $query->where('full_name', 'LIKE', "%{$searchValue}%");
                            })
                            ->orWhereHas('subSubject', function ($query) use ($searchValue) {
                                $query->where('name', 'LIKE', "%{$searchValue}%");
                            });
                        })
                        ->orWhereHas('user', function ($query) use ($searchValue) {
                            $query->where('first_name', 'LIKE', "%{$searchValue}%");
                            $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                            $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                            $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                        })
                        ->orWhereHas('note', function ($query) use ($searchValue) {
                            $query->whereRaw("DATE_FORMAT(class_date, '%m/%d/%Y') LIKE ?", ["%{$searchValue}%"]);
                            $query->orWhereRaw("DATE_FORMAT(class_date, '%m-%d-%Y') LIKE ?", ["%{$searchValue}%"]);
                        });
                    })
                    ->orWhere(function ($subQuery) use ($params) {
                        DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], array_filter($params['columns'], function($column) {
                            return $column['data'] !== 'name' && $column['data'] !== 'class_date'&& $column['data'] !== 'school' && $column['data'] !== 'subSubject' && $column['data'] !== 'id';
                        }));
                    });
                });
            }


            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);
            foreach ($result as $row) {
                $action =  $this->generateActionButtons($row ,$res);
                $viewUser = $viewProgram = "";
                if ($row->program) {
                    $viewProgramRoute =  url('view-program/step1/' . encrypt_str($row->program_id));
                    $viewProgram = " <a target='_blank' href='{$viewProgramRoute}'>{$row->program->id}</a>";
                }

                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a target='_blank' href='{$viewUserRoute}'>{$userName}</a>";
                }

                $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
                $formattedRate = '$' . number_format($row->rate, 2, '.', ',');

                $data[] = [
                    "id" => $row->id,
                    "name" => $viewProgram,
                    "school" => $row->program->school ? $row->program->school->full_name: '',
                    "subSubject" => $row->program->subSubject ?$row->program->subSubject->name : '',
                    "user_id" => $viewUser,
                    "type" => getInstructorType($row->type),
                    "class_date" => !empty($row->additional_class_date) ? ($row->additional_class_date)->format('m-d-Y') : ($row->note ? ($row->note->class_date)->format('m-d-Y') : ''),
                    "hours" => $row->hours,
                    "minutes" => $row->minutes,
                    "rate" => $formattedRate,
                    "amount" => $formattedAmount,
                    "additional_comment" => $row->additional_comment,
                    "payment_date_updated" => getAdminTimestamp($row->payment_date_updated),
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                ];

                $i++;
            }
            $additional_data = [
                "programId" => $programId,
                "userId" => $userId,
                "range" => $range,
                "payment_status" => $payment_status
            ];

            return DataTableHelper::generateResponse($params['draw'], $count, $data, $additional_data);
        }

        return view("admin.manage-payments.payments", compact("sidebarMenu", "programs", "users", "allPrograms"));
    }
    public function view($id, Request $request)
    {
        $info = ProgramNoteAmount::with('note', 'user', 'program')->findOrFail($id);

        $class = $info->note;
        $user = $info->user;
        $program = $info->program;

        $totalamount = $user->programNoteAmounts()->sum('amount');
        $totalhours = $user->programNoteAmounts()->sum('hours');
        $totalminutes = $user->programNoteAmounts()->sum('minutes');


        $view = view("components.admin.modals.view-payment-details", compact('info', 'class', 'program', 'user','totalamount','totalhours','totalminutes'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    private function generateActionButtons($row,$res)
    {
        $id = $row->id;
        $encryptedStrId = encrypt_str($row->id);
        $javascriptVoid = "javascript:void(0);";
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $viewClassButton = '';
        if (isset($res['managePayment'])) :
            if (array_key_exists('managePayment', $res)) :
                if (in_array('view', json_decode($res['managePayment'], true))) :
        $viewClassRoute = route('admin.manage-payments.view', ['id' => $id]);

        $editClassRoute = route('admin.manage-payments-update.update', ['id' => $id]);

        $viewClassButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=\"openAdminModalWithData('{$viewClassRoute}', 'filter-form')\"><i class='fa fa-eye' aria-hidden='true'></i></a>  &nbsp;";
       endif;
       if (in_array('update', json_decode($res['managePayment'], true))) :
        $editButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=openAdminModalsm('{$editClassRoute}')><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";

    endif;
    if (in_array('delete', json_decode($res['managePayment'], true))) :

        $deleteButton = "<a class='admin_payment_delete'  href='{$actionUrl}' data-id='{$encryptedStrId}'><button type='button' title='Delete' class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a> &nbsp;";


    endif;
   endif;
    endif;

        $html = "<div class='w-100 d-flex justify-content-around align-items-around'>{$editButton}{$viewClassButton}{$deleteButton}</div>";
        return $html;
    }


    public function export(Request $request)
    {
        $filters = [];
        parse_str($request->filter_data, $filters);
        $range = '';
        $status = '';
        $rangeFileName = 'Payments';

        if (!empty($filters)) {
            if (!empty($filters['status']) && $filters['status'] != 'all') {
                $status = $filters['status'];
            }

            if (!empty($filters['daterange'])) {
                if(strpos($filters['daterange'], ' TO ') !== false){
                    $dateRange = explode(' TO ', $filters['daterange']);
                    $startDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[0]))->format('mdY');
                    $endDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[1]))->format('mdY');
                    $range = $startDate . '-' . $endDate;
                    // $rangeFileName = 'Payments:'.$range.'.xlsx';
                }
            }
        }

        if (!empty($status)) {
            $rangeFileName .= '_' . $status;
        }
        if (!empty($range)) {
            $rangeFileName .= '_' . $range;
        }
        if (empty($status) && empty($range)) {
            $rangeFileName .= time();
        }

        $rangeFileName .= '.xlsx';

        try {
            ob_end_clean();
            ob_start();
            $fileName = $rangeFileName;
            return Excel::download(new ExportPayments($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }


    public function instructorPaymentsexport(Request $request)
    {
        $range = '';
        $status = '';
        $rangeFileName = 'InstrutcorPayments';

        if (!empty($request->daterange)) {
            if(strpos($request->daterange, ' TO ') !== false){
                $dateRange = explode(' TO ', $request->daterange);
                $startDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[0]))->format('mdY');
                $endDate = DateTime::createFromFormat('m-d-Y', trim($dateRange[1]))->format('mdY');
                $range = $startDate . '-' . $endDate;
                // $rangeFileName = 'InstrutcorPayments:'.$range.'.xlsx';
            }
        }

        if (!empty($request->status) && $request->status != 'all') {
            $status = $request->status;
        }

        if (!empty($status)) {
            $rangeFileName .= '_' . $status;
        }
        if (!empty($range)) {
            $rangeFileName .= '_' . $range;
        }
        if (empty($status) && empty($range)) {
            $rangeFileName .= time();
        }

        $rangeFileName .= '.xlsx';

        try {
            ob_end_clean();
            ob_start();
            $fileName = $rangeFileName;
            return Excel::download(new ExportinstructorPayments($request->all()), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }


    public function delete_payment(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = ProgramNoteAmount::where("id", $id)->first();
            if ($record && $record->program_note_id) {
                $getNotesRecord = ProgramNote::where('id',$record->program_note_id)->first();
                $getNotesRecord->status = 3;
                $getNotesRecord->delete_payment_reason = $request->reason;
                $getNotesRecord->update();
                $res = ProgramNoteAmount::where("id", "=", $id)->delete();
                if($res) {
                    sendDeletePaymentReasonToInstructore($getNotesRecord,$request->reason);
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function update($id, Request $request)
    {
        $info = ProgramNoteAmount::with('note', 'user', 'program')->findOrFail($id);

        $class = $info->note;
        $user = $info->user;
        $program = $info->program;

        $view = view("components.admin.modals.update-payment-details", compact('info', 'class', 'program', 'user'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function updatePayment($id, Request $request)
    {

        $request->validate(
            [
                'amount' => 'required',
                'minutes' => 'required',
                'hours' => 'required',

            ]
        );


        $data["amount"] = $request->amount;
        $data["minutes"] = $request->minutes;
        $data["hours"] = $request->hours;

        $save = ProgramNoteAmount::where("id", $id)->update($data);
        return response()->json(['status' => true, 'message' => "Amount updated successfully"]);
    }


    public function instructorPayments(Request $request)
    {
        setlocale(LC_MONETARY, 'en_US.UTF-8');

        $userId = '';
        $range = '';
        $payment_status = '';
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];
        $programsQry = Programs::query();
        $programsQry->whereHas('programNoteAmounts');

        $programs = $programsQry->pluck('name', 'id');
        $programIds = array_keys($programs->toArray());
        $q = ProgramNoteAmount::where(function ($query) use ($programIds, $request) {
            if ($request->status == 'paid') {
                $query->whereNotNull('payment_date_updated');
            } elseif ($request->status == 'unpaid') {
                $query->whereNull('payment_date_updated');
            }
            $query->whereIn('program_id', $programIds);
        });
        $userIds = $q->pluck('user_id')->toArray();
        $users = User::whereIn('id', $userIds)->get(['id', 'first_name', 'last_name']);
        $sidebarMenu = 'manage-payments';

        if ($request->ajax()) {
            $query = ProgramNoteAmount::with('note', 'user', 'program');

            $params = DataTableHelper::getParams($request);

            if ($request->user_id) {
                $userId = ProgramNoteAmount::with('note', 'user', 'program')->where('user_id', $request->user_id)->first();
                $userId = $userId->user->first_name .' '. $userId->user->last_name;
                $query->where('program_note_amounts.user_id', $request->user_id);
            }
            switch($request->status) {
                case 'paid': $query->whereNotNull('payment_date_updated'); $payment_status = 'paid'; break;
                case 'unpaid': $query->whereNull('payment_date_updated'); $payment_status = 'unpaid'; break;
            }

            if ($request->daterange && strpos($request->daterange, ' TO ') !== false) {
                $range = $request->daterange;
                $separator = ' TO ';

                $dateRange = explode($separator, $request->daterange);
                $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
                $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();
                $query->whereHas('note', function ($q) use ($startDate, $endDate) {
                    $q->whereBetween('class_date', [$startDate, $endDate]);
                });
            }
            if(!empty($params['columnName'])) {
                switch($params['columnName']){
                    case 'id':
                        $params['columnName'] = 'program_note_amounts.id';
                        break;
                    case 'user_id':
                        $params['columnName'] = 'users.first_name';
                        break;
                    case 'user_email':
                        $params['columnName'] = 'users.email';
                        break;
                }
            }
            $searchValue = $params['searchValue'];
            if (!empty($params['searchValue'])) {
                $query->where(function ($que) use ($searchValue) {
                    $que->whereHas('user', function ($query) use ($searchValue) {
                        $query->where('first_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                        $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                        $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                    });
                })
                ->orWhere(function ($subQuery) use ($params) {
                    DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], array_filter($params['columns'], function($column) {
                        return $column['data'] !== 'user_email' && $column['data'] !== 'id' && $column['data'] !== 'class_count' && $column['data'] !== 'user_id';
                    }));
                });
            }
            $query->join('users', 'program_note_amounts.user_id', '=', 'users.id')
                ->select(
                    'program_note_amounts.id',
                    DB::raw('sum(amount) as amount'),
                    DB::raw('count(*) as class_count'),
                    DB::raw('FLOOR(SUM(minutes) / 60) + SUM(hours) as hours'),  // Add extra hours from minutes
                    DB::raw('MOD(SUM(minutes), 60) as minutes'),
                    'program_note_amounts.user_id'
                )
                ->groupBy('program_note_amounts.user_id')
                ->orderBy($params['columnName'] ?? 'program_note_amounts.id', $params['columnSortOrder'] ?? 'desc');

            $aggregationQuery = clone $query;

            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);
            $count = $aggregationQuery->get()->count();

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);


            foreach ($result as $row) {

                $action =  $this->generateActionButtonsins($row,$res);
                $viewUser = $viewProgram = "";


                if ($row->user) {
                    $viewUserRoute =  url('viewinstructordetails/step1/' . encrypt_str($row->user_id));
                    $userName = $row->user->first_name . ' ' . $row->user->last_name;
                    $viewUser = " <a target='_blank' href='{$viewUserRoute}'>{$userName}</a>";
                }

                $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');
                $formattedRate = '$' . number_format($row->rate, 2, '.', ',');

                $data[] = [
                    "id" => $row->id,
                    "user_id" => $viewUser,
                    "user_email" =>$row->user ? $row->user->email : '',
                    "class_count" =>$row->class_count,
                    "hours" => $row->hours,
                    "minutes" => $row->minutes,
                    "amount" => $formattedAmount,
                    "action" => $action,
                ];

                $i++;
            }
            $additional_data = [
                "userId" => $userId,
                "range" => $range,
                "payment_status" => $payment_status
            ];

            return DataTableHelper::generateResponse($params['draw'], $count, $data, $additional_data);
        }

        return view("admin.manage-payments.instructorpayments", compact("sidebarMenu", "programs", "users"));
    }

    private function generateActionButtonsins($row ,$res)
    {
        $id = $row->id;
        $encryptedStrId = encrypt_str($row->id);
        $javascriptVoid = "javascript:void(0);";
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $viewClassButton = '';
        if (isset($res['managePayment'])) :
            if (array_key_exists('managePayment', $res)) :
                if (in_array('view', json_decode($res['managePayment'], true))) :
        $viewClassRoute = route('admin.manage-payments.viewinstructorpaymentmodel', ['id' => $row->user_id]);

        $editClassRoute = route('admin.manage-payments-update.update', ['id' => $id]);

        $viewClassButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=\"openAdminModalWithData('{$viewClassRoute}', 'filter-form')\"><i class='fa fa-eye' aria-hidden='true'></i></a>  &nbsp;";
       endif;
       if (in_array('update', json_decode($res['managePayment'], true))) :
        $editButton = "<a href='{$javascriptVoid}' class='btn btn-outline-info btn-rounded' onclick=openAdminModalsm('{$editClassRoute}')><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";

        endif;
        if (in_array('delete', json_decode($res['managePayment'], true))) :

        $deleteButton = "<a class='admin_payment_delete'  href='{$actionUrl}' data-id='{$encryptedStrId}'><button type='button' title='Delete' class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a> &nbsp;";

        endif;
        endif;
        endif;

        $html = "<div class='w-100 d-flex justify-content-around align-items-around'>{$viewClassButton}</div>";
        return $html;
    }

    // public function viewinstructorpaymentmodel($id, Request $request)
    // {
    //     $user = User::where('id',$id)->first();
    //     $info = ProgramNoteAmount::with('note', 'program')->where('user_id',$user->id)->get();
    //     foreach($info as $infoss){
    //         $scoolname = schoolusername($infoss->program->school_name) ?? '';
    //         $subsubject = subsubjectname($infoss->program->sub_subject_id) ?? '';
    //     }
    //     $view = view("components.admin.modals.view-instructor-payment-details", compact('info','scoolname','subsubject'))->render();
    //     return response()->json(['status' => true, 'view' => $view]);
    // }

    public function viewinstructorpaymentmodel($id, Request $request)
    {
        $user = User::findOrFail($id);
        // Retrieve ProgramNoteAmount records with relationships
        $query = ProgramNoteAmount::with(['note', 'program'])
            ->where('user_id', $user->id);

        switch($request->status) {
            case 'paid': $query->whereNotNull('payment_date_updated'); break;
            case 'unpaid': $query->whereNull('payment_date_updated'); break;
        }
        if ($request->daterange && strpos($request->daterange, ' TO ') !== false) {
            $separator = ' TO ';
            $dateRange = explode($separator, $request->daterange);
            $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
            $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();
            $query->whereHas('note', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('class_date', [$startDate, $endDate]);
            });
        }
        $info = $query->get();

        $userInfo = User::where('id', $user->id)->first();
        // $email = '<EMAIL>';
        $email = $userInfo->email;
        // $quickbooksController = new QuickBooksAuthController();
        // $contractor = $quickbooksController->contractor($email);
        $contractor = null;
        if(!empty($contractor) && $contractor != 'null' && empty($contractor['errorMessage'])){
            $data = json_decode(json_encode($contractor), true);
            $contractor_id = $data['vendor']['Id'];
        }
        else{
            $contractor_id = '';
        }
        $confirmModelRoute = route('admin.manage-payments.confirmpaymentmodel');
        $view = view("components.admin.modals.view-instructor-payment-details", compact('info','contractor','contractor_id','email','confirmModelRoute'))->render();

        return response()->json(['status' => true, 'view' => $view]);
    }



    public function updatePaymentStatus(Request $request)
    {
        $ids = $request->input('program_note_amount_ids');

        $description = $request->description ?? '';
        $totalAmount = $request->total_amount;

        if ($ids) {
            ProgramNoteAmount::whereIn('id', $ids)->update([
                'payment_status' => 'done',
                'payment_date_updated' => \Carbon\Carbon::now(),
            ]);
        }

        if (!empty($request->pay_method) && $request->pay_method == 'quickbooks') {
            // $quickbooksController = new QuickBooksAuthController();
            $payments = [ 'error' => "Quickbooks not integrated" ];
            // $payments = $quickbooksController->billPayment($request->contractor_id, $totalAmount, $description);
            if (!empty($payments['error'])) {
                return response()->json([
                    'success' => true,
                    'message' => $payments['error'],
                    "reload" => false
                ]);
            }
            return response()->json([
                'success' => true,
                'message' => json_encode($payments),
                "reload" => true
            ]);
        }

        if ($ids) {
            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully!',
                "reload" => true
            ]);
        }
    }

    public function getProgramDetails(Request $request)
    {
        $programDetails = Programs::with(['userNotes' => function($query) use ($request) {
                                $query->where('class_date', $request->class_date);
                            }, 'subject', 'subSubject', 'school'])
                            ->where('id', $request->program_id)
                            ->get()
                            ->map(function($program) {
                                // Iterate through each userNote to check sub_user_id
                                $program->userNotes->map(function($note) use ($program) {
                                    // Check if sub_user_id is not null
                                    if ($note->sub_user_id) {
                                        // If sub_user_id is not null, load the user associated with it
                                        $note->user = User::find($note->sub_user_id);
                                    } else {
                                        // If sub_user_id is null, load the main user associated with the program
                                        $note->user = User::find($note->user_id); // Ensure user_id is accessible
                                    }
                                    // Format the amount from the user data, assuming it's a property of user
                                    if (isset($note->user->inpersonrate)) { // Check if amount exists in user data
                                        $note->user->inpersonrate = number_format($note->user->inpersonrate, 2, '.', ''); // Format to 2 decimal places
                                    }elseif ($note->user->onlinerate) {
                                        $note->user->onlinerate = number_format($note->user->onlinerate, 2, '.', '');
                                    }
                                    return $note;
                                });
                                return $program;
                            });
        return response()->json(['status' => 1, 'data'=> $programDetails]);
    }

    public function addPayment(Request $request)
    {
        ProgramNoteAmount::create([
            'program_id' => $request->program_id,
            'user_id' => $request->user_id,
            'program_note_id' => $request->program_note_id ? $request->program_note_id : null,
            'rate' => $request->hourly_per_rate,
            'hours' => $request->hours,
            'minutes' => $request->minutes,
            'format' => $request->format,
            'amount' => $request->amount,
            'type' => $request->instructor_type == 'Main' ? 1 : 0,
            'additional_class_date' => $request->class_date,
            'additional_comment' => $request->additional_comment
        ]);

        return back()->with('success', 'Added Successfully');
    }

    public function confirmpaymentmodel(Request $request)
    {
        if (empty($request->checkedValues)) {
            return response()->json(['status' => false, 'message' => 'Please checked atleast one programs.'],400);
        }
        $query = ProgramNoteAmount::with(['note', 'program', 'user'])
                ->whereIn('id', $request->checkedValues);

        $info = $query->get();
        $email = $request->userEmail;
        $total_amt = 0;
        $contractor_id = $request->contractor_id;
        foreach ($info as $data){
            $program_name = $data->program->name;
            $total_amt += $data->amount; 
        }
        $view = view("components.admin.modals.instructor-confirm-payment", compact('info','email','program_name','total_amt','contractor_id'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


}
