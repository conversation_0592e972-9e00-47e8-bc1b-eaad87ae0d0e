<?php

namespace App;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
DB::enableQueryLog();

class Users extends Model
{
     use SoftDeletes;
     protected $table = 'users';
	 public static function get_single_record($data)
	 {
     	 $value=self::where($data)->first();
     	 return $value;
 	 }


 	  public static function get_client_record($data,$paginate_count)
	 {
     	 $value=self::where($data)->paginate($paginate_count);
     	 return $value;
 	 }

 public static function get_all_client_record($data)
	 {
     	 $value=self::where($data)->orderBy('id','desc')->get();
     	 return $value;
 	 }


  public static function get_all_records($table,$data){
      $value=DB::table($table)->where($data)->get();
      return $value;
  }


 public static function get_records_with_few_fields($data){
         $value=self::select('id','first_name','last_name','institute_name','email','created_at as registeration_date','phone_number','about','image','facebook_url','linkedin_url','twitter_url','status')->where($data)->get();
        return $value;
    }

       public function getJWTIdentifier()
    {
        return $this->getKey();
    }
    public function getJWTCustomClaims()
    {
        return [];
    }


    protected $fillable = [
        'first_name',
        'last_name',
        'image',
        'type',
        'email_verify_time',
        'email_verify_status',
        'email',
        'password',
        'status',
        'social_id'
    ];


}
