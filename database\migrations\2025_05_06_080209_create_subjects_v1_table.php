<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubjectsV1Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subjects_v1', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->unsignedBigInteger('subject_area_id'); // Foreign key for subject area
            $table->string('subject_code', 6)->unique(); // Unique subject code with max length of 6 characters
            $table->string('title'); // Subject title
            $table->text('description'); // Subject description
            $table->timestamps(); // created_at and updated_at

            // Foreign key relation to subject_area_v1 table
            $table->foreign('subject_area_id')->references('id')->on('subject_area_v1')->onDelete('cascade');

            // Index for fast searching by subject code (although unique already indexes it)
            $table->index('subject_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subjects_v1');
    }
}
