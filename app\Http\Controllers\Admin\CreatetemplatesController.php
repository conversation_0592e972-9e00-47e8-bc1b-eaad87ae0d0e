<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Session;

use DB;
use App\Http\Requests;
use App\Users;
use Validator;
use View;
use URL;
use DateTime;
use App\CommomModel;
use App\EmailTemplate;
use Maatwebsite\Excel\Facades\Excel;
use Hash;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

DB::enableQueryLog();

class CreatetemplatesController extends Controller
{
    public function templates_list(Request $request)
    {
        $template = EmailTemplate::paginate(10);
        return view("admin.templates-create.templateslist", [
            "template" => $template,
        ]);
    }
    public function add_email_templates(Request $request)
    {
        return view("admin.templates-create.addtemplates");
    }
    public function save_email_templates(Request $request)
    {
        $data = [];
        $data["title"] = $request->input("title");
        $data["subject"] = $request->input("subjects");
        $data["description"] = $request->input("descriptions");
        $data["status"] = 1;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $save = EmailTemplate::insert($data);
        if ($save) {
            $data["success"] = true;
            $data["message"] = "Email templates added successfully";
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }
    public function email_templates_status_change(Request $request)
    {
        $data["status"] = $request->id1;
        $id = $request->id;
        $record = EmailTemplate::where("email_template_id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = 0;
            $res = EmailTemplate::where("email_template_id", $id)->update(
                $data
            );
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = 1;
            $res = EmailTemplate::where("email_template_id", $id)->update(
                $data
            );
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }
    public function email_templates_delete(Request $request)
    {
        $id = decrypt_str($request->id);
        // echo"<pre>"; print_r($_POST);die;
        if (isset($id)) {
            $record = EmailTemplate::where("email_template_id", $id)->first();
            if ($record) {
                $res = EmailTemplate::where(
                    "email_template_id",
                    "=",
                    $id
                )->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully deleteed",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
    public function edit_email_templates(Request $request)
    {
        $email_template_id = Crypt::decryptString($request->id);
        $templates = EmailTemplate::where(
            "email_template_id",
            $email_template_id
        )->first();
        return view("admin.templates-create.edittemplates", [
            "templates" => $templates,
        ]);
    }
    public function update_email_templates(Request $request)
    {
        $data = [];
        $data["title"] = $request->input("title");
        $data["subject"] = $request->input("subjects");
        $data["description"] = $request->input("descriptions");
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $email_template_id = $request->input("email_template_id");

        $update = EmailTemplate::where(
            "email_template_id",
            $email_template_id
        )->update($data);
        if ($update) {
            $data["success"] = true;
            $data["message"] = "update successfully";
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }

    public function update_news_image(Request $request)
    {
        if ($request->hasFile("news_image")) {
            $image = $request->file("news_image");
            $name = time() . "." . $image->getClientOriginalExtension();
            $destinationPath = public_path("/uploads/news");
            $image->move($destinationPath, $name);
            $folderName = url("/uploads/news/" . $name);
        }

        $id = $request->input("news_id");
        $obj = [];
        $obj["image"] = $folderName;
        $result = NewsModel::where("news_id", $id)->update($obj);
        if ($result) {
            $data["success"] = true;
            $data["message"] = "Image Updated successfully";
            $data["img"] = $name;
        } else {
            $data["success"] = false;
            $data["message"] = "Something went wrong";
        }
        return response()->json($data);
    }
    public function addNewstags(Request $request)
    {
        if ($request->ajax()) {
            $dataArray = [];
            $newstag = $request->input("newstag");
            $news_id = $request->input("news_id");

            $tags = NewsModel::where(["news_id" => $news_id])
                ->select("tags")
                ->first();
            $updatedTags = $tags->tags . "," . $newstag;

            $dataArray = ["tags" => $updatedTags];
            $save = NewsModel::where("news_id", $news_id)->update($dataArray);
            if ($save) {
                $data["success"] = true;
                $data["message"] = "Successfully saved";
            } else {
                $data["success"] = false;
                $data["message"] = "Something went wrong";
            }
            return response()->json($data);
        }
    }
    public function remove_news_tag(Request $request)
    {
        if ($request->ajax()) {
            $id = $request->input("id");
            $tag = $request->input("tag");

            $tags = NewsModel::where(["news_id" => $id])
                ->select("tags")
                ->first();
            $currentTags = explode(",", $tags->tags);
            unset($currentTags[$tag]);
            $currentTags = array_values($currentTags);

            $currentTags = implode(",", $currentTags);
            $save = NewsModel::where("news_id", $id)->update([
                "tags" => $currentTags,
            ]);
            if ($save) {
                $data["success"] = true;
                $data["message"] = "Successfully deleted";
            } else {
                $data["success"] = false;
                $data["message"] = "Something went wrong";
            }
            return response()->json($data);
        }
    }
    public function share_news_details($id)
    {
        $user_id = session("Adminnewlogin")["id"];

        $data["active"] = "news_corner";
        $data["news_id"] = decrypt_str($id);
        $data["student"] = StudentsModel::all();
        $data["list"] = NewsAssignStudent::where("owner_id", $user_id)
            ->with("students")
            ->latest()
            ->get();
        return view("admin.templates-create.sharenewstostudent", $data);
    }

    public function add_student_for_news(request $request)
    {
        $rules["student_id"] = "required";
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->with("error", "Please Select Student");
        }

        $request->owner_id = session("Adminnewlogin")["id"];
        $model = new NewsAssignStudent();
        $model->add_update($request);

        return back();
    }

    public function update_status_assign_stu(request $request)
    {
        $obj = NewsAssignStudent::find($request->id);
        if ($obj->status == 1) {
            $obj->status = 0;
            $message = "Status Deactivated Successfully.";
        } else {
            $obj->status = 1;
            $message = "Status Activated Successfully.";
        }

        $obj->save();

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }
}
