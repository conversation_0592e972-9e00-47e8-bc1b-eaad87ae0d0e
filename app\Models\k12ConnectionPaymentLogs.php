<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class k12ConnectionPaymentLogs extends Model
{
    protected $table = 'k12_connection_payment_logs';
    protected $fillable = [
        'program_id',
        'class_id',
        'user_id',
        'hourly_rate',
        'total_hours',
        'total_minutes',
        'total_amount',
        'status',
        'additional_date',
        'comments',
        'cancellation_reason',
        'paid_at',
    ];

    public function note()
    {
        return $this->belongsTo(k12ConnectionClassLogs::class, 'program_id');
    }
}
