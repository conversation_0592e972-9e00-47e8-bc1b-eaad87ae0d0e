<?php
namespace App\Http\Controllers\WEB;
use App\AvailabilityDayTimeSlotsModel;
use App\AvailabilityModel;
use App\AvailabilityRangeModel;
use App\AvailablityLocationModel;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AvailabilityController extends Controller
{

    public function myavailability()
    {
        $data['hasActiveClasses'] = false;
        $data['user'] = $user = auth()->user();
        if (count($user->activeClasses) > 0 || count($user->activeSubClasses) > 0) {
            $data['hasActiveClasses'] = true;
        }
        $data['availability'] = $availability = $user->availability;

        $data['orgCount'] = 0;
        if ($availability) {
            $data['orgCount'] = $availability->ranges()->count();
        }
        if (
            in_array($user["profile_status"], ['8', '12', '14', '15', '16', '17', '20'])
        ) {

            if ($user->is_approved == '20' || (empty($user->is_approved) && empty($user->teach))) {
                return view('web.user.preferences.myavailability')->with($data);
            } elseif ($user->is_approved == '16' || $user->teach == 'online') {
                return view('web.user.preferences.myavailability_online')->with($data);
            } elseif ($user->is_approved == '17' || $user->teach == 'in-person') {
                return view('web.user.preferences.myavailability_in_person')->with($data);
            } else {
                return view('web.user.preferences.myavailability')->with($data);
            }
        } else {
            return redirect("/web-dashboard");
        }
    }



    public function submitAvailability(Request $request)
    {

        $user = auth()->user();

        if(!empty($request->same_as_online)){
            foreach ($request->same_as_online as $val) {
                if($val==1){
            $fdate=  $request->from_date;
            if (isset($fdate['online'])) {
            }else{
                return response()
                    ->json([
                        'success' => false, 'message' => "Enter online availability details"

                    ], 200);

            }
        }
        }
        }

        $userId = $user->id;
        $teachingTimezone = $request->teach_in_person_timezone ?? config('app.timezone', 'America/Los_Angeles');

        if ($request->avtype == "both") {


            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',

            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_in_person_location_same',
                'teach_in_person_timezone_same',
                'teach_online_timezone',
                "from_teach_in_person_location",
                "to_teach_in_person_location",
                "avtype",
                "same_as_online",
                "lat",
                "lng",
            ]);
            $location = $request->from_teach_in_person_location;
            $lat = $request->lat;
            $lng = $request->lng;
            if($location){

            foreach ($location as $keycheck => $rowslocation) {
                if($lat[$keycheck]){

                }else{
                    return response()
                    ->json([
                        'success' => false, 'message' => "Please select a valid location from the picker."

                    ], 200);
                }

            }
        }



            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;
            $data['teach_in_person_location'] = @$request->teach_in_person_location_same;
            $data['to_teach_in_person_location_same'] = @$request->to_teach_in_person_location_same;
            $data['user_id'] = $userId;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $availabilityModel = AvailabilityModel::where(['user_id' => $userId])->first();
            $oldId = @$availabilityModel->id ?? null;
            optional($availabilityModel)->delete();



            $result = AvailabilityModel::insertGetId($data);




            if ($result) {

                AvailablityLocationModel::where(['user_id' => $userId])->delete();
                if($oldId){

                    AvailabilityRangeModel::where(['availability_id' => $oldId])->delete();
                }
                AvailabilityDayTimeSlotsModel::where(['user_id' => $userId])->delete();

                if (isset($request->from_teach_in_person_location) && isset($request->to_teach_in_person_location)) {
                    $location = $request->from_teach_in_person_location;
                    $radius = $request->to_teach_in_person_location;
                    $lat = $request->lat;
                    $lng = $request->lng;
                    $same_as_online = $request->same_as_online;


                    foreach ($location as $keyl => $rowslocation) {

                        $locationArray[] = ['user_id' => $userId, 'location' => $rowslocation, 'radius' => $radius[$keyl], 'same_as_online' => $same_as_online[$keyl], 'lat' => $lat[$keyl], 'lng' => $lng[$keyl]];
                    }



                    AvailablityLocationModel::insert($locationArray);


                }else{
                    $location=array();
                }

                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];
                    $availabilityrangearrayy = [];
                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {
                        if (isset($fdate['online'])) {
                            if (!empty(array_filter($fdate['online']))) {
                                $k = 0;
                                foreach ($fdate['online'] as $key => $fdates) {
                                    $k++;
                                    $availabilityrangearray[] = ['availability_id' => $result, 'type' => 'online', 'step' => $k, 'position' => $k, 'from_date' => date('Y-m-d',strtotime($fdates)), 'to_date' => date('Y-m-d',strtotime($tdated['online'][$key])) , 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                }
                            }
                        }






                            $locationArray = array();
                              $stkey=0;
                            foreach ($location as $keyl => $rowslocation) {

                                $stkey++;

                                if ($same_as_online[$keyl]=='1') {
                                    $k1=0;
                                    if (isset($fdate['online'])) {
                                    foreach ($fdate['online'] as $key => $fdates) {
                                        $k1++;


                                        $availabilityrangearray[] = ['availability_id' => $result, 'type' => 'inperson', 'step' => $stkey, 'position' =>$stkey, 'from_date' => date('Y-m-d',strtotime($fdates)), 'to_date' => date('Y-m-d',strtotime($tdated['online'][$key])) , 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                               }else{
                                if (isset($fdate['inperson'])) {
                                if (!empty(array_filter($fdate['inperson']))) {
                                    $ke = 0;
                                    foreach ($fdate['inperson'] as $key => $infdates) {
                                        $ke++;



                                        foreach ($infdates as $keys => $rowsdate) {

                                            if($rowsdate[0]){
                                                $fromDate=date('Y-m-d',strtotime($rowsdate[0]));

                                            }else{
                                                $fromDate=NULL;
                                            }

                                            if($tdated['inperson'][$key][$keys][0]){
                                                $toDate=date('Y-m-d',strtotime($tdated['inperson'][$key][$keys][0]));

                                            }else{
                                                $toDate=NULL;
                                            }

                                            $availabilityrangearrayy[] = ['availability_id' => $result, 'type' => 'inperson', 'step' => $ke, 'position' => $keys, 'from_date' =>$fromDate , 'to_date' =>$toDate , 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                        }
                                    }
                                }
                                }



                        }
                        }



                    }


                    $rangeData = array_merge($availabilityrangearray, $availabilityrangearrayy);

                    AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;



                    $availabilitytimesloatrray = [];
                    $availabilitytimesloatrrayy = [];


                    if ($day = $request->day) {

                        if (isset($day['online'])) {
                            $arr12 = array_values(array_filter($day['online']));
                            $arr123 = array_combine(range(1, count($arr12)), $arr12);
                            if (count($arr123) > 0) {
                                $fo1 = array_values(array_filter($from_time['online']));
                                $fo12 = array_combine(range(1, count($fo1)), $fo1);

                                $to1 = array_values(array_filter($to_time['online']));
                                $to12 = array_combine(range(1, count($to1)), $to1);

                                foreach ($arr123 as $key => $ondayrow) {

                                    foreach (array_filter($ondayrow) as $keys => $rowonlinedata) {


                                        $fromTime = formatTimeForTimezone(($fo12[$key][$keys]), $teachingTimezone);
                                        $toTime = formatTimeForTimezone(($to12[$key][$keys]), $teachingTimezone);
                                        $range_id = AvailabilityRangeModel::where(['availability_id' => $result, 'step' => $key,'type' => 'online'])->orderBy('id', 'DESC')->first();

                                        $availabilitytimesloatrray[] =
                                            [
                                                'range_id' =>  optional($range_id)->id ?? null,
                                                'user_id' => $userId,
                                                'step' => $key,
                                                'position' => $key,
                                                'type' => 'online',
                                                'day' => $rowonlinedata,
                                                'from_time' => $fromTime,
                                                'to_time' => $toTime,
                                                'created_at' => date('Y-m-d H:i:s'),
                                                'updated_at' => date('Y-m-d H:i:s')
                                            ];
                                    }
                                }
                            }
                        }
                        $stpkey=0;
   foreach ($location as $keyl => $rowslocation) {
    $stpkey++;
    if ($same_as_online[$keyl]=='1') {


        if (isset($day['online'])) {
            $arr12 = array_values(array_filter($day['online']));
            $arr123 = array_combine(range(1, count($arr12)), $arr12);
            if (count($arr123) > 0) {
                $fo1 = array_values(array_filter($from_time['online']));
                $fo12 = array_combine(range(1, count($fo1)), $fo1);

                $to1 = array_values(array_filter($to_time['online']));
                $to12 = array_combine(range(1, count($to1)), $to1);
                $position=0;
                foreach ($arr123 as $key => $ondayrow) {
                    $position++;
                    foreach (array_filter($ondayrow) as $keys => $rowonlinedata) {


                        $fromTime = formatTimeForTimezone(($fo12[$key][$keys]), $teachingTimezone);
                        $toTime = formatTimeForTimezone(($to12[$key][$keys]), $teachingTimezone);
                        $range_id = AvailabilityRangeModel::where(['availability_id' => $result, 'step' => $stpkey,'type' => 'inperson'])->orderBy('id', 'DESC')->first();

                        $availabilitytimesloatrray[] =
                            [
                                'range_id' =>  optional($range_id)->id ?? null,
                                'user_id' => $userId,
                                'step' => $stpkey,
                                'position' => $position,
                                'type' => 'inperson',
                                'day' => $rowonlinedata,
                                'from_time' => $fromTime,
                                'to_time' => $toTime,
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ];
                    }
                }
            }
        }

    }else{
        if (isset($day['inperson'])) {
            $arr1 = array_values(array_filter($day['inperson']));
            $arr = array_combine(range(1, count($arr1)), $arr1);

            if (count($arr) > 0) {
                $fi1 = array_values(array_filter($from_time['inperson']));
                $fi12 = array_combine(range(1, count($fi1)), $fi1);

                $ti1 = array_values(array_filter($to_time['inperson']));
                $ti12 = array_combine(range(1, count($ti1)), $ti1);



                foreach ($arr as $keyy => $indayrow) {

                    foreach (array_filter($indayrow) as $keyys => $rowinpersondata) {


                        foreach ($rowinpersondata as $k => $rowspersondata) {

                            $fromTime = formatTimeForTimezone(($fi12[$keyy][$keyys][$k]), $teachingTimezone);
                            $toTime = formatTimeForTimezone(($ti12[$keyy][$keyys][$k]), $teachingTimezone);
                            $range_id = AvailabilityRangeModel::where(['availability_id' => $result, 'step' => $keyy,'position'=>$keyys,'type' => 'inperson'])->orderBy('id', 'DESC')->first();

                            $availabilitytimesloatrrayy[] =
                                [
                                    'range_id' =>  @$range_id->id ?? null,
                                    'user_id' => $userId,
                                    'step' => $keyy,
                                    'position' => $keyys,
                                    'type' => 'inperson',
                                    'day' => $rowspersondata,
                                    'from_time' => $fromTime,
                                    'to_time' => $toTime,
                                    'created_at' => date('Y-m-d H:i:s'),
                                    'updated_at' => date('Y-m-d H:i:s')
                                ];
                        }
                    }
                }
            }
        }
    }

   }




                    }

                    $rangeDatas = array_merge($availabilitytimesloatrray, $availabilitytimesloatrrayy);



                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        } elseif ($request->avtype == "online") {

            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',


            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_online_timezone',
                "avtype",
                "lat",
                "lng",

            ]);
            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;


            $data['user_id'] = Auth::user()->id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $first = AvailabilityModel::where(['user_id' => Auth::user()->id])->first();
            if (!empty($first)) {
                AvailabilityModel::where(['user_id' => Auth::user()->id])->update($data);
                $result = $first->id;
            } else {
                $result = AvailabilityModel::insertGetId($data);
            }

            if ($result) {
                AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'online'])->delete();
                AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'online'])->delete();

                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];

                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {
                        if (isset($fdate['online'])) {
                            if (!empty(array_filter($fdate['online']))) {
                                $k = 0;
                                foreach ($fdate['online'] as $key => $fdates) {
                                    $k++;
                                    $availabilityrangearray[] = ['availability_id' => $result, 'type' => 'online', 'step' => $k, 'position' => $k, 'from_date' => date('Y-m-d',strtotime($fdates)), 'to_date' =>  date('Y-m-d',strtotime($tdated['online'][$key])), 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                }
                            }
                        }
                    }


                    $rangeData = $availabilityrangearray;

                    $rangeid = AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;

                    $teach_in_person_timezone = $request->teach_in_person_timezone;

                    $availabilitytimesloatrray = [];

                    if ($day = $request->day) {
                        if (isset($day['online'])) {
                            $arr12 = array_values(array_filter($day['online']));
                            $arr123 = array_combine(range(1, count($arr12)), $arr12);
                            if (count($arr123) > 0) {
                                $fo1 = array_values(array_filter($from_time['online']));
                                $fo12 = array_combine(range(1, count($fo1)), $fo1);

                                $to1 = array_values(array_filter($to_time['online']));
                                $to12 = array_combine(range(1, count($to1)), $to1);

                                foreach ($arr123 as $key => $ondayrow) {

                                    foreach (array_filter($ondayrow) as $keys => $rowonlinedata) {

                                        $fromTime = formatTimeForTimezone(($fo12[$key][$keys]), $teachingTimezone);
                                        $toTime = formatTimeForTimezone(($to12[$key][$keys]), $teachingTimezone);
                                        $range_id = AvailabilityRangeModel::where(['availability_id' => $result, 'step' => $key,'type' => 'online'])->orderBy('id', 'DESC')->first();

                                        $availabilitytimesloatrray[] = [
                                            'user_id' => Auth::user()->id,
                                            'range_id' =>  @$range_id->id ?? null,
                                            'step' => $key,
                                            'position' => $key,
                                            'type' => 'online',
                                            'day' => $rowonlinedata,
                                            'from_time' => $fromTime,
                                            'to_time' => $toTime,
                                            'created_at' => date('Y-m-d H:i:s'),
                                             'updated_at' => date('Y-m-d H:i:s')
                                            ];
                                    }
                                }
                            }
                        }
                    }

                    $rangeDatas = $availabilitytimesloatrray;


                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        } elseif ($request->avtype == "in_person") {

            $validator = Validator::make($request->all(), [
                'teach_minimum' => 'required',
                'teach_maximum' => 'required',


            ]);

            if ($validator->fails()) {
                return response()
                    ->json([
                        'success' => false, 'error' => "Please enter valid details",
                        'message' => $validator->errors(),
                    ], 400);
            }
            $data = $request->except([
                '_token',
                'from_date',
                'to_date',
                'day',
                'from_time',
                'to_time',
                'teach_in_person_location_same',
                'teach_in_person_timezone_same',
                "from_teach_in_person_location",
                "to_teach_in_person_location",
                "avtype",
                "lat",
                "lng",
            ]);
            if (isset($request->same_as_online)) {
                $data['same_as_online'] = $request->same_as_online;
            } else {
                $data['same_as_online'] = 0;
            }
            $data['teach_in_person_timezone'] = $request->teach_in_person_timezone;
            $data['teach_in_person_location'] = $request->teach_in_person_location_same;
            $data['to_teach_in_person_location_same'] = $request->to_teach_in_person_location_same;

            $data['user_id'] = Auth::user()->id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $first = AvailabilityModel::where(['user_id' => Auth::user()->id])->first();
            if (!empty($first)) {
                AvailabilityModel::where(['user_id' => Auth::user()->id])->update($data);
                $result = $first->id;
            } else {
                $result = AvailabilityModel::insertGetId($data);
            }

            if ($result) {
                AvailablityLocationModel::where(['user_id' => $userId])->delete();
                AvailabilityRangeModel::where(['availability_id' => $result, 'type' => 'inperson'])->delete();
                AvailabilityDayTimeSlotsModel::where(['user_id' => Auth::user()->id, 'type' => 'inperson'])->delete();

                if (isset($request->from_teach_in_person_location) && isset($request->to_teach_in_person_location)) {
                    $location = $request->from_teach_in_person_location;
                    $radius = $request->to_teach_in_person_location;
                    $lat = $request->lat;
                    $lng = $request->lng;
                    $locationArray = array();
                    foreach ($location as $keyl => $rowslocation) {
                        $locationArray[] = ['user_id' => Auth::user()->id, 'location' => $rowslocation, 'radius' => $radius[$keyl], 'lat' => $lat[$keyl], 'lng' => $lng[$keyl]];
                    }

                    AvailablityLocationModel::insert($locationArray);
                }
                if (isset($request->from_date) && isset($request->to_date)) {
                    $availabilityrangearray = [];
                    $availabilityrangearrayy = [];
                    $tdated = $request->to_date;
                    if ($fdate = $request->from_date) {

                        if (isset($fdate['inperson'])) {
                            if (!empty(array_filter($fdate['inperson']))) {
                                $ke = 0;
                                foreach ($fdate['inperson'] as $key => $infdates) {
                                    $ke++;

                                    foreach ($infdates as $keys => $rowsdate) {

                                        $availabilityrangearrayy[] = ['availability_id' => $result, 'type' => 'inperson', 'step' => $ke, 'position' => $keys, 'from_date' => date('Y-m-d',strtotime($rowsdate[0])) , 'to_date' =>  date('Y-m-d',strtotime($tdated['inperson'][$key][$keys][0])) , 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')];
                                    }
                                }
                            }
                        }
                    }


                    $rangeData = $availabilityrangearrayy;


                    $rangeid = AvailabilityRangeModel::insert($rangeData);
                }
                if (isset($request->day) && isset($request->from_time) && isset($request->to_time)) {
                    $day = $request->day;
                    $from_time = $request->from_time;
                    $to_time = $request->to_time;

                    $from_teach_in_person_location = $request->from_teach_in_person_location;
                    $to_teach_in_person_location = $request->to_teach_in_person_location;

                    $teach_in_person_timezone = $request->teach_in_person_timezone;
                    $teach_online_timezone = $request->teach_online_timezone;



                    $availabilitytimesloatrray = [];
                    $availabilitytimesloatrrayy = [];


                    if ($day = $request->day) {

                        if (isset($day['inperson'])) {
                            $arr1 = array_values(array_filter($day['inperson']));
                            $arr = array_combine(range(1, count($arr1)), $arr1);

                            if (count($arr) > 0) {
                                $fi1 = array_values(array_filter($from_time['inperson']));
                                $fi12 = array_combine(range(1, count($fi1)), $fi1);

                                $ti1 = array_values(array_filter($to_time['inperson']));
                                $ti12 = array_combine(range(1, count($ti1)), $ti1);



                                foreach ($arr as $keyy => $indayrow) {

                                    foreach (array_filter($indayrow) as $keyys => $rowinpersondata) {


                                        foreach ($rowinpersondata as $k => $rowspersondata) {

                                            $fromTime = formatTimeForTimezone(($fi12[$keyy][$keyys][$k]), $teachingTimezone);
                                            $toTime = formatTimeForTimezone(($ti12[$keyy][$keyys][$k]), $teachingTimezone);

                                            $range_id = AvailabilityRangeModel::where(['availability_id' => $result, 'step' => $keyy,'position' => $keyys,'type' => 'inperson'])->orderBy('id', 'DESC')->first();;




                                            $availabilitytimesloatrrayy[] = [
                                                'user_id' => Auth::user()->id,
                                                'range_id' =>  @$range_id->id ?? null,
                                                'step' => $keyy,
                                                'position' => $keyys,
                                                'type' => 'inperson',
                                                'day' => $rowspersondata,
                                                'from_time' => $fromTime,
                                                'to_time' => $toTime,
                                                'created_at' => date('Y-m-d H:i:s'),
                                                'updated_at' => date('Y-m-d H:i:s')
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $rangeDatas = $availabilitytimesloatrrayy;


                    AvailabilityDayTimeSlotsModel::insert($rangeDatas);
                }
                return response()->json(['success' => true, 'message' => 'Submitted successfully']);
            } else {
                return response()->json(['success' => false, 'error' => 'Something went wrong']);
            }
        }

    }

    public function getFields(Request $request)
    {
        $form = $request->view;
        $view = view("components.availability.{$form}", compact('request'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
}
