<?php

namespace App\Exports\Front;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportReimbursements implements FromCollection, WithHeadings, WithMapping
{
    protected $requestFilters;

    public function __construct($request)
    {
        $this->requestFilters = $request->filter_data;
    }


    public function collection()
    {
        $user = auth()->user();

        $query = $user->reimbursements()->where(function ($que) {
            $que->where('status', 1)->orWhere('status', 3);
        });
        $this->applyFilters($query);

        return $query->get();
    }

    protected function applyFilters($query)
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);


        if (!empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }
        $separator = ' TO ';

        if (!empty($filters['daterange']) && strpos($filters['daterange'], $separator) !== false) {
            $this->applyDateRangeFilter($query, $filters['daterange'], $separator);
        }
    }

    protected function applyDateRangeFilter($query, $daterange, $separator)
    {

        $dateRange = explode($separator, $daterange);
        $startDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[0]))->startOfDay();
        $endDate = Carbon::createFromFormat('m-d-Y', trim($dateRange[1]))->endOfDay();

        $query->whereBetween('updated_at', [$startDate, $endDate]);
    }

    public function headings(): array
    {
        $filtersInfo = $this->getFiltersInfo();
        $baseHeadings =  [
            'School Name',
            'Program Name',
            'Date Approved',
            'Description',
            'Amount',
        ];

        $headings = [];
        if (!empty($filtersInfo)) {
            $filtersRows = explode(PHP_EOL, $filtersInfo);
            foreach ($filtersRows as $filter) {
                $headings[] = [$filter];
            }
        }
        $headings[] = $baseHeadings;

        return $headings;
    }

    public function map($row): array
    {
        $formattedAmount = '$' . number_format($row->amount, 2, '.', ',');

        return [
            @$row->program->school->full_name??'NIL',
            $row->program ? $row->program->name : 'NIL',
            $row->updated_at ? $row->updated_at->format('m-d-Y') : null,
            $row->description,
            $formattedAmount,
        ];
    }

    // Function to get details of applied filters.
    protected function getFiltersInfo(): string
    {
        $filters = [];
        parse_str($this->requestFilters, $filters);

        $filtersInfo = '';

        if (!empty($filters['program_id'])) {
            $filtersInfo .= 'Program: ' . Programs::find($filters['program_id'])->name . PHP_EOL;
        }

        if (!empty($filters['daterange'])) {
            $filtersInfo .= 'Date Range: ' . $filters['daterange'] . PHP_EOL;
        }

        return $filtersInfo;
    }
}
