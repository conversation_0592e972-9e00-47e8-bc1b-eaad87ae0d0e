<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\EmailTemplate;
use App\Helpers\NotificationHelper;
use DB;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Users;
use App\LastChatModel;


class ChatController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */


    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managechat','view')!=true){
            return redirect("/no-permission");
        }

        $id = session("Adminnewlogin")["id"];
        $user=Users::select('users.*')->join('tbl_last_chat_message','tbl_last_chat_message.to_id','=','users.id')->where(['tbl_last_chat_message.from_id'=> $id])->orderBy('tbl_last_chat_message.id','DESC')->get();

        return view("admin.chat.index", compact("user"));
    }

    public function sendMsgForAdmin(Request $request){
        $already=LastChatModel::where(['from_id'=>$request->from_id,'to_id'=>$request->to_id])->first();
        if(!$already){
            LastChatModel::insert(['from_id'=>$request->from_id,'to_id'=>$request->to_id]);
        }

    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function sendMultiMsgForAdmin(Request $request){
        $from_id=session("Adminnewlogin")["id"];
        $toid=$_POST['id'];
        $message = $_POST['message'];
        $message_type = $_POST['message_type'];
        $count = 0;
        foreach($toid as $id){
            $already = LastChatModel::where(['from_id'=>$from_id, 'to_id'=>$id, 'message'=>$message])->first();
            if(!$already){
                $count++;
                $user = Users::find($id);
                $email = $user['email'];
                $emailTemplate = EmailTemplate::where('title', 'Message copy')->limit(1)->get();
                $subject = $this->getEmailSubject($message_type);
                $fullName = $user['first_name'] . ' ' . $user['last_name'];
                $emailBody = str_replace(['{{ NAME }}','{{ message }}'], [$fullName, $message], $emailTemplate[0]->description);
                NotificationHelper::sendEmail($email, $subject, $emailBody);
                LastChatModel::insert(['from_id'=>$from_id,'to_id'=>$id, 'message'=>$message]);
            }
        }
        if($count > 0){
            return response()->json([
                "success" => true,
                "message" => "Message sent successfully",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Message already sent",
            ]);
        }
    }

    private function getEmailSubject($message_type, $company_name = 'Whizara') {
        $subject = '';
        switch ($message_type) {
            case 'action_required':
                $subject = "Action Required: Important Message from $company_name";
                break;
            case 'important_notification':
                $subject = "Important Notification: Message from $company_name";
                break;
            case 'reminder':
                $subject = "Reminder: Please Review Your Latest Message";
                break;
            case 'system_alert':
                $subject = "System Alert: Important Update from $company_name";
                break;
            case 'event_update':
                $subject = "Event Update: Important Details Inside";
                break;
            case 'new_feature_announcement':
                $subject = "New Feature: Check Out What's New at $company_name";
                break;
            case 'security_alert':
                $subject = "Security Alert: Important Message Regarding Your Account";
                break;
            case 'general_information':
                $subject = "Information Update: New Message from $company_name";
                break;
            default:
                $subject = "New Message from $company_name";
                break;
        }
        return $subject;
    }
}

?>
