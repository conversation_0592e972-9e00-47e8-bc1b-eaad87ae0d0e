<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Classes;
use Hash;
use Mail;
use Crypt;
use App\CommomModel;
DB::enableQueryLog();

class ClassController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'manageclass','view')!=true){
            return redirect("/no-permission");
        }  
        $where = [];
        $class = Classes::all();
        return view("admin.class.classess", compact("class"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function addclass()
    {
        return view("admin.class.addclass");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function saveclass(Request $request)
    {
       
        $name = $request->name;

        if ($name != "") {
            $classExits = Classes::where("class_name", "=", $name)->get();
            if (count($classExits)) {
                return response()->json([
                    "success" => false,
                    "message" => "Class already exits",
                ]);
            } else {
                $data["class_name"] = $request->name;

                $data["status"] = "1";
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = Classes::insertGetId($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "class  successfully created",
                        "redirect" => url("/class-list"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $class = Classes::where("id", $id)->first();

        return view("admin.class.edit_class", ["class" => $class]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
       
        $id = $request->id;
        $name = $request->name;

        if ($name) {
            $Exits = Classes::where("class_name", "=", $name)
                ->where("id", "!=", $id)
                ->get();

            if (count($Exits)) {
                return response()->json([
                    "success" => false,
                    "message" => "Class already exits",
                ]);
            } else {
                $data["class_name"] = $request->name;

                $data["status"] = "1";
                $data["updated_at"] = date("Y-m-d H:i:s");
                $save = Classes::where("id", $id)->update($data);

                if ($save) {
                    return response()->json([
                        "success" => true,
                        "message" => "Details successfully updated",
                        "redirect" => url("/class-list"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Classes::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Classes::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Classes::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = Classes::where("id", $id)->first();
            if ($record) {
                $res = Classes::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
