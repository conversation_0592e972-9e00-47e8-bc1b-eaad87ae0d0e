<?php

namespace App\Models;

use App\Subject;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlatformSchoolRequirements extends Model
{
    use SoftDeletes;
    protected $table = 'platform_school_requirements';
    protected $fillable = [
        'status',
        'requirement_type',
        'requirement_title',
        'requirement_name',
        'school_id',
        'proctor_id',
        'class_type',
        'delivery_mode',
        'subject_area_id',
        'subject_id',
        'grade_levels_id',
        'capacity',
        'description',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'start_date',
        'end_date',
        'time_zone',
        'no_class_dates',
        'schedules',
        'sw_requirements',
        'bg_check_requirements',
        'medical_requirement',
        'other_requirements',
        'certifications_valid',
        'compensation_type',
        'compensation_amount_min',
        'compensation_amount_max',
        'benefits',
        'is_valid',
        'totalHours',
        'finalize_setup',
        'profileType_requirements',
        'language_requirements',
        'will_choose_educator',
        'credential_check',
        'special_education_certificate',
        'will_follow_provided_curriculum',
        'provide_schedule_access',
        'no_instrtructional_days',
        'class_duration',
        'no_non_instructional_hr',
        'schedule_type',
        'regular_days',
        'schedule_1_days',
        'schedule_2_days',
        'sch_cal_screenshot',
        'district_cal_screenshot',
        'teacher_schedule_screenshot',
        'class_details',
        'experience',
        'qualifications',
        'total_budget',
        'benefits'

    ];

    public function subject() {
        return $this->belongsTo(Subject::class, 'subject_area_id');
    }

    public function school()
    {
        return $this->belongsTo(User::class, 'school_id');
    }

    public function totalClassHours()
    {
        return 10;
    }

    public function reviewApplicants()
    {
        return $this->hasMany(SchoolReviewApplicants::class, 'requirement_id');
    }

    public function classes()
    {
        return $this->hasManyThrough(
            k12ConnectionClasses::class,  // Final table
            k12ConnectionPrograms::class, // Intermediate table
            'requirement_id', // Foreign key in k12ConnectionPrograms (jo `platform_school_requirements` ko reference karega)
            'program_id', // Foreign key in k12ConnectionClasses (jo `k12ConnectionPrograms` ko reference karega)
            'id', // Local key in PlatformSchoolRequirements
            'id' // Local key in k12ConnectionPrograms
        );
    }

    // Relationship with Chat
    public function chats()
    {
        return $this->hasMany(Chat::class, 'referance_id', 'id');
    }

    public function proctor() 
    {
        return $this->belongsTo(PlatformSchoolProctor::class, 'proctor_id');
    }

}