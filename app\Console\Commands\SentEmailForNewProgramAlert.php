<?php

namespace App\Console\Commands;


use App\Programs;
use App\Helpers\CustomHelper;
use Illuminate\Console\Command;

class SentEmailForNewProgramAlert extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'programs:sent-email-for-new-programs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check programs with notification_sent set to true, reset it, and run additional code';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Fetch all programs with notification_sent set to true
        $programs = Programs::where('notification_sent', false)->get();
        try {
            foreach ($programs as $program) {
                // Reset notification_sent to true
                $program->notification_sent = true;
                $program->save();

                CustomHelper::alertProgramUsers($program, false);
            }
        } catch (Exception $e) {
            echo $e->getMessage();
        }

        $this->info('Notification flags reset and additional processing done for all relevant programs.');
        return Command::SUCCESS;
    }
}
