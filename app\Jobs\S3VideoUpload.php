<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class S3VideoUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filename;
    protected $tempFilePath;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($filename, $tempFilePath)
    {
        $this->filename = $filename;
        $this->tempFilePath  = $tempFilePath;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $filePath = public_path($this->tempFilePath);
        if (File::exists($filePath)) {
            // Read the content of the video file
            $videoContent = File::get($filePath);

            // Log the process for debugging (you may want to remove this in production)
            Log::info('Uploading video to S3: ' . $this->filename);

            // Upload the video to S3 using your custom upload function
            uploads3image($this->filename, $videoContent);

            // Delete the file after uploading
            File::delete($filePath);

            // Log success
            Log::info('Video uploaded and temporary file deleted.');
        } else {
            // Log an error if the file does not exist
            Log::error('File not found: ' . $filePath);
        }
    }
}
