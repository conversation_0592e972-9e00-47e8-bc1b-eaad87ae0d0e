<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\CustomHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\InstructorAssignRequest;
use App\Http\Requests\Admin\ResendInviteRequest;
use App\invite_programs;
use App\InviteProgramNote;
use App\ProgramNote;
use App\{Programs,Notification_content, OnboardingInstructor, ShortlistMarketplaceInstructorModel, Users};
use App\User;
use Illuminate\Http\Request;
use App\Helpers\DataTableHelper;
use App\ProgramCertificate;
use DB;


class InstructorAssignController extends Controller
{
    public function assignClassSub(ProgramNote $programNote, Request $request)
    {
        /*         $errors = CustomHelper::checkProgramsBackground($program);

        if (!empty($errors)) {
            return $this->jsonErrorResponse(["message" => $errors], false);
        } */
        $users = CustomHelper::getProgramUsers($programNote->program, null,  true);
        // $users = $usrQry->select('users.id', 'users.first_name', 'users.last_name')->get();
        $storeRoute = route('admin.program-class.assign-sub-instructor.store', ['programNote' => $programNote->id]);

        $view = view("components.admin.modals.assign-sub-instructor", compact('programNote', 'users', 'storeRoute'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeClassSub(ProgramNote $programNote, InstructorAssignRequest $request)
    {

        $program = $programNote->program;
        $user_id = $request->user_id;
        $errors = CustomHelper::checkProgramsBackground($program, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }

        $programId = $programNote->program_id;
        $deadline = $request->deadline;
        $invite = invite_programs::create([
            'program_id' => $programId,
            'user_id' => $user_id,
            'deadline' => $deadline ?? null,
            'admin_type' => 0,
            'is_sub_only' => 1,
        ]);
        $programNote->is_sub_requested = 1;
        $programNote->save();

        $obj = new InviteProgramNote();
        $obj->invite_program_id = $invite->id;
        $obj->program_note_id = $programNote->id;
        $obj->save();

        $this->sendNotification($user_id);

        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function assignMultiClassSub(Programs $program, Request $request)
    {
        if (!$request->filled('program_note_id')) {
            return $this->jsonErrorResponse(["message" => "Please select at least one class"], false);
        }
        $program_note_ids = $request->program_note_id;
        $timezone = $program->timezone ?? 'America/Los_Angeles';

        $classDate = $program->userNotes()->whereIn('id', $program_note_ids)->value('class_date');
        $toDate = \Carbon\Carbon::parse($classDate)->timezone($timezone);
        $maxClassDate = $classDate
                ? $classDate->isPast() 
                    ? \Carbon\Carbon::now()->addDays(5)->timezone($timezone)->format('m/d/Y')
                    : $toDate->format('m/d/Y')
                : null;

        $storeRoute = route('admin.program.assign-multi-main.store');
        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $program->id)
            ->get();
        $programIds = [];
        $programIds[] = $program->id;
        $view = view("components.admin.modals.assignfilter.assign-multi-class-sub-instructor", compact('program', 'maxClassDate', 'storeRoute', 'program_note_ids', 'slots', 'programIds', 'timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function storeMultiClassSub(Programs $program, InstructorAssignRequest $request)
    {
        $user_id = $request->user_id;
        $errors = CustomHelper::checkProgramsBackground($program, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }

        $programId = $program->id;
        $deadline = $request->deadline;
        $program_note_ids = $request->program_note_id;
        $timezone = $program->timezone;
        $currentTime = $request->deadline_time;

        $invite = invite_programs::create([
            'program_id' => $programId,
            'user_id' => $user_id,
            'deadline' => $deadline ?? null,
            'timezone' => $timezone,
            'current_time' => $currentTime,
            'admin_type' => 0,
            'is_sub_only' => 1,
        ]);


        foreach ($program_note_ids as $program_note_id) {
            $programNote = ProgramNote::find($program_note_id);
            $programNote->is_sub_requested = 1;
            $programNote->save();
            $obj = new InviteProgramNote();
            $obj->invite_program_id = $invite->id;
            $obj->program_note_id = $program_note_id;
            $obj->save();
        }



        $this->sendNotification($user_id);

        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function assignSub(Programs $program, Request $request)
    {

        /* $errors = CustomHelper::checkProgramsBackground($program);

        if (!empty($errors)) {
            return $this->jsonErrorResponse(["message" => $errors], false);
        }

        $users = CustomHelper::getProgramUsers($program, $program->subUser->id ?? null, $is_sub = 1);
        */
        $programIds = [];
        $programIds[] = $program->id;

        $storeRoute = route('admin.program.assign-multi-main.store');

        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $program->id)
            ->get();
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $startDate = $program->start_date ? \Carbon\Carbon::parse($program->start_date)->timezone($timezone)->format('m/d/Y') : null;
        $endDate = $program->end_date ? \Carbon\Carbon::parse($program->end_date)->timezone($timezone)->format('m/d/Y') : null;
        $view = view("components.admin.modals.assign-sub-common-Instructor", compact('storeRoute', 'programIds', 'program', 'slots', 'timezone', 'startDate', 'endDate'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function assignSubstandby(Programs $program, Request $request)
    {

        /* $errors = CustomHelper::checkProgramsBackground($program);

        if (!empty($errors)) {
            return $this->jsonErrorResponse(["message" => $errors], false);
        }

        $users = CustomHelper::getProgramUsers($program, $program->subUser->id ?? null, $is_sub = 1);
        */
        $programIds = [];
        $programIds[] = $program->id;

        $storeRoute = route('admin.program.assign-multi-main.store');

        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $program->id)
            ->get();
        $view = view("components.admin.modals.assignfilter.assign-substandby-common-Instructor", compact('storeRoute', 'programIds', 'program', 'slots'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function storeSub(Programs $program, InstructorAssignRequest $request)
    {

        $user_id = $request->user_id;
        $errors = CustomHelper::checkProgramsBackground($program, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }
        $programId = $program->id;
        $timezone = $program->timezone;
        $deadline = $request->deadline;
        invite_programs::create([
            'program_id' => $programId,
            'user_id' => $user_id,
            'timezone' => $timezone,
            'deadline' => $deadline ?? null,
            'admin_type' => 0,
            'is_sub_only' => 1,
        ]);
        /*         Programs::where("id", $programId)->update([
            'program_status' => 'Upcoming',
        ]); */
        $this->sendNotification($user_id);


        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function assignMain(Programs $program, Request $request)
    {
        $programIds = [$program->id];
        $storeRoute = route('admin.program.assign-multi-main.store');
        $userNotes = $program->userNotes()->orderBy('class_date', 'asc')->get();
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $now = \Carbon\Carbon::now()->timezone($timezone);
        $maxDeadline = null;

        foreach ($userNotes as $note) {
            $classDate = \Carbon\Carbon::parse($note->class_date)->timezone($timezone)->format('Y-m-d');
            $startTime = \Carbon\Carbon::parse($note->start_time)->timezone($timezone)->format('H:i:s');
            $classDateTime = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $classDate . ' ' . $startTime, $timezone);
            if ($note->status !== 0 && $note->status !== 1 && $note->status !== 2 && $note->status !== 3 && $classDateTime->isFuture()) {
                $maxDeadline = $classDateTime;
                break;
            }
        }
        if (!$maxDeadline) {
            $maxDeadline = $now;
        }

        $maxDate = $maxDeadline->format('m/d/Y');
        $minDate = $now->format('m/d/Y');
        $view = view("components.admin.modals.assignfilter.assign-main-common-Instructor", compact('storeRoute', 'programIds', 'program', 'timezone', 'now', 'minDate', 'maxDate'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeMain(Programs $program, InstructorAssignRequest $request)
    {
        $user_id = $request->user_id;
        $errors = CustomHelper::checkProgramsBackground($program, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }
        $programId = $program->id;
        $deadline = $request->deadline;
        invite_programs::create([
            'program_id' => $programId,
            'user_id' => $user_id,
            'deadline' => $deadline ?? null,
            'admin_type' => 1,
        ]);
        /*         Programs::where("id", $programId)->update([
            'program_status' => 'Upcoming',
        ]); */
        $this->sendNotification($user_id);



        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }


    public function assignMultiMain(Request $request)
    {
        if (!$request->filled('program_id')) {

            return $this->jsonErrorResponse(["message" => "Please select at least one program"], false);
        }
        $programIds = $request->program_id;
        $errors = CustomHelper::checkProgramsBackground($programIds);
        if (!empty($errors)) {
            //return $this->jsonErrorResponse(["message" => $errors], false);
        }
        $users = CustomHelper::getProgramsUsers($programIds, null, null);
        $program = Programs::where("id", $programIds[0])->with('creator')->first();
        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $programIds[0])
            ->get();
        $storeRoute = route('admin.program.assign-multi-main.store');
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        // $view = view("components.admin.modals.assign-main-Instructor", compact('users',  'storeRoute', 'programIds'))->render();
        $view = view("components.admin.modals.assignfilter.assign-common-Instructor", compact('users',  'storeRoute', 'programIds', 'program', 'slots', 'timezone'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeMultiMain(InstructorAssignRequest $request)
    {
        $user_id = $request->user_id;
        $programIds = $request->program_id;
        $errors = CustomHelper::checkProgramsBackground($programIds, $user_id);
        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }
        foreach ($programIds as $programId) {

            $deadline = $request->deadline;

            invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'deadline' => $deadline ?? null,
                'admin_type' => 1,
            ]);
            /*             Programs::where("id", $programId)->update([
                'program_status' => 'Upcoming',
            ]); */
            $this->sendNotification($user_id);
        }


        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function assignMultiSub(Request $request)
    {
        if (!$request->filled('program_id')) {
            return $this->jsonErrorResponse(["message" => "Please select at least one program"], false);
        }
        $programIds = $request->program_id;
        $errors = CustomHelper::checkProgramsBackground($programIds);

        if (!empty($errors)) {

            //return $this->jsonErrorResponse(["message" => $errors], false);

        }
        $users = CustomHelper::getProgramsUsers($programIds, true);

        $storeRoute = route('admin.program.assign-multi-sub.store');
        $view = view("components.admin.modals.assign-sub-instructor", compact('users',  'storeRoute', 'programIds'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }


    public function storeMultiSub(InstructorAssignRequest $request)
    {
        $user_id = $request->user_id;
        $programIds = $request->program_id;
        $errors = CustomHelper::checkProgramsBackground($programIds, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }



        foreach ($programIds as $programId) {

            $deadline = $request->deadline;

            invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'deadline' => $deadline ?? null,
                'admin_type' => 0,

            ]);
            /*             Programs::where("id", $programId)->update([
                'program_status' => 'Upcoming',
            ]); */
            $this->sendNotification($user_id);
        }
        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    private function sendNotification($user_id, $title = 'New Program Alert', $notification = 'New Program Alert', $type = 'program',$programid = 0)
    {
            if($programid > 0){
                $program = Programs::where('id',$programid)->first();
                $body =  @$notification;
                $format = $program->delivery_type;
                if ($format == 'In-Person') {
                    $city_name = 'in ' . $program->city;
                } else {
                    $city_name = '';
                }
                $start_date = date('m-d-Y', strtotime($program->start_date));
                $end_date = date('m-d-Y', strtotime($program->end_date));
                $school_name = schoolusername($program->school_name);
                $body = str_replace('{{school_name}}', $school_name, $body);
                $body = str_replace('{{format}}', $format, $body);
                $body = str_replace('{{city_name}}', $city_name, $body);
                $body = str_replace('{{start_date}}', $start_date, $body);
                $body = str_replace('{{end_date}}', $end_date, $body);
            }
            $data['user_id'] = $user_id;
            $data['title'] = $title;
            $data['notification'] = $body;
            $data['type'] = $type;
            $data['is_read'] = 0;
            createNotification($data);
    }


    private function jsonResponse($data, $status = true, $reload = true)
    {

        return response()->json(array_merge([
            "status" => $status,
            "reload" => $reload,
        ], $data), 200);
    }

    private function jsonErrorResponse($data, $status = true, $reload = true)
    {

        return response()->json(array_merge([
            "status" => $status,
            "reload" => $reload,
        ], $data), 422);
    }
    public function searchInstructors(Request $request)
    {
        $qry = User::active()->where("type", "=", "5")
            ->where("profile_status", "=", "12")
            ->orderBy('inpersonrate', 'ASC')
            ->orderBy('onlinerate', 'ASC');
        $this->applyFilters($qry, $request);
        $searchUsers = $qry->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);
        $specialUsers =  User::active()->special()->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

        $allUsers = $searchUsers->merge($specialUsers);
        $users = $allUsers->unique('id');
        $view = view("components.admin.instructors", compact('users'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    private function applyFilters($q, Request $request)
    {


        if ($request->filled('background_check')) {
            $q->whereHas('backgroundVerifications', function ($query) {
                $query->where(["type" => "background_check", "status" => "1"]);
            });
        }
        if ($request->filled('medical_requirements')) {
            $q->whereHas('backgroundVerifications', function ($query) {
                $query->where(["type" => "medical_requirements", "status" => "1"]);
            });
        }


        if ($request->filled(['location', 'lat', 'lng'])) {
            $lat = $request->lat;
            $lng = $request->lng;

            $q->whereHas('availableLocations', function ($query) use ($lat, $lng) {
                $query->selectRaw(
                    '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                    [$lat, $lng, $lat]
                )
                    ->orderByRaw('distance');
            });
        }
        //tbl_user_doc_requests

        if ($request->filled('state')) {
            $q->where('state', 'LIKE', "%{$request->input('state')}%");
        }

        if ($request->filled('city')) {
            $q->where('city', 'LIKE', "%{$request->input('city')}%");
        }

        if ($request->filled('subject')) {
            $q->join('tbl_user_teaching_preferences', 'tbl_user_teaching_preferences.user_id', '=', 'users.id')
                ->join('tbl_user_subjects', 'tbl_user_subjects.step_id', '=', 'tbl_user_teaching_preferences.id')
                ->where('tbl_user_subjects.subject', trim($request->subject));

            if ($request->filled('sub_subject')) {
                $q->where('tbl_user_subjects.sub_subject', trim($request->sub_subject));
            }
        }


        if ($request->filled('certified_teacher')) {
            $q->join('tbl_user_experiences', 'tbl_user_experiences.user_id', '=', 'users.id')
                ->where('tbl_user_experiences.profile_type', 'LIKE', "%{$request->input('certified_teacher')}%");
        }

        if ($request->filled('profile_status')) {
            $q->where('profile_status', 'LIKE', "%{$request->input('profile_status')}%");
        }

        if ($request->filled('format')) {
            $q->join('tbl_user_teaching_preferences as third', 'third.user_id', '=', 'users.id');

            $str = '';
            $i = 1;

            foreach ($request->input('format') as $key => $value) {
                $str .= 'FIND_IN_SET("' . $value . '" ,third.format)';
                if ($i < count($request->input('format'))) {
                    $str .= ' OR ';
                }
                $i++;
            }

            $q->whereRaw($str);
        }
    }

    public function assignMultiSearch(Request $request)
    {
        if (!$request->filled('user_id')) {

            return $this->jsonErrorResponse(["message" => "Please select an Instructor"], false);
        }

        if (!$request->filled('program_id')) {

            return $this->jsonErrorResponse(["message" => "Please select at least one program"], false);
        }
        $programIds = $request->program_id;
        $user_id = $request->user_id;

        $storeRoute = route('admin.program.assign-multi-search.store');

        $view = view("components.admin.modals.assign-search-Instructor", compact('user_id', 'storeRoute', 'programIds'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function storeMultiSearch(InstructorAssignRequest $request)
    {
        $user_id = $request->user_id;
        $programIds = $request->program_id;
        $admin_type = $request->admin_type;
        $errors = CustomHelper::checkProgramsBackground($programIds, $user_id);

        if (!empty($errors)) {
            return $this->jsonResponse(["message" => $errors], false, false);
        }
        foreach ($programIds as $programId) {

            $deadline = $request->deadline;

            invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'deadline' => $deadline ?? null,
                'admin_type' => $admin_type,
            ]);
            /*             Programs::where("id", $programId)->update([
                'program_status' => 'Upcoming',
            ]); */
            $this->sendNotification($user_id);
        }


        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function getProgaramInstructors(Programs $program, Request $request)
    {
        $admin_type = $request->admin_type;

        $user_id = null;
        $is_sub =  false;

        if ($admin_type  == '0') {
            $user_id = $program->subUser->id ?? null;
            $is_sub = true;
        } elseif ($admin_type  == '1') {
            $user_id = $program->mainUser->id ?? null;
        }

        $users = CustomHelper::getProgramUsers($program, $user_id, $is_sub);
        $view = view("components.admin.instructors", compact('users'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function showResendInvite($id)
    {
        $program = invite_programs::find($id);
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $storeRoute = route('admin.program.resend-invite.store', ['id' => $program->id]);
        $view = view("components.admin.modals.resend-invite", compact('storeRoute','timezone','program'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function resendInvite($id, ResendInviteRequest $request)
    {
        $notify_resend_invite = Notification_content::where('signature','new-main-instructor-invite')->first();
        $invite_program = invite_programs::find($id);
        $deadline = $request->deadline;
        $invite_program->increment('resend_count');
        $invite_program->deadline = $deadline;
        $invite_program->created_at = now();
        $invite_program->save();
        $deadline_date = date('m-d-Y', strtotime($deadline));
        $deadline_time = date('h:i A', strtotime($deadline));
        $content = $notify_resend_invite->content;
        $content = str_replace('{{deadline_date}}', $deadline_date, $content);
        $content = str_replace('{{deadline_time}}', $deadline_time, $content);



        $this->sendNotification($invite_program->user_id,$notify_resend_invite->title,$content,'Program',$invite_program->program_id);

        return $this->jsonResponse(["message" => "Invite Successfully Resend"]);
    }

    public function search_invite_ins_program(Request $request)
    {

        $programIds = $request->program_id;
        $All = $request->All ?? null;
        $Special = $request->Special ?? null;

        $ins_type = $request->ins_type ?? null;
        if ($ins_type == 'Sub') {
            $qry = User::active()->where("type", "=", "5")
                ->where("profile_status", "=", "12")->where("is_sub", "=", "1");
        } else {
            $qry = User::active()->where("type", "=", "5")
                ->where("profile_status", "=", "12");
        }

        $searchUsers = $qry->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);
        if ($All) {
            if ($ins_type == 'Sub') {
                $allUsers =  User::active()->where("type", "=", "5")->where("profile_status", "=", "12")->where("is_sub", "=", "1")->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);
            } else {
                $allUsers =  User::active()->where("type", "=", "5")->where("profile_status", "=", "12")->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);
            }
            $users = $allUsers->unique('id');
        } elseif ($Special) {
            $specialUsers =  User::active()->special()->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

            $allUsers = $searchUsers->merge($specialUsers);
            $users = $allUsers->unique('id');
        } else {
            $users = CustomHelper::getProgramsUsersCommon($programIds, null, null);
        }


    }

    public function instructorinvitelist(Request $request)
    {

        $program = Programs::find($request->program_id);

        if ($request->ajax()) {

            $delivery_type = $program->delivery_type;

            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }

            $qry = User::query();
            $qry->active()
                ->whereNotNull('users.is_approved')
                ->where("users.type", "=", "5")
                ->where("profile_status", "=", "12");
            if ($request->is_makeuppopup != 1 && $program->activeMainUser()) {
                $qry->whereDoesntHave('invitePrograms', function ($query) use ($program) {
                    $query->where('status', 1)
                        ->where('admin_type', 1)
                        ->where('program_id', $program->id)
                        ->where(function ($subQuery) {
                            $subQuery->whereColumn('updated_at', '<=', DB::raw('(SELECT MAX(updated_at) FROM tbl_invite_programs WHERE status = 1 AND admin_type = 1 AND program_id = tbl_invite_programs.program_id)'));
                        });
                });
            }

            $this->applyInviteFilters($qry, $request, $program);

            $qry->select('users.*')
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc')
                ->orderBy('inpersonrate', 'ASC')
                ->orderBy('onlinerate', 'ASC');

            $qry->where(function ($que) use ($params) {
                $que->where(function ($que) use ($params) {
                    DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                })->orWhere(function ($query) use ($params) {
                    $searchValue = $params['searchValue'];
                    $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                    $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                    $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                });
            });

            $result = $qry->get();

            if ($request->Availability) {
                $filteredUsers = $result->filter(function ($user) use ($program, $request) {
                    return CustomHelper::checkUserAvalibility($user, $program, $request);
                });
                [$count, $result] = DataTableHelper::applyCustomPagination($filteredUsers, $params['row'], $params['rowperpage']);
            }




            if ($request->is_makeuppopup == 1) {
                if ($request->class_date && $request->start_time &&  $request->end_time) {

                    $filteredUsers =$result = $qry->get();
                    $class = [];
                    $class['class_date'] =  date('Y-m-d', strtotime($request->class_date));
                    $class['start_time'] = formatTimeAdminTimezone($request->start_time, $program->timezone);
                    $class['end_time'] =  formatTimeAdminTimezone($request->end_time, $program->timezone);
                    if ($request->Availability) {


                        $filteredUsers = $result->filter(function ($user) use ($program, $request, $class) {
                            if (CustomHelper::checkUserAvalibilityforMakupClass($user, $program, $request)) {

                                $pids = CustomHelper::checkAndGetUserClass($user, $class);
                                $user->pids = $pids;
                                return empty($pids) ? true : false;
                            }
                            return false;
                        });
                    }

                    [$count, $result] = DataTableHelper::applyCustomPagination($filteredUsers, $params['row'], $params['rowperpage']);
                } else {
                    return DataTableHelper::generateResponse($params['draw'], 0, $data = []);
                }
            }



            if (!$request->Availability && $request->is_makeuppopup != 1) {

                [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);
            } elseif ($request->Availability && $request->is_makeuppopup != 1) {

                $result = $qry->get();

                $filteredUsers = $result->filter(function ($user) use ($program, $request) {
                    return CustomHelper::checkUserAvalibilityWithDate($user, $program, $request);
                });

                [$count, $result] = DataTableHelper::applyCustomPagination($filteredUsers, $params['row'], $params['rowperpage']);
            }

            $data = [];
            $i = 1;
            foreach ($result as $row) {

                if ($request->is_makeuppopup == 1 && isset($class)) {
                    $pids = CustomHelper::checkAndGetUserClass($row, $class);
                } else {
                    $pids = CustomHelper::checkAndGetUserClasses($row, $program);
                }


                if ($request->is_makeuppopup == 1 && @$program->mainAssignedUser->id == @$row->id) {

                    $distance = "";
                    if ($delivery_type == 'In-Person') {
                        $distance = "NIL";
                        $distanceCalculation = '(3959 * acos(cos(radians(?)) * cos(radians(avLocation.lat)) * cos(radians(avLocation.lng) - radians(?)) + sin(radians(?)) * sin(radians(avLocation.lat))))';

                        $distanceModel = DB::table('tbl_user_availability_location as avLocation')
                            ->where('avLocation.user_id', $row->id)
                            ->whereNotNull('avLocation.lat')
                            ->whereNotNull('avLocation.lng')
                            ->selectRaw("$distanceCalculation as distance", [
                                $program->lat,
                                $program->lng,
                                $program->lat
                            ])
                            ->orderByRaw('distance ASC')
                            ->first();
                        if ($distanceModel) {
                            $distance = !is_null($distanceModel->distance) ? number_format($distanceModel->distance, 2) . ' miles' : 'NIL';
                        }
                    }

                    $viewButton = $viewAButton = $programsButton = "";
                    $encryptedStrId = encrypt_str($row->id);

                    $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                    $viewRoute =  url('viewinstructordetails/step1/' . $encryptedStrId);
                    $viewARoute =  url('viewinstructordetails/step12/' . $encryptedStrId);
                    $viewButton .= " <a href='{$viewRoute}' target='_blank'>{$row->first_name} {$row->last_name}</a>";

                    $viewAButton = " <a href='{$viewARoute}' target='_blank'>Check Availability</a>";

                    if (!empty($pids)) {
                        $programs =  Programs::whereIn('id', $pids)->pluck('name', 'id');
                        foreach ($programs as $key => $value) {
                            $viewProgramRoute =  url('view-program/step1/' . encrypt_str($key));
                            $programsButton .= " <a target='_blank' href='{$viewProgramRoute}'>{$value}</a>";
                        }
                    } else {
                        $programsButton = "<span class='badge bg-success'>Available</span>";
                    }

                    $viewLButton = " <a href='{$viewARoute}' target='_blank'>View location</a>";


                    $data[] = [
                        "id" => $row->id,
                        "first_name" => $viewButton,
                        "email" => $row->email,
                        "grade" => $this->generategrade($row->id),
                        "city" => $viewLButton,
                        "distance" => $distance,
                        "aval" => $viewAButton,
                        "programs" => $programsButton,
                        "teach" => $this->generateTeachtype($row->is_approved),


                    ];

                    $i++;
                } else {

                    $distance = "";
                    if ($delivery_type == 'In-Person') {
                        $distance = "NIL";
                        $distanceCalculation = '(3959 * acos(cos(radians(?)) * cos(radians(avLocation.lat)) * cos(radians(avLocation.lng) - radians(?)) + sin(radians(?)) * sin(radians(avLocation.lat))))';

                        $distanceModel = DB::table('tbl_user_availability_location as avLocation')
                            ->where('avLocation.user_id', $row->id)
                            ->whereNotNull('avLocation.lat')
                            ->whereNotNull('avLocation.lng')
                            ->selectRaw("$distanceCalculation as distance", [
                                $program->lat,
                                $program->lng,
                                $program->lat
                            ])
                            ->orderByRaw('distance ASC')
                            ->first();
                        if ($distanceModel) {
                            $distance = !is_null($distanceModel->distance) ? number_format($distanceModel->distance, 2) . ' miles' : 'NIL';
                        }
                    }


                    $viewButton = $viewAButton = $programsButton = "";
                    $encryptedStrId = encrypt_str($row->id);
                    if (empty($pids)) {

                        $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                    } else {
                        if ($request->model_type == 'Sub') {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }
                        if ($request->class_date && $request->start_time &&  $request->end_time) {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }

                        if (in_array($program->id, $pids) && $request->is_makeuppopup != 1 && $request->model_type != 'Sub') {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }
                    }
                    $viewRoute =  url('viewinstructordetails/step1/' . $encryptedStrId);
                    $viewARoute =  url('viewinstructordetails/step12/' . $encryptedStrId);
                    $viewButton .= " <a href='{$viewRoute}' target='_blank'>{$row->first_name} {$row->last_name}</a>";

                    $viewAButton = " <a href='{$viewARoute}' target='_blank'>Check Availability</a>";

                    // if (!empty($pids) && $request->Availability) {
                    //     $programsButton = "<span class='badge bg-danger'>Not Available</span>";
                    // } else

                    if (!empty($pids)) {
                        $programs =  Programs::whereIn('id', $pids)->pluck('name', 'id');
                        foreach ($programs as $key => $value) {
                            $viewProgramRoute =  url('view-program/step1/' . encrypt_str($key));
                            $programsButton .= " <a target='_blank' href='{$viewProgramRoute}'>{$value}</a>";
                        }
                    } else {
                        $programsButton = "<span class='badge bg-success'>Available</span>";
                    }


                    $viewLButton = " <a href='{$viewARoute}' target='_blank'>View location</a>";


                    $data[] = [
                        "id" => $row->id,

                        "first_name" => $viewButton,

                        "email" => $row->email,
                        "grade" => $this->generategrade($row->id),
                        "city" => $viewLButton,
                        "distance" => $distance,
                        "aval" => $viewAButton,
                        "programs" => $programsButton,
                        "teach" => $this->generateTeachtype($row->is_approved),


                    ];

                    $i++;
                }
            }


            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }


    public function instructorsubinvitelist(Request $request)
    {
        $program = Programs::find($request->program_id);

        if ($request->ajax()) {
            $delivery_type = $program->delivery_type;
            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'users.id';
            }

            $qry = User::query();
            $qry->active()
                ->whereNotNull('users.is_approved')
                ->where("users.type", "=", "5")
                ->where("profile_status", "=", "12");

            $this->applyInviteFilters($qry, $request, $program);

            $qry->select('users.*')
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc')
                ->orderBy('inpersonrate', 'ASC')
                ->orderBy('onlinerate', 'ASC');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            $result = $qry->get();

            if ($request->Availability) {
                $filteredUsers = $result->filter(function ($user) use ($program, $request) {
                    return CustomHelper::checkUserSubAvalibility($user, $program, $request);
                });
                [$count, $result] = DataTableHelper::applyCustomPagination($filteredUsers, $params['row'], $params['rowperpage']);
            } else {
                [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);
            }

            $data = [];
            $i = 1;
            foreach ($result as $row) {
                $pids = CustomHelper::checkAndGetUserClasses($row, $program);
                if ($request->is_makeuppopup == 1 && $program->mainAssignedUser->id == $row->id) {
                    $distance = "";
                    if ($delivery_type == 'In-Person') {
                        $distance = "NIL";
                        $distanceCalculation = '(3959 * acos(cos(radians(?)) * cos(radians(avLocation.lat)) * cos(radians(avLocation.lng) - radians(?)) + sin(radians(?)) * sin(radians(avLocation.lat))))';
                        $distanceModel = User::where('users.id', $row->id)->selectRaw("$distanceCalculation as distance", [$program->lat, $program->lng, $program->lat])
                            ->rightJoin('tbl_user_availability_location as avLocation', 'avLocation.user_id', '=', 'users.id')
                            ->orderByRaw('distance', 'ASC')
                            ->first();
                        if ($distanceModel) {
                            $distance = !is_null($distanceModel->distance) ? number_format($distanceModel->distance, 2) . ' miles' : 'NIL';
                        }
                    }

                    $viewButton = $viewAButton = $programsButton = "";
                    $encryptedStrId = encrypt_str($row->id);
                    $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                    $viewRoute =  url('viewinstructordetails/step1/' . $encryptedStrId);
                    $viewARoute =  url('viewinstructordetails/step12/' . $encryptedStrId);
                    $viewButton .= " <a href='{$viewRoute}' target='_blank'>{$row->first_name} {$row->last_name}</a>";
                    $viewAButton = " <a href='{$viewARoute}' target='_blank'>Check Availability</a>";

                    if (!empty($pids)) {
                        $programs =  Programs::whereIn('id', $pids)->pluck('name', 'id');
                        foreach ($programs as $key => $value) {
                            $viewProgramRoute =  url('view-program/step1/' . encrypt_str($key));
                            $programsButton .= " <a target='_blank' href='{$viewProgramRoute}'>{$value}</a>";
                        }
                    } else {
                        $programsButton = "<span class='badge bg-success'>Available</span>";
                    }

                    $viewLButton = " <a href='{$viewARoute}' target='_blank'>View location</a>";

                    $data[] = [
                        "id" => $row->id,
                        "first_name" => $viewButton,
                        "email" => $row->email,
                        "grade" => $this->generategrade($row->id),
                        "city" => $viewLButton,
                        "distance" => $distance,
                        "aval" => $viewAButton,
                        "programs" => $programsButton,
                        "teach" => $this->generateTeachtype($row->is_approved),
                    ];
                    $i++;
                } else {
                    $distance = "";
                    if ($delivery_type == 'In-Person') {
                        $distance = "NIL";
                        $distanceCalculation = '(3959 * acos(cos(radians(?)) * cos(radians(avLocation.lat)) * cos(radians(avLocation.lng) - radians(?)) + sin(radians(?)) * sin(radians(avLocation.lat))))';
                        $distanceModel = User::where('users.id', $row->id)->selectRaw("$distanceCalculation as distance", [$program->lat, $program->lng, $program->lat])
                            ->rightJoin('tbl_user_availability_location as avLocation', 'avLocation.user_id', '=', 'users.id')
                            ->orderByRaw('distance', 'ASC')
                            ->first();
                        if ($distanceModel) {
                            $distance = !is_null($distanceModel->distance) ? number_format($distanceModel->distance, 2) . ' miles' : 'NIL';
                        }
                    }

                    $viewButton = $viewAButton = $programsButton = "";
                    $encryptedStrId = encrypt_str($row->id);

                    if (empty($pids)) {
                        $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                    } else {
                        if ($request->model_type == 'Sub') {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }
                        if ($request->class_date && $request->start_time &&  $request->end_time) {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }
                        if (in_array($program->id, $pids) && $request->is_makeuppopup != 1 && $request->model_type != 'Sub') {
                            $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                        }
                    }

                    $viewRoute =  url('viewinstructordetails/step1/' . $encryptedStrId);
                    $viewARoute =  url('viewinstructordetails/step12/' . $encryptedStrId);
                    $viewButton .= " <a href='{$viewRoute}' target='_blank'>{$row->first_name} {$row->last_name}</a>";
                    $viewAButton = " <a href='{$viewARoute}' target='_blank'>Check Availability</a>";

                    if (!empty($pids)) {
                        $programs =  Programs::whereIn('id', $pids)->pluck('name', 'id');
                        foreach ($programs as $key => $value) {
                            $viewProgramRoute =  url('view-program/step1/' . encrypt_str($key));
                            $programsButton .= " <a target='_blank' href='{$viewProgramRoute}'>{$value}</a>";
                        }
                    } else {
                        $programsButton = "<span class='badge bg-success'>Available</span>";
                    }

                    $viewLButton = " <a href='{$viewARoute}' target='_blank'>View location</a>";

                    $data[] = [
                        "id" => $row->id,
                        "first_name" => $viewButton,
                        "email" => $row->email,
                        "grade" => $this->generategrade($row->id),
                        "city" => $viewLButton,
                        "distance" => $distance,
                        "aval" => $viewAButton,
                        "programs" => $programsButton,
                        "teach" => $this->generateTeachtype($row->is_approved),
                    ];
                    $i++;
                }
            }
            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }

    private function applyInviteFilters($q, Request $request, $programs)
    {
        $standbyIds = $programs->standByUsersActive->pluck('id')->toArray();

        // $blockUsers = [];
        // if ($request->is_makeuppopup != 1) {
        //     $blockUsers = $programs->mainAssignedUser ? [$programs->mainAssignedUser->id] : [];
        // }
        // $blockedUsers = $programs->userNotes()->where('is_makeup_class', null)->whereIn('user_id', $blockUsers)->pluck('user_id')->toArray();

        $invitesidsmain = array();
        $invitesids = array();

        // if ($request->standBy) {
        //     $invitesids = $programs->invites()->where('user_id', '!=', null)->where('is_standby', '!=', 1)->pluck('user_id')->toArray();
        //     if (!empty($invitesids)) {
        //         $invitesidsmain = $programs->userNotes()->where('is_makeup_class', null)->whereIn('user_id', $invitesids)->pluck('user_id')->toArray();
        //         if (!empty($invitesidsmain)) {
        //             $q->whereNotIn('users.id', $invitesidsmain);
        //         }
        //     }
        // } else {
        //     $invitesids = $programs->invites()->where('user_id', '!=', null)->where('status', '=', 1)->pluck('user_id')->toArray();
        //     if (!empty($invitesids) && !$request->model_type == 'Sub' && !$request->assign_type == '0') {
        //         $invitesidsmain = $programs->userNotes()->where('is_makeup_class', null)->whereIn('user_id', $invitesids)->pluck('user_id')->toArray();
        //         if (!empty($invitesidsmain)) {
        //             $q->whereNotIn('users.id', $invitesidsmain);
        //         }
        //     }
        // }


        // if (!$request->standBy) {
        //     if (!empty($blockedUsers)) {
        //         $q->whereNotIn('users.id', $blockedUsers);
        //     }
        // }


        if ($request->standBy) {
            $q->whereIn('users.id', $standbyIds)->whereHas('programs', function ($query) {
                $query->where('tbl_invite_programs.is_standby', 1)
                    ->where('tbl_invite_programs.status', 1);
            });
        } elseif ($request->assign_type && $request->assign_type == 'standBy') {
            $q->whereNotIn('users.id', $standbyIds);
        }

        if ($request->request_by) {
            $q->where('users.id', '!=', $request->request_by);
        }

        if ($request->ins_type == 'Sub') {
            $q->where('users.is_sub', '1');
        }

        if ($request->input('backgroundcheck')) {
            $q->whereHas('backgroundVerifications', function ($query) {
                $query->where(["type" => "background_check", "status" => "1"]);
            });
        }

        if ($request->input('Medical')) {
            $q->whereHas('backgroundVerifications', function ($query) {
                $query->where(["type" => "medical_requirements", "status" => "1"]);
            });
        }

        if ($request->Location) {
            $isApprovedArr = getApprovedArray($programs->delivery_type);
            if (!in_array(16, $isApprovedArr)) {
                $q->whereHas('availableLocations', function ($query) use ($programs) {
                    $query->selectRaw(
                        '(6371000 * acos(cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) + sin(radians(?)) * sin(radians(lat)))) as distance',
                        [$programs->lat, $programs->lng, $programs->lat]
                    )->addSelect('radius')
                        ->havingRaw('distance <=  (radius * ?)', [1609.34])->orderByRaw('distance');
                });
            }
        }
        $delivery_type = $programs->delivery_type;

        if ($request->Format) {
            if ($delivery_type == 'Online') {
                $q->whereIn('users.is_approved', array('16', '20'));
            } elseif ($delivery_type == 'In-Person') {
                $q->whereIn('users.is_approved', array('17', '20'));
            }
        }

        if ($request->Grade) {
            $q->join('tbl_user_teaching_preferences as third', 'third.user_id', '=', 'users.id');
            $str = '';
            $i = 1;
            $classs = [];
            $classs = $programs->classes()->pluck('class_id')->toArray();
            if (!empty($classs)) {
                foreach ($classs as  $value) {
                    $str .= 'FIND_IN_SET("' . $value . '" ,third.i_prefer_to_teach)';
                    if ($i < count($classs)) {
                        $str .= ' AND ';
                    }
                    $i++;
                }
                $q->whereRaw($str);
                if (!empty($invitesids) &&  !$request->assign_type == 'Sub') {
                    $q->whereNotIn('third.user_id', $invitesidsmain);
                    $q->whereNotIn('users.id', $invitesidsmain);
                }
            }
        }

        if ($request->Subject && $programs->subject_id) {
            $q->with('subjects');
            $subsubject = $request->subsubject;
            $subject_id = $programs->subject_id;
            $sub_subject_id = $programs->sub_subject_id;
            $q->whereHas('subjects', function ($query) use ($subject_id, $subsubject, $sub_subject_id) {
                $query->where('tbl_user_subjects.subject', '=', $subject_id);
                if ($subsubject && $sub_subject_id) {
                    $query->where('tbl_user_subjects.sub_subject', '=',  $sub_subject_id);
                }
            });
        }

        if ($request->Format) {
            $delivery_type = $programs->delivery_type;
            if ($delivery_type == 'Online') {
                $q->whereIn('users.is_approved', array('16', '20'));
            } elseif ($delivery_type == 'In-Person') {
                $q->whereIn('users.is_approved', array('17', '20'));
            }
        }

        if ($request->filled('certifications')) {
            $certIds = $request->certifications;
            $certificates = ProgramCertificate::whereIn('id', $certIds)->get();
            if ($certificates->isNotEmpty()) {
                foreach ($certificates as $certificate) {
                    $q->whereHas('certificates', function ($query) use ($certificate) {
                        $query->where("certification", "yes")
                            ->where("teaching_certification_states", $certificate->state)
                            ->whereRaw("FIND_IN_SET(?, certified_special_education)", [$certificate->certificate]);
                    });
                }
            }
        }
    }

    private function generateTeachtype($is_approved)
    {
        switch ($is_approved) {
            case 16:
                return 'Online';
            case 17:
                return 'In-person';
            case 20:
                return 'Online/In-person';
            default:
                return '';
        }
    }
    private function generategrade($id)
    {

        $user_third_step = DB::table("tbl_user_teaching_preferences")
            ->where("user_id", $id)
            ->first();

        if (isset($user_third_step->i_prefer_to_teach)) {
            return  rtrim(gradeLevel($user_third_step->i_prefer_to_teach), ",");
        } else {
            return 'NIL';
        }
    }

    public function storeMultiCommon(Request $request)
    {
        if ($request->assign_type == 'standBy' && $request->ins_type == 'standBy') {
            return $this->jsonResponse(["message" => "Instructor already StandBy"], false, false);
        }
        $currentDate = now()->toDateString();
        $request->validate(
            ['assign_type' => 'required',]
        );
        if (!$request->filled('standBy')) {
            $request->validate(
                ['deadline' => 'required',]
            );
        }
        $assign_type = $request->assign_type;
        $all_classes = $request->all_classes;
        $programId = $request->program_id;
        $user_id = $request->instructor;
        $deadline = $request->deadline;
        $nassign_type = $request->assign_type;
        $program = Programs::find($programId);
        $timezone = $program->timezone;
        if ($request->replacement_start_date) {
            $replacement_start_date =  date('Y-m-d', strtotime($request->replacement_start_date));
            $currentDate = $replacement_start_date;
        }
        if ($assign_type == 0 && !$all_classes) {
            if (!$request->filled('Classes')) {
                return $this->jsonResponse(["message" => "Please select Classes"], false, false);
            }
        }
        if (!$request->filled('instructor')) {
            return $this->jsonResponse(["message" => "Please select instructor"], false, false);
        }
        if ($program->program_status == 'Draft') {
            $program_invite_type = 'Draft';
            if ($request->assign_type == '1') {
                $deaftInvites = invite_programs::where([
                    'program_id' => $programId,
                    'user_id' => $user_id,
                    'admin_type' => 1,
                    'timezone' => $timezone,
                    'program_invite_type' => 'Draft',
                ])->where('deadline', '>=', now()->toDateString())->pluck('id')->toArray();
                if (!empty($deaftInvites)) {
                    return $this->jsonResponse(["message" => "Already Saved"], false, false);
                }
            } elseif ($request->assign_type == 'standBy') {
                $deaftInvites = invite_programs::where([
                    'program_id' => $programId,
                    'user_id' => $user_id,
                    'is_standby' => 1,
                    'timezone' => $timezone,
                    'program_invite_type' => 'Draft',
                ])->where('deadline', '>=', now()->toDateString())->pluck('id')->toArray();
                if (!empty($deaftInvites)) {
                    return $this->jsonResponse(["message" => "Already Saved"], false, false);
                }
            }
        } else {
            $program_invite_type = NULL;
        }
        if ($program->program_status == 'Publish') {
            if ($request->assign_type == '1') {
                $deaftInvites = invite_programs::where([
                    'program_id' => $programId,
                    'user_id' => $user_id,
                    'admin_type' => 1,
                    'timezone' => $timezone,
                    'status' => NULL,
                ])->where('deadline', '>=', now()->toDateString())->pluck('id')->toArray();
                if (!empty($deaftInvites)) {
                    return $this->jsonResponse(["message" => "Already Invited"], false, false);
                }
            } elseif ($request->assign_type == 'standBy') {
                $deaftInvites = invite_programs::where([
                    'program_id' => $programId,
                    'user_id' => $user_id,
                    'is_standby' => 1,
                    'timezone' => $timezone,
                    'status' => NULL,
                ])->where('deadline', '>=', now()->toDateString())->pluck('id')->toArray();
                if (!empty($deaftInvites)) {
                    return $this->jsonResponse(["message" => "Already Invited"], false, false);
                }
            } elseif ($request->assign_type == '0') {
                $deaftInvites = invite_programs::where([
                    'program_id' => $programId,
                    'user_id' => $user_id,
                    'admin_type' => 0,
                    'timezone' => $timezone,
                    'status' => NULL,
                ])->where('deadline', '>=', now()->toDateTimeString())->pluck('id')->toArray();
                if (!empty($deaftInvites)) {
                    $subInvites = InviteProgramNote::whereIn('invite_program_id', $deaftInvites)->whereIn('program_note_id', $request->Classes)->pluck('id')->toArray();
                    if (!empty($subInvites) && count($request->Classes) == count($subInvites)) {
                        return $this->jsonResponse(["message" => "Already Invited"], false, false);
                    }
                }
            }
        }
        $standBy = 0;
        $programNoteQuery = ProgramNote::whereNull(['note', 'status']);
        $programNoteQuery->where('program_id', $programId);
        if (!$all_classes  && $assign_type != 1) {
            $programNoteQuery->whereIn('id', $request->Classes);
        }

        $classes = ($programNoteQuery->pluck('class_date')->toArray());
        $program_note_ids = $programNoteQuery->pluck('id')->toArray();
        $users_ids = $programNoteQuery->pluck('user_id')->toArray();
        if ($assign_type == 0 || $assign_type == 'standBy') {
            $standBy = $assign_type == 'standBy' ? 1 : 0;
            $assign_type = 0;
            if (empty($program_note_ids)) {
                return $this->jsonResponse(["message" => "Class log for the selected date is already submitted"], false, false);
            }
        }
        if ($request->ins_type == 'standBy' && $assign_type != 0  && $assign_type != 1) {
            $standByInvite = invite_programs::where([
                'program_id' => $programId,
                'user_id' => $user_id,
                'is_standby' => 1,
                'timezone' => $timezone,
                'is_approved' => 1,
            ])->first();
            if (!$standByInvite) {
                return $this->jsonResponse(["message" => "Invite to instructor failed"], false, false);
            }
            if ($standByInvite->notes->isNotEmpty() && empty($program_note_ids)) {
                $noteIds = $standByInvite->notes->pluck('program_note_id')->toArray();
                $classes = ($programNoteQuery->pluck('class_date')->toArray());
                ProgramNote::whereIn('id', $noteIds)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            } elseif (!empty($program_note_ids)) {
                ProgramNote::whereIn('id', $program_note_ids)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            }
            $obj =   invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'program_invite_type' => $program_invite_type,
                'deadline' => $deadline ?? null,
                'timezone' => $timezone,
                'replacement_start_date' => $replacement_start_date ?? null,
                'admin_type' => 0,
                'type' => 0,
                'status' => 1,
                'is_approved' => 1,
                'is_sub_only' => 0,
                'is_standby' => 0,
            ]);
            return $this->jsonResponse(["message" => "Successfully Invited"]);
        }
        if ($request->ins_type == 'standBy' && $assign_type == 1) {
            $standByInvite = invite_programs::where([
                'program_id' => $programId,
                'user_id' => $user_id,
                'is_standby' => 1,
                'is_approved' => 1,
                'timezone' => $timezone,
            ])->first();
            if (!$standByInvite) {
                return $this->jsonResponse(["message" => "Invite to main instructor failed"], false, false);
            }
            if (!empty($users_ids)) {
                invite_programs::whereIn('user_id', $users_ids)->where('program_id', $programId)->where('is_standby', '!=', 1)->whereNull('is_approved')->update(['is_standby' => 1]);
            }
            if ($standByInvite->notes->isNotEmpty() && empty($program_note_ids)) {
                $noteIds = $standByInvite->notes->pluck('program_note_id')->toArray();
                ProgramNote::whereIn('id', $noteIds)->where('program_id', $programId)->update(['user_id' => $user_id]);
            } elseif (!empty($program_note_ids)) {
                ProgramNote::whereIn('id', $program_note_ids)->where('program_id', $programId)->update(['user_id' => $user_id]);
            }
            $obj =   invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'program_invite_type' => $program_invite_type,
                'deadline' => $deadline ?? null,
                'timezone' => $timezone,
                'replacement_start_date' => $replacement_start_date ?? null,
                'admin_type' => 1,
                'type' => 1,
                'status' => 1,
                'is_approved' => 1,
                'is_sub_only' => 0,
                'is_standby' => 0,
            ]);
            return $this->jsonResponse(["message" => "Successfully Invited"]);
        }
        if ($request->standBy == 'standBy' && $assign_type == 1 && false) {
            if ($program->school_name) {
                $school_name = institudeName($program->school_name);
            } else {
                $school_name = '';
            }
            $standByInvite = invite_programs::where([
                'program_id' => $programId,
                'user_id' => $user_id,
                'is_standby' => 1,
                'timezone' => $timezone,
                'status' => 1,
            ])->first();
            if (!$standByInvite) {
                return $this->jsonResponse(["message" => "Invite to main instructor failed"], false, false);
            }
            if (!empty($users_ids)) {
                invite_programs::whereIn('user_id', $users_ids)->where('program_id', $programId)->where('is_standby', '!=', 1)->whereNull('is_approved')->update(['is_standby' => 1]);
            }
            if ($standByInvite->notes->isNotEmpty() && empty($program_note_ids)) {
                $noteIds = $standByInvite->notes->pluck('program_note_id')->toArray();
                ProgramNote::whereIn('id', $noteIds)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            } elseif (!empty($program_note_ids)) {
                ProgramNote::whereIn('id', $program_note_ids)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            }
            $obj =   invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'program_invite_type' => $program_invite_type,
                'deadline' => $deadline ?? null,
                'timezone' => $timezone,
                'replacement_start_date' => $replacement_start_date ?? null,
                'admin_type' => 1,
                'type' => 0,
                'is_approved' => 0,
                'is_sub_only' => 0,
                'is_standby' => 0
            ]);
            createProgramCommanNotification($user_id, $deadline, $program, '9', 'user', 'user', $school_name, $sendFooter = true, $classes);
            return $this->jsonResponse(["message" => "Successfully Invited"]);
        }
        if ($assign_type == 0 && $request->ins_type == 'standBy') {
            $standByInvite = invite_programs::where([
                'program_id' => $programId,
                'user_id' => $user_id,
                'is_standby' => 1,
                'timezone' => $timezone,
                'is_approved' => 1,
            ])->first();
            if (!$standByInvite) {
                return $this->jsonResponse(["message" => "Invite to main instructor failed"], false, false);
            }
            if ($standByInvite->notes->isNotEmpty() && empty($program_note_ids)) {
                $noteIds = $standByInvite->notes->pluck('program_note_id')->toArray();
                ProgramNote::whereIn('id', $noteIds)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            } elseif (!empty($program_note_ids)) {
                ProgramNote::whereIn('id', $program_note_ids)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            }
            $obj =   invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'program_invite_type' => $program_invite_type,
                'deadline' => $deadline ?? null,
                'timezone' => $timezone,
                'replacement_start_date' => $replacement_start_date ?? null,
                'admin_type' => 0,
                'type' => 0,
                'status' => 1,
                'is_approved' => 1,
                'is_sub_only' => 1,
                'is_standby' => 0,
            ]);
            return $this->jsonResponse(["message" => "Successfully Invited"]);
        }
        if ($assign_type == 0 && $request->standBy == 'standBy') {
            $standByInvite = invite_programs::where([
                'program_id' => $programId,
                'user_id' => $user_id,
                'is_standby' => 1,
                'timezone' => $timezone,
                'status' => 1,
            ])->first();
            if (!$standByInvite) {
                return $this->jsonResponse(["message" => "Invite to main instructor failed"], false, false);
            }
            if ($standByInvite->notes->isNotEmpty() && empty($program_note_ids)) {
                $noteIds = $standByInvite->notes->pluck('program_note_id')->toArray();
                ProgramNote::whereIn('id', $noteIds)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            } elseif (!empty($program_note_ids)) {
                ProgramNote::whereIn('id', $program_note_ids)->where('program_id', $programId)->update(['sub_user_id' => $user_id]);
            }
            $obj =   invite_programs::create([
                'program_id' => $programId,
                'user_id' => $user_id,
                'program_invite_type' => $program_invite_type,
                'deadline' => $deadline ?? null,
                'timezone' => $timezone,
                'replacement_start_date' => $replacement_start_date ?? null,
                'admin_type' => 0,
                'type' => 0,
                'status' => 1,
                'is_approved' => 1,
                'is_sub_only' => 1,
                'is_standby' => 0,
            ]);
            return $this->jsonResponse(["message" => "Successfully Assigned"]);
        }
        if ($request->replacement) {
            $replacement = 1;
        } else {
            $replacement = 0;
        }
        $obj = invite_programs::create([
            'program_id' => $programId,
            'user_id' => $user_id,
            'program_invite_type' => $program_invite_type,
            'deadline' => $deadline ?? null,
            'timezone' => $timezone,
            'replacement_start_date' => $replacement_start_date ?? null,
            'admin_type' => $assign_type,
            'is_sub_only' => $assign_type == 0,
            'is_standby' => $standBy,
            'is_replace' => $replacement,
        ]);
        if (!$all_classes && $assign_type == 0 && $obj) {
            ProgramNote::whereIn('id', $program_note_ids)->update(['is_sub_requested' => 1]);
            foreach ($program_note_ids as $program_note_id) {
                $inviteProgramNote = new InviteProgramNote();
                $inviteProgramNote->invite_program_id = $obj->id;
                $inviteProgramNote->program_note_id = $program_note_id;
                $inviteProgramNote->save();
            }
        }
        if ($program->school_name) {
            $school_name = institudeName($program->school_name);
        } else {
            $school_name = '';
        }

        $sendFooter = true;
        if ($nassign_type === "1") {
            createProgramCommanNotification($user_id, $deadline, $program, '9', 'user', 'user', $school_name, $sendFooter, $classes);
        } elseif ($nassign_type === "0") {
            createProgramCommanNotification($user_id, $deadline, $program, '18', 'user', 'user', $school_name, $sendFooter, $classes);
        } elseif ($nassign_type === 'standBy') {
            createProgramCommanNotification($user_id, $deadline, $program, '12', 'user', 'user', $school_name, $sendFooter, $classes);
        }
        return $this->jsonResponse(["message" => "Successfully Invited"]);
    }

    public function cancelSub(Programs $program, Request $request)
    {
        $currentDate = now()->toDateString();

        $programNoteIds = ProgramNote::where('class_date', '>=', $currentDate)
            ->whereNull(['note', 'status'])
            ->where(function ($query) {
                $query->whereNotNull('sub_user_id')
                    ->orWhere('is_sub_requested', 1);
            })
            ->whereIn('id', $request->program_note_id)
            ->pluck('id')->toArray();

        $sub_userIds = ProgramNote::where('class_date', '>=', $currentDate)
            ->whereNull(['note', 'status'])
            ->where(function ($query) {
                $query->whereNotNull('sub_user_id')
                    ->orWhere('is_sub_requested', 1);
            })
            ->whereIn('id', $request->program_note_id)
            ->pluck('sub_user_id')->toArray();


        if (empty($programNoteIds)) {
            return $this->jsonResponse(["message" => "Sub instructor removal failed"], false, false);
        }
        InviteProgramNote::whereIn('program_note_id', $programNoteIds)->forceDelete();
        if (!empty($sub_userIds)) {
            invite_programs::where('program_id', $program->id)
                ->where('admin_type', 0)
                ->where('is_standby', '!=', 1)
                ->whereIn('user_id', $sub_userIds)
                ->whereDoesntHave('notes')
                ->forceDelete();
        }
        $cancelled = ProgramNote::whereIn('id', $programNoteIds)->update([
            'sub_user_id' => null,
            'is_sub_requested' => 2,
            'user_sub_requested' => 4,
        ]);



        if (!$cancelled) {
            return $this->jsonResponse(["message" => "Sub instructor removal failed"], false, false);
        }

        return response()->json(['status' => true, 'reload' => true, 'message' => 'Sub instructor removed successfully']);
    }

    public function deleteStandBy($id)
    {

        $invite = invite_programs::findOrFail($id);

        $res = $invite->forceDelete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
                'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }

    public function removeMainInstructor(Programs $program, Request $request)
    {
        $programIds = [$program->id];
        if ($program->sub_subject_id) {
            $subsubject = subsubjectname($program->sub_subject_id);
        } else {
            $subsubject = '';
        }
        $storeRoute = route('admin.program.remove-instructor.store');
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $now = \Carbon\Carbon::now()->timezone($timezone)->format('m-d-Y');
        $userNotes = $program->userNotes()
                                ->whereNotIn('status', ['0', '1', '2', '3'])
                                ->orderBy('class_date', 'asc')
                                ->pluck('class_date')
                                ->map(function ($date) {
                                    return \Carbon\Carbon::parse($date)->format('m/d/Y'); // Format to mm/dd/yyyy for datepicker
                                })
                                ->toArray();
        $start_date = \Carbon\Carbon::parse($program->start_date)->timezone($timezone)->format('m-d-Y');
        $end_date = \Carbon\Carbon::parse($program->end_date)->timezone($timezone)->format('m-d-Y');
        $view = view("components.admin.modals.remove-main-Instructor", compact('storeRoute', 'programIds', 'program','userNotes','start_date','end_date','now','subsubject'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function removeMainInstructorStore(Programs $program, Request $request)
    {
        $program_id = $request->program_id;
        $instructor_id = $request->instructor_id;
        $user = Users::where('id', $request->instructor_id)->first();
        $removeDate = $request->remove_date;
        // Example of formatting dates correctly
        $endDate = \Carbon\Carbon::createFromFormat('m-d-Y', $request->end_date)->format('Y-m-d');


        $programNotes = ProgramNote::where('program_id', $program_id)->whereBetween('class_date', [$removeDate, $endDate])->get();
        foreach ($programNotes as $note) {
            $note->user_id = null;
            $note->update(); // Save the changes to the database
        }
        sendNotificationRemoveInstructor($program_id,$request->school_name, $request->sub_subject,$request->start_date,$user);
        return $this->jsonResponse(["message" => "Successfully Removed Instructor"]);
    }

    public function assignMarketplaceInstructor(Programs $program, Request $request)
    {
        $programIds = [];
        $programIds[] = $program->id;

        $storeRoute = route('admin.program.assign-marketplace-instructor.store');

        $slots = DB::table("tbl_program_slots")
            ->where("program_id", $program->id)
            ->get();
        $timezone = $program->timezone ?? 'America/Los_Angeles';
        $startDate = $program->start_date ? \Carbon\Carbon::parse($program->start_date)->timezone($timezone)->format('m/d/Y') : null;
        $endDate = $program->end_date ? \Carbon\Carbon::parse($program->end_date)->timezone($timezone)->format('m/d/Y') : null;
        $view = view("components.admin.modals.assign-marketplace-Instructor", compact('storeRoute', 'programIds', 'program', 'slots', 'timezone', 'startDate', 'endDate'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function shortlistMarketplaceInstructorStore(Request $request)
    {
        if (!empty($request->instructor)) {
            $existInstructor = OnboardingInstructor::find($request->instructor);
            if ($existInstructor) {
                $shortlist = new ShortlistMarketplaceInstructorModel();
                $shortlist->program_id = $request->program_id;
                $shortlist->user_id = $request->instructor;
                $shortlist->status = $request->user_status;
                $shortlist->save();
            } else {
                return $this->jsonErrorResponse(["message" => "Instructor not found"]);
            }

            return $this->jsonResponse(["message" => "Successfully Added"]);
        }

        return $this->jsonErrorResponse(["message" => "Instructor not found"]);
        
    }

    public function marketplaceInstructorList(Request $request)
    {
        $program = Programs::find($request->program_id);

        if ($request->ajax()) {


            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName']   = 'new_onboarding_instructor.id';
            }

            $qry = OnboardingInstructor::query();
            $qry->whereIn("user_status", ['UnderReview','Active']);
            
            // $qry->select('users.*')
            //     ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc')
            //     ->orderBy('inpersonrate', 'ASC')
            //     ->orderBy('onlinerate', 'ASC');

            $qry->where(function ($que) use ($params) {
                $que->where(function ($que) use ($params) {
                    DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                })->orWhere(function ($query) use ($params) {
                    $searchValue = $params['searchValue'];
                    $query->where('first_name', 'LIKE', "%{$searchValue}%");
                    $query->orWhere('last_name', 'LIKE', "%{$searchValue}%");
                    $query->orWhere('email', 'LIKE', "%{$searchValue}%");
                    $query->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchValue}%"]);
                });
            });

            $result = $qry->get();

                [$count, $result] = DataTableHelper::applyCustomPagination($result, $params['row'], $params['rowperpage']);


            $data = [];
            $i = 1;
            foreach ($result as $row) {

                    $viewButton = $viewStatusButton = "";
                    $encryptedStrId = encrypt_str($row->id);
                    

                    $viewRoute =  url('admin/set-instructor-id/' . $encryptedStrId);
                    $viewButton .= "<input type='radio'  value='{$row->id}' form='assigntoinstructor' name='instructor'> ";
                    $viewButton .= " <a href='{$viewRoute}' target='_blank'>{$row->first_name} {$row->last_name}</a>";

                    $viewStatusButton .= "<input type='hidden'  value='{$row->user_status}' form='assigntoinstructor' name='user_status'> "; 
                    $viewStatusButton .= $row->user_status; 
                    $data[] = [
                        "id" => $row->id,
                        "first_name" => $viewButton,
                        "email" => $row->email,
                        "status" => $viewStatusButton,
                    ];

                    $i++;
                
            }
            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
    }

    public function marketplaceInstructorListDelete($id)
    {
        $instructor = ShortlistMarketplaceInstructorModel::find($id);

        $res = $instructor->delete();

        if ($res) {
            return response()->json([
                "status" => true,
                "message" => "Successfully deleted",
                'reload' => true
            ]);
        } else {
            return response()->json([
                "status" => false,
                "message" => "Something went worng",
            ]);
        }
    }
}
