<?php

namespace App\Console;

use App\Console\Commands\UploadAdminFilesToS3;
use App\Console\Commands\Feedbacks\AfterProgramCommand;
use App\Console\Commands\Feedbacks\FirstClassCommand;
use App\Console\Commands\Feedbacks\FirstMonthCommand;
use App\Console\Commands\Feedbacks\FirstQuarterCommand;
use App\Console\Commands\Reminders\BackgroundCheckCommand;
use App\Console\Commands\Reminders\CheckClassNotesCommand;
use App\Console\Commands\Reminders\IncompleteApplicationCommand;
use App\Console\Commands\Reminders\MainInstructorInviteCommand;
use App\Console\Commands\Reminders\MedicalCheckCommand;
use App\Console\Commands\Reminders\NextWeekProgramsCommand;
use App\Console\Commands\Reminders\PendingContractCommand;
use App\Console\Commands\Reminders\StandByInstructorInviteCommand;
use App\Console\Commands\Reminders\StandByProgramsCommand;
use App\Console\Commands\Reminders\SubInstructorInviteCommand;
use App\Console\Commands\Reminders\TodayInstructorCommand;
use App\Console\Commands\Reminders\TomorrowInstructorCommand;
use App\Console\Commands\MarketplaceInstructor\AfterThreeDaysProfileIncompleteCommand;
use App\Console\Commands\MarketplaceInstructor\AfterFiveDaysProfileIncompleteCommand;
use App\Console\Commands\MarketplaceInstructor\AfterTenDaysProfileIncompleteCommand;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $timezone = 'reminder:stand-by-programs';

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        TodayInstructorCommand::class,
        TomorrowInstructorCommand::class,
        IncompleteApplicationCommand::class,
        PendingContractCommand::class,
        MainInstructorInviteCommand::class,
        SubInstructorInviteCommand::class,
        StandByInstructorInviteCommand::class,
        StandByProgramsCommand::class,
        NextWeekProgramsCommand::class,
        CheckClassNotesCommand::class,
        BackgroundCheckCommand::class,
        MedicalCheckCommand::class,

        UploadAdminFilesToS3::class,
        AfterProgramCommand::class,
        FirstClassCommand::class,
        FirstMonthCommand::class,
        FirstQuarterCommand::class,
        //Commands\CustomTask::class,
        AfterThreeDaysProfileIncompleteCommand::class,
        AfterFiveDaysProfileIncompleteCommand::class,
        AfterTenDaysProfileIncompleteCommand::class,
        \App\Console\Commands\MigrateWithoutFKChecks::class,
    ];
    
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('logs:clear-old')->daily();
        
        /**
         * make sure of having all files in s3 bucket
         * and remove them from local storage
         */
        $schedule->command('upload:admin-files-to-s3')->hourly();
        /**
         * Send reminders to All instructors One day before the background check is due or every two days?
         */
        $schedule->command('reminder:background-check --type=oneday')
            ->daily();
        $schedule->command('reminder:background-check --type=everytwodays')
        ->cron('0 0 */2 * *');

        /**
         * Send reminders to All instructors One day before the medical check is due or every two days?
         */
        $schedule->command('reminder:medical-check --type=oneday')
            ->daily();

        $schedule->command('reminder:medical-check --type=everytwodays')
        ->cron('0 0 */2 * *');

        /**
         * Send reminders to All instructors with no class notes submission for classes completed on that day
         */
        $schedule->command('reminder:check-class-notes')
            ->timezone('America/Chicago')  // Central Time
            ->dailyAt('18:00');  // 6 PM

        /**
         * Send reminders to All instructors assigned as main or sub instructor for at least one program with a class next week
         */
        $schedule->command('reminder:next-week-programs')
            ->timezone('America/Chicago')  // Central Time
            ->thursdays()
            ->at('18:00');  // 6 PM
        /**
         * Send reminders to All instructors marked as standby
         */
        $schedule->command('reminder:stand-by-programs')
            ->timezone('America/Chicago')  // Central Time
            ->thursdays()
            ->at('18:00');  // 6 PM

        /**
         * Send reminders to instructors One day after sending the invite to be standby instructor
         */
        $schedule->command('reminder:stand-by-instructor-invite')
            ->daily();

        /**
         * Send reminders to instructors One day after sending the invite to be sub instructor and if no instructor has accepted invite yet
         */
        $schedule->command('reminder:sub-instructor-invite')
            ->daily();

        /**
         * Send reminders to instructors One day after sending the invite to be main instructor and if no instructor has accepted invite yet
         */
        $schedule->command('reminder:main-instructor-invite')
            ->daily();

        /**
         * Send reminders to instructors with incomplete application
         */
        $schedule->command('reminder:incomplete-application')
            ->daily();

        /**
         * Send reminders to instructors If candidate hasn't signed contract 1 day after sending contract, and 3 days after sending contract
         */
        $schedule->command('reminder:pending-contract')
            ->daily();

        /**
         * All instructors teaching the next day
         */
        $schedule->command('reminder:tomorrow-instructors')
            ->dailyAt('16:00') // 4 pm
            ->timezone('America/Chicago');

        /**
         * All instructors teaching the today
         */
        $schedule->command('reminder:today-instructors')
            ->dailyAt('05:00') // 5 am
            ->timezone('America/Chicago');

        /**
         * Ask feedback from instructors after completion of program
         */
        $schedule->command('feedback:after-program')
            ->daily();

        /**
         * Ask feedback from instructors after completion of first-month of program
         */
        $schedule->command('feedback:first-month')
            ->daily();

        /**
         * Ask feedback from instructors after completion of first-quarter of program
         */
        $schedule->command('feedback:first-quarter')
            ->daily();

        /**
         * Ask feedback from instructors after completion of first-class of program
         */
        $schedule->command('feedback:feedback:first-class')
            ->everyFiveMinutes();

        $schedule->command('programs:sent-notification-for-new-programs')
            ->everyFiveMinutes();

        $schedule->command('programs:sent-email-for-new-programs')
            ->everyFiveMinutes();
        /**
         * Ask profile complete from marketplace instructor after 3 days
         */
        // $schedule->command('MarketplaceInstructor:after-three-days')
        //     ->daily();
        
        /**
         * Ask profile complete from marketplace instructor after 5 days
         */
        // $schedule->command('MarketplaceInstructor:after-five-days')
        //     ->daily();

        /**
         * Ask profile complete from marketplace instructor after 10 days
         */
        // $schedule->command('MarketplaceInstructor:after-ten-days')
        //     ->daily();

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    /*     protected function scheduleTimezone()
    {
        return 'America/Chicago';
    } */
}
