<?php

namespace App\Http\Controllers;

use App\AdditionalCertificateCategory;
use App\AdditionalCertificateCategoryModel;
use App\AdditionalCertificateSubcategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdditionalCertificate extends Controller
{


    public function index()
    {


        $categories = AdditionalCertificateCategory::all();

        return view("admin.additional_certificates.index", compact('categories'));
    }
    public function add_additional_category()
    {

        return view("admin.additional_certificates.add_additonal_category");
    }
    public function storeCategory(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'category' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput(); // keeps the old input values
        } else {

            $category = new AdditionalCertificateCategory();
            $category->name = $request->category;
            $category->save();

            return redirect()->to('/additional_certificate');
        }
    }
    public function add_sub_category($id)
    {

        $decryptId = decrypt_str($id);

        $subcategories = AdditionalCertificateSubcategory::with('category')
            ->where('category_id', $decryptId)
            ->get();
        $category = AdditionalCertificateCategory::where("id", $decryptId)->first();


        return view('admin.additional_certificates.add_additional_subcategory', compact('subcategories', 'category'));
    }

    public function storesubcategory(Request $request)
    {

        $hasSubcategories = AdditionalCertificateSubcategory::where("category_id", $request->category_id)->exists();

        if ($hasSubcategories) {
            AdditionalCertificateSubcategory::where("category_id", $request->category_id)->delete();
        }

        foreach ($request->subcategory as $singlecategory) {

            $subcategory = new AdditionalCertificateSubcategory();
            $subcategory->category_id = $request->category_id;
            $subcategory->name = $singlecategory;
            $subcategory->save();
        }

        return  redirect()->to("/additional_certificate");
    }

    public function delete_existing_sub_Category(Request $request)
    {

        $subcategory_id = $request->id;

        $delete_sub_category = AdditionalCertificateSubcategory::where("id", $subcategory_id)->delete();

        if ($delete_sub_category) {

            return response()->json([
                "status" => "success",
                "message" => "subcategory deleted successfully",
            ]);
        }
    }
    public function edit_category_view($id){
        
          $id= decrypt_str($id);
           
          $category=AdditionalCertificateCategory::where("id",$id)->first();


          return view("admin.additional_certificates.update_category",compact("category"));

    }

    public function updatecategory(Request $request){

        $category_id=$request->category_id;
        
        $category = AdditionalCertificateCategory::find($category_id);

        if ($category) {
            // Update the category name
            $category->name = $request->category;
            
            // Save changes
            $category->save();
            
            
           return redirect()->to("/additional_certificate");
            
        } else {
            return response()->json(['success' => false, 'message' => 'Category not found.']);
        }



    }

    public function delete_existing_category(Request $request){

        $categor_id= decrypt_str($request->id);
       
        $deletecategory=AdditionalCertificateCategory::where("id",$categor_id)->delete();

        if($deletecategory){

            return response()->json([
                "status" => "success",
                "message" => "Category deleted successfully",
            ]);
        }

    }
}
