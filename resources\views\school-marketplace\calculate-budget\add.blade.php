<style>
    .select2-container.select2-container--open {
        z-index: 9999 !important;
    }

    .select2-search.select2-search--dropdown {
        position: relative;
        font-size: 16px;
    }

    .select2-search.select2-search--dropdown input {
        padding-left: 24px;
    }

    .select2-search.select2-search--dropdown::after {
        content: "\f002";
        font-family: FontAwesome;
        /* use FA 4 font */
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #555;
        /* icon color */
        font-size: 16px;
        /* matching size */
    }

    .select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
        color: black !important;
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        font-weight: 400;
        font-size: 16px !important;

    }

    .select2-container--bootstrap4 .select2-selection--single {
        border: 1px solid black;

    }

    /* .dollar_absolute {
        right: 197px !important;
        font-weight: bold;
        color: #004CBD;

    } */
    .dollar-prefix {
        position: relative;
        padding-left: 20px;
    }

    .dollar-prefix::before {
        content: "$";
        position: absolute;
        left: 71px;
        top: 50%;
        transform: translateY(-50%);
        color: #004CBD;
        font-weight: bold;
        pointer-events: none;
    }

    .custom-select-wrapper .select2-selection__arrow {
        display: none !important;
    }

    .error_border {
        border: 1px solid red !important;
    }

    .select2 .select2-selection__rendered {
        color: black !important;
        font-size: 16px !important;
    }

    /* .select2-container--bootstrap4.select2-container--focus .select2-selection {
        border-color: black !important;
    } */
</style>


<div class="modal fade budgetcalculatormodal @if(!empty($budget)) edit_model @endif" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width:1106px!important;" role="document">
        <div class="modal-content p-3 budget_modal_content @if(!empty($budget)) edit_model_content @endif">
            <div class="modal-header" style="border-bottom:none">
                <div class="d-flex align-items-center">
                    <p class="ms-3  new_card_text heading_text bold mb-0">Calculate budget for your requirement</p>
                </div>

                <button type="button" class="close_budgetcontent_modal" data-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 1.5rem; line-height: 1; color: #000;">
                    <span aria-hidden="true" style="font-size:2rem;color:#787777">&times;</span>
                </button>
            </div>
            <div class="">
                <p style="font-size:14px;width:85%;margin-left:3%">Enter required details to calculate the budget. You can change the values to adjust as per desired budget.
                    You can save budget as well and use it later for posting a class.</p>
            </div>
            <!-- <div class="container">
                <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
            </div> -->
            <div class="mx-5">
                <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">

            </div>

            <div class="modal-body">
                <form id=@if(!empty($budget)) "savebudget_edit_form" @else "savebudget_form" @endif>
                    <div class="row d-flex justify-content-between">

                        <div class="col-md-6">

                            <div class="row d-flex align-items-center">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Requirement Type<span class="text-danger">*</span></label>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:48px">

                                        <select
                                            class="budget_requirement_type error_calculate_budget delivery_mode  budget_input"
                                            name="requirement_type"
                                            style="border:1px solid black!important;border-radius:32px!important; padding:3% 6%;"
                                            onfocus="this.style.outline='none'; this.style.boxShadow='none';">
                                            <option value="" disabled hidden class="placeholder-option" selected>Select</option>
                                            <option value="Class" {{ (!empty($budget) && $budget->requirement_type == 'Class') ? 'selected' : '' }}>Class</option>
                                            <option value="Case Management" {{ (!empty($budget) && $budget->requirement_type == 'Case Management') ? 'selected' : '' }}>Case Management</option>
                                            <option value="Speech therapy" {{ (!empty($budget) && $budget->requirement_type == 'Speech therapy') ? 'selected' : '' }}>Speech therapy</option>
                                            <option value="Other" {{ (!empty($budget) && $budget->requirement_type == 'Other') ? 'selected' : '' }}>Other</option>
                                        </select>

                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                            </svg>
                                        </div>
                                    </div>


                                </div>
                                <div class="row d-flex justify-content-between my-3">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Subject<span class="text-danger">*</span></label>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper  " style="width:250px;margin-right:48px">

                                        <select class="budget_sub_subject select22 new_budget error_calculate_budget budget_input" id="subject" name="subject_id" style="outline:none;">
                                            <option value="" selected hidden style="color:black">Select</option>
                                            @foreach ($subjectArea as $subjects)
                                            @if (!empty($subjects->subject_area) && $subjects->subjects->isNotEmpty())
                                            <optgroup label="{{ $subjects->subject_area }}">
                                                @foreach ($subjects->subjects as $subject)
                                                @if (!empty($subject->title))
                                                <option
                                                    value="{{ (int) $subject->id }}"
                                                    data-amount_base="{{ number_format($subject->subjectBudget->base_pay_0_3, 2, '.', '') }}"
                                                    data-amount_inc_3_6="{{ number_format($subject->subjectBudget->pay_3_6, 2, '.', '') }}"
                                                    data-amount_inc_6_10="{{ number_format($subject->subjectBudget->pay_6_10, 2, '.', '') }}"
                                                    data-amount_inc_10="{{ number_format($subject->subjectBudget->pay_10_plus, 2, '.', '') }}"
                                                    data-amount_curriculum="{{ number_format($subject->subjectBudget->curriculum_inc, 2, '.', '') }}"
                                                    {{ (!empty($budget) && $budget->subject_id == $subject->id) ? 'selected' : '' }}>
                                                    {{ $subject->title }} {{ (!empty($budget) && $budget->subject_id == $subject->id) ? 'selected' : '' }}
                                                </option>
                                                @endif
                                                @endforeach
                                            </optgroup>
                                            @endif
                                            @endforeach
                                        </select>


                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                            </svg>
                                        </div>
                                    </div>


                                </div>
                                <div>

                                </div>
                                <div>

                                </div>
                                <div class=" col-md-4 mt-4">
                                    <label class="form-label platform_school_label m-0 d-inline-block">Educator Profile<span class="text-danger">*</span></label>
                                </div>
                                <!-- //credentialed_or_non_credentialed -->
                                <div class="col-md-6 mt-4">
                                    <div class="col d-inline-flex align-items-center">

                                        <div class="form-check-inline d-flex">
                                            <input class="form-check-input custom-input credentialed budget_input"
                                                type="radio"
                                                name="educator_profile"
                                                value="Credentialed"
                                                style="height:18px!important"
                                                @if (empty($budget) || (!empty($budget) && $budget->educator_profile == 'Credentialed')) checked @endif>
                                            <label class="form-check-label custom-lab" for="classType" style="color:black!important">Credentialed</label>
                                        </div>

                                        <div class="form-check-inline d-flex align-items-center">
                                            <input class="form-check-input custom-input non_credentialed budget_input"
                                                type="radio"
                                                name="educator_profile"
                                                id="nonTeachingType"
                                                value="Non-credentialed"
                                                style="height:18px"
                                                @if (!empty($budget) && $budget->educator_profile == 'Non-credentialed') checked @endif>
                                            <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Non-credentialed</label>
                                        </div>


                                    </div>
                                </div>

                            </div>


                            <!-- special education required -->
                            <div class="row d-flex align-items-center my-3">
                                <div class=" col-md-4">
                                    <label class="form-label platform_school_label m-0 d-inline-block">Special education certification required<span class="text-danger">*</span></label>
                                </div>
                                <div class="col-md-6">
                                    <div class="col d-inline-flex align-items-center">

                                        <div class="form-check-inline d-flex">
                                            <input class="form-check-input custom-input budget_input"
                                                type="radio"
                                                id="classType"
                                                name="special_education_certification"
                                                value="0"
                                                style="height:18px!important"
                                                @if (empty($budget) || (!empty($budget) && $budget->special_education_certification == 0)) checked @endif>
                                            <label class="form-check-label custom-lab" for="classType" style="color:black!important">No</label>
                                        </div>

                                        <div class="form-check-inline d-flex align-items-center ">
                                            <input class="form-check-input custom-input budget_input"
                                                type="radio"
                                                name="special_education_certification"
                                                id="nonTeachingType"
                                                value="1"
                                                style="height:18px"
                                                @if (!empty($budget) && $budget->special_education_certification == 1) checked @endif>
                                            <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Yes</label>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <!-- //delivery mode -->
                            <div class="row d-flex align-items-center my-3">
                                <div class=" col-md-4">
                                    <label class="form-label platform_school_label m-0 d-inline-block">Delivery Mode<span class="text-danger">*</span></label>
                                </div>
                                <div class="col-md-6">
                                    <div class="col d-inline-flex align-items-center">

                                        <div class="form-check-inline d-flex">
                                            <input class="form-check-input custom-input  budget_input"
                                                type="radio"
                                                name="delivery_mode"
                                                id="classType"
                                                value="Online"
                                                style="height:18px!important"
                                                @if (empty($budget) || (!empty($budget) && $budget->delivery_mode == "Online")) checked @endif>
                                            <label class="form-check-label custom-lab" for="classType" style="color:black!important">Online</label>
                                        </div>

                                        <div class="form-check-inline d-flex align-items-center">
                                            <input class="form-check-input custom-input non_credentialed"
                                                type="radio"
                                                name="delivery_mode"
                                                id="nonTeachingType"
                                                value="Hybrid"
                                                style="height:18px"
                                                @if (!empty($budget) && $budget->delivery_mode == "Hybrid") checked @endif>
                                            <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Hybrid</label>
                                        </div>


                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="col-md-5">
                            <div class="col-lg-12">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Years of Experience<span class="text-danger">*</span></label>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper ">
                                        <select class="delivery_mode error_calculate_budget budget_input" name="years_of_experience" style="border-radius:32px!important;max-width:300px!important">
                                            <option class="px-0" value="" hidden {{ empty($budget) ? 'selected' : '' }}>Select experience</option>
                                            <option class="px-0" value="0-3" {{ !empty($budget) && $budget->years_of_experience == '0-3' ? 'selected' : '' }}>0-3 years of experience</option>
                                            <option class="px-0" value="3-6" {{ !empty($budget) && $budget->years_of_experience == '3-6' ? 'selected' : '' }}>3-6 years of experience</option>
                                            <option class="px-0" value="6-10" {{ !empty($budget) && $budget->years_of_experience == '6-10' ? 'selected' : '' }}>6-10 years of experience</option>
                                            <option class="px-0" value="10+" {{ !empty($budget) && $budget->years_of_experience == '10+' ? 'selected' : '' }}>10+ years of experience</option>

                                        </select>
                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                            </svg>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="col-lg-12">

                                    <div class="row d-flex justify-content-between my-4">
                                        <div class="col-lg-6 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of instructional days<span class="text-danger">*</span></label>
                                        </div>

                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget) ? $budget->instructional_days : '' }}"
                                                placeholder="Enter"
                                                name="instructional_days"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input "
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;" />

                                            <div class=" input_absolute  position-absolute">
                                                days
                                            </div>


                                        </div>

                                    </div>


                                </div>


                            </div>

                            <div class="col-lg-12">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Class duration (in hours)<span class="text-danger">*</span></label>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper ">
                                        <select class="class_duration_budget error_calculate_budget budget_input" style="border-radius:32px!important;border:1px solid black" name="class_duration_hours">
                                            <option value="" disabled selected hidden class="placeholder-option">Select</option>
                                            <!-- @for ($minutes = 30; $minutes <= 1440; $minutes +=30)
                                                    @php
                                                    $decimalHour=$minutes / 60;
                                                    $label=fmod($decimalHour, 1)==0 ? intval($decimalHour) . ' Hr' : $decimalHour . ' Hr' ;
                                                    @endphp
                                                    <option value="{{ $minutes }}"
                                                    @if(!empty($schoolData) && !empty($schoolData->class_duration) && $schoolData->class_duration == $minutes) selected @endif>
                                                    {{ $label }}
                                                    </option>
                                                    @endfor -->
                                            @for ($minutes = 30; $minutes <= 1440; $minutes +=30)
                                                @php
                                                $decimalHour=$minutes / 60;
                                                $label=fmod($decimalHour, 1)==0 ? intval($decimalHour) . ' Hr' : $decimalHour . ' Hr' ;
                                                @endphp

                                                <option value="{{ $minutes }}"
                                                @if (
                                                (!empty($schoolData) && $schoolData->class_duration == $minutes) ||
                                                (!empty($budget) && $budget->class_duration_hours == $minutes)
                                                ) selected @endif>
                                                {{ $label }}
                                                </option>
                                                @endfor

                                        </select>
                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                            </svg>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="col-lg-12">

                                    <div class="row d-flex justify-content-between my-4">
                                        <div class="col-lg-6 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of non-instructional hours<span class="text-danger">*</span></label>
                                        </div>

                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget) ? $budget->non_instructional_hours : '' }}"
                                                placeholder="Enter"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input"
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;"
                                                name="non_instructional_hours" />

                                            <div class=" input_absolute  position-absolute">
                                                hours
                                            </div>


                                        </div>

                                    </div>


                                </div>


                            </div>
                            <div class="col-lg-12">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Language of Instruction<span class="text-danger">*</span></label>
                                    </div>
                                    @php
                                    $languages = json_decode($languages[0]->value); // decode the string into an array
                                    @endphp
                                    <div class="col-lg-4 position-relative custom-select-wrapper ">
                                        <select name="language_of_instruction" class="delivery_mode error_calculate_budget budget_input" style="border-radius:32px!important;border:1px solid black">
                                            <option value="" disabled selected hidden class="placeholder-option">Select</option>

                                            <!-- @foreach($languages as $lang)
                                                <option value="{{ $lang }}">{{ $lang }}</option>
                                                @endforeach -->
                                            @foreach($languages as $lang)
                                            <option value="{{ $lang }}"
                                                @if (!empty($budget) && $budget->language_of_instruction == $lang) selected @endif>
                                                {{ $lang }}
                                            </option>
                                            @endforeach

                                        </select>
                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                            </svg>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="col-lg-12">

                                    <div class="row d-flex justify-content-between">
                                        <div class="col-lg-6 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Expected class size<span class="text-danger">*</span></label>
                                        </div>

                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget)?$budget->expected_class_size:'' }}"
                                                placeholder="Enter"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input"
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;"
                                                name="expected_class_size" />
                                            <div class=" input_absolute_students  position-absolute">
                                                students
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p style="font-size:12px">(Max: 30 students)</p>


                            </div>
                        </div>

                    </div>
                    <div class="d-flex align-items-center mt-5">
                        <input @if(!empty($budget) && $budget->school_curriculum_provided==1) checked @endif type="checkbox" value=1 name="school_curriculum_provided" class="budget_input" style="accent-color: #004CBD;">
                        <p class="ms-2" style="color:black;font-size:12px;font-weight:500">Educator will use my school-provided curriculum and teaching materials on the school’s LMS</p>

                    </div>

                    <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
                    <div class="d-flex justify-content-end amount_budget_management my-5">
                        <div class="d-flex gap-2 ">
                            @if(!empty($budget))

                            <div data-button_class="calculate_budget_button_edit" class="border border-success px-3 py-2 calculate_budget_button_edit" style="background-color:#004CBD;color:white;border-radius:33px;cursor:pointer">
                                Budget
                            </div>
                            @else

                            <div data-button_class="calculate_budget_button" class="border border-success px-3 py-2 calculate_budget_button" style="background-color:#004CBD;color:white;border-radius:33px;cursor:pointer">
                                Calculate Budget
                            </div>


                            @endif

                            <div class="px-3 dollar-prefix py-2 position-realtive" style="border:2px solid #004CBD;border-radius:33px;color:004CBD">
                                <!-- <input style="outline:none;border:none;font-weight:bold;color:#004CBD" name="calculated_budget" class="text-center calculated_budget" type="number" value="0"> -->
                                <input
                                    style="outline: none; border: none; font-weight: bold; color: #004CBD"
                                    name="calculated_budget"
                                    class="text-center calculated_budget"
                                    type="number"
                                    value="{{ !empty($budget) ? $budget->calculated_budget : 0 }}">


                            </div>
                        </div>
                    </div>
                    <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
            </div>
            <div class="modal-footer py-3 mx-4" style="border-top: 0px; display: flex; justify-content: space-between; width: 95%;background-color:#F2F6FB;border-radius:12px">
                <div>
                    <!-- <p style="color:black;font-weight:bold">Looks good? Let’s post this class.</p>
                    <p>You can edit before posting</p> -->
                </div>
                <div class="w-50 d-flex justify-content-end gap-3">
                    <button type="button" id="post_requirement_submit_budget" class="btn px-2 py-3" data-dismiss="modal" style="border-radius:21px;border:1px solid black;padding:9px 15px!important;background-color:#004CBD;color:white;font-weight:700">Post Requirement</button>
                    @if (!empty($budget))
                    <button class="py-2 px-3"
                        

                        data-id="{{ $budget->id }}"
                        type="button"
                        id="edit_budget"
                        style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black; color:black">
                        Edit Budget
                    </button>
                    @else
                    <button class="py-2 px-3"
                        type="button"
                        id="save_budget"
                        style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black; color:black;display:none">
                        Save Budget
                    </button>

                    @endif


                </div>
            </div>
        </div>
    </div>

</div>
<div class="modal fade" id="dynamicModal" tabindex="-1" role="dialog" aria-labelledby="dynamicModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width:611px!important;" role="document">
        <div class="modal-content p-3 " style="box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);border-radius:23px">
            <div class="modal-header d-flex justify-content-end" style="border-bottom:0px!important">

                <button type="button" class="close cros_icon_budget_pop_up"  data-dismiss="modal" aria-label="Close"
                    style="font-size: 2rem; color: #787777; line-height: 1; padding: 0; height: auto; align-self: center; background-color: transparent; border: none;margin-bottom:-15px">
                    <span aria-hidden="true" class="cros_icon_budget_pop_up">&times;</span>
                </button>



            </div>
            <div class="modal-body budget_screen_pop_up">
                <h5  class="text-center heading lato_text" style="color:#004CBD;font-size:24px;font-weight:bold">Discard Budget</h5>
                <p style="color:#004CBD ;color:#004CBD" class="text-center lato_text sub_heading">The calculated budget is not saved.Closing this popup will permanently remove the saved budget. Do you want to save it?</p>
            </div>
            <div class="modal-footer d-flex justify-content-between" style="border-top:none">
               <button type="button"  class="btn cancel_button_budget_popup first_button" style="padding:2% 15%;border-radius:32px;border:1px solid #787777;color:#787777;font-weight:bold">cancel</button>
               <button type="button" class="btn yes_button_budget_popup second_button" style="padding:2% 17%;border-radius:32px;border:1px solid #004CBD;color:#004CBD;font-weight:bold">Yes</button>
            </div>
        </div>
    </div>
</div>