<?php

namespace App;

use App\Models\PlatformSchoolRequirements;
use Illuminate\Database\Eloquent\Model;

class SchoolInstructorHiring extends Model
{
    protected $table = 'school_instructor_hiring';

    protected $fillable = ['requirment_id', 'school_id', 'instructor_id', 'invited_by_school', 'payment_option', 'pay_hour', 'total_contract_cost', 'additional_contract_terms', 'contract_file', 'status'];

    public function user()
    {
        return $this->hasOne(OnboardingInstructor::class, 'id','instructor_id');
    }

    public function requirements()
    {
        return $this->hasOne(PlatformSchoolRequirements::class, 'id', 'requirment_id');
    }

}

?>