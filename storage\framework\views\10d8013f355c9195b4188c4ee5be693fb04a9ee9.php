

<?php $__env->startSection('title'); ?>School List | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php
    $res = get_permission(session('Adminnewlogin')['type']);
    $marketplacePermissions = isset($res['managemarketplace']) ? json_decode($res['managemarketplace'], true) : [];
?>
<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php if(isset($res['dashboard'])): ?>
                    <?php if(array_key_exists('dashboard', $res)): ?>
                        <?php if(in_array('add', json_decode($res['dashboard'], true))): ?>
                            <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if(in_array('platform schools', $marketplacePermissions)): ?>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('messages.list_platform_institute')); ?></li>
                    <li class="breadcrumb-item active float-right" aria-current="page"><a href="<?php echo e(url('admin/k12connections/add-platform-schools')); ?>"><?php echo e(__('messages.add_platform_institute')); ?></a></li>
                <?php endif; ?>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <div class="table-responsive">
            <table id="dataTable" class="table table-striped dtlist" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <!-- <th>#</th> -->
                        <th>School id</th>
                        <th>Instituts Name</th>
                        <th><?php echo e(__('messages.email')); ?></th>
                        <th>Organization Type</th>
                        <th>Organization Name</th>
                        <th><?php echo e(__('messages.status')); ?></th>
                        <th><?php echo e(__('messages.registration_date')); ?></th>
                        <th><?php echo e(__('messages.action')); ?></th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        <!-- END -->
        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/datatables.min.css')); ?>">
<script src="<?php echo e(asset('js/datatables.min.js')); ?>"></script>
<script>
    $(function() {
        if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
            dataTable.destroy();
        }
        window.dataTable = initializeAdminDataTable("#dataTable", "<?php echo e(url('/admin/k12connections/manage-platform-schools')); ?>", [
            {data: 'id',},
            {data: 'full_name'},
            {data: 'email'},
            {data: 'organizationtype'},
            {data: 'organizationname'},
            {data: 'status', searchable: false, orderable: false},
            {data: 'created_at'},
            {data: 'action', searchable: false, orderable: false},
        ]);
    });

    function status_update(id) {
        var url = base_url + 'change-status-institute';
        var status = $('.changestatuscls-' + id).data('data');
        if (status == 0) {
            confirm_message = 'Are you sure you want to Deactivate ?';
        } else {
            confirm_message = 'Are you sure you want to Deactivate ?';
        }
          update_status(id, url, confirm_message);
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/manageplatformschools.blade.php ENDPATH**/ ?>