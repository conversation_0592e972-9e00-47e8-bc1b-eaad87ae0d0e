<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\{User, Programs,ProgramNote};
use DB;
use App\Exports\Admin\ExportClasses;
use App\Exports\Admin\ExportCanceledClasses;
use Excel;


class ClassesController extends Controller
{
    public function index(Programs $program){
        $completedClasses =ProgramNote::with(['user', 'program'])
                    ->whereNotNull(['program_notes.note', 'program_notes.attendance', 'program_notes.status'])
                    ->orderBy('program_notes.updated_at','DESC')
                    ->get();
            foreach($completedClasses as $row){
                if ($row->program->school_name) {
                    $row->schoolName = schoolusername($row->program->school_name);
                } else {
                    $row->schoolName = '';
                }

                if ($row->program->sub_subject_id) {
                    $row->subject = subsubjectname($row->program->sub_subject_id);
                } else {
                    $row->subject = '';
                }

                if ($row->program->grade) {
                    $row->grade = getGrade($row->program->grade);
                } else {
                    $row->grade = '';
                }
            }
        return view('admin.complete-classes.index',compact('completedClasses'));
    }

    public function makeupCancel(Programs $program){
        // $userNotes = ProgramNote::with(['user', 'program'])
        //             ->whereNotNull(['program_notes.status'])
        //             ->where('program_notes.status',0)
        //             ->orderByRaw('cancelled_date DESC, cancelled_time DESC')
        //             ->get();

        $userNotes = ProgramNote::with(['user', 'program'])
                    ->where('program_notes.status', 0)
                    ->orderByRaw('cancelled_date DESC, COALESCE(cancelled_time, "00:00:00") DESC')
                    ->get();
        return view('admin.complete-classes.cancel-and-makeup',compact('userNotes'));
    }

    public function export(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Classes'.time().'.xlsx';
            return Excel::download(new ExportClasses($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

    public function cancelExport(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'ClassesCanceled'.time().'.xlsx';
            return Excel::download(new ExportCanceledClasses($request), $fileName,\Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }
}
