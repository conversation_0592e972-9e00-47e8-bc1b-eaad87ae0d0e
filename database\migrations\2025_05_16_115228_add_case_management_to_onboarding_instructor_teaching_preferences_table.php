<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCaseManagementToOnboardingInstructorTeachingPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('onboarding_instructor_teaching_preferences', function (Blueprint $table) {
            $table->boolean('case_management')->default(false)->after('language_teach_that_i_teach'); // Replace 'language_teach_that_i_teach' with the column after which you want this new one
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('onboarding_instructor_teaching_preferences', function (Blueprint $table) {
            $table->dropColumn('case_management');
        });
    }
}
